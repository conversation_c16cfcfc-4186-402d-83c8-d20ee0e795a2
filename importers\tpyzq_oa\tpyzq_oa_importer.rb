module AasOracleImporter
  class TpyzqOaImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space']
      days_of_data = importer_config['days_of_data'] || 150
      @days_of_data = Time.now - days_of_data.to_i.days
    end

    def import_to_do
      import_leave
      import_forced_leave
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'OA 系统')
    end

    private

    def users_sql
      <<-SQL
        select id, loginid, lastname,email  from #{@table_space}jihe_hrresource
      SQL
    end

    def users_json
      @users_json ||= get_users_json
    end

    def get_users_json
      users_json = {}
      @database.exec(users_sql) do |r|
        next if r[1].blank?
        user = User.find_by(id_number: r[1])
        next unless user
        user.update(email: r[3])
        users_json[r[0].to_s] = user
      end
      users_json
    end

    def import_leave_sql

      <<-SQL
        select shenqr,qingjrqq,qingjsjq,qingjrqz,qingjsjz from #{@table_space}jihe_zbqj
        union all
        select shenqr,qingjrqq,qingjsjq,qingjrqz,qingjsjz from #{@table_space}jihe_fzjgqj
      SQL
    end

    def import_leave
      @database.exec(import_leave_sql) do |r|
        start_time = Time.parse("#{r[1]} #{r[2]}")
        user = users_json[r[0].to_s]
        if start_time > @days_of_data && user
          notify = TpyzqOaNotify.find_or_create_by(
            user_id: user.id,
            notify_type: 'leave',
            start_time: start_time,
            end_time: "#{r[3]} #{r[4]}"
          )
        end
      end
    end

    def import_forced_leave_sql
      <<-SQL
        select qiangzlgrxm, qzlgksrq, qzlgjsrq, jiegrxm from #{@table_space}jihe_qzlg
      SQL
    end

    def import_forced_leave
      @database.exec(import_forced_leave_sql) do |r|
        next if r[1].blank?
        user = users_json[r[0].to_s]
        notify_user = users_json[r[3].to_s]
        start_time = Time.parse(r[1].to_s)
        if start_time > @days_of_data && user && notify_user
          notify = TpyzqOaNotify.find_or_create_by(
            user_id: user.id,
            notify_type: 'forced_leave',
            start_time: r[1],
            end_time: r[2]
          )
          notify.update(
            content: {to_user_id: notify_user.id},
            notify_user_ids: notify_user ? [notify_user.id] : []
          )
        end
      end
    end
  end
end
