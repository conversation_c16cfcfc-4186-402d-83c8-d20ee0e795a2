module AasOracleImporter

  class GuohuTaImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      super
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_additional_permissions
      import_acccounts_menus
      import_menus_roles

      import_ledgers(GuohuTa::Account)
    end

    def import_accounts_sql
      <<-SQL
        select u.vc_user_code,
               u.vc_user_name,
               u.c_status
          from
               #{@table_space}FM_USER#{@sid_suffix} u
          where
            u.dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')
          order by u.vc_user_name
      SQL
    end

    def import_accounts
      GuohuTa::Account.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            GuohuTa::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_i == 1
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_group_id, vc_group_name from #{@table_space}FM_GROUP#{@sid_suffix}
        where
          dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')
      SQL
    end

    # 考虑到历史情况，之前guohu_ta角色是没有编码的，很多客户又限制数据中心的字段，所以现在依然只导入名称为编码
    def import_roles
      GuohuTa::Role.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          GuohuTa::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          vc_menu_no, vc_menu_name
        from
          #{@table_space}FM_MENUITEM#{@sid_suffix}
        where
          c_menu_type = 1
          and dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')
        order by
          l_order
      SQL
    end

    def import_menus
      GuohuTa::Menu.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          GuohuTa::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_additional_permissions_sql
      <<-SQL
        select
          vc_menu_no,
          c_menu_right,
          vc_menu_right_name
        from
          #{@table_space}FM_EXTRAMENURIGHT#{@sid_suffix}
        where
          dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')
      SQL
    end

    def import_additional_permissions
      GuohuTa::AdditionalPermission.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_additional_permissions_sql) do |r|
          menu = GuohuTa::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          GuohuTa::AdditionalPermission.create(
            quarter_id: @quarter_id,
            sub_code: r[1],
            name: r[2],
            menu_id: menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          t.vc_menu_no,
          t.vc_menu_rights
        from 
          (select * from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) t,
          (select * from #{@table_space}FM_GROUP#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) g
        where 
          t.l_group_id = g.l_group_id
      SQL
    end


    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = GuohuTa::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          menu = GuohuTa::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            GuohuTaMenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              GuohuTa::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              GuohuTaAdditionalPermissionsRoles.create(
                role_id: role.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_acccounts_menus_sql
      <<-SQL
        select
          u.vc_user_code,
          t.vc_menu_no,
          t.vc_menu_rights
        from 
          (select * from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) t,
          (select * from #{@table_space}FM_USER#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) u
        where 
          t.l_user_id = u.l_user_id
      SQL
    end

    def import_acccounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_acccounts_menus_sql) do |r|
          account = GuohuTa::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = GuohuTa::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            GuohuTaAccountsMenus.create(menu_id: menu.id, account_id: account.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              GuohuTa::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              GuohuTaAccountsAdditionalPermissions.create(
                account_id: account.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          u.vc_user_code
        from
          (select * from #{@table_space}FM_GROUPUSER#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) gu,
          (select * from #{@table_space}FM_GROUP#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) g,
          (select * from #{@table_space}FM_USER#{@sid_suffix} where dt = date_format(date_sub(current_date(), 1), 'yyyyMMdd')) u
        where
          gu.l_group_id = g.l_group_id
          and gu.l_user_id = u.l_user_id
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = GuohuTa::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = GuohuTa::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            GuohuTaAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



