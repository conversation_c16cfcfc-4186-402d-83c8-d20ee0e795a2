module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'tuoguan_guzhi_'
  end
end

class TuoguanGuzhi::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :booksets
end

class TuoguanGuzhi::Bookset < ActiveRecord::Base
  has_and_belongs_to_many :accounts
end

class TuoguanGuzhi::Menu < ActiveRecord::Base
end

class TuoguanGuzhi::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_many :permissions
end

class TuoguanGuzhi::Permission < ActiveRecord::Base
  belongs_to :role
  belongs_to :menu
end

class TuoguanGuzhi::AccountsRoles < ActiveRecord::Base; end
class TuoguanGuzhi::AccountsBooksets < ActiveRecord::Base; end
