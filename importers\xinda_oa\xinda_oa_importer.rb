module AasOracleImporter
  class XindaOaImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @users       = []
      initialize_tables
    end

    def initialize_tables
      # 总部员工 入职日期表
      @table_join1 = "#{@table_space}U_F_XDZJ_YGBD#{@sid_suffix}"
      # 分支机构后台员工 入职日期表
      @table_join2 = "#{@table_space}U_F_FZJGYGRZSP#{@sid_suffix}"
      # 分支机构理财顾问及经纪人 入职日期表
      @table_join3 = "#{@table_space}U_F_FZJGYXYGRZ#{@sid_suffix}"
    end

    def import_to_do
      import_departments
      import_users
      change_old_user_status
    end

    private

    def import_department_sql
      <<-SQL
        select deptid, deptname, parentdeptid, shorder, hidedept
        from #{@table_space}t_dept
        where delflag=0 and (
          deptid in (8534, 9258, 9102, 9599, 10107, 8535, 8536) or
          parentdeptid in (8534, 9258, 9102, 9599, 10107, 8535, 8536) or
          parentdeptid in (select deptid from #{@table_space}t_dept where parentdeptid in (8534, 9258, 9102, 9599, 10107, 8535, 8536))
        )
      SQL
    end

    def import_departments
      rows = []
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code).uniq
      new_codes       = []

      ActiveRecord::Base.transaction do
        @database.exec(import_department_sql) do |row|
          new_codes << row[0]
          rows << row
        end
      end

      # 多余的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      create_departments(rows)

      Department.all.each do |dept|
        p1 = dept.parent
        p2 = dept.parent&.parent
        p3 = dept.parent&.parent&.parent
        p4 = dept.parent&.parent&.parent&.parent
        level = 1
        level += 1 if p1&.inservice
        level += 1 if p2&.inservice
        level += 1 if p3&.inservice
        level += 1 if p4&.inservice
        dept.update_column(:level, level)
      end
    end

    def create_departments(rows)
      rows.each { |row| find_or_create_department(row) }
      # 更新所有部门的parent_id
      Department.all.each do |d|
        # 获取当前部门的row记录
        row = rows.find { |r| r[0] == d.code }
        # 如果当前row记录的parent_code不会空
        next unless !row.nil? && !row[2].to_s.empty?

        p = Department.find_by(code: row[2], inservice: true)
        d.update_column(:parent_id, p&.id)
      end
    end

    def find_or_create_department(r)
      code      = r[0]
      name      = r[1]
      position  = r[3]
      inservice = r[4].to_s != '1'
      # parent_code = r[3]
      return nil if name.nil? || (name == '')
      return nil if code.nil? || (code == '')

      d = Department.find_or_create_by(code: code)
      d.update(name: name, position: position, inservice: inservice)
    end

    def import_users_sql
      <<-SQL
        select u.userid, u.username, u.delflag, d.deptid, d.deptname, d.parentdeptid, fd.lzrq, u.loginname, u.email, u.mobile, j1.SJBDRQ, j2.LDHTSXRQ, j3.HTSXRQ from #{@table_space}t_user u
        left join #{@table_space}t_dept d on d.deptid = u.deptid
        left join (SELECT xm_id, max(lzrq) AS lzrq FROM #{@table_space}u_f_departure where lzrq is not null GROUP BY xm_id) fd on fd.xm_id = u.userid
        left join #{@table_join1} j1  on j1.ygbh = u.loginname
        left join #{@table_join2} j2  on j2.ygbh = u.loginname
        left join #{@table_join3} j3  on j3.ygbh = u.loginname
      SQL
    end

    def import_users
      # login_name 是工号
      ActiveRecord::Base.transaction do
        @database.exec(import_users_sql) do |r|
          rzrqs = [r[10], r[11], r[12]]
          rzrq = rzrqs.find(&:present?)
          department        = Department.find_by(code: r[3])
          user              = User.find_or_create_by(code: r[0])
          user.name         = r[1]
          user.department   = department
          user.inservice    = r[2].to_s == '0'
          user.disable_date = r[6]
          user.join_date    = rzrq
          user.login_name   = r[7]
          user.email        = r[8]
          user.cellphone    = r[9]
          user.save
          @users << user
        end
      end
    end

    def change_old_user_status
      codes = User.pluck(:code) - @users.map(&:code)
      codes.each do |code|
        User.find_by(code: code)&.update(inservice: false)
      end
    end

    # def import_departments
    #   sql = <<-SQL
    #     select deptid, deptname, parentdeptid, shorder
    #     from #{@table_space}t_dept
    #     where delflag=0 and parentdeptid in (8534, 9021, 8560) and deptid not in (11284, 10890, 9186, 9158, 8556, 9024)
    #   SQL

    #   ActiveRecord::Base.transaction do
    #     @database.exec(sql) do |r|
    #       department = Department.find_or_create_by(code: r[0])
    #       department.name = r[1]
    #       department.inservice = true
    #       department.parent_id = r[2]
    #       department.position  = r[3]
    #       department.save
    #     end

    #     Department.all.each do |dept|
    #       dept.update(parent_id: Department.find_by(code: dept.parent_id)&.id)
    #     end

    #     Department.all.each do |dept|
    #       level = 1
    #       level += 1 if dept.parent
    #       level += 1 if dept.parent&.parent
    #       level += 1 if dept.parent&.parent&.parent
    #       level += 1 if dept.parent&.parent&.parent&.parent
    #       dept.update(level: level)
    #     end
    #   end
    # end

  end
end