module AasOracleImporter
  class JzTouguImporter < ImporterBase
    def config
      @bs_id       = 261
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      @data4_permissions = []
      @data4_accounts_roles_permissions = []

      @data5_permissions = []
      @data5_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}UUM_USER#{@sid_suffix}"
      @table_role         = "#{@table_space}UUM_ROLE#{@sid_suffix}"
      @table_account_role = "#{@table_space}UUM_USER_ROLE#{@sid_suffix}"
      @table_menu         = "#{@table_space}UUM_PERMISSION#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}UUM_USER_PERM#{@sid_suffix}"
      @table_account_menu = "#{@table_space}UUM_USER_PERM#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_role_permissions
      import_data2_account_permissions

      import_data3_permissions
      import_data3_role_permissions
      import_data3_account_permissions

      import_data4_permissions
      import_data4_role_permissions
      import_data4_account_permissions

      import_data5_permissions
      import_data5_role_permissions
      import_data5_account_permissions

      import_ledgers(JzTougu::Account)
    end

    def destroy_exist_datas
      accounts = JzTougu::Account.where(quarter_id: @quarter_id)
      JzTougu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all

      JzTougu::Role.where(quarter_id: @quarter_id).delete_all

      JzTougu::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzTougu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      JzTougu::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JzTougu::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      JzTougu::Data3Permission.where(quarter_id: @quarter_id).delete_all
      JzTougu::Data3AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      JzTougu::Data4Permission.where(quarter_id: @quarter_id).delete_all
      JzTougu::Data4AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      JzTougu::Data5Permission.where(quarter_id: @quarter_id).delete_all
      JzTougu::Data5AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select USER_CODE, USER_CODE, USER_NAME, USER_STA from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          account = JzTougu::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '1'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzTougu::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select ROLE_ID, ROLE_ID, ROLE_NAME from #{@table_role} where ROLE_STA = 1
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzTougu::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLE_ID, USER_CODE from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          JzTougu::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select PERM_ID, PERM_ID, PERM_NAME, PAR_PERM from #{@table_menu} where PERM_STA = 1 and PERM_TYPE = 1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << JzTougu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_role_menu} where OBJ_TYPE=1
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzTougu::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_account_menu} where OBJ_TYPE=2
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzTougu::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select PERM_ID, PERM_ID, PERM_NAME, PAR_PERM from #{@table_menu} where PERM_STA = 1 and PERM_TYPE = 2
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = get_enum(enums, 'level1_name', r[level1_name_index])

          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data2_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzTougu::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_role_menu} where OBJ_TYPE=1
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzTougu::Data2AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data2_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_account_menu} where OBJ_TYPE=2
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzTougu::Data2AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data2_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select PERM_ID, PERM_ID, PERM_NAME, PAR_PERM from #{@table_menu} where PERM_STA = 1 and PERM_TYPE = 3
      EOF
    end

    def import_data3_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data3_permissions_sql).each do |r|
          name = get_enum(enums, 'level1_name', r[level1_name_index])
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data3_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data3_permissions << JzTougu::Data3Permission.create(json)
        end
      end
    end

    def import_data3_role_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_role_menu} where OBJ_TYPE=1
      EOF
    end

    def import_data3_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzTougu::Data3AccountsRolesPermission.create(
            quarter_id: @quarter_id,
            role_id: role.id,
            data3_permission_id:
            permission.id, additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data3_account_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_account_menu} where OBJ_TYPE=2
      EOF
    end

    def import_data3_account_permissions
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzTougu::Data3AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data3_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def import_data4_permissions_sql
      <<-EOF
        select PERM_ID, PERM_ID, PERM_NAME, PAR_PERM from #{@table_menu} where PERM_STA = 1 and PERM_TYPE = 4
      EOF
    end

    def import_data4_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data4_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data4_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data4_permissions << JzTougu::Data4Permission.create(json)
        end
      end
    end

    def import_data4_role_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_role_menu} where OBJ_TYPE=1
      EOF
    end

    def import_data4_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data4_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data4_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzTougu::Data4AccountsRolesPermission.create(
            quarter_id: @quarter_id,
            role_id: role.id,
            data4_permission_id:
            permission.id, additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data4_account_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_account_menu} where OBJ_TYPE=2
      EOF
    end

    def import_data4_account_permissions
      select_db_datas(import_data4_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data4_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzTougu::Data4AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data4_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def import_data5_permissions_sql
      <<-EOF
        select PERM_ID, PERM_ID, PERM_NAME, PAR_PERM from #{@table_menu} where PERM_STA = 1 and PERM_TYPE = 5
      EOF
    end

    def import_data5_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data5_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data5_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data5_permissions << JzTougu::Data5Permission.create(json)
        end
      end
    end

    def import_data5_role_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_role_menu} where OBJ_TYPE=1
      EOF
    end

    def import_data5_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data5_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data5_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzTougu::Data5AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data5_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data5_account_permissions_sql
      <<-EOF
        select OBJ_ID, PERM_ID, AUTH_TYPE from #{@table_account_menu} where OBJ_TYPE=2
      EOF
    end

    def import_data5_account_permissions
      select_db_datas(import_data5_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data5_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzTougu::Data5AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data5_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    # 附件权限
    def additional_permission_text(code)
      case code.to_s
      when '1' then '管理菜单'
      when '2' then '门户菜单'
      when '3' then '组件权限'
      when '4' then '特殊权限'
      when '5' then '按钮'
      else
        code
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
