module AasOracleImporter

  class GreatlifeAssetImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_ledgers(GreatlifeAsset::Account)
    end

    def import_accounts

      GreatlifeAsset::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT
          USERCODE,
          USERNAME,
          COMCODE,
          EMAIL,
          USEFLAG
        FROM
          USERS
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            GreatlifeAsset::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1].strip,
              email: r[3]&.strip,
              company_code: r[2]&.strip,
              status: r[4]
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      GreatlifeAsset::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT USERGROUPID, USERGROUPNAME FROM USERGROUP
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GreatlifeAsset::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select USERID, USERGROUPID FROM USERINGROUP
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = GreatlifeAsset::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = GreatlifeAsset::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            GreatlifeAssetAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



