# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: mfcteda_test
  customer: 泰达宏利
  ipaddr: localhost
  log_level: DEBUG
  workday:
    enable: false
    mode: mfcteda
    config:
      tnsname: bidb
  quarter_format: previous_quarter
  server:
    path: "/opt/aas-app/aas"
    skip_after_import_rake: false
    after_importer_rake: after_import:manual_import_remind_to_admin
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_mfcteda_production
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
      host: 127.0.0.1
  agent:
    oracle:
      bidb:
        db_host: **********
        db_name: EDIC12C
        db_user: privaudit
        db_pass: priaudit2020!!
        db_port: '1522'
      fxq_db:
        db_host: **********
        db_name: aml12cts
        db_user: priv_audit
        db_pass: Audit_priv_2023
        db_port: '1527'
      kf_db:
        db_host: **********
        db_name: ccp12cts
        db_user: audit_priv
        db_pass: Audit_priv_2023
        db_port: '1527'
  importers:
  - name: guohu_sa
    bs_id: 26
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_sa_
    sid_suffix: ''
  - name: zhixiao_zhongxin
    bs_id: 27
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_ds_
    sid_suffix: ''
    sub_system: CENTER
  - name: zhixiao_guitai
    bs_id: 28
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_ds_
    sid_suffix: ''
    sub_system: COUNTER
  - name: yuancheng_guitai
    bs_id: 29
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_ds_
    sid_suffix: ''
    sub_system: REMOTE
  - name: guohu_ta
    bs_id: 30
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_ta_
    sid_suffix: ''
  - name: guzhi_yss45
    bs_id: 24
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_fa_
    post_table_name: t_s_user_post_dat
    sync_booksets_table: true
    sid_suffix: ''
  - name: yss_qingsuan
    bs_id: 22
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_qs_
    sid_suffix: ''
    import_bookset: false
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_trade_
    sid_suffix: ''
    days_of_data: 365
    #temporary: true
    #operator_no_importer: true
    #trade_type_importer: true
    #station_importer: true
    display_status: true

  # 恒生反洗钱系统
  - name: mfcteda_fxq
    bs_id: 311
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_aml_
    sid_suffix: ''
  # 客服系统
  - name: mfcteda_kefu
    bs_id: 310
    db_type: oracle
    tnsname: bidb
    table_space: ods.qxjh_ccp_
    sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: "/opt/aas-app/aas"
    skip_after_import_rake: false
    after_importer_rake: after_import:manual_import_remind_to_admin
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_mfcteda_production
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
      host: 127.0.0.1


