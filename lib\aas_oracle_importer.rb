# frozen_string_literal: true

require 'yaml'
require 'erb'
require 'date'
require 'fileutils'
require 'logger'
require 'pp'
require 'socket'
require 'parallel'
require 'etc'
require 'active_support'
# bulk_insert 对 ActiveRecord 的自动注册需要依赖于 active_support，因此需要先 require 'active_support'
require 'bulk_insert'
require 'trading_days'

require_relative 'aas_oracle_importer/agent'

module AasOracleImporter
  class NoSupportDriver < StandardError; end
  class DisabledBusinessSystem < StandardError; end
end

