module AasOracleImporter

  class BopImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
    end

    def import_to_do
      destroy_all_datas
      import_accounts
      import_permissions
      import_account_permissions
      import_ledgers(Bop::Account)
    end

    private

    def destroy_all_datas
      Bop::Account.where(quarter_id: @quarter_id).destroy_all
      Bop::Role.where(quarter_id: @quarter_id).destroy_all
      Bop::Permission.where(quarter_id: @quarter_id).destroy_all
      Bop::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select OPERATOR_NO, 
               OPERATOR_NAME, 
               OPER_STATUS,
               OP_BRANCH_NO, 
               MOBILE_TEL 
        from #{@table_space}OPERATORS
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          import_account(r)
        end
      end
    end

    def import_account(row)
      code, name, status, branch_code, cellphone = row
      return if code.to_s == '' || name.to_s == '' || status.to_s == ''

      account           = Bop::Account.find_or_initialize_by(quarter_id: @quarter_id, code: code)
      account.name      = name
      account.status    = status.to_i == 0
      account.cellphone = cellphone
      account.objid     = branch_code
      account.save
    end

    def import_roles_sql
      <<-SQL
        select ROLE_ID,ROLE_KIND,ROLE_NAME from #{@table_space}ROLES
      SQL
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        additional_json = {
          a: '非本柜查询允许',
          b: '无密码操作',
          c: '本柜冲销允许',
          d: '非本柜冲销允许',
          e: '卖出善意透支允许',
          f: '不允许修改客户姓名和身份证',
          g: '不受密码复杂度限制(密码永不过期)',
          l: '允许导入本地图片',
          m: '查询非本柜操作员日志',
          n: '允许修改档案任务图片是否必扫项',
          p: '允许查看客户影像时重扫',
          t: '允许交易时间做数据汇总',
          y: '允许查询数据导出',
          z: '允许操作员调整出资账户资金',
          G: '查询其他柜员委托',
          H: '撤销其他柜员委托',
          I: '查询销户客户',
          J: '查询非本网点操作员日志',
          T: '允许开市时间执行统计和报表菜单',
          k: '透支委托权限',
          F: '允许操作期货业务',
          S: '允许操作证券业务',
          X: '不受客户柜员限制',
          Z: '允许通办',
          h: '禁止查询数据打印',
          x: '禁止单客户查询模糊查询'
        }
        Bop::Permission.create(quarter_id: @quarter_id, code: 'asset', name: '资产账户', permission_type: '资产账户')
        additional_json.each do |key, value|
          Bop::Permission.create(quarter_id: @quarter_id, code: key.to_s, name: value, permission_type: '特殊权限')
        end

        @database.exec(import_roles_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            Bop::Permission.create(quarter_id: @quarter_id, code: r[0], name: r[2], permission_type: '角色权限')
          end
        end
      end
    end

    def import_user_right_sql
      <<-SQL
        SELECT USER_ID,OPER_ROLES,AUTH_ROLES,OPERATOR_RIGHTS,EN_ASSET_PROP,AUTH_OPERATOR_RIGHTS FROM #{@table_space}USERRIGHT
      SQL
    end

    def import_account_permissions
      ActiveRecord::Base.transaction do
        permission_list_json = {}
        Bop::Permission.where(quarter_id: @quarter_id).each do |permission|
          permission_list_json[permission.code] = permission.id
        end
        account_list_json = {}
        Bop::Account.where(quarter_id: @quarter_id).each do |account|
          account_list_json[account.code] = account.id
        end
        asset_key_json = { '0' => '普通', '7' => '信用', 'B' => '期权' }

        @database.exec(import_user_right_sql) do |r|
          account_id = account_list_json[r[0]]
          if account_id
            oper_roles  = r[1].each_char.map.with_index { |x, i| i if x == '1' }.compact.map { |x| (x + 1).to_s }
            auth_roles  = r[2].each_char.map.with_index { |x, i| i if x == '1' }.compact.map { |x| (x + 1).to_s }
            oper_rights = r[3].each_char.to_a

            oper_roles.each do |code|
              permission_id = permission_list_json[code]
              if permission_id
                additional_permission = auth_roles.include?(code) ? '操作,授权' : '操作'

                Bop::AccountsRolesPermission.create(
                  quarter_id:            @quarter_id,
                  permission_id:         permission_id,
                  account_id:            account_id,
                  additional_permission: additional_permission
                )
              else
                puts "没有找到权限： #{code} #{permission_id}"
              end
            end

            auth_roles.each do |code|
              permission_id = permission_list_json[code]
              if permission_id && !oper_roles.include?(code)
                Bop::AccountsRolesPermission.create(
                  quarter_id:            @quarter_id,
                  permission_id:         permission_id,
                  account_id:            account_id,
                  additional_permission: '授权'
                )
              else
                puts "没有找到权限：#{permission_id}"
              end
            end

            oper_rights.each do |code|
              permission_id = permission_list_json[code]
              if permission_id
                account_role_permission = Bop::AccountsRolesPermission.create(
                  quarter_id:    @quarter_id,
                  permission_id: permission_id,
                  account_id:    account_id
                )
              end
            end

            if r[4] != ' '
              permission_id = permission_list_json['asset']
              asset_list    = []
              r[4].each_char.to_a.each do |a|
                asset_list << asset_key_json[a]
              end
              account_role_permission = Bop::AccountsRolesPermission.create(
                quarter_id:            @quarter_id,
                permission_id:         permission_id,
                account_id:            account_id,
                additional_permission: asset_list.join(',')
              )
            end
          end
        end
      end
    end

  end
end



