module AasOracleImporter
  class MomImporter < ImporterBase
    def config
      @bs_id       = 309
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_ledgers(Mom::Account)
    end

    def destroy_exist_datas
      accounts = Mom::Account.where(quarter_id: @quarter_id)
      Mom::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Mom::Role.where(quarter_id: @quarter_id).delete_all

      Mom::Data1Permission.where(quarter_id: @quarter_id).delete_all

      Mom::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select
          c.operator_no,
          c.operator_code,
          c.operator_name,
          c.operator_status
        from
          dbtrade.yh_toperator c
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '1', 'enum_value' => '正常', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Mom::Account.create(
            quarter_id: @quarter_id,

            source_id:  get_enum(enums, 'source_id', r[0]),

            code:       get_enum(enums, 'code', r[1]),

            name:       get_enum(enums, 'name', r[2]),

            status:     r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<~EOF
        select
          u.role_id,
          u.role_id,
          u.role_name
        from
          (select
            c.role_id,
            c.role_name
          from
            dbtrade.yh_tcompanyrole c
          union
          select
            p.role_id,
            p.role_name
          from
            dbtrade.pub_trole p
          ) u
      EOF
    end

    # select
    #   a.role_id,
    #   a.role_code,
    #   a.role_name
    # from
    #   dbtrade.yh_toperrole a
    # where
    #   a.company_id in (9998,300000)

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Mom::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<~EOF
        select
          a.role_id,
          a.operator_no
        from
          dbtrade.yh_toperrole a
        where
          a.company_id in (9998,300000)
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          Mom::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<~EOF
        select
          d.menu_id,
          d.menu_id,
          d.menu_name,
          e.menu_right_name
        from
          dbtrade.pub_tmenu d,
          dbtrade.pub_tmenuitemfunc e
        where
          e.menu_id = d.menu_id
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        level2_name_index = 3
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name1 = r[level1_name_index]
          name2 = r[level2_name_index]

          level1_name = replace_blank_name(name1)
          level2_name = replace_blank_name(name2)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name,

            level2_name: level2_name

          }
          @data1_permissions << Mom::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<~EOF
        select
          u.role_id,
          u.menu_id
        from
          (
            select
              b.role_id,
              b.menu_id
            from
              dbtrade.yh_toperrole a,
              dbtrade.pub_trolemenu b,
              dbtrade.pub_tmenuitemfunc e
            where a.role_id = b.role_id
            and e.menu_right_id = b.menu_right_id
            and a.sys_role_flag = '1'
            union
            select
              b.role_id,
              b.menu_id
            from
              dbtrade.yh_toperrole a,
              dbtrade.yh_tcomrolemenu b,
              dbtrade.pub_tmenuitemfunc e
            where a.role_id = b.role_id
            and e.menu_right_id = b.menu_right_id
            and a.sys_role_flag = '0'
          ) u
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          Mom::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                   data1_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
