# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'default'
  customer: '测试'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server: &1
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_default_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent: &2
    mysql:
      data_center_db:
        db_host: ************
        db_name: data_center
        db_user: root
        db_pass: 123456

      aas_db:
        db_host: ************
        db_name: aas_default_production
        db_user: root
        db_pass: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"

  importers: &3
    - name: fake_user
      db_type: mysql
      tnsname: data_center_db
      expect_status: success
      is_user: true # 标记是否导入员工

    - name: jiayang_hr
      bs_id: 3
      db_type: mysql
      tnsname: data_center_db
      table_space: 'hr_'
      filter_user: # 过滤用户规则，blank_column空字段，regexp为正则
        blank_column:
          - department_id
        regexp:
          code: ^[S|s].*


    - name: didaima
      bs_id: 25
      db_type: mysql
      tnsname: data_center_db
      table_space: 'cloudpivot_'
      sid_suffix: ''

    - name: jiaoyi
      bs_id: 31
      db_type: mysql
      tnsname: data_center_db
      table_space: 'o32_'
      sid_suffix: ''
      time_control: false
      temporary: true
      days_of_data: 999
      # 导入员工号
      operator_no_importer: true
      # 交易类型
      trade_type_importer: true
      # 站点权限
      station_importer: true
      c_menu_type: [1]

    # 清算系统
    - name: qingsuan_department
      bs_id: 25
      db_type: mysql
      tnsname: data_center_db
      table_space: 'cis_'
      sid_suffix: ''

    - name: hspb
      bs_id: 52
      db_type: mysql
      tnsname: data_center_db
      table_space: hspbdbdg_
      sid_suffix: ''
      trade_type_importer: true
      station_importer: true
    - name: sdata_aas
      bs_id: 6666
      db_type: mysql
      tnsname: aas_db

    - name: fund_disclosure_fgfund
      bs_id: 32
      db_type: mysql
      tnsname: data_center_db
      table_space: 'fd_'
      sid_suffix: ''
      inservice_fund_statuses:
        - 运作开放
        - 运作封闭
        - 清算中
      disabled_fund_statuses:
        - 终止

    - name: external1
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external2
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external3
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external4
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external5
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external6
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external7
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''
    - name: external8
      db_type: mysql
      tnsname: aas_db
      table_space: 'external_'
      sid_suffix: ''

development:
  customer_id: default
  customer: 测试
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  workday:
    enable: true
    mode: 'trading_day'
  server: *1
  agent: *2
  importers: *3

test:
  customer_id: default
  customer: 测试
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_default_test
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: localhost
  agent: *2
  importers: *3

production:
  customer_id: default
  customer: 测试
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  workday:
    enable: true
    mode: 'trading_day'
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_default_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: localhost
  agent: *2
  importers: *3
