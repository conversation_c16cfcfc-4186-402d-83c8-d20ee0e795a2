require 'fileutils'
require 'pathname'
require 'yaml'
require 'erb'

module CustomerRakes
  CUSTOMER_FILES_PATTERNS = {
    /^.*\.yml/      => 'config',
    /^.*\.sh/       => 'bin',
    /CUSTOMER_GEMS/ => '.'
  }.freeze

  module_function

  def root_path
    Pathname.new File.expand_path('../..', __dir__)
  end

  def environment
    ENV['RAILS_ENV'] || 'development'
  end

  def list
    root_path.join('customers').each_child do |sub_dir|
      next if sub_dir.basename.to_s == 'default'
      next if sub_dir.basename.to_s == '.DS_Store'

      app_yml_file = sub_dir.join('app.yml')
      config       = read_yaml_file(app_yml_file)

      printf "%-10s => %-10s\n", sub_dir.basename.to_s, config['customer']
    end
  end

  def current
    config_app_yml = ENV['AAS_IMPORTER_CONFIG'] || '/opt/aas-app/config/importers/app.yml'
    app_yml_file = root_path.join(config_app_yml)

    unless app_yml_file.exist?
      puts 'Customer not set.'
      return nil
    end

    config = read_yaml_file(app_yml_file)
    puts 'Current customer: ' + config['customer']
  end

  def change(customer_name)
    clear

    link_customer_files(customer_name)
    customer_importers(customer_name).each do |x|
      importer_name    = x[:name]
      importer_version = x[:version]
      link_files_in_importer(importer_name, importer_version)
    end
  end

  def clear
    clear_customer_links
  end

  ########## private #############

  def read_yaml_file(file_path)
    raise "Error: Not found #{file_path}" unless file_path.exist?

    app_config = YAML.load ERB.new(IO.read(file_path)).result
    app_config[environment]
  end

  def customer_app_config
    config_app_yml = ENV['AAS_IMPORTER_CONFIG'] || '/opt/aas-app/config/importers/app.yml'
    root_path.join(config_app_yml)
  end

  def clear_customer_links
    folders_has_links = %w[
      lib/importers
      lib/models
    ]
    (folders_has_links + CUSTOMER_FILES_PATTERNS.values).uniq.each do |sub_dir|
      root_path.join(sub_dir).each_child do |file|
        file.delete if file.ftype == 'link'
      end
    end

    puts "Clear customer links...\n\n"
  end

  def link_files_in_importer(name, version)
    importer_dir =
      if version.present?
        root_path.join("importers/#{name}_v#{version}")
      else
        root_path.join("importers/#{name}")
      end

    raise "not found path #{importer_dir}" unless importer_dir.exist?

    importer_dir.each_child do |file|
      link_file(file, root_path.join('lib/importers/')) if file.fnmatch? '*_importer.rb'

      link_file(file, root_path.join('lib/models/')) if file.fnmatch? '*_models.rb'

      link_file(file, root_path.join('config/')) if file.fnmatch? '*_config.yml'
    end
  end

  def link_file(source, target_dir)
    target_file          = target_dir.join(source.basename)
    source_file_relative = source.relative_path_from(target_dir)
    target_file.make_symlink(source_file_relative)
    puts "link: #{target_file.relative_path_from(root_path)} -> #{source.relative_path_from(root_path)}"
  end

  def customer_config_file(customer_name)
    root_path.join("customers/#{customer_name}/app.yml")
  end

  def customer_files(customer_name)
    root_path.join("customers/#{customer_name}").children
  end

  def link_customer_files(customer_name)
    customer_files(customer_name).each do |file|
      linked_flag = false
      CUSTOMER_FILES_PATTERNS.each do |pattern, target_dir|
        if file.basename.to_s.match? pattern
          linked_flag = true
          link_file file, root_path.join("#{target_dir}/")
        end
      end

      puts "WARNING: not found link pattern for #{file}" unless linked_flag
    end
  end

  def customer_config(customer_name)
    read_yaml_file customer_config_file(customer_name)
  end

  def customer_importers(customer_name)
    app_config = customer_config(customer_name)
    app_config['importers'].map { |x| { name: x['name'], version: x['version'] } }
  end
end
