module AasOracleImporter
  class JuyuanYanbaoImporter < ImporterBase
    def config
      @bs_id       = 327
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_ledgers(JuyuanYanbao::Account)
    end

    def destroy_exist_datas
      accounts = JuyuanYanbao::Account.where(quarter_id: @quarter_id)
      JuyuanYanbao::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JuyuanYanbao::Role.where(quarter_id: @quarter_id).delete_all

      JuyuanYanbao::Data1Permission.where(quarter_id: @quarter_id).delete_all

      JuyuanYanbao::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    # del_flag: 1 删除， 0 正常
    def import_accounts_sql
      <<-EOF
        select id, job_number, real_name, user_state from jydb2user.cfg_user where del_flag = 0
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [
          { 'name' => 'user_state', 'value' => '1', 'enum_value' => '正常', 'label' => '账号状态' },
          { 'name' => 'user_state', 'value' => '2', 'enum_value' => '离职', 'label' => '账号状态' },
          { 'name' => 'user_state', 'value' => '3', 'enum_value' => '禁用', 'label' => '账号状态' },
          { 'name' => 'user_state', 'value' => '4', 'enum_value' => '临时开放', 'label' => '账号状态' }
        ]
        select_db_datas(import_accounts_sql).each do |r|
          account = JuyuanYanbao::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == '1'
          )
          @accounts << account

          QuarterAccountInfo.create(
            account_id:         account.id,
            account_type:       'JuyuanYanbao::Account',
            business_system_id: @bs_id,
            quarter_id:         @quarter.id,
            display_status:     r[3]&.to_s
          )

          # next unless r[3] == '4'
          # type_id = PublicAccountType.find_by(key: 'temporary').id
          # data_json = {
          #   name:      account.name,
          #   system_id: @bs_id,
          #   code:      account.code,
          #   remark:    '自动识别',
          #   type_id:   type_id
          # }
          # public_account = ::PublicAccount.find_or_initialize_by(data_json)
          # public_account.valid_date = Date.parse(end_time)
          # public_account.save
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id, role_name from jydb2user.cfg_role
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << JuyuanYanbao::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id from jydb2user.cfg_role_user
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          JuyuanYanbao::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, id, menu_name, menu_type, parent_id from jydb2user.cfg_menu
      EOF
    end

    def import_data1_permissions
      enums = [
        { 'name' => 'menu_type', 'value' => '0', 'enum_value' => '菜单', 'label' => '菜单类型' },
        { 'name' => 'menu_type', 'value' => '1', 'enum_value' => '按钮', 'label' => '菜单类型' }
      ]

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 4
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index))

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name,

            level2_name: get_enum(enums, 'menu_type', r[3])

          }
          @data1_permissions << JuyuanYanbao::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, menu_id from jydb2user.cfg_menu_role
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JuyuanYanbao::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
