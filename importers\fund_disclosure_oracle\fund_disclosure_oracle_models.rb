module FundDisclosure
  def self.table_name_prefix
    'fund_disclosure_'
  end
end

class FundDisclosure::Account < ActiveRecord::Base
  belongs_to :user
end

class FundDisclosure::Fund < ActiveRecord::Base
  serialize :o32_fund_codes, ConvertTools::JsonWithSymbolizeNames
end

class FundDisclosure::FundPosition < ActiveRecord::Base
end

class FundDisclosure::Role < ActiveRecord::Base
end

module FundEmailsCheck
  def self.table_name_prefix
    'fund_emails_check_'
  end
end

class FundEmailsCheck::Fund < ActiveRecord::Base
end