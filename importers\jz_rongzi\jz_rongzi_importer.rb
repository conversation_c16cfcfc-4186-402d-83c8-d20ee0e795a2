module AasOracleImporter
  class JzRongziImporter < ImporterBase
    def config
      @bs_id       = 251
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}operator#{@sid_suffix}"
      @table_role         = "#{@table_space}opermodel#{@sid_suffix}"
      @table_account_role = "#{@table_space}operofmodel#{@sid_suffix}"
      @table_menu         = "#{@table_space}sysmenu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}opermodelright#{@sid_suffix}"
      @table_account_menu = "#{@table_space}operusememu#{@sid_suffix}"
      @table_menu2        = "#{@table_space}dictvalue#{@sid_suffix}"
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_data3_permissions
      import_data3_account_permissions

      import_ledgers(JzRongzi::Account)
    end

    def import_accounts_sql
      <<-EOF
        select operid, operid, opername, status from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '0', 'enum_value' => '1', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << JzRongzi::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     get_enum(enums, 'status', r[3])&.to_s == '1'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select opermodelid, opermodelid, opermodelname from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzRongzi::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select opermodelid,operid from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          JzRongzi::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select menuid, menuid, menuprompt, menupos from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = full_name(name, sql, r[3])
          @data1_permissions << JzRongzi::Data1Permission.create(
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          )
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select opermodelid, menuid from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzRongzi::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select operid, menuid from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzRongzi::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select d.subitem, d.subitem, d.subitemname from #{@table_menu2} d where d.dictitem='Cgyqx'
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzRongzi::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select o.operid, d.subitem from #{@table_account} o, #{@table_menu2} d where o.operright=d.subitem and d.dictitem='Cgyqx'
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzRongzi::Data2AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data2_permission_id: permission.id
        )
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select d.subitem, d.subitem, d.subitemname from #{@table_menu2} d where d.dictitem='Cgytsqx'
      EOF
    end

    def import_data3_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data3_permissions << JzRongzi::Data3Permission.create(json)
        end
      end
    end

    def import_data3_account_permissions_sql
      <<-EOF
        select o.operid, d.subitem from #{@table_account} o, #{@table_menu2} d where o.specialright=d.subitem and d.dictitem='Cgytsqx'
      EOF
    end

    def import_data3_account_permissions
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzRongzi::Data3AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data3_permission_id: permission.id
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    def full_name(name, sql, parent_id = nil)
      return name if parent_id.blank? || parent_id.length == 1

      parent_id = parent_id.chars.to(-2).join
      new_sql = sql + " where menupos='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[2]
        root_parent_id = r[3]
        name = "#{parent_name} -> #{name}" if parent_name.present?
        name = full_name(name, sql, root_parent_id)
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
