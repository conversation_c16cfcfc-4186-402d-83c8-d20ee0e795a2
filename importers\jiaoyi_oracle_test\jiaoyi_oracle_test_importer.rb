module AasOracleImporter

  class JiaoyiOracleTestImporter < ImporterBase

    def config
      @bs_id      = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @temporary = importer_config['temporary']
    end

    def import_to_do
      destroy_old_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_sub_systems
      import_menus
      import_menu_additions
      import_acccounts_menus
      import_menus_roles
      import_funds
      import_accounts_funds

      # 导入临时权限
      import_temp_menu_rights
      import_temp_fund_rights
      import_temp_fund_unit_rights
      import_temp_fund_combi_rights

      import_time_controls

      import_ledgers(JiaoyiOracle::Account)
      import_temp_all_caches
    end

    def destroy_old_datas
      JiaoyiOracle::Account.where( quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::Role.where(    quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::Menu.where(    quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::System.where(  quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::MenuAddition.where(   quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::MenuPermission.where( quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::MenuAddition.where(   quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::MenuAdditionPermission.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::Fund.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::FundUnit.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::FundCombination.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::FundPermission.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::FundUnitPermission.where(quarter_id: @quarter_id).destroy_all
      JiaoyiOracle::FundCombinationPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts
      sql = <<-EOF
        select   l_operator_no,
                vc_operator_name,
                 c_operator_status,
                 l_register_date,
                 l_cancel_date
        from #{@table_space}toperator#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            account = JiaoyiOracle::Account.create(
                quarter_id: @quarter_id,
                code: r[0],
                name: r[1],
                status: r[2].to_i == 1
              )
            if importer_config['register_date']
              account.register_date = r[3]
              account.cancel_date = r[4]
            end
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles
      sql = <<-EOF
        select l_role_id, vc_role_name, vc_remarks
        from #{@table_space}trole#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JiaoyiOracle::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            description: r[2]
            )
        end
      end
    end

    def import_menus
      sql = <<-EOF
        select
          vc_menu_no, vc_menu_name, c_subsystem_no
        from
          #{@table_space}tmenuitem#{@sid_suffix}
        where
          vc_menu_name <> '-'
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          sub_system = JiaoyiOracle::System.find_by(quarter_id: @quarter_id, code: r[2])
          next unless sub_system

          JiaoyiOracle::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            system_id: sub_system.id
            )
        end
      end
    end

    def import_sub_systems
      sql = <<-EOF
        select
          c_lemma_item, vc_item_name
        from
          #{@table_space}tdictionary#{@sid_suffix}
        where
          l_dictionary_no = '10002'
        and
          c_lemma_item <> '!'
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JiaoyiOracle::System.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
          )
        end
      end
    end

    def import_menu_additions
      sql = <<-EOF
        select
          vc_menu_no,
          c_rights_id,
          vc_remarks
        from
          #{@table_space}tmenurights#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          menu = JiaoyiOracle::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          JiaoyiOracle::MenuAddition.create(
            quarter_id: @quarter_id,
            sub_code: r[1],
            name: r[2],
            menu_id: menu.id
          )
        end
      end
    end


    def import_menus_roles
      sql = <<-EOF
        select
          l_role_id,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_operator_no = -1
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = JiaoyiOracle::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = JiaoyiOracle::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            JiaoyiOracleMenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              JiaoyiOracle::MenuAddition.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              JiaoyiOracleMenuAdditionsRoles.create(
                role_id: role.id,
                menu_addition_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_acccounts_menus
      sql = <<-EOF
        select
          l_operator_no,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_role_id = -1
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = JiaoyiOracle::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and account
            JiaoyiOracle::MenuPermission.create(
              menu_id: menu.id,
              account_id: account.id,
              quarter_id: @quarter_id
            )

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              JiaoyiOracle::MenuAddition.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              JiaoyiOracle::MenuAdditionPermission.create(
                account_id: account.id,
                menu_addition_id: ap.id,
                quarter_id: @quarter_id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select l_operator_no, l_role_id
        from #{@table_space}toprolerights#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = JiaoyiOracle::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account and role
            JiaoyiOracleAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_funds
      fund_sql = <<-EOF
        select l_fund_id, vc_fund_code, vc_fund_name, vc_fund_caption, c_fund_status
        from #{@table_space}tfundinfo#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(fund_sql) do |r|
          JiaoyiOracle::Fund.create(
            quarter_id: @quarter_id,
            code: r[0],
            fund_code: r[1],
            name: r[2],
            fund_caption: r[3],
            inservice: (r[4].to_i == 2)
          )
        end
      end

      asset_sql = <<-EOF
        select l_fund_id, l_asset_id, vc_asset_name
        from #{@table_space}tasset#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(asset_sql) do |r|
          fund = JiaoyiOracle::Fund.find_by(quarter_id: @quarter_id, code: r[0])
          next unless fund

          JiaoyiOracle::FundUnit.create(
            quarter_id: @quarter_id,
            fund_id: fund.id,
            code: r[1],
            name: r[2]
          )
        end
      end

      combi_sql = <<-EOF
        select l_asset_id, l_combi_id, vc_combi_name
        from #{@table_space}tcombi#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(combi_sql) do |r|
          unit = JiaoyiOracle::FundUnit.find_by(quarter_id: @quarter_id, code: r[0])
          next unless unit

          JiaoyiOracle::FundCombination.create(
            quarter_id: @quarter_id,
            fund_unit_id: unit.id,
            code: r[1],
            name: r[2]
          )
        end
      end
    end

    def import_accounts_funds
      fund_sql = <<-EOF
        select
          l_operator_no,
          l_fund_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_fund_id <> -1
        and l_asset_id = -1
        and l_basecombi_id = -1
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(fund_sql) do |r|
          account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          fund    = JiaoyiOracle::Fund.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && fund

          JiaoyiOracle::FundPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id: fund.id,
            permission: r[2]
          )
        end
      end


      uniq_sql = <<-EOF
        select
          l_operator_no,
          l_asset_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_asset_id <> -1
        and l_basecombi_id = -1
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(uniq_sql) do |r|
          account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          unit    = JiaoyiOracle::FundUnit.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && unit

          JiaoyiOracle::FundUnitPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_unit_id: unit.id,
            permission: r[2]
          )
        end
      end

      combi_sql = <<-EOF
        select
          l_operator_no,
          l_basecombi_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_basecombi_id <> -1
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(combi_sql) do |r|
          account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          combi   = JiaoyiOracle::FundCombination.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && combi

          JiaoyiOracle::FundCombinationPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_combination_id: combi.id,
            permission: r[2]
          )
        end
      end
    end

    def import_temp_menu_rights
      sql = <<-EOF
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '1'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '1', '2' )
        ORDER BY
          t.vc_right
      EOF

      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 过期的跳过
          start_date = Date.parse(r[0].to_s)
          end_date   = Date.parse(r[1].to_s)
          next if today > end_date

          account       = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[2])
          grant_account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[3])
          menu          = JiaoyiOracle::Menu.where(quarter_id: @quarter_id).find_by_code(r[4])

          next unless account && menu

          JiaoyiOracle::MenuPermission.create(
            quarter_id:              @quarter_id,
            account_id:              account.id,
            menu_id:                 menu.id,
            is_temp:                 true,
            temp_start_date:         start_date,
            temp_end_date:           end_date,
            authorizer_account_id:   grant_account.id,
            authorizer_account_name: grant_account.name
          )

          next unless r[5]

          additional_permission_codes = r[5].chars
          aps =
            JiaoyiOracle::MenuAddition.where(
              quarter_id: @quarter_id,
              menu_id:    menu.id,
              sub_code:   additional_permission_codes
            )

          aps.each do |ap|
            JiaoyiOracle::MenuAdditionPermission.create(
              quarter_id:              @quarter_id,
              account_id:              account.id,
              menu_addition_id:        ap.id,
              is_temp:                 true,
              temp_start_date:         start_date,
              temp_end_date:           end_date,
              authorizer_account_id:   grant_account.id,
              authorizer_account_name: grant_account.name
            )
          end
        end
      end
    end

    def import_temp_fund_rights
      sql = <<-EOF
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '1', '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id = -1
          and t.l_basecombi_id = -1
        ORDER BY
          t.vc_right
      EOF

      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 过期的跳过
          start_date = Date.parse(r[0].to_s)
          end_date   = Date.parse(r[1].to_s)
          next if today > end_date

          account       = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[2])
          grant_account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[3])
          fund          = JiaoyiOracle::Fund.where(quarter_id: @quarter_id).find_by_code(r[4])

          next unless account && fund

          JiaoyiOracle::FundPermission.create(
            quarter_id:              @quarter_id,
            account_id:              account.id,
            fund_id:                 fund.id,
            permission:              r[5],
            is_temp:                 true,
            temp_start_date:         start_date,
            temp_end_date:           end_date,
            authorizer_account_id:   grant_account.id,
            authorizer_account_name: grant_account.name
          )
        end
      end
    end

    def import_temp_fund_unit_rights
      sql = <<-EOF
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '1', '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id = -1
        ORDER BY
          t.vc_right
      EOF

      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 过期的跳过
          start_date = Date.parse(r[0].to_s)
          end_date   = Date.parse(r[1].to_s)
          next if today > end_date

          account       = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[2])
          grant_account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[3])
          unit          = JiaoyiOracle::FundUnit.where(quarter_id: @quarter_id).find_by_code(r[6])

          next unless account && unit

          JiaoyiOracle::FundUnitPermission.create(
            quarter_id:              @quarter_id,
            account_id:              account.id,
            fund_unit_id:            unit.id,
            permission:              r[5],
            is_temp:                 true,
            temp_start_date:         start_date,
            temp_end_date:           end_date,
            authorizer_account_id:   grant_account.id,
            authorizer_account_name: grant_account.name
          )
        end
      end
    end

    def import_temp_fund_combi_rights
      sql = <<-EOF
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '1', '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id <> -1
        ORDER BY
          t.vc_right
      EOF

      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 过期的跳过
          start_date = Date.parse(r[0].to_s)
          end_date   = Date.parse(r[1].to_s)
          next if today > end_date

          account       = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[2])
          grant_account = JiaoyiOracle::Account.where(quarter_id: @quarter_id).find_by_code(r[3])
          combi         = JiaoyiOracle::FundCombination.where(quarter_id: @quarter_id).find_by_code(r[7])

          next unless account && combi

          JiaoyiOracle::FundCombinationPermission.create(
            quarter_id:              @quarter_id,
            account_id:              account.id,
            fund_combination_id:     combi.id,
            permission:              r[5],
            is_temp:                 true,
            temp_start_date:         start_date,
            temp_end_date:           end_date,
            authorizer_account_id:   grant_account.id,
            authorizer_account_name: grant_account.name
          )
        end
      end
    end

    def import_temp_all_caches
      return true if !@temporary
      #JiaoyiOracle::Temporary.all.destroy_all
      days_of_data = (Time.now - importer_config['days_of_data'].to_i.days).strftime("%Y%m%d")
      sql = <<-EOF
        select
          L_OPERATOR_NO,
          C_RIGHT_TYPE,
          C_RIGHT_OPERATOR,
          VC_RIGHT,
          L_ASSET_ID,
          L_BASECOMBI_ID,
          VC_RIGHTS_ID,
          L_BEGIN_DATE,
          L_END_DATE,
          L_GRANT_OPERATOR,
          L_INPUT_OPERATOR,
          L_GRANT_DATE,
          L_GRANT_TIME,
          C_STATUS
        from
          #{@table_space}toptempright#{@sid_suffix}
        where
          L_BEGIN_DATE >= #{days_of_data}
      EOF
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          g_time = r[12].to_s
          while g_time.size < 6
            g_time = "0"+g_time
          end
          temporary = JiaoyiOracle::Temporary.find_or_create_by(
            account_code: r[0],
            authorizer_account_code: r[9],
            operator_account_code: r[10],
            operation_type: r[2],
            permission_type: r[1].to_i,
            temp_start_date: Date.parse(r[7].to_s),
            temp_end_date: Date.parse(r[8].to_s),
            authorize_time: Time.parse(r[11].to_s+g_time),
            permission_code: r[3],
            unit_code: r[4],
            combination_code: r[5],
            addition_code: r[6]
          )
          temporary.update(
            authorize_status: r[13]
          )
        end
      end
    end

    def import_time_controls
      if importer_config['time_control']
        sql = "select * from #{@table_space}toptimecontrol#{@sid_suffix}"
        ActiveRecord::Base.transaction do
          @database.exec(sql) do |r|
            JiaoyiOracle::TimeControl.create(
              quarter_id: @quarter.id,
              role_code: r[0],
              account_code: r[1],
              system_code: r[2],
              menu_code: r[3],
              begin_date: r[4],
              begin_time: r[5],
              end_date: r[6],
              end_time: r[7]
            )
          end
        end
      end
    end

    def class_exists?(class_name)
      klass = Module.const_get(class_name)
      klass.first
      return klass.is_a?(Class)
    rescue NameError
      return false
    end

  end
end



