module Ji<PERSON><PERSON>
  def self.table_name_prefix
    'jiaoyi_'
  end
end

class Jiaoyi::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class Jiaoyi::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class Jiaoyi::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class Jiaoyi::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class Jiaoyi::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class Jiaoyi::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class Jiaoyi::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class Jiaoyi::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class Jiaoyi::System < ActiveRecord::Base
  has_many :menus
end

class Jiaoyi::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Jiaoyi::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class Jiaoyi::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class Jiaoyi::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class Jiaoyi::Temporary < ActiveRecord::Base; end

class JiaoyiAccountsRoles < ActiveRecord::Base; end
class JiaoyiMenusRoles < ActiveRecord::Base; end
class JiaoyiMenuAdditionsRoles < ActiveRecord::Base; end

class Jiaoyi::TimeControl < ActiveRecord::Base; end

class Jiaoyi::TopTradeType < ActiveRecord::Base; end
class Jiaoyi::TopStation < ActiveRecord::Base; end
class Jiaoyi::TradeType < ActiveRecord::Base; end