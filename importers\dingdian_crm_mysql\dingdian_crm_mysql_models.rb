module DingdianCrm
  def self.table_name_prefix
    'dingdian_crm_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class DingdianCrm::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  
  
  has_many :data1_accounts_roles_permissions
  
  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end


class DingdianCrm::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  
  
  has_many :data1_accounts_roles_permissions
  

  validates :code, presence: true
  validates :name, presence: true
end

class DingdianCrm::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end

class User < ActiveRecord::Base

end


  
  class DingdianCrm::Data1Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class DingdianCrm::Data1AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'dingdian_crm_data1_arps'
    belongs_to :data1_permission
    belongs_to :role, optional: true

    validates :data1_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end

