require 'httparty'
module AasOracleImporter
  class YibanImporter < ImporterBase
    include HTTParty

    def config
      @bs_id              = importer_config['bs_id']
      @corpid             = importer_config['corpid']
      @corpsecret         = importer_config['corpsecret']
      @base_url           = importer_config['base_uri']
      @users              = []
      @old_user_codes     = User.where(inservice: true).pluck(:code)
      @depts              = []
      @old_dept_codes     = Department.pluck(:code)
      @jobs               = []
      @old_job_codes      = Job.pluck(:code)
      @oa_flows           = []
      self.class.base_uri importer_config['base_uri']
    end

    def yiban_config
      YAML.load_file File.join(__dir__, '../../config/yiban_config.yml')
    end

    def api_config
      yiban_config[@key.to_s]
    end

    def import_to_do
      @api_token = api_token
      # 是否需要先把所有员工和部门都设置为禁用
      ActiveRecord::Base.transaction { import_departments }
      ActiveRecord::Base.transaction { import_users }
      # ActiveRecord::Base.transaction { import_oa_flows }
    end

    def test
      @key = 'dept_list'
      all_info.each do |dept|
        
        pp dept.to_s
        pp '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~'
        @key = 'user_list'
        @dept_id = dept['id']
        all_info.each do |user|
          pp user.to_s
        end
      end

    end

    private

    def import_departments
      @key = 'dept_list'
      all_info.each do |dept|
        next unless dept['id'].present? && dept['name'].present?

        department           = Department.find_or_initialize_by(id: dept['id'])
        department.code      = dept['id']
        department.name      = dept['name']
        department.inservice = true
        department.parent_id = dept['parentid'].to_i == 0 ? nil : dept['parentid']
        department.save

        @depts << department
      end

      update_disabled_dept
      set_department_level
    end

    def set_department_level
      Department.all.each do |dt|
        level = 1
        dt2 = dt.parent
        while dt2
          level = level + 1
          dt2 = dt2.parent
        end
        dt.update(level: level)
      end
    end

    def import_users
      @key = 'user_list'
      @depts.each do |dept|
        @dept_id = dept.id

        all_info.each do |user|
          job = create_or_init_job(user)
          create_or_init_user(user, job, dept)
        end
      end

      update_disabled_user
      update_disabled_job
    end

    def create_or_init_job(job)
      return nil unless job['position'].present?

      position           = Job.find_or_initialize_by(name: job['position'])
      position.code      = job['position']
      position.inservice = true
      position.save

      @jobs << position
      position
    end

    def create_or_init_user(user, job, dept)
      return unless user['userid'].present? && user['name'].present?

      u                        = User.find_or_initialize_by(code: user['userid'])
      u.name                   = user['name']
      u.email                  = user['email']
      u.department_id          = @dept_id
      u.position               = job&.name
      u.inservice              = true
      u.department_name        = dept&.name
      u.parent_department_name = @depts.find { |d| d.id == dept&.parent_id }&.name
      u.save

      @users << u
    end

    def update_disabled_dept
      disabled_dept_codes = @old_dept_codes - @depts.map(&:code)
      Department.where(code: disabled_dept_codes).update_all(inservice: false)
    end

    def update_disabled_user
      disabled_user_codes = @old_user_codes - @users.map(&:code)
      User.where(code: disabled_user_codes).update_all(inservice: false, disable_date: Date.today)
    end

    def update_disabled_job
      disabled_job_codes = @old_job_codes - @jobs.map(&:code)
      Job.where(code: disabled_job_codes).update_all(inservice: false)
    end

    def all_info
      send_request[api_config['resp'].to_s]
    end

    def api_token
      @key = 'get_token'
      send_request[api_config['resp'].to_s]
    end

    def send_request
      response = self.class.get("#{@base_url}#{api_config['url']}?#{send("#{@key}_params")}", {
                                  headers: headers,
                                  verify:  false
                                })
      result = JSON.parse response.body

      unless result['errcode'].to_i.zero?
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers
      { 'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8' }
    end

    def get_token_params
      "corpid=#{@corpid}&corpsecret=#{@corpsecret}"
    end

    def dept_list_params
      "access_token=#{@api_token}&no_fetch_child=0"
    end

    def user_list_params
      "access_token=#{@api_token}&department_id=#{@dept_id}&fetch_child=0"
    end
  end
end
