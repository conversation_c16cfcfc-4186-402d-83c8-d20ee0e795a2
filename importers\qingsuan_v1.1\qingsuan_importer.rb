module AasOracleImporter

  class QingsuanImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @data1_permissions = []
      initialize_tables
    end

    def initialize_tables
      @table_users           = "#{@table_space}users#{@sid_suffix}"
      @table_user_roles      = "#{@table_space}user_role#{@sid_suffix}"
      @table_menus           = "#{@table_space}menu_def#{@sid_suffix}"
      @table_user_role_rela  = "#{@table_space}user_role_rela#{@sid_suffix}"
      @table_user_role_right = "#{@table_space}user_role_right#{@sid_suffix}"
      @table_user_menu       = "#{@table_space}user_right#{@sid_suffix}"
      @table_second_user_menu = "#{@table_space}depmanager_right#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_records
      import_accounts
      import_roles
      import_menus
      import_accounts_roles
      import_menus_roles
      import_data1_account_permissions
      import_data1_second_account_permissions

      import_ledgers(Qingsuan::Account)
    end

    def destroy_exist_records
      Qingsuan::Account.where(quarter_id: @quarter_id).destroy_all
      Qingsuan::Role.where(quarter_id: @quarter_id).destroy_all
      Qingsuan::Menu.where(quarter_id: @quarter_id).destroy_all
      QingsuanAccountsMenus.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select user_code, user_name, user_status from #{@table_users}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] and r[1]
            account = Qingsuan::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i.zero?
            )
            @accounts << account

            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       'Qingsuan::Account',
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[2]&.to_s
              )
            end
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select role_id, role_name from #{@table_user_roles}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          Qingsuan::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          menu_code,
          menu_name,
          menu_level,
          parent_menu_code
        from
          #{@table_menus}
        order by
          menu_code
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        menus = []
        @database.exec(import_menus_sql) do |r|
          menus << r
        end
        new_menus = calculate_menus(menus)
        time = Time.now
        Qingsuan::Menu.bulk_insert(:quarter_id, :code, :name, :level, :parent_code, :full_name, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          new_menus.each do |menu|
            obj.add [@quarter_id, menu[0], menu[1], menu[2], menu[3], menu[4], time, time]
          end
        end
      end
      @data1_permissions = Qingsuan::Menu.where(quarter_id: @quarter_id)
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          user_code, role_id
        from
          #{@table_user_role_rela}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Qingsuan::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Qingsuan::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account and role
            QingsuanAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          role_id, menu_code
        from
          #{@table_user_role_right}
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = Qingsuan::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Qingsuan::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            QingsuanMenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}.#{__method__}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_user_menu}
      EOF
    end

    def import_data1_account_permissions
      unless exist_table?(@table_user_menu)
        @logger.warn "#{self.class}.#{__method__}: not found table #{@table_user_menu}"
        return
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_data1_account_permissions_sql) do |r|
          account    = @accounts.find { |x| x.code.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless permission && account

          QingsuanAccountsMenus.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    permission.id
          )
        end
      end
    end

    def import_data1_second_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_second_user_menu}
      EOF
    end

    def import_data1_second_account_permissions
      unless exist_table?(@table_second_user_menu)
        @logger.warn "#{self.class}.#{__method__}: not found table #{@table_second_user_menu}"
        return
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_data1_second_account_permissions_sql) do |r|
          account    = @accounts.find { |x| x.code.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless permission && account

          QingsuanAccountsMenus.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    permission.id
          )
        end
      end
    end

    # 菜单数据进行运算，计算树形菜单组合的名称
    def calculate_menus(menus)
      menus.map do |menu|
        full_name = get_full_name(menu, menus)
        menu << full_name
      end
    end

    # 菜单全名，包括所有祖先级菜单
    def get_full_name(menu, menus)
      names = []
      names << menu[1]
      while (menu_data = menus.find { |m| menu[3] == m[0] }).present?
        names << menu_data[1]
        menu = menu_data
      end
      names.reverse.join(' -> ')
    end
  end
end



