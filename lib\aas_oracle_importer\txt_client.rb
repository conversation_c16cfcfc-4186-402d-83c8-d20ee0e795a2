# frozen_string_literal: true

module AasOracleImporter
  # CSV 客户端
  class TxtClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(file_path, args = {})
      encode         = args[:encode] || 'r:bom|utf-8'
      drop_lines     = args[:drop_lines] || 0
      headers_enable = args[:headers_enable] || false
      file = File.open(file_path, encode)

      output_data = file.readlines.drop(drop_lines).join
      args[:source_encode] ? output_data.force_encoding(args[:source_encode]).encode('utf-8', replace: nil) : output_data
    end

    def txt_path
      @path
    end

    private

    def initialize_driver
      @path = Pathname.new(database_info['path'])
    end
  end
end
