module AasOracleImporter

  class Jiaoyi2Importer < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @temporary   = importer_config['temporary']
      @display_status = importer_config['display_status']
      @c_menu_type = importer_config['c_menu_type']
      @history_temp = importer_config['history_temp']
      super
    end

    def import_to_do
      destroy_old_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_sub_systems
      import_menus
      import_menu_additions
      import_accounts_menus
      import_menus_roles
      import_funds
      import_accounts_funds

      # 导入临时权限
      import_temp_menu_rights
      import_temp_fund_rights
      import_temp_fund_unit_rights
      import_temp_fund_combi_rights

      import_time_controls if importer_config['time_control']
      cpic_custom_importer if importer_config['cpic_custom_importer']
      trade_type_importer if importer_config['trade_type_importer']
      station_importer if importer_config['station_importer']
      import_temp_all_caches
      import_ledgers(Jiaoyi2::Account)

    end

    def destroy_old_datas
      Jiaoyi2::Account.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::Role.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::Menu.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::System.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::MenuPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::MenuAdditionPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::Fund.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::FundUnit.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::FundCombination.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::FundPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::FundUnitPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi2::FundCombinationPermission.where(quarter_id: @quarter_id).destroy_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all if @display_status
    end

    def import_station_sql
      <<-SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,VC_STATION_NO
        from
          #{@table_space}topstation#{@sid_suffix}
      SQL
    end

    def station_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_station_sql) do |r|
          Jiaoyi2::TopStation.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            station_code: r[2]
          )
        end
      end
    end

    def import_trade_type_sql
      <<-SQL
        select
          L_SERIAL_ID,VC_TRADE_NO,VC_TRADE_NAME,L_PARENT_ID
        from
          #{@table_space}ttradetypes#{@sid_suffix}
      SQL
    end

    def trade_type_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_trade_type_sql) do |r|
          Jiaoyi2::TradeType.create(
            quarter_id:  @quarter_id,
            l_serial_id: r[0],
            code:        r[1],
            name:        r[2],
            parent_id:   r[3]
          )
        end

        @database.exec(import_top_trade_type_sql) do |r|
          Jiaoyi2::TopTradeType.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            l_serial_id:  r[2],
            vc_right:     r[3],
            c_status:     r[4]
          )
        end
      end
    end

    def import_top_trade_type_sql
      <<-SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,L_SERIAL_ID,VC_RIGHT,C_STATUS
        from
          #{@table_space}toptradetype#{@sid_suffix}
      SQL
    end

    def import_accounts_sql
      vc_operator_no = importer_config['operator_no_importer'] ? ', vc_operator_no' : ''
      <<-SQL
        select   l_operator_no,
                vc_operator_name,
                 c_operator_status,
                 l_register_date,
                 l_cancel_date #{vc_operator_no}
        from #{@table_space}toperator#{@sid_suffix}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Jiaoyi2::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i == 1
            )

            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       'Jiaoyi2::Account',
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[2]&.to_s
              )
            end

            if importer_config['register_date']
              account.register_date = r[3]
              account.cancel_date   = r[4]
              account.save
            end

            if importer_config['operator_no_importer']
              account.operator_no = r[5]
              account.save
            end
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_role_id, vc_role_name, vc_remarks
        from #{@table_space}trole#{@sid_suffix}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          Jiaoyi2::Role.create(
            quarter_id:  @quarter_id,
            code:        r[0],
            name:        r[1],
            description: r[2]
          )
        end
      end
    end

    def menus_sql_no_type
      <<-SQL
        select
          vc_menu_no, vc_menu_name, c_subsystem_no
        from
          #{@table_space}tmenuitem#{@sid_suffix}
        where
          vc_menu_name <> '-'
      SQL
    end


    def import_menus_sql
      menus_sql_with_type = menus_sql_no_type
      menus_sql_with_type = menus_sql_no_type + " AND c_menu_type in (#{@c_menu_type.join(",")})" unless @c_menu_type.blank?
      menus_sql_with_type
    end

    def import_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          sub_system = Jiaoyi2::System.find_by(quarter_id: @quarter_id, code: r[2])
          next unless sub_system

          Jiaoyi2::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1],
            system_id:  sub_system.id
          )
        end
      end
    end

    def import_sub_systems_sql
      <<-SQL
        select
          c_lemma_item, vc_item_name
        from
          #{@table_space}tdictionary#{@sid_suffix}
        where
          l_dictionary_no = '10002'
        and
          c_lemma_item <> '!'
      SQL
    end

    def import_sub_systems
      ActiveRecord::Base.transaction do
        @database.exec(import_sub_systems_sql) do |r|
          Jiaoyi2::System.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menu_additions_sql
      <<-SQL
        select
          vc_menu_no,
          c_rights_id,
          vc_remarks
        from
          #{@table_space}tmenurights#{@sid_suffix}
      SQL
    end

    def import_menu_additions
      ActiveRecord::Base.transaction do
        @database.exec(import_menu_additions_sql) do |r|
          menu = Jiaoyi2::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          Jiaoyi2::MenuAddition.create(
            quarter_id: @quarter_id,
            sub_code:   r[1],
            name:       r[2],
            menu_id:    menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          l_role_id,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_operator_no = -1
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = Jiaoyi2::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Jiaoyi2::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            Jiaoyi2MenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            # mysql 查询默认不区分大小写
            adapter = AasOracleImporter.config&.[]('server')&.[]('database')&.[]('adapter')
            sql = if adapter == 'odbc'
                    str = additional_permission_codes.count.times.map { |_| 'UTL_RAW.cast_to_raw(?)' }.join(', ')
                    "UTL_RAW.cast_to_raw(sub_code) in (#{str})"
                  else
                    'binary sub_code in (?)'
                  end
            aps =
              Jiaoyi2::MenuAddition
                .where(quarter_id: @quarter_id, menu_id: menu.id)
                .where(sql, *(adapter == 'odbc' ? additional_permission_codes : [additional_permission_codes]))

            aps.each do |ap|
              Jiaoyi2MenuAdditionsRoles.create(
                role_id:          role.id,
                menu_addition_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_menus_sql
      <<-SQL
        select
          l_operator_no,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_role_id = -1
      SQL
    end

    def import_accounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_menus_sql) do |r|
          account = Jiaoyi2::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = Jiaoyi2::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            Jiaoyi2::MenuPermission.create(
              menu_id:    menu.id,
              account_id: account.id,
              quarter_id: @quarter_id
            )

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps                         =
              Jiaoyi2::MenuAddition.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              Jiaoyi2::MenuAdditionPermission.create(
                account_id:       account.id,
                menu_addition_id: ap.id,
                quarter_id:       @quarter_id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select l_operator_no, l_role_id
        from #{@table_space}toprolerights#{@sid_suffix}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Jiaoyi2::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Jiaoyi2::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            Jiaoyi2AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_funds_sql
      <<-SQL
        select l_fund_id, vc_fund_code, vc_fund_name, vc_fund_caption, c_fund_status
        from #{@table_space}tfundinfo#{@sid_suffix}
      SQL
    end

    def import_fund_asset_sql
      <<-SQL
        select l_fund_id, l_asset_id, vc_asset_name
        from #{@table_space}tasset#{@sid_suffix}
      SQL
    end

    def import_fund_combi_sql
      <<-SQL
        select l_asset_id, l_combi_id, vc_combi_name
        from #{@table_space}tcombi#{@sid_suffix}
      SQL
    end

    def import_funds
      ActiveRecord::Base.transaction do
        @database.exec(import_funds_sql) do |r|
          # 长盛客户code是浮点型，例如123.0
          code = r[0].to_s.sub('.0', '')
          Jiaoyi2::Fund.create(
            quarter_id:   @quarter_id,
            code:         code,
            fund_code:    r[1],
            name:         r[2],
            fund_caption: r[3],
            inservice:    (r[4].to_i == 2)
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_fund_asset_sql) do |r|
          fund = Jiaoyi2::Fund.find_by(quarter_id: @quarter_id, code: r[0])
          next unless fund

          Jiaoyi2::FundUnit.create(
            quarter_id: @quarter_id,
            fund_id:    fund.id,
            code:       r[1],
            name:       r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_fund_combi_sql) do |r|
          unit = Jiaoyi2::FundUnit.find_by(quarter_id: @quarter_id, code: r[0])
          next unless unit

          Jiaoyi2::FundCombination.create(
            quarter_id:   @quarter_id,
            fund_unit_id: unit.id,
            code:         r[1],
            name:         r[2]
          )
        end
      end
    end

    # MARK: 在投资交易中，要咨询客户是否有通过角色进行授权基金的情况，根据表结构，在 O32 系统中其是支持的。
    # 但是多数客户都是直接授权给人。
    def import_accounts_funds_sql
      <<-SQL
        select
          l_operator_no,
          l_fund_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_fund_id <> -1
        and l_asset_id = -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_uniq_sql
      <<-SQL
        select
          l_operator_no,
          l_asset_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_asset_id <> -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_combi_sql
      <<-SQL
        select
          l_operator_no,
          l_basecombi_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_basecombi_id <> -1
      SQL
    end

    def import_accounts_funds
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_funds_sql) do |r|
          account = Jiaoyi2::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          fund    = Jiaoyi2::Fund.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && fund

          Jiaoyi2::FundPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id:    fund.id,
            permission: r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_uniq_sql) do |r|
          account = Jiaoyi2::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          unit    = Jiaoyi2::FundUnit.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && unit

          Jiaoyi2::FundUnitPermission.create(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            fund_unit_id: unit.id,
            permission:   r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_combi_sql) do |r|
          account = Jiaoyi2::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          combi   = Jiaoyi2::FundCombination.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && combi

          Jiaoyi2::FundCombinationPermission.create(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            fund_combination_id: combi.id,
            permission:          r[2]
          )
        end
      end
    end

    def import_temp_menu_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '1'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
      SQL
    end

    def import_temp_menu_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_menu_rights_sql
        sql = sql + " union " + import_temp_menu_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, menu_code, addi_codes = r
          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)

          next if today > end_date

          account       = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          menu          = Jiaoyi2::Menu.find_by(quarter_id: @quarter_id, code: menu_code)

          next unless account && menu

          perm = Jiaoyi2::MenuPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    menu.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Jiaoyi2::MenuPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              menu_id:    menu.id
            )
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            update_temp_right(perm, start_date, end_date, grant_account)
          end

          next unless addi_codes

          additional_permission_codes = addi_codes.chars
          aps                         =
            Jiaoyi2::MenuAddition.where(
              quarter_id: @quarter_id,
              menu_id:    menu.id,
              sub_code:   additional_permission_codes
            )

          aps.each do |ap|
            # map: MenuAdditionPermission 的缩写
            map_record = Jiaoyi2::MenuAdditionPermission.find_or_initialize_by(
              quarter_id:       @quarter_id,
              account_id:       account.id,
              menu_addition_id: ap.id
            )

            # 如果已经存在临时授权了，可能是授权重复
            if map_record.is_temp
              new_perm = Jiaoyi2::MenuAdditionPermission.new(
                quarter_id:       @quarter_id,
                account_id:       account.id,
                menu_addition_id: ap.id
              )
              update_temp_right(new_perm, start_date, end_date, grant_account)
            else
              update_temp_right(map_record, start_date, end_date, grant_account)
            end
          end
        end
      end
    end

    def import_temp_fund_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id = -1
          and t.l_basecombi_id = -1
      SQL
    end

    def import_temp_fund_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_rights_sql
        sql = sql + " union " + import_temp_fund_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, fund_code, perm_codes, _asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          fund          = Jiaoyi2::Fund.find_by(quarter_id: @quarter_id, code: fund_code)

          next unless account && fund

          perm = Jiaoyi2::FundPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id:    fund.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm            = Jiaoyi2::FundPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              fund_id:    fund.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_unit_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id = -1
      SQL
    end

    def import_temp_fund_unit_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_unit_rights_sql
        sql = sql + " union " + import_temp_fund_unit_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          unit          = Jiaoyi2::FundUnit.find_by(quarter_id: @quarter_id, code: asset_code)

          next unless account && unit

          perm = Jiaoyi2::FundUnitPermission.find_or_initialize_by(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            fund_unit_id: unit.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm            = Jiaoyi2::FundUnitPermission.new(
              quarter_id:   @quarter_id,
              account_id:   account.id,
              fund_unit_id: unit.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_combi_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id <> -1
      SQL
    end

    def import_temp_fund_combi_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_combi_rights_sql
        sql = sql + " union " + import_temp_fund_combi_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, _asset_code, combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi2::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          combi         = Jiaoyi2::FundCombination.find_by(quarter_id: @quarter_id, code: combi_code)

          next unless account && combi

          perm = Jiaoyi2::FundCombinationPermission.find_or_initialize_by(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            fund_combination_id: combi.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm            = Jiaoyi2::FundCombinationPermission.new(
              quarter_id:          @quarter_id,
              account_id:          account.id,
              fund_combination_id: combi.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_all_caches
      return true unless @temporary
      @destroy_datas = get_destroy_datas if importer_config['temp_delete_record']

      days_of_data = (Time.now - importer_config['days_of_data'].to_i.days).strftime('%Y%m%d')

      history_temp_sql = ''

      if importer_config['history_temp']
        history_temp_sql = <<-SQL
          union
          select
            L_OPERATOR_NO,
            C_RIGHT_TYPE,
            C_RIGHT_OPERATOR,
            VC_RIGHT,
            L_ASSET_ID,
            L_BASECOMBI_ID,
            VC_RIGHTS_ID,
            L_BEGIN_DATE,
            L_END_DATE,
            L_GRANT_OPERATOR,
            L_INPUT_OPERATOR,
            L_GRANT_DATE,
            L_GRANT_TIME,
            C_STATUS,
            L_BEGIN_TIME,
            L_END_TIME
          from
            #{@table_space}THISOPTEMPRIGHT#{@sid_suffix}
          where
            L_BEGIN_DATE >= #{days_of_data}
        SQL
      end

      sql = <<-SQL
        select
          L_OPERATOR_NO,
          C_RIGHT_TYPE,
          C_RIGHT_OPERATOR,
          VC_RIGHT,
          L_ASSET_ID,
          L_BASECOMBI_ID,
          VC_RIGHTS_ID,
          L_BEGIN_DATE,
          L_END_DATE,
          L_GRANT_OPERATOR,
          L_INPUT_OPERATOR,
          L_GRANT_DATE,
          L_GRANT_TIME,
          C_STATUS#{ importer_config['history_temp'] ? ', L_BEGIN_TIME, L_END_TIME' : '' }
        from
          #{@table_space}toptempright#{@sid_suffix}
        where
          L_BEGIN_DATE >= #{days_of_data}
        #{history_temp_sql}
      SQL

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 当授权时间不存在时，跳出后面操作
          next unless r[7] && r[8]

          g_time = r[12].to_s
          while g_time.size < 6
            g_time = '0' + g_time
          end

          temporary = Jiaoyi2::Temporary.find_or_create_by(
            account_code:            r[0],
            authorizer_account_code: r[9],
            operator_account_code:   r[10],
            operation_type:          r[2],
            permission_type:         r[1].to_i,
            temp_start_date:         Date.parse(r[7].to_s),
            temp_end_date:           Date.parse(r[8].to_s),
            authorize_time:          Time.parse(r[11].to_s + g_time),
            permission_code:         r[3],
            unit_code:               r[4],
            combination_code:        r[5],
            addition_code:           r[6]
          )

          temporary.l_begin_time = r[14]
          temporary.l_end_time   = r[15]

          if importer_config['temp_delete_record'] && r[13].to_i == 5
            destroy_data                    = find_destroy_data(temporary)
            temporary.destroy_time          = destroy_data[:L_TIME] if destroy_data
            temporary.last_authorize_status = temporary.authorize_status
          end

          temporary.update(
            authorize_status: r[13]
          )
        end
      end
    end

    def find_destroy_data(temporary)
      if temporary.permission_type == 1
        t_data = [
          temporary.account_code,
          '2',
          temporary.permission_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.gsub('-', ''),
          (temporary.temp_end_date.to_s.gsub('-', '').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..6] == t_data }
      elsif temporary.permission_type == 0
        t_data = [
          temporary.account_code,
          '1',
          temporary.permission_code,
          temporary.unit_code,
          temporary.combination_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.gsub('-', ''),
          (temporary.temp_end_date.to_s.gsub('-', '').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..8] == t_data }
      end
    end

    def get_destroy_datas
      sql = <<-SQL
        select
          L_SERIAL_NO, L_OPERATOR_NO, L_DATE, L_TIME, VC_REMARKS
        from
          #{@table_space}thissystemlog#{@sid_suffix}
        where
          VC_OPCONTENT = '删除临时授权,[复核通过]'
        ORDER BY L_SERIAL_NO ASC
      SQL
      output_datas = []
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[4].to_s.include?('[续前一条]')
            output_datas.last[:VC_REMARKS] = output_datas.last[:VC_REMARKS] + r[4].gsub('[续前一条]', '')
          else
            g_time = r[3].to_s
            while g_time.size < 6
              g_time = '0' + g_time
            end
            output_datas << {
              L_SERIAL_NO:   r[0],
              L_OPERATOR_NO: r[1],
              L_TIME:        Time.parse(r[2].to_s + g_time),
              VC_REMARKS:    r[4]
            }
          end
        end
      end

      destroy_datas_list = []
      output_datas.each do |output_data|
        output_data[:VC_REMARKS].split('][').each do |remark|
          destroy_datas_list << {
            L_SERIAL_NO:   output_data[:L_SERIAL_NO],
            L_OPERATOR_NO: output_data[:L_OPERATOR_NO],
            L_TIME:        output_data[:L_TIME],
            VC_REMARK:     remark.split(',').map { |y| y.split('：')[1].to_s }
          }
        end
      end

      destroy_datas_list
    end

    def import_time_controls_sql
      <<-SQL
        select * from #{@table_space}toptimecontrol#{@sid_suffix}
      SQL
    end

    def import_time_controls
      if importer_config['time_control']
        ActiveRecord::Base.transaction do
          @database.exec(import_time_controls_sql) do |r|
            Jiaoyi2::TimeControl.create(
              quarter_id:   @quarter.id,
              role_code:    r[0],
              account_code: r[1],
              system_code:  r[2],
              menu_code:    r[3],
              begin_date:   r[4],
              begin_time:   r[5],
              end_date:     r[6],
              end_time:     r[7]
            )
          end
        end
      end
    end

    def update_temp_right(perm, start_date, end_date, grant_account)
      perm.is_temp                 = true
      perm.temp_start_date         = start_date
      perm.temp_end_date           = end_date
      perm.authorizer_account_id   = grant_account.id
      perm.authorizer_account_name = grant_account.name
      perm.save
    end

    # 国联安特殊的登录时间生成 excel
    def cpic_custom_importer
      require 'axlsx'
      sql = <<-SQL
        select distinct (x.l_operator_no) 账号编码,
        y.vc_operator_name 账号名称,
        case
        when y.c_operator_status = 1 then
        '正常'
        when y.c_operator_status = 2 then
        '冻结'
        end as 账号状态,
        case
        when l_optime_control = 1 then
        '有限制'
        /*else '无'*/
        end as 登录限制
        from (select distinct (b.l_operator_no) as l_operator_no
        from trade32.topmenurights t, trade32.toprolerights b
        where ((t.vc_menu_no = '2501' and
        (t.vc_menu_rights like '%4%' or t.vc_menu_rights like '%5%')) or
        t.vc_menu_no in ('1509', '2802'))
        and t.l_role_id ！ = -1
        and t.l_role_id = b.l_role_id
        and b.l_operator_no != 0
        union all
        select distinct (t.l_operator_no) as l_operator_no
        from trade32.topmenurights t
        where ((t.vc_menu_no in ('2501') and
        (t.vc_menu_rights like '%4%' or t.vc_menu_rights like '%5%')) or
        t.vc_menu_no in ('1509', '2802'))
        and t.l_role_id = -1) x,
        (select a.l_operator_no,
        a.vc_operator_name,
        d.l_optime_control,
        a.c_operator_status
        from trade32.toperator a
        left outer join (select distinct (c.l_operator_no) as l_operator_no,
        1 as l_optime_control
        from trade32.toptimecontrol c) d
        on a.l_operator_no = d.l_operator_no) y
        where x.l_operator_no = y.l_operator_no
        and y.c_operator_status！ = '3'
        and x.l_operator_no > 1000
        group by x.l_operator_no,
        y.vc_operator_name,
        y.l_optime_control,
        y.c_operator_status
        order by x.l_operator_no
      SQL

      export_excel                    = Axlsx::Package.new
      export_excel.use_shared_strings = true
      title_style                     = export_excel.workbook.styles.add_style({ sz: 10, border: Axlsx::STYLE_THIN_BORDER })
      content_style                   = export_excel.workbook.styles.add_style({ sz: 10, border: Axlsx::STYLE_THIN_BORDER, alignment: { :vertical => :center } })
      export_excel.workbook.add_worksheet(:name => 'sheet') do |sheet|
        i = 1
        sheet.add_row ['', '账号编码', '账号名称', '账号状态', '登录限制'], style: title_style
        @database.exec(sql) do |r|
          sheet.add_row [i, r[0], r[1].split('(')[0].split('-')[0], r[2], r[3]], style: content_style
          i = i + 1
        end
      end

      `mkdir /opt/data/export/excel/#{@quarter_id}/`
      file_path = "/opt/data/export/excel/#{@quarter_id}/用户可登录时间.xlsx"
      export_excel.serialize(file_path)
    end

    def class_exists?(class_name)
      klass = Module.const_get(class_name)
      klass.first
      return klass.is_a?(Class)
    rescue NameError
      return false
    end
  end
end

