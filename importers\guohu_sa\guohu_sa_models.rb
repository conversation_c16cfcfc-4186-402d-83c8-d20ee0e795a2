module <PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'guohu_sa_'
  end
end

class GuohuSa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSa::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSa::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaAccountsRoles < ActiveRecord::Base; end


class GuohuSaMenusRoles < ActiveRecord::Base; end
class GuohuSaAccountsMenus < ActiveRecord::Base; end
class GuohuSaAccountsOtherPermissions < ActiveRecord::Base; end
class GuohuSaOtherPermissionsRoles  < ActiveRecord::Base; end
