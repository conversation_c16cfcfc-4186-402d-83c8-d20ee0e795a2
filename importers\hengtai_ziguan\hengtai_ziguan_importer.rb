module AasOracleImporter
  class HengtaiZiguanImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @permissions = []
      @accounts = []
      @roles = []
    end

    def import_to_do
      destroy_all_datas
      import_accounts
      import_roles
      import_permissions
      import_ledgers(HengtaiZiguan::Account)
    end

    private

    def destroy_all_datas
      HengtaiZiguan::Account.where(quarter_id: @quarter_id).destroy_all
      HengtaiZiguan::Role.where(quarter_id: @quarter_id).destroy_all
      HengtaiZiguan::Permission.where(quarter_id: @quarter_id).destroy_all
      HengtaiZiguan::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      "SELECT U_ID,U_NAME,U_STATE FROM #{@table_space}TSYS_USER"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            @accounts << HengtaiZiguan::Account.find_or_create_by(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i.zero?
            )
          end
        end
      end
    end

    def import_roles_sql
      "SELECT R_ID,R_FLAG,R_NAME FROM #{@table_space}TSYS_ROLE"
    end

    def import_accounts_roles_sql
      "SELECT R_ID,U_ID FROM #{@table_space}TSYS_USER_ROLE_MAPPING"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            @roles << HengtaiZiguan::Role.find_or_create_by(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[2],
              role_type:  r[1]
            )
          end
        end
        @database.exec(import_accounts_roles_sql) do |r|
          role = @roles.find { |x| x.code.to_s == r[0].to_s }
          account = @accounts.find { |x| x.code.to_s == r[1].to_s }
          next unless account && role

          account.roles << role unless account.roles.include?(role)
        end
      end
    end

    def import_modules_sql
      "SELECT SM_ID,SM_CODE,SM_NAME,SM_PID FROM #{@table_space}TSYS_MODULE"
    end

    def import_role_modules_sql
      "SELECT R_ID,SM_ID,V_FLAG FROM #{@table_space}TSYS_ROLE_MODULE_MAPPING"
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        parent_id_index = 3
        @database.exec(import_modules_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            name = r[2]
            parent_id_value = r[parent_id_index]
            full_name = full_name(import_modules_sql, name, parent_id_value, 2, parent_id_index)

            @permissions << HengtaiZiguan::Permission.create(
              quarter_id:      @quarter_id,
              code:            r[0],
              name:            full_name,
              parent_code:     r[3],
              permission_type: '菜单权限'
            )
          end
        end
        @database.exec(import_role_modules_sql) do |r|
          role = @roles.find { |x| x.code.to_s == r[0].to_s }
          permission = @permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless role && permission

          HengtaiZiguan::AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            permission_id:         permission.id,
            role_id:               role.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def additional_permission_text(code)
      case code.to_s
      when '0' then '读'
      when '1' then '写'
      else
        code
      end
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    # 菜单名称中会有根菜单这样的数据
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      @database.exec(new_sql) do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end
  end
end
