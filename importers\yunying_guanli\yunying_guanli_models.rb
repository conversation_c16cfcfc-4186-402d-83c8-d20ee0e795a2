module Yunying<PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'yunying_guanli_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class YunyingGuanli::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles, -> { distinct }
  
  
  has_many :data1_accounts_roles_permissions
  
  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end


class YunyingGuanli::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  
  
  has_many :data1_accounts_roles_permissions
  

  validates :code, presence: true
  validates :name, presence: true
end

class YunyingGuanli::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end


  
  class YunyingGuanli::Data1Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class YunyingGuanli::Data1AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'yunying_guanli_data1_arps'
    belongs_to :data1_permission
    belongs_to :role, optional: true

    validates :data1_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end

module FundEmailsCheck
  def self.table_name_prefix
    'fund_emails_check_'
  end
end

class FundEmailsCheck::Fund < ActiveRecord::Base
end

class FundEmailsCheck::Report < ActiveRecord::Base
  has_and_belongs_to_many :funds
end