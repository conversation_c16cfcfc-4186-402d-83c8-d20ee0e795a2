# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'syfund'
  customer: '上银基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_syfund_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      tradedb:
        db_host: ************
        db_name: hfttrade
        db_user: dc_query
        db_pass: dc_query
      tadb:
        db_host: ***********
        db_name: hftta
        db_user: dc_query
        db_pass: dc_query
      guzhidb:
        db_host: ***********
        db_name: hftta
        db_user: dc_query
        db_pass: dc_query
      qingsuandb:
        db_host: ************
        db_name: fav45
        db_user: dc_query
        db_pass: DC_QUEYR
      oadb:
        db_host: ***********
        db_name: touyandb
        db_user: dc_query
        db_pass: DC_QUEYR
      zhixiaodb:
        db_host: ************
        db_name: hftfa
        db_user: dc_query
        db_pass: dc_query
  importers:
    - name: fanwei_oa_users
      # 接口对接，没有其他属性, 下面这个为了通过 initialize
      db_type: sqlserver
      tnsname: oadb
      code_column: u.id
      syfund_o32temp_import: true
      syfund_o32temp_recover_import: true
      syfund_o32temp_import_day: 90

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'hstrade.'
      sid_suffix: ''
      temporary: true
      days_of_data: 90
      history_temp: true
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: tadb
      table_space: ta4.
      sid_suffix: ''
    # 直销系统
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zhixiaodb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "CENTER"

    # 直销系统
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zhixiaodb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "COUNTER"

    # 直销系统
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zhixiaodb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "REMOTE"
    
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: guzhidb
      table_space: zrfa.
      sid_suffix: ''
      hs_customer_code: 70879
    - name: hs_qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: qingsuandb
      table_space: hsliqpower.
      sid_suffix: ''
development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_hftfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1



