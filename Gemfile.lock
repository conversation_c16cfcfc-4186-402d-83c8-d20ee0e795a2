GEM
  remote: https://gems.ruby-china.com/
  specs:
    activemodel (6.1.7.10)
      activesupport (= 6.1.7.10)
    activerecord (6.1.7.10)
      activemodel (= 6.1.7.10)
      activesupport (= 6.1.7.10)
    activerecord-jdbc-adapter (61.3-java)
      activerecord (~> 6.1.0)
    activesupport (6.1.7.10)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    ancestry (3.2.1)
      activerecord (>= 4.2.0)
    bulk_insert (1.9.0)
      activerecord (>= 3.2.0)
    concurrent-ruby (1.3.5)
    diff-lcs (1.5.1)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    jdbc-mysql (8.4.0.1)
    jruby-jars (9.4.9.0)
    jruby-rack (1.2.2)
    mini_mime (1.1.5)
    minitest (5.25.4)
    multi_xml (0.6.0)
    parallel (1.20.1)
    rake (10.5.0)
    redis (4.5.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.2)
    rubyzip (1.3.0)
    trading_days (0.3.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    warbler (2.0.5)
      jruby-jars (>= *******)
      jruby-rack (>= 1.1.1, < 1.3)
      rake (>= 10.1.0)
      rubyzip (~> 1.0, < 1.4)
    zeitwerk (2.6.18)

PLATFORMS
  universal-java-1.8
  x86_64-linux

DEPENDENCIES
  activerecord (~> 6.1.2)
  activerecord-jdbc-adapter (~> 61.2)
  activesupport (~> 6.1.2)
  ancestry
  bulk_insert
  bundler
  httparty
  jdbc-mysql (~> 8.0, >= 8.0.17)
  parallel (~> 1.20.1)
  rake
  redis (= 4.5.1)
  rspec (~> 3.0)
  trading_days (= 0.3.2)
  warbler

BUNDLED WITH
   2.4.22
