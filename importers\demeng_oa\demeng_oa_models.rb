module DemengOa
  def self.table_name_prefix
    'demeng_oa_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class User < ActiveRecord::Base; end

class Department < ActiveRecord::Base; end

class DemengOa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class DemengOa::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission

  validates :permission_id, presence: true
  validates :quarter_id, presence: true

end

class DemengOa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end


class DemengOa::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :permission_type, presence: true 
  validates :quarter_id, presence: true 
end

class DemengOa::JiaoyiTemp < ActiveRecord::Base; end
class DemengOa::LeaveUser < ActiveRecord::Base; end

class DemengOa::Approval < ActiveRecord::Base
  serialize :data_json, JsonWithSymbolizeNames
end