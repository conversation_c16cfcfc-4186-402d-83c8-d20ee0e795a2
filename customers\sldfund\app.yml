defaults: &defaults
  customer_id: sldfund
  customer: 施罗德基金
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  server: &1
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      host: **********
      database: aas_sldfund_production
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"

  agent: &2
    ldap:
      ad_db:
        host: shpssv-vwadla.corp.schroders.cn
        port: 636
        base: ou=schroders,dc=corp,dc=schroders,dc=cn
        user: sdataldapsync
        password:
        not_change_user: true
        is_tls: true
    oracle:
      jiaoyi_db:
        db_host: ************
        db_name: hsplinv_dg3
        db_user: SDATA
        db_pass: sdata
    importers: &3
      - name: jiaoyi
        bs_id: 31
        db_type: oracle
        tnsname: jiaoyi_db
        table_space: 'trade.'
        sid_suffix: ''
        days_of_data: 365
        temporary: false
        temp_delete_record: false
        history_temp: false
        # 交易类型
        trade_type_importer: true
        # 站点权限
        station_importer: true
        version: 1.1
      - name: ldap_sldfund_users
        bs_id: 1
        db_type: ldap
        tnsname: ad_db
        account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
        account_attrs:
        - dn
        - name
        - cn
        - samaccountname
        - title
        - useraccountcontrol
        - memberof
        - objectSid
        - primaryGroupID
        - description
        - department
        - mail
        # - mobile
        # - ipphone
        group_filter: # 不需要过滤了，在top_ou下
        group_attrs:
        - dn
        - name
        - memberOf
        - objectSid
        - primaryGroupID
        top_ou:
        - Users
        ignore_accountname: []
        ignore_ou:
        - Schroders
        - Users
        ignore_ou_but_import_users:
        # - 用户
        user_ou_sequence:
          default: 1
          用户: 2
          离职人员: 1

development:
  <<: *defaults
  server: *1
  agent: *2
  importers: *3
test:
  <<: *defaults
production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      host: 127.0.0.1
      database: aas_sldfund_production
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
  agent: *2
  importers: *3
