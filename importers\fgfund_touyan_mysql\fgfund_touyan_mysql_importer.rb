module AasOracleImporter

  class FgfundTouyanMysqlImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @level1_menus = []
      @level2_menus = []
      @level3_menus = []
      super
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_accounts_menus
      import_menus_roles

      import_ledgers(FgfundTouyan::Account)
    end

    def import_accounts_sql
      <<-SQL
        select user_id, user_name, user_status 
          from #{@table_space}tsys_user#{@sid_suffix}
      SQL
    end

    def import_accounts
      FgfundTouyan::Account.where(quarter_id: @quarter_id).destroy_all
      @accounts = []
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = FgfundTouyan::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_i == 0
              )
            @accounts << account
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select role_code, role_name from #{@table_space}tsys_role#{@sid_suffix}
      SQL
    end

    def import_roles
      FgfundTouyan::Role.where(quarter_id: @quarter_id).destroy_all
      @roles = []
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          role = FgfundTouyan::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
          @roles << role
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select user_code,role_code from #{@table_space}tsys_role_user#{@sid_suffix}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = FgfundTouyan::Role.where(quarter_id: @quarter_id).find_by(code: r[1])
          account = FgfundTouyan::Account.where(quarter_id: @quarter_id).find_by(code: r[0])

          if account && role
            FgfundTouyanAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_level_1_menus_sql
      <<-SQL
        select
          menu_code, menu_name, parent_code 
        from
          #{@table_space}tsys_menu#{@sid_suffix}
        where parent_code = 'bizroot'
      SQL
    end

    def import_level_2_menus_sql
      <<-SQL
        select
          menu_code, menu_name, parent_code 
        from
          #{@table_space}tsys_menu#{@sid_suffix}
        where parent_code IN (#{@level1_menus.map{|x| "'#{x.code}'"}.join(', ')})
      SQL
    end

    def import_level_3_menus_sql
      <<-SQL
        select
          menu_code, menu_name, parent_code 
        from
          #{@table_space}tsys_menu#{@sid_suffix}
        where parent_code IN (#{@level2_menus.map{|x| "'#{x.code}'"}.join(', ')})
      SQL
    end

    def import_level_4_menus_sql
      <<-SQL
        select
          menu_code, menu_name, parent_code 
        from
          #{@table_space}tsys_menu#{@sid_suffix}
        where parent_code IN (#{@level3_menus.map{|x| "'#{x.code}'"}.join(', ')})
      SQL
    end

    def import_menus
      FgfundTouyan::Menu.where(quarter_id: @quarter_id).destroy_all
      import_level1_menus
      import_level2_menus
      import_level3_menus
      import_level4_menus
    end

    def import_level1_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_level_1_menus_sql) do |r|
          menu = FgfundTouyan::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            name_level1: r[1],
            name_level2: '',
            name_level3: '',
            name_level4: ''
          )
          @level1_menus << menu
        end
      end
    end

    def import_level2_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_level_2_menus_sql) do |r|
          parent_menu = FgfundTouyan::Menu.where(quarter_id: @quarter_id).find_by(code: r[2])
          menu = FgfundTouyan::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            name_level1: parent_menu.name_level1,
            name_level2: r[1],
            name_level3: '',
            name_level4: ''
          )
          @level2_menus << menu
        end
      end
    end

    def import_level3_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_level_3_menus_sql) do |r|
          parent_menu = FgfundTouyan::Menu.where(quarter_id: @quarter_id).find_by(code: r[2])
          menu = FgfundTouyan::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            name_level1: parent_menu.name_level1,
            name_level2: parent_menu.name_level2,
            name_level3: r[1],
            name_level4: ''
          )
          @level3_menus << menu
        end
      end
    end

    def import_level4_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_level_4_menus_sql).each(as: :array) do |r|
          parent_menu = FgfundTouyan::Menu.where(quarter_id: @quarter_id).find_by(code: r[2])
          menu = FgfundTouyan::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            name_level1: parent_menu.name_level1,
            name_level2: parent_menu.name_level2,
            name_level3: parent_menu.name_level3,
            name_level4: r[1]
          )
          @level3_menus << menu
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select sub_trans_code,role_code,right_flag from tsys_role_right #{@table_space}tsys_role_right#{@sid_suffix}
      SQL
    end


    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = FgfundTouyan::Role.where(quarter_id: @quarter_id).find_by(code: r[1])
          menu = FgfundTouyan::Menu.where(quarter_id: @quarter_id).find_by(code: r[0])

          if menu && role
            FgfundTouyan::MenuPermission.create(
              quarter_id: @quarter_id,
              menu_id: menu.id,
              role_id: role.id,
              permission_code: r[2]
              )
          else
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[0]}" unless menu
          end
        end
      end
    end

    def import_accounts_menus_sql

      <<-SQL
        select sub_trans_code,user_id,right_flag from tsys_user_right #{@table_space}tsys_user_right#{@sid_suffix}
      SQL
    end

    def import_accounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_menus_sql) do |r|
          account = FgfundTouyan::Account.where(quarter_id: @quarter_id).find_by(code: r[1])
          menu    = FgfundTouyan::Menu.where(quarter_id: @quarter_id).find_by(code: r[0])

          if menu && account
            FgfundTouyan::MenuPermission.create(
              quarter_id: @quarter_id,
              menu_id: menu.id,
              account_id: account.id,
              permission_code: r[2]
              )
          else
            @logger.warn "#{self.class}: not found account #{r[1]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[0]}" unless menu
          end
        end
      end
    end
  end
end



