module AasOracleImporter
  class EtfYyImporter < ImporterBase
    def config
      @bs_id       = 310
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_data1_account_permissions

      import_data2_permissions

      import_data2_account_permissions

      import_data3_permissions

      import_data3_account_permissions

      import_ledgers(EtfYy::Account)
    end

    def destroy_exist_datas
      accounts = EtfYy::Account.where(quarter_id: @quarter_id)
      EtfYy::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      EtfYy::Role.where(quarter_id: @quarter_id).delete_all

      EtfYy::Data1Permission.where(quarter_id: @quarter_id).delete_all

      EtfYy::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      EtfYy::Data2Permission.where(quarter_id: @quarter_id).delete_all

      EtfYy::Data3Permission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<~EOF
        select
          a.operator_no,
          a.operator_code,
          a.operator_name,
          a.operator_status
        from
          etf.etf_toperator a
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '1', 'enum_value' => '正常', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << EtfYy::Account.create(
            quarter_id: @quarter_id,

            source_id:  get_enum(enums, 'source_id', r[0]),

            code:       get_enum(enums, 'code', r[1]),

            name:       get_enum(enums, 'name', r[2]),

            status:     r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<~EOF
        select
          r.role_code,
          r.role_code,
          r.role_name
        from
          bizframe.tsys_role r
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << EtfYy::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<~EOF
        select
          r.role_no,
          r.operator_no
	from
	  etf.etf_tifoprolerights r
          
      EOF
    end

    def import_accounts_roles 
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          EtfYy::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<~EOF
        select
          m.menu_code,
          m.menu_code,
          m.menu_name,
          b.sub_trans_name
        from
          bizframe.tsys_menu m
        left join
          bizframe.tsys_subtrans b
        on b.trans_code = m.menu_code
        where m.trans_code like '3328%'
        and m.trans_code != '3328'
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name,

            level2_name: r[3]

          }
          @data1_permissions << EtfYy::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<~EOF
        select
          role_code,
          trans_code
        from
          bizframe.tsys_role_right
        where
          right_flag = '1'
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          EtfYy::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                     data1_permission_id: permission.id)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<~EOF
        select
          user_id,
          trans_code
        from
          bizframe.tsys_user_right
        where
          right_flag = '1'
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.code.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        EtfYy::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id,
                                                   data1_permission_id: permission.id)
      end
    end

    def import_data2_permissions_sql
      <<~EOF
        select
          process_no,
          process_no,
          process_name
        from
          etf.etf_tprocessitems
        where
          enable_flag = 1
        and process_no not in (29,11)
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data2_permissions << EtfYy::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<~EOF
        select
          operator_no,
          process_name
        from
          etf.etf_tetfprocessright
      EOF
    end

    def import_data2_account_permissions
      enums = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.level1_name.to_s == r[1].to_s }
        next unless account && permission

        EtfYy::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id,
                                                   data2_permission_id: permission.id)
      end
    end

    def import_data3_permissions_sql
      <<~EOF
        select
          fund_id,
          fund_id,
          fund_name
        from
          etf.etf_tiffundinfo
      EOF
    end

    def import_data3_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data3_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data3_permissions << EtfYy::Data3Permission.create(json)
        end
      end
    end

    def import_data3_account_permissions_sql
      <<-EOF
        select
          c.operator_no,
          c.fund_id,
          e.asset_name,
          f.combi_name,
          c.asset_id,
          c.combi_id
        from
          etf.etf_tifopfundright c
        left join etf.etf_tifcombi f on c.combi_id = f.combi_id
        left join etf.etf_tifasset e on c.asset_id = e.asset_id
      EOF
    end

    def import_data3_account_permissions
      enums = []
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        EtfYy::Data3AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data3_permission_id: permission.id,
          asset_name:          r[2],
          combi_name:          r[3],
          asset_id:            r[4],
          combi_id:            r[5]
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
