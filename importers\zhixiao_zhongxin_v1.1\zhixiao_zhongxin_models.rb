module Zhixiao<PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'zhixiao_zhongxin_'
  end
end

class ZhixiaoZhongxin::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class ZhixiaoZhongxin::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class ZhixiaoZhongxin::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class ZhixiaoZhongxin::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class ZhixiaoZhongxinAccountsRoles < ActiveRecord::Base; end


class ZhixiaoZhongxinMenusRoles < ActiveRecord::Base; end
class ZhixiaoZhongxinAccountsMenus < ActiveRecord::Base; end
class ZhixiaoZhongxinAccountsOtherPermissions < ActiveRecord::Base; end
class ZhixiaoZhongxinOtherPermissionsRoles  < ActiveRecord::Base; end
