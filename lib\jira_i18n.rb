require 'active_support/inflector'
require 'yaml'

module AasOracleImporter
  class JiraI18n
    attr_reader :properties

    def initialize
      relative_path = File.expand_path('../jira_permissions.yaml', __FILE__)
      @properties = YAML.load_file(relative_path)
    end

    # 获取权限名称
    def permission_name(permission_key)
      key = "admin.permissions.#{permission_key}".to_sym
      properties[key] || permission_key
    end

    # 获取全局权限
    def global_permission_name(permission_key)
      deal_permission_key = ActiveSupport::Inflector.underscore(permission_key).split("_").join(".")
      key = "admin.global.permissions.#{deal_permission_key}".to_sym
      properties[key] || permission_key
    end

    # 获取权限描述
    def permission_desc(permission_key)
      key = "admin.permissions.descriptions.#{permission_key}".to_sym
      properties[key] || permission_key
    end
  end
end
