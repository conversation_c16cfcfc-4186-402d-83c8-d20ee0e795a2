module Mot
  def self.table_name_prefix
    'mot_'
  end
end

class Mot::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :positions
end

class Mot::Position < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :accounts
end

class Mot::Role < ActiveRecord::Base
  has_and_belongs_to_many :positions
  has_and_belongs_to_many :accounts
end

class MotAccountsRoles < ActiveRecord::Base; end
class MotAccountsPositions < ActiveRecord::Base; end
class MotPositionsRoles < ActiveRecord::Base; end
