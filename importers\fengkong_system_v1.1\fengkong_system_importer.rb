module AasOracleImporter

  class FengkongSystemImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix  = importer_config['sid_suffix'] #''
    end

    def import_to_do
      import_accounts
      import_roles
      import_account_role
      import_permissions
      import_role_permissions
      import_data2_permissions
      import_data2_account_permissions
      #import_data3_account_permissions #需要看一下国联安服务器是否有data3
      import_ledgers(FengkongSystem::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def account_sql
      "SELECT EM_ID,EM_USERNO,EM_USERNAME,EM_STATE FROM #{@table_space}TSYS_USER"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(account_sql) do |r|
          if r[0].to_s != ''
            FengkongSystem::Account.create(quarter_id: @quarter_id, code: r[1], name: r[2], status: r[3].to_i == 0,objid: r[0])
          end
        end
      end
    end

    def roles_sql
      "SELECT R_ID,R_NAME FROM #{@table_space}TSYS_ROLE"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(roles_sql) do |r|
          if r[0].to_s != ''
            role = FengkongSystem::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
          end
        end
      end
    end

    def account_role_sql
      "SELECT R_ID, EM_ID FROM #{@table_space}TSYS_ROLE_MAPPING"
    end

    def import_account_role
      ActiveRecord::Base.transaction do
        @database.exec(account_role_sql) do |r|
          if r[0].to_s != ''
            account = FengkongSystem::Account.find_by(quarter_id: @quarter_id, objid: r[1])
            role = FengkongSystem::Role.find_by(quarter_id: @quarter_id, code: r[0])
            if account && role
              account.roles << role
            end
          end
        end
      end
    end

    def permission_sql
      "SELECT SM_CODE, SM_NAME, SM_CODE_PARENT FROM #{@table_space}TSYS_MODULE"
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.exec(permission_sql) do |r|
          if r[0].to_s != ''
            FengkongSystem::Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], parent_code: r[2], permission_type: '模块权限')
          end
        end
      end
    end

    def role_permission_sql
      "SELECT a.R_ID, a.SM_CODE, a.FUNCTION_CODE, b.FUNCTION_DESC FROM #{@table_space}TSYS_MODULE_MAPPING a LEFT JOIN #{@table_space}TSYS_MODULE_MAPPING_ITEM b ON b.FUNCTION_CODE = a.FUNCTION_CODE"
    end

    def import_role_permissions
      ActiveRecord::Base.transaction do
        @database.exec(role_permission_sql) do |r|
          if r[0].to_s != ''
            permission = FengkongSystem::Permission.find_by(quarter_id: @quarter_id, code: r[1])
            role = FengkongSystem::Role.find_by(quarter_id: @quarter_id, code: r[0])
            if permission && role
              FengkongSystem::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id: permission.id, role_id: role.id, additional_permission: r[2], data_json: {permission_name: r[3]})
            end
          end
        end
      end
    end

    def data2_permissions_sql
      "SELECT port_code, p_name FROM #{@table_space}tprt where p_type=12 and maturity_date >= '#{Date.today.to_s}'"
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        @database.exec(data2_permissions_sql) do |r|
          if r[0].to_s != ''
            FengkongSystem::Data2Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1])
          end
        end
      end
    end

    def data2_account_permissions_sql
      "SELECT port_code, em_id FROM #{@table_space}tsys_port_mapping"
    end

    def import_data2_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(data2_account_permissions_sql) do |r|
          if r[0].to_s != ''
            permission = FengkongSystem::Data2Permission.find_by(quarter_id: @quarter_id, code: r[0])
            account = FengkongSystem::Account.find_by(quarter_id: @quarter_id, objid: r[1])
            if permission && account
              FengkongSystem::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, data2_permission_id: permission.id, account_id: account.id)
            end
          end
        end
      end
    end

    def data3_permissions_sql
      <<-EOF
        select a.em_userno, a.group_name, b.group_name
  from (select distinct A.*, D.Em_Userno
          from TREP_MODULE_GROUP A,
               TREP_INFO B,
               TSYS_MODULE_MAPPING C,
               (SELECT DISTINCT A.R_ID, EM_USERNO
                  FROM TSYS_ROLE_MAPPING A, TSYS_USER B
                 WHERE A.EM_ID = B.EM_ID) D
         where A.GROUP_ID = B.GROUP_ID
           and B.REP_CODE = C.SM_CODE
           and C.R_ID = D.R_ID
           and A.group_flag = '2'
           and A.group_parent IN ('-9999')) A,
       (select distinct A.*, D.Em_Userno
          from TREP_MODULE_GROUP A,
               TREP_INFO B,
               TSYS_MODULE_MAPPING C,
               (SELECT DISTINCT A.R_ID, EM_USERNO
                  FROM TSYS_ROLE_MAPPING A, TSYS_USER B
                 WHERE A.EM_ID = B.EM_ID) D
         where A.GROUP_ID = B.GROUP_ID
           and B.REP_CODE = C.SM_CODE
           and C.R_ID = D.R_ID
           and A.group_flag = '2'
           and A.group_parent IN ('-202', '-201')) B
 where a.group_id = b.group_parent
   and a.em_userno = B.em_userno 
      EOF
    end

    def import_data3_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(data3_permissions_sql) do |r|
          if r[0].to_s != ''
            permission = FengkongSystem::Data3Permission.find_or_create_by(quarter_id: @quarter_id, code: "#{r[1]}-#{r[2]}", name: r[2], permission_type: r[1])
            account = FengkongSystem::Account.find_by(quarter_id: @quarter_id, code: r[0])
            if permission && account
              FengkongSystem::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, data3_permission_id: permission.id, account_id: account.id)
            end
          end
        end
      end
    end
  end
end



