module AasOracleImporter
  class JzRongziUImporter < ImporterBase
    def config
      @bs_id       = 260
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      initialize_tables
    end

    def initialize_tables
      @table_operator     = "#{@table_space}operators#{@sid_suffix}"
      @table_account      = "#{@table_space}users#{@sid_suffix}"
      @table_role         = "#{@table_space}user_grp_std_setting#{@sid_suffix}"
      @table_account_role = "#{@table_space}user_group#{@sid_suffix}"
      @table_menu         = "#{@table_space}ui_tree#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}ui_grp_rights#{@sid_suffix}"
      @table_account_menu = "#{@table_space}ui_rights#{@sid_suffix}"
      @table_org          = "#{@table_space}branch#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      import_ledgers(JzRongziU::Account)
    end

    def destroy_exist_datas
      JzRongziU::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JzRongziU::Account.where(quarter_id: @quarter_id)
      JzRongziU::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzRongziU::Role.where(quarter_id: @quarter_id).delete_all
      JzRongziU::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzRongziU::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select branch, branch_id, brh_name, parent_branch from #{@table_org} where status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzRongziU::Department.create(
          quarter_id: @quarter_id,
          source_id:  r[0],
          code:       r[1],
          name:       r[2],
          status:     true
        )
      end
      # 建联关联关系
      JzRongziU::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzRongziU::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzRongziU::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts_sql
      <<-EOF
        select o.OP_CODE, u.USER_CODE, u.USER_NAME, o.status, u.OPEN_BRH from #{@table_operator} o, #{@table_account} u where o.OP_CODE = u.USER_CODE
      EOF
    end

    def import_accounts
      @departments = JzRongziU::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }

          account = JzRongziU::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '0',
            department_id: department&.id
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzRongziU::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select GRP_STD, GRP_STD_NAME from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzRongziU::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select USER_CODE, GRP_STD from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[1].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
          JzRongziU::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select NODE, NODE_FUNC, CAPTION, PARENT_NODE from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[1],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << JzRongziU::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select GRP_STD, NODE_FUNC, PERMIT_TYPE from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzRongziU::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select USER_CODE, NODE_FUNC, PERMIT_TYPE from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzRongziU::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def additional_permission_text(name)
      case name.to_s
      when '0' then '执行'
      when '1' then '授权'
      when '2' then '执行及授权'
      else
        name
      end
    end

    # 角色名称
    def role_text(role_code)
      case role_code.to_s
      when '1' then '客户'
      when '2' then '操作员'
      when '3' then '经纪人'
      when '4' then '代理人'
      else
        role_code
      end
    end
  end
end
