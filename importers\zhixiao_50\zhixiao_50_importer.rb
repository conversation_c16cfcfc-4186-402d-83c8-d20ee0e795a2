module AasOracleImporter

  class Zhixiao50Importer < ImporterBase

    def config
      @bs_id = 53
      @accounts = []
      @roles = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']


      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      import_ledgers(Zhixiao50::Account)
    end

    def import_accounts_sql
      <<-EOF
        select user_id, user_name, user_status from #{@table_space}tsys_user#{@sid_suffix}
      EOF
    end

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Zhixiao50::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            name: get_enum(enums, "name", r[1]),
            status: get_enum(enums, "status", r[2])&.to_s == '0',
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_code, role_name from #{@table_space}tsys_role#{@sid_suffix}
      EOF
    end
    

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Zhixiao50::Role.create(
            quarter_id: @quarter_id, 
            source_id: get_enum(enums, "source_id", r[0]), 
            code: get_enum(enums, "code", r[0]), 
            name: get_enum(enums, "name", r[1]))
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_code, user_code from #{@table_space}tsys_role_user#{@sid_suffix}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            Zhixiao50::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select MENU_CODE, MENU_NAME, KIND_CODE 
        from #{@table_space}tsys_menu#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1],
            level2_name: kind_code_json[r[2].to_s]
          }
          @data1_permissions << Zhixiao50::Data1Permission.create(json)
        end
      end
      
    end
    
    def kind_code_json
      {
        'BSFRAME' => '柜台',
        'UOTFRAME' => '运营终端',
        'BIZFRAME' => '用户权限系统',
        'RICFRAME' => '权益中心'
      }
    end
    
    def import_data1_role_permissions_sql
      <<-EOF
        SELECT 
          a.role_code,
          c.MENU_CODE,
          b.sub_trans_name
        from #{@table_space}TSYS_ROLE_RIGHT#{@sid_suffix} a
        left join #{@table_space}tsys_subtrans#{@sid_suffix} b
          ON a.sub_trans_code = b.sub_trans_code
        LEFT JOIN #{@table_space}TSYS_MENU#{@sid_suffix} c
          ON a.TRANS_CODE = c.MENU_CODE
        where a.right_flag = '1'
          AND c.menu_type in ('2', '6', '3')
          and c.MENU_CODE NOT IN ('mainIndex', '907c5d1895164794')
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            Zhixiao50::Data1AccountsRolesPermission.create(
              quarter_id: @quarter_id, 
              role_id: role.id, 
              data1_permission_id: permission.id,
              additional_permission: r[2]
            )
          end
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        SELECT 
          a.USER_ID,
          c.MENU_CODE,
          b.sub_trans_name
        from #{@table_space}TSYS_USER_RIGHT#{@sid_suffix} a
        left join #{@table_space}tsys_subtrans#{@sid_suffix} b
          ON a.sub_trans_code = b.sub_trans_code
        LEFT JOIN #{@table_space}TSYS_MENU#{@sid_suffix} c
          ON a.TRANS_CODE = c.MENU_CODE
        where a.right_flag = '1'
          AND c.menu_type in ('2', '6', '3')
          and c.MENU_CODE NOT IN ('mainIndex', '907c5d1895164794')
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
        if account && permission
          Zhixiao50::Data1AccountsRolesPermission.create(
            quarter_id: @quarter_id, 
            account_id: account.id, 
            data1_permission_id: permission.id,
            additional_permission: r[2]
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.gsub(';','')
      
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
      
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?
      name.gsub('&nbsp;', ' ')
    end

  end
end



