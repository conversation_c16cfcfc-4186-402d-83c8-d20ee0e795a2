module ProductCenter
  def self.table_name_prefix
    'product_center_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class ProductCenter::Account < ActiveRecord::Base
  belongs_to :user
end

class ProductCenter::Fund < ActiveRecord::Base
  serialize :o32_fund_codes, ConvertTools::JsonWithSymbolizeNames
  serialize :guzhi_fund_codes, ConvertTools::JsonWithSymbolizeNames
end

class ProductCenter::FundPosition < ActiveRecord::Base
end

class ProductCenter::Role < ActiveRecord::Base
end
