class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

module ImsJiaoyiOracle
  def self.table_name_prefix
    'ims_jiaoyi_oracle_'
  end
end

class ImsJiaoyiOracle::Account < ActiveRecord::Base

  has_and_belongs_to_many :roles
  has_many :accounts_roles_permissions

  validates :code, presence: true
  validates :name, presence: true
end

class ImsJiaoyiOracle::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission

  validates :permission_id, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class ImsJiaoyiOracle::Permission < ActiveRecord::Base
  belongs_to :quarter

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter, presence: true
  validates :permission_type, presence: true
  serialize :data_json, JsonWithSymbolizeNames
end

class ImsJiaoyiOracle::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  belongs_to :quarter
  has_many :accounts_roles_permissions

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter, presence: true
end
