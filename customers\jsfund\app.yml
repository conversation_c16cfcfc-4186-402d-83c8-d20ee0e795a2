# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'jsfund'
  customer: '嘉实基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_jsfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      bidb:
        # in tns name
        db_name: 'bidb'
        db_user: 'sdata'
        db_pass: 'Aa111111'

  importers:
    # 按照顺序依次导入
    - name: jsfund_hr
      tnsname: bidb
      table_name: 'PS_JS_ACC_MAG_SYS'
      user_normal_status_text: '在职'

    - name: jsfund_zhixiao
      bs_id: 21
      tnsname: bidb
      table_accounts: "hsi_user"
      table_roles: "hsi_group"
      table_accounts_roles: "hsi_usergroup"
      table_menus: "hsi_right"
      table_menus_roles: "hsi_groupright"


development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'mail:new_quarter'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_jsfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1

