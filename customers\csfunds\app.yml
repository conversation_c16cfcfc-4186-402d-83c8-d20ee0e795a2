defaults: &defaults
  customer_id: csfunds
  customer: 长盛基金
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_csfunds_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    ldap:
      ad_db:
        host: *************
        port: 389
        base: dc=CS,dc=csfunds,dc=com,dc=cn
        user: yanx
        password: 4vfrTyhnBG
      cs_yw_ad_db:
        host: *************
        port: 389
        base: dc=CS,dc=csfunds,dc=com,dc=cn
        user: yanx
        password: 4vfrTyhnBG
    oracle:
      lanlin_db:
        db_host: *************
        db_name: oadbtest
        db_user: ekp
        db_pass: ekp202111
      guzhi_db:
        db_host: *************
        db_name: guzhi
        db_user: v45
        db_pass: '1'
      qingsuan_db:
        db_host: ************
        db_name: orcltest
        db_user: es_dba
        db_pass: 123456Aa
      zhixiao_db:
        db_host: ************
        db_name: orcl48
        db_user: qxgl
        db_pass: qxgl
      guohu_ta_db:
        db_host: ************
        db_name: orcl48
        db_user: qxgl
        db_pass: qxgl
      guohu_sa_db:
        db_host: ************
        db_name: orcl48
        db_user: qxgl
        db_pass: qxgl
      tradedb:
        db_host: ************
        db_name: trade
        db_user: tzjc
        db_pass: tzjc321
      qdii_db:
        db_host: ************
        db_name: qdtest
        db_user: tzjc
        db_pass: tzjc321
      fund_disclosure_yss_db:
        db_host: *************
        db_name: guzhi
        db_user: bbzx
        db_pass: '1'
      crm_db:
        db_host: ************
        db_name: crmdbt
        db_user: standdb_reader
        db_pass: standdb_reader
      touyan_db:
        db_host: ************
        db_name: yjdb
        db_user: Csjj
        db_pass: CSJJsinitek_2022
      fengkong_db:
        db_host: *************
        db_name: htrisk
        db_user: fsjzx
        db_pass: fsjzx321
      fanxiqian_db:
        db_host: *************
        db_name: ccdbtest
        db_user: customer
        db_pass: customer
      hs_kefu_db:
        db_host: *************
        db_name: ccdbtest
        db_user: customer
        db_pass: customer
      yh_jianguan_db:
        db_host: ************
        db_name: orcl
        db_user: yhrpt_test
        db_pass: yhrpt_test
      custom10002:
        db_host: ************
        db_name: orcl
        db_user: fdc_test
        db_pass: fdc_test
      yss_xinpi_db:
        db_host: *************
        db_name: guzhi
        db_user: sofa
        db_pass: '1'
      hs_yujiata_db:
        db_host: ************
        db_name: orcl48
        db_user: qxgl
        db_pass: 'qxgl'
  importers:
  - name: lanling_oa
    bs_id: 2
    db_type: oracle
    tnsname: lanlin_db
    table_space: ''
    sid_suffix: ''
    ignore_department_codes:
      - 17d7e279238f6b6835cab184821a9c94
      - 1872282fd26418d9088b5dd4db9bd6b8
  - name: guzhi_yss45
    bs_id: 24
    db_type: oracle
    tnsname: guzhi_db
    table_space: ''
    sid_suffix: ''
  - name: qingsuan
    version: 1.1
    bs_id: 25
    db_type: oracle
    tnsname: qingsuan_db
    table_space: es_system.
    sid_suffix: ''
  - name: zhixiao_zhongxin
    bs_id: 27
    db_type: oracle
    tnsname: zhixiao_db
    table_space: csds_20210714.
    sid_suffix: ''
    sub_system: CENTER
  - name: zhixiao_guitai
    bs_id: 28
    db_type: oracle
    tnsname: zhixiao_db
    table_space: csds_20210714.
    sid_suffix: ''
    sub_system: COUNTER
  - name: yuancheng_guitai
    bs_id: 29
    db_type: oracle
    tnsname: zhixiao_db
    table_space: csds_20210714.
    sid_suffix: ''
    sub_system: REMOTE
  - name: guohu_ta
    bs_id: 30
    db_type: oracle
    tnsname: guohu_ta_db
    table_space: ta4_20210714.
    sid_suffix: ''
    version: 1.1
  - name: guohu_sa
    bs_id: 26
    db_type: oracle
    tnsname: guohu_sa_db
    table_space: subta_20210714.
    sid_suffix: ''
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: tradedb
    table_space: trade32.
    sid_suffix: ''
    days_of_data: 365
    temporary: true
    temp_delete_record: true
    history_temp: true
  - name: qdii
    bs_id: 62
    db_type: oracle
    tnsname: qdii_db
    table_space: qdii.
    sid_suffix: ''
    days_of_data: 365
    temporary: false
    temp_delete_record: false
    history_temp: false
  - name: fund_disclosure_yss_oracle
    bs_id: 32
    db_type: oracle
    tnsname: fund_disclosure_yss_db
    table_space: ''
    sid_suffix: ''
  - name: crm_system
    bs_id: 37
    db_type: oracle
    tnsname: crm_db
    table_space: standdb.
    sid_suffix: ''
  - name: xiening_touyan_csfunds
    bs_id: 38
    db_type: oracle
    tnsname: touyan_db
    table_space: ''
    sid_suffix: ''
  - name: fengkong_system
    bs_id: 45
    db_type: oracle
    tnsname: fengkong_db
    table_space: xrisk.
    sid_suffix: ''
  - name: mfcteda_fxq
    bs_id: 311
    db_type: oracle
    tnsname: fanxiqian_db
    table_space: stg.aml_
    sid_suffix: ''
  - name: ad
    bs_id: 230
    db_type: ldap
    tnsname: ad_db
    account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass =
      computer)))"
    account_attrs:
    - dn
    - name
    - cn
    - samaccountname
    - title
    - useraccountcontrol
    - memberof
    - objectSid
    - primaryGroupID
    - description
    - sAMAccountType
    group_filter: "(&(objectclass = group))"
    group_attrs:
    - dn
    - name
    - memberOf
    - objectSid
    - primaryGroupID
    - sAMAccountType
  - name: cs_yw_ad
    bs_id: 231
    db_type: ldap
    tnsname: cs_yw_ad_db
    account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass =
      computer)))"
    account_attrs:
    - dn
    - name
    - cn
    - samaccountname
    - title
    - useraccountcontrol
    - memberof
    - objectSid
    - primaryGroupID
    - description
    - sAMAccountType
    group_filter: "(&(objectclass = group))"
    group_attrs:
    - dn
    - name
    - memberOf
    - objectSid
    - primaryGroupID
    - sAMAccountType
  - name: hs_kefu
    bs_id: 305
    db_type: oracle
    tnsname: hs_kefu_db
    table_space: ''
    sid_suffix: ''
  - name: yh_jianguan
    bs_id: 306
    db_type: oracle
    tnsname: yh_jianguan_db
    table_space: ''
    sid_suffix: ''
  - name: yss_xinpi
    bs_id: 307
    db_type: oracle
    tnsname: yss_xinpi_db
    table_space: ''
    sid_suffix: ''
  - name: hs_yujiata
    bs_id: 308
    db_type: oracle
    tnsname: hs_yujiata_db
    table_space: 'yebta_20210714.'
    sid_suffix: ''
  # - name: custom10002
  #   bs_id: 10002
  #   db_type: oracle
  #   tnsname: custom10002
development:
  <<: *defaults
test:
  <<: *defaults
production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_csfunds_production
      username: aas
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
      host: *************
