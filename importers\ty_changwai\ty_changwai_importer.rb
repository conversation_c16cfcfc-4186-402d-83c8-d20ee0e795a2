module AasOracleImporter
  class TyChangwaiImporter < ImporterBase
    def config
      @bs_id       = 263
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      # 角色菜单关联关系表名称太长，会导致报错，有可能需要修改名称，所以这里支持自定义
      role_menu_table      = importer_config['role_menu']
      role_menu_table_name = role_menu_table.nil? || role_menu_table.empty? ? 'auth_role_resource_permission' : role_menu_table

      @table_account      = "#{@table_space}auth_user#{@sid_suffix}"
      @table_role         = "#{@table_space}auth_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}auth_user_role#{@sid_suffix}"
      @table_menu         = "#{@table_space}auth_resource#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}#{role_menu_table_name}#{@sid_suffix}"
      @table_account_menu = "#{@table_space}auth_resource_permission#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_ledgers(TyChangwai::Account)
    end

    def destroy_exist_datas
      accounts = TyChangwai::Account.where(quarter_id: @quarter_id)
      TyChangwai::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      TyChangwai::Role.where(quarter_id: @quarter_id).delete_all
      TyChangwai::Data1Permission.where(quarter_id: @quarter_id).delete_all
      TyChangwai::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select id, username, nick_name, expired from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        # 注意: expired值是0.0这怎样的float类型，所以要to_i转换
        select_db_datas(import_accounts_sql).each do |r|
          account = TyChangwai::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_i&.to_s == '0'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'TyChangwai::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id, role_name from #{@table_role} where REVOKED = 0
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << TyChangwai::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          TyChangwai::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select ID, ID, RESOURCE_NAME, PARENT_ID from #{@table_menu} where REVOKED = 0
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << TyChangwai::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, RESOURCE_ID, RESOURCE_PERMISSION_TYPE from #{@table_role_menu} where REVOKED = 0
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          TyChangwai::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select USER_ID, RESOURCE_ID, RESOURCE_PERMISSION_TYPE from #{@table_account_menu} where REVOKED = 0
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        TyChangwai::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def additional_permission_text(code)
      case code.to_s
      when 'CREATE_ROLE' then '创建角色'
      when 'UPDATE_ROLE' then '修改角色'
      when 'DELETE_ROLE' then '删除角色'
      when 'GRANT_ACTION' then '授权'
      when 'READ_RESOURCE' then '查看资源'
      when 'CREATE_USER' then '创建用户'
      when 'UPDATE_USER' then '修改用户'
      when 'DELETE_USER' then '删除用户'
      when 'CREATE_SCRIPT_USER' then '创建脚本用户'
      when 'READ_USER' then '读取用户'
      when 'LOCK_USER' then '锁定用户'
      when 'UNLOCK_USER' then '解锁用户'
      when 'EXPIRE_USER' then '使用户过期'
      when 'UNEXPIRE_USER' then '取消用户过期状态'
      when 'CHANGE_PASSWORD' then '重置用户密码'
      when 'CREATE_NAMESPACE' then '创建资源组'
      when 'DELETE_NAMESPACE' then '删除资源组'
      when 'UPDATE_NAMESPACE' then '更新资源组'
      when 'CREATE_BOOK' then '创建交易簿'
      when 'CREATE_TRADE' then '创建交易'
      when 'UPDATE_TRADE' then '更新'
      when 'DELETE_TRADE' then '删除交易'
      when 'READ_TRADE' then '查看交易'
      when 'UPDATE_BOOK' then '更新交易簿'
      when 'DELETE_BOOK' then '删除交易簿'
      when 'READ_BOOK' then '查看交易簿'
      when 'READ_PORTFOLIO' then '查看投资组合'
      when 'CREATE_PORTFOLIO' then '创建投资组合'
      when 'UPDATE_PORTFOLIO' then '修改投资组合'
      when 'DELETE_PORTFOLIO' then '删除投资组合'
      when 'CREATE_DEPARTMENT' then '创建部门'
      when 'UPDATE_DEPARTMENT' then '更新部门'
      when 'DELETE_DEPARTMENT' then '删除部门'
      when 'CREATE_INSTRUMENT' then '创建标的物'
      when 'DELETE_INSTRUMENT' then '删除标的物'
      when 'UPDATE_INSTRUMENT' then '修改标的物'
      when 'UPDATE_MARGIN' then '更新保证金'
      when 'READ_CLIENT' then '读取客户'
      when 'CREATE_CLIENT' then '创建客户'
      when 'UPDATE_CLIENT' then '更新客户'
      when 'DELETE_CLIENT' then '删除客户'
      when 'CREATE_APPROVAL_GROUP' then '创建审批组'
      when 'UPDATE_APPROVAL_GROUP' then '更新审批组'
      when 'DELETE_APPROVAL_GROUP' then '删除审批组'
      when 'UPDATE_APPROVAL_GROUP_USER' then '更新审批组用户列表'
      when 'UPDATE_TASK_NODE' then '审批组关联任务节点'
      when 'CREATE_PROCESS_AND_TRIGGER' then '流程定义与触发器管理创建'
      when 'UPDATE_PROCESS_DEFINITION' then '流程定义修改'
      when 'BIND_PROCESS_TRIGGER' then '流程绑定触发器'
      when 'CREATE_TRIGGER' then '创建触发器'
      when 'UPDATE_TRIGGER' then '更新触发器'
      when 'DELETE_TRIGGER' then '删除触发器'
      else
        code
      end
    end
  end
end
