# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'cjhxfund'
  customer: '创金合信'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_cjhxfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
  agent:
    oracle:
      tradedb:
        db_name: cjhx
        db_user: trade
        db_pass: trade
        db_host: ************
        db_port: 1528
  importers:

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'trade.'
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults



