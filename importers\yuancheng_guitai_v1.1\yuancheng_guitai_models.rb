module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'yuancheng_guitai_'
  end
end

class YuanchengGuitai::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class <PERSON><PERSON><PERSON><PERSON><PERSON>::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class Yuan<PERSON>Guitai::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class YuanchengGuitai::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class YuanchengGuitaiAccountsRoles < ActiveRecord::Base; end


class YuanchengGuitaiMenusRoles < ActiveRecord::Base; end
class YuanchengGuitaiAccountsMenus < ActiveRecord::Base; end
class YuanchengGuitaiAccountsOtherPermissions < ActiveRecord::Base; end
class YuanchengGuitaiOtherPermissionsRoles  < ActiveRecord::Base; end
