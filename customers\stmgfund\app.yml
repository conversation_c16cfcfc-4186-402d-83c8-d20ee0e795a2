# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'stmgfund'
  customer: '上投摩根基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_stmgfund_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      tradedb:
        db_host: *************
        db_name: fmpdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      zxdb:
        db_host: *************
        db_port: 5001
        db_name: dspdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      tadb:
        db_host: *************
        db_name: tapdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      qsdb:
        db_host: ************
        db_name: fcv2test
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      gzdb:
        db_host: *************
        db_name: fapdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      fkdb:
        db_host: **************
        db_name: investdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      crmdb:
        db_host: **************
        db_name: crmpdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      oadb:
        db_host: *************
        db_port: 5007
        db_name: Inpdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      xpdb:
        db_host: **************
        db_name: Xbrldb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
      fxqdb:
        db_host: **************
        db_name: Amlpdb
        db_user: AUTHAUDIT
        db_pass: Cifm_123
  importers:
    # MARK: 按照顺序依次导入
    # HR 系统
    - name: fanwei_oa_users
      # 接口对接，没有其他属性, 下面这个为了通过 initialize
      db_type: oracle
      tnsname: oadb
      clear_name_regexp: "\\[.*\\]$"
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: FMP32.
      sid_suffix: ''
      c_menu_type: [1]
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: tadb
      table_space: tap4.
      sid_suffix: ''
    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: gzdb
      table_space: fa45.
      sid_suffix: ''
    # 清算系统
    - name: qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: xedmdb
      table_space: 'es_system.'
      sid_suffix: ''
    # 衡泰债券，上投摩根叫风控系统
    - name: hengtai_zhaiquan_v43
      bs_id: 39
      db_type: oracle
      tnsname: fkdb
      table_space: xrisk.
      sid_suffix: ''
    - name: dingdian_crm
      bs_id: 37
      db_type: oracle
      tnsname: crmdb
      name_must_in_users: false
      table_space: jjcrm.
      sid_suffix: ''
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
    - name: xbrl_xinpi
      bs_id: 10057
      db_type: oracle
      tnsname: xpdb
      table_space: xbrlnew.
      sid_suffix: ''
    - name: xiening_fxq
      bs_id: 71
      db_type: oracle
      tnsname: fxqdb
      table_space: Aml_st_product.
      sid_suffix: ''
development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_stmgfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
