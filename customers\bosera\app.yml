# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'bosera'
  customer: '博时基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_bosera_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
  agent:
    oracle:
      tradedb:
        db_name: 'TRADE_195'
        db_user: 'trade_reader'
        db_pass: 'trade_reader'
    sqlserver:
      bs_oa:
        db_host: '***********'
        db_user: 'omss'
        db_pass: 'p@ssword'
        db_name: 'bs_docmanager'
        tds_v: '8.0'


  importers:

    - name: bosera_oa
      bs_id: 1
      db_type: sqlserver
      tnsname: bs_oa

    - name: ji<PERSON>yi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'trade.'
      sid_suffix: ''
      days_of_data: 365
      temporary: true

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults



