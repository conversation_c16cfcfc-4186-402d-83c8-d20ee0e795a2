---
defaults:
  customer_id: xdfund
  customer: 信达澳亚
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: &1
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_development
      username:
      password: 123456
      host: 127.0.0.1
  agent: &2
    oracle:
      o32db:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      gzdb:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      xduser:
        db_host: *************
        db_name: orcl
        db_user: workweixin
        db_pass: workweixin
      
    mysql:
      seedb:
        db_host: 
        db_name: 
        db_port: 
        db_user: 
        db_pass: 
    http:
      th_jiaoyi:
        url: 
  importers: &3
  - name: xdfund_users
    db_type: oracle
    tnsname: xduser
    table_space: 'hrsysv9.'
    is_user: true
  # - name: jiaoyi
  #   bs_id: 31
  #   db_type: oracle
  #   tnsname: o32_db
  #   table_space: HSTRADE.
  #   sid_suffix: ''
  #   days_of_data: 30
  #   temporary: false
  #   time_control: false
  #   temp_delete_record: false
  #   history_temp: false
  #   station_user_importer: false
  #   trade_type_importer: true
  #   old_fund_temp_check: false
  #   c_menu_type:
  #   - 1
  #   import_log:
  #     enable: false
  #     start_at: 5
  #   import_password_security:
  #     enable: false
  #   import_last_login_at:
  #     enable: false
  #     start_at: 1
  #   disable_status:
  #   - 3
  # - name: guzhi
  #   bs_id: 24
  #   db_type: oracle
  #   tnsname: xedmdb
  #   table_space: 'xedm_edge.thsgz_'
  #   sid_suffix: ''
development:
  customer_id: xdfund
  customer: 信达澳亚
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: *1
  agent: *2
  importers: *3
production:
  customer_id: xdfund
  customer: 信达澳亚
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: "<%= ENV['AAS_DATABASE_ADAPTER'] || 'mysql2' %>"
      encoding: utf8
      database: "<%= ENV['AAS_DATABASE_NAME'] %>"
      host: "<%= ENV['AAS_DATABASE_HOST'] %>"
      port: "<%= ENV['AAS_DATABASE_PORT'] %>"
      username: "<%= ENV['AAS_DATABASE_USER'] %>"
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
  agent: *2
  importers: *3
