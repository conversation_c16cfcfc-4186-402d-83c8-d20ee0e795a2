module <PERSON><PERSON><PERSON>G<PERSON><PERSON>
  def self.table_name_prefix
    'waibao_guzhi_'
  end
end

class WaibaoGuzhi::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :booksets
end

class W<PERSON><PERSON>Guzhi::Bookset < ActiveRecord::Base
  has_and_belongs_to_many :accounts
end

class W<PERSON>baoGuzhi::Menu < ActiveRecord::Base
end

class WaibaoGuzhi::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_many :permissions
end

class WaibaoGuzhi::Permission < ActiveRecord::Base
  belongs_to :role
  belongs_to :menu
end

class WaibaoGuzhi::AccountsRoles < ActiveRecord::Base; end
class WaibaoGuzhi::AccountsBooksets < ActiveRecord::Base; end
