# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'efunds'
  customer: '易方达'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/home/<USER>/qidianproject/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_efunds_development
      username: root
      password: 123456
      host: 127.0.0.1
  workday:
    enable: false
    api: http://localhost:3000/api/external/is_workday

  agent:
    oracle:
      # tnsname
      o32db:
        # in tns name
        db_name: 'oradb'
        db_user: 'user'
        db_pass: 'user#123'
        db_host: '**********'
      imsdb:
        db_name: 'DCPRO2'
        db_user: 'user'
        db_pass: 'user#123'
        db_host: '**********'
    http:
      zhdc_org_person:
        base_url: http://************:33303

  importers:
    # MARK: 按照顺序依次导入
    - name: efunds_user
      db_type: http
      tnsname: zhdc_org_person
      base_url: http://************:33303
      token: QUFT
      reqSys: AAS
      pass_company: ['易方达党委', '税务临检']
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o32db
      table_space: 'trade.'
      sid_suffix: ''
      days_of_data: 30
      temporary: true
      station_importer: true
      trade_type_importer: true
      c_menu_type: [1]

    - name: ims2022
      bs_id: 362
      db_type: oracle
      tnsname: imsdb
      base_url: http://************:33303
      token: QUFT
      reqSys: AAS
      table_space: ''
      sid_suffix: ''

    - name: ims_jiaoyi_oracle
      bs_id: 35
      db_type: oracle
      tnsname: imsdb
      table_space: 'trade.'
      sid_suffix: '@rtradedb'

    - name: fund_disclosure
      bs_id: 32
      db_type: http
      tnsname: zhdc_org_person
      base_url: http://************:33303
      token: QUFT
      reqSys: AAS
      version: efunds

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/home/<USER>/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_efunds_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
  workday:
    enable: false
    api: http://*************/api/external/is_workday



