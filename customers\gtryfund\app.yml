# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'gtryfund'
  customer: '国投瑞银基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_gtryfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      o3:
        db_name: 'o32'
        db_user: 'tradeia'
        db_pass: 'Tradeia20090603'
      ufosdb:
        db_name: 'ufos'
        db_user: 'ufosread'
        db_pass: 'ufosread!2018'
    ldap:
      oa:
        host: ***********
        port: 389
        base: 'dc=ubssdic, dc=com'
        user: rsa
        password: Ubssdic@123
      mn:
        host: ************
        port: 389
        base: 'dc=ubssdicmn, dc=com'
        user: test
        password: Ubssdic2020
      ta:
        host: ***********
        port: 389
        base: 'dc=ubssdicta, dc=com'
        user: test
        password: Ubssdic2020
      yeb:
        host: ***********
        port: 389
        base: 'dc=ubssdica, dc=com'
        user: test
        password: Ubssdic2020
      trade:
        host: *************
        port: 389
        base: 'dc=zrjj, dc=zrjj'
        user: test
        password: Ubssdic2020
      dev:
        host: ************
        port: 389
        base: 'dc=s-data, dc=cn'
        user: wanghao
        password: Aa111111

    txt:
      share_1:
        path: '/root'

    sqlserver:

  importers:
    # MARK: 按照顺序依次导入
    - name: ufos
      bs_id: 203
      db_type: oracle
      tnsname: ufosdb
      table_space: ''
      sid_suffix: ''

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o3
      table_space: 'trade.'
      sid_suffix: ''
      temporary: false

    - name: ad_group_mn
      bs_id: 207
      db_type: ldap
      tnsname: mn
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof', 'objectSid', 'primaryGroupID', 'description', 'sAMAccountType']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name', 'memberOf', 'objectSid', 'primaryGroupID','sAMAccountType']
      #touyan_sync: true
      set_gtry_account_type: true

    - name: ad_group_oa
      bs_id: 209
      db_type: ldap
      tnsname: oa
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof', 'objectSid', 'primaryGroupID', 'description', 'sAMAccountType', 'department']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name', 'memberOf', 'objectSid', 'primaryGroupID','sAMAccountType']
      #touyan_sync: true
      set_gtry_account_type: true

    - name: ad_group_ta
      bs_id: 211
      db_type: ldap
      tnsname: ta
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof', 'objectSid', 'primaryGroupID', 'description', 'sAMAccountType']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name', 'memberOf', 'objectSid', 'primaryGroupID','sAMAccountType']
      #touyan_sync: true
      set_gtry_account_type: true

    - name: ad_group_yeb
      bs_id: 213
      db_type: ldap
      tnsname: yeb
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof', 'objectSid', 'primaryGroupID', 'description', 'sAMAccountType']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name', 'memberOf', 'objectSid', 'primaryGroupID','sAMAccountType']
      #touyan_sync: true
      set_gtry_account_type: true

    - name: ad_group_zrjj
      bs_id: 215
      db_type: ldap
      tnsname: trade
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof', 'objectSid', 'primaryGroupID', 'description', 'sAMAccountType']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name', 'memberOf', 'objectSid', 'primaryGroupID','sAMAccountType']
      #touyan_sync: true
      set_gtry_account_type: true

    - name: share_oa_person
      tnsname: share_1
      ad_name: UBSSDIC
      bs_id: 210
      db_type: txt
      ad_group_id: 209
      file_path: '/opt/share_files/OA-person'
      config_path: '/opt/data/import'

    - name: share_oa_shrdata
      tnsname: share_1
      ad_name: UBSSDIC
      bs_id: 212
      db_type: txt
      ad_group_id: 209
      file_path: '/opt/share_files/OA-shrdata'
      config_path: '/opt/data/import'

    - name: share_oa_swap1
      tnsname: share_1
      ad_name: UBSSDIC
      bs_id: 214
      db_type: txt
      ad_group_id: 209
      file_path: '/opt/share_files/OA-swap1'
      config_path: '/opt/data/import'

    - name: share_oa_swap2
      tnsname: share_1
      ad_name: UBSSDIC
      bs_id: 216
      db_type: txt
      ad_group_id: 209
      file_path: '/opt/share_files/OA-swap2'
      config_path: '/opt/data/import'

    - name: share_oa_ubssdiccm
      tnsname: share_1
      ad_name: UBSSDIC
      bs_id: 218
      db_type: txt
      ad_group_id: 209
      file_path: '/opt/share_files/OA-ubssdiccm'
      config_path: '/opt/data/import'

    - name: share_ta_sharedata
      tnsname: share_1
      ad_name: UBSSDICTA
      bs_id: 220
      db_type: txt
      ad_group_id: 211
      file_path: '/opt/share_files/TA-ShareData'
      config_path: '/opt/data/import'

    - name: share_trade_sharedata
      tnsname: share_1
      ad_name: ZRJJ
      bs_id: 222
      db_type: txt
      ad_group_id: 215
      file_path: '/opt/share_files/Trade-ShareData'
      config_path: '/opt/data/import'

    - name: share_yeb_sharedata
      tnsname: share_1
      ad_name: UBSSDICA
      bs_id: 224
      db_type: txt
      ad_group_id: 213
      file_path: '/opt/share_files/YEB-ShareData'
      config_path: '/opt/data/import'


development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_gtryfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


