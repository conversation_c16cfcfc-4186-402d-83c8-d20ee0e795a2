# frozen_string_literal: true

module AasOracleImporter
  # 达梦客户端
  class DmClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.execute(sql).each do |row|
          yield row
        end
      else
        @database.execute(sql)
      end
    end

    # 输出hash数组
    def query(sql)
      @database.exec_query(sql)
    end

    private

    def initialize_driver
      load_driver_gem
      # ActiveRecord::ConnectionAdapters::ODBCAdapter.new 可以new实例，但是使用不方便，所以通过如下方式new实例
      @database = ActiveRecord::Base.odbc_connection(database_info)
    rescue ODBCAdapter::ConnectionFailedError => e
      raise ODBCAdapter::ConnectionFailedError, message_prefix + e.message
    end

    def load_driver_gem
      require 'odbc_adapter'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'odbc_adapter\'.')
      exit(-127)
    end
  end
end
