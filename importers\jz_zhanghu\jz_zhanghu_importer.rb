module AasOracleImporter
  class JzZhanghuImporter < ImporterBase
    def config
      @bs_id       = 252
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}OPERATOR#{@sid_suffix}"
      @table_user         = "#{@table_space}USERS#{@sid_suffix}"
      @table_role         = "#{@table_space}POST#{@sid_suffix}"
      @table_account_role = "#{@table_space}USER_POST#{@sid_suffix}"
      @table_menu         = "#{@table_space}sys_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}post_menu#{@sid_suffix}"
      @table_account_menu = "#{@table_space}user_menu#{@sid_suffix}"
      @table_org          = "#{@table_space}org#{@sid_suffix}"
      @table_user_org     = "#{@table_space}user_rtobj#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_ledgers(JzZhanghu::Account)
    end

    def destroy_exist_datas
      JzZhanghu::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JzZhanghu::Account.where(quarter_id: @quarter_id)
      JzZhanghu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzZhanghu::Role.where(quarter_id: @quarter_id).delete_all
      JzZhanghu::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzZhanghu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JzZhanghu::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JzZhanghu::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select org_code, org_code, org_name, parent_org from #{@table_org} where org_status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzZhanghu::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end
      # 建联关联关系
      JzZhanghu::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzZhanghu::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzZhanghu::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts_sql
      <<-EOF
        select O.OP_CODE,O.OP_CODE,U.USER_NAME,O.OP_STATUS, O.INT_ORG FROM #{@table_account} O,#{@table_user} U WHERE O.OP_CODE=U.USER_CODE
      EOF
    end

    def import_accounts
      @departments = JzZhanghu::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          account = JzZhanghu::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '0',
            department_id: department&.id
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzZhanghu::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        SELECT POST_CODE, POST_CODE, POST_NAME FROM #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzZhanghu::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select POST_CODE, USER_CODE, RIGHT_TYPE from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = [
          { 'name' => 'role_scope', 'value' => '0', 'enum_value' => '执行权限', 'label' => '角色范围' },
          { 'name' => 'role_scope', 'value' => '1', 'enum_value' => '授权权限', 'label' => '角色范围' },
          { 'name' => 'role_scope', 'value' => '2', 'enum_value' => '执行加授权', 'label' => '角色范围' }
        ]
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          JzZhanghu::AccountsRole.create(
            account_id: account.id,
            role_id:    role.id,
            role_scope: get_enum(enums, 'role_scope', r[2])
          )
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select menu_code, menu_code, menu_name from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << JzZhanghu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select post_code, menu_code, right_type from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      enums = [
        { 'name' => 'additional_permission', 'value' => '0', 'enum_value' => '执行权限', 'label' => '附加权限' },
        { 'name' => 'additional_permission', 'value' => '1', 'enum_value' => '授权权限', 'label' => '附加权限' },
        { 'name' => 'additional_permission', 'value' => '2', 'enum_value' => '执行加授权', 'label' => '附加权限' }
      ]
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzZhanghu::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: get_enum(enums, 'additional_permission', r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_code, menu_code, right_type from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      enums = [
        { 'name' => 'additional_permission', 'value' => '0', 'enum_value' => '执行权限', 'label' => '附加权限' },
        { 'name' => 'additional_permission', 'value' => '1', 'enum_value' => '授权权限', 'label' => '附加权限' },
        { 'name' => 'additional_permission', 'value' => '2', 'enum_value' => '执行加授权', 'label' => '附加权限' }
      ]
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzZhanghu::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: get_enum(enums, 'additional_permission', r[2])
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select org_code, org_code, org_name from #{@table_org}
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzZhanghu::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select user_code, rtobj from #{@table_user_org}
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzZhanghu::Data2AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data2_permission_id: permission.id
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
