---
:admin.permission.group.attachments.permissions: 附件权限
:admin.permission.group.comments.permissions: 评论权限
:admin.permission.group.issue.permissions: 问题权限
:admin.permission.group.other.permissions: 其它权限
:admin.permission.group.project.permissions: 项目权限
:admin.permission.group.time.tracking.permissions: 时间跟踪权限
:admin.permission.group.unavailable.permissions: 未提供的权限
:admin.permission.group.voters.and.watchers.permissions: 决策人和关注人权限
:admin.permission.project.edit: 编辑
:admin.permission.project.grant: 授权
:admin.permission.project.grant.header: 授予权限
:admin.permission.project.grant.permission.button: 授予权限
:admin.permission.project.granted.to: 授权给
:admin.permission.project.invalid.id.requested: 您尝试访问该权限方案不存在。
:admin.permission.project.invalid.user.warning: 下列用户有分配给他们的权限，但在用户管理内找不到。如果他们的删除是有意的，请删除相关的权限。
:admin.permission.project.permission: 权限
:admin.permission.project.permission.to: 权限
:admin.permission.project.remove: 移除
:admin.permission.project.remove.from: 从中删除
:admin.permission.project.remove.invalid.user.dialog.warning: 在用户管理内找不到此用户。如果他们的删除是故意的，请删除此权限。
:admin.permission.project.remove.plural: 移除权限
:admin.permission.project.remove.singular: 移除权限
:admin.permission.project.remove.specific: 删除权限。{0}从
:admin.permission.project.schemes: 权限方案
:admin.permission.project.security.types.less: 显示更少
:admin.permission.project.security.types.more: 显示详情
:admin.permission.project.will.be.granted: 将授予
:admin.permission.types.application.role: 应用程序访问权
:admin.permission.types.application.role.any: 任何登录的用户
:admin.permission.types.current.assignee: 当前经办人
:admin.permission.types.current.assignee.has.assignable.perm: 经办人 (仅对具有被分配权限的用户显示项目)
:admin.permission.types.current.reporter.has.create.perm: 报告人 (仅对具有创建权限的用户显示项目)
:admin.permission.types.group: 用户组
:admin.permission.types.group.custom.field: 组自定义域值
:admin.permission.types.project.lead: 项目主管
:admin.permission.types.reporter: 报告人
:admin.permission.types.single.user: 单个用户
:admin.permission.types.user.custom.field: 用户自定义域值
:admin.permissions.ARCHIVE_ISSUES: 事务存档
:admin.permissions.ASSIGNABLE_USER: 可分配得用户
:admin.permissions.ASSIGN_ISSUES: 分配问题
:admin.permissions.ATTACHMENT_DELETE_ALL: 删除所有附件
:admin.permissions.ATTACHMENT_DELETE_OWN: 删除自己的附件
:admin.permissions.BROWSE: 浏览项目
:admin.permissions.BROWSE_ARCHIVE: 浏览项目归档
:admin.permissions.CLOSE_ISSUES: 关闭问题
:admin.permissions.DELETE_ALL_COMMENTS: 删除所有评论
:admin.permissions.DELETE_OWN_COMMENTS: 删除自己的评论
:admin.permissions.EDIT_ALL_COMMENTS: 编辑所有评论
:admin.permissions.EDIT_OWN_COMMENTS: 编辑自己的评论
:admin.permissions.COMMENT_ISSUES: 添加评论
:admin.permissions.CREATE_ATTACHMENT: 创建附件
:admin.permissions.CREATE_ISSUES: 创建问题
:admin.permissions.DELETE_ISSUES: 删除问题
:admin.permissions.EDIT_ISSUES: 编辑问题
:admin.permissions.EXT_PROJECT_ADMIN: 扩展的项目管理
:admin.permissions.GLOBAL_BROWSE_ARCHIVE: 浏览归档
:admin.permissions.LINK_ISSUES: 链接问题
:admin.permissions.MANAGE_WATCHERS: 管理关注人
:admin.permissions.MODIFY_REPORTER: 修改报告人
:admin.permissions.MOVE_ISSUES: 移动问题
:admin.permissions.PROJECT_ADMIN: 管理项目
:admin.permissions.ADMINISTER_PROJECTS: 管理项目
:admin.permissions.RESOLVE_ISSUES: 解决问题
:admin.permissions.RESTORE_ISSUES: 恢复事务
:admin.permissions.SCHEDULE_ISSUES: 规划问题日程
:admin.permissions.SET_ISSUE_SECURITY: 设置安全级别
:admin.permissions.TRANSITION_ISSUES: 转换问题
:admin.permissions.VIEW_VERSION_CONTROL: 查看开发工具
:admin.permissions.VIEW_VOTERS_AND_WATCHERS: 查看决策人与关注人
:admin.permissions.VIEW_WORKFLOW_READONLY: 查看仅读工作流
:admin.permissions.DELETE_ALL_WORKLOGS: 删除所有工作日志
:admin.permissions.DELETE_OWN_WORKLOGS: 删除自己的工作日志
:admin.permissions.EDIT_ALL_WORKLOGS: 编辑所有工作日志
:admin.permissions.EDIT_OWN_WORKLOGS: 编辑自己的工作日志
:admin.permissions.WORK_ISSUES: 解决问题
:admin.permissions.DELETE_ALL_ATTACHMENTS: 删除所有附件
:admin.permissions.BROWSE_PROJECTS: 浏览项目
:admin.permissions.EDIT_SPRINT_NAME_AND_GOAL_PERMISSION: 编辑冲刺
:admin.permissions.MANAGE_SPRINTS_PERMISSION: 管理Sprint
:admin.permissions.START_STOP_SPRINTS_PERMISSION: 开始/完成冲刺
:admin.permissions.ADD_COMMENTS: 添加评论
:admin.permissions.CREATE_ATTACHMENTS: 创建附件
:admin.permissions.WORK_ON_ISSUES: 解决问题
:admin.permissions.DELETE_OWN_ATTACHMENTS: 删除自己的附件
:admin.permissions.VIEW_DEV_TOOLS: 查看开发工具
:admin.permissions.VIEW_READONLY_WORKFLOW: 查看仅读工作流
:admin.permissions.add.longdesc: 请选择要添加到此授权方案的权限类型
:admin.permissions.add.short.instruction: 请选择要分配的权限
:admin.permissions.add.title: 添加新的权限
:admin.permissions.deleting.confirmation: 您确定要删除 {0}{1}权限：{2}
:admin.permissions.descriptions.ADMINISTER_PROJECTS: 能够执行大多数管理功能(不包括导入和导出、SMTP 配置等)。
:admin.permissions.descriptions.ARCHIVE_ISSUES: 为特定项目存档事务的能力。
:admin.permissions.descriptions.ASSIGNABLE_USER: 可能把有这个权限的用户分配给问题。
:admin.permissions.descriptions.ASSIGN_ISSUES: 分配问题给其他人的能力。
:admin.permissions.descriptions.DELETE_ALL_ATTACHMENTS: 有这个权限的用户可以删除所有附件。
:admin.permissions.descriptions.DELETE_OWN_ATTACHMENTS: 有这个权限的用户可以删除自己的附件。
:admin.permissions.descriptions.BROWSE: 允许浏览项目和项目所属的问题。
:admin.permissions.descriptions.BROWSE_ARCHIVE: 从特定项目浏览已归档事务的能力。
:admin.permissions.descriptions.BULK_CHANGE: 能够修改数据收集的问题。例如, 解决多个问题中的一步。
:admin.permissions.descriptions.CLOSE_ISSUES: 能力的问题。常常是有用的在您的开发人员解决的问题、和 QA 部关闭它们。
:admin.permissions.descriptions.DELETE_ALL_COMMENTS: 删除问题上所有评论的能力。
:admin.permissions.descriptions.DELETE_OWN_COMMENTS: 删除问题上自己评论的能力。
:admin.permissions.descriptions.EDIT_ALL_COMMENTS: 编辑问题上所有评论的能力。
:admin.permissions.descriptions.EDIT_OWN_COMMENTS: 编辑问题上自己评论的能力。
:admin.permissions.descriptions.COMMENT_ISSUES: 在问题上评论的能力。
:admin.permissions.descriptions.CREATE_ATTACHMENT: 有此权限的用户可以为问题添加附件。
:admin.permissions.descriptions.CREATE_ISSUES: 创建问题的能力。
:admin.permissions.descriptions.CREATE_SHARED_OBJECTS: 分享能力的仪表板和过滤器与其他用户、组和角色。
:admin.permissions.descriptions.DELETE_ISSUES: 删除问题的能力。
:admin.permissions.descriptions.EDIT_ISSUES: 编辑问题的能力。
:admin.permissions.descriptions.EXT_PROJECT_ADMIN: 授予扩展{0}项目管理权限{1}。
:admin.permissions.descriptions.GLOBAL_BROWSE_ARCHIVE: 浏览所有已归档事务的能力。
:admin.permissions.descriptions.LINK_ISSUES: 能够链接问题放在一起并创建链接的问题。只有有用的链接的问题是打开的。
:admin.permissions.descriptions.MANAGE_GROUP_FILTER_SUBSCRIPTIONS: 管理(创建和删除)组筛选器订阅的能力。
:admin.permissions.descriptions.MANAGE_WATCHER_LIST: 管理问题关注人的能力。
:admin.permissions.descriptions.MODIFY_REPORTER: 在创建或编辑问题时修改报告人的能力。
:admin.permissions.descriptions.MOVE_ISSUES: 能够将问题在项目之间之间或工作流的同一项目(如果适用)。注意用户只可以移动的问题项目他或她有创建权限。
:admin.permissions.descriptions.PROJECT_ADMIN: 在Jira中管理项目的能力。
:admin.permissions.descriptions.RESOLVE_ISSUES: 能够解决和重新打开的问题。这包括设置功能的修复版本。
:admin.permissions.descriptions.RESTORE_ISSUES: 为特定项目恢复事务的能力。
:admin.permissions.descriptions.SCHEDULE_ISSUES: 查看或编辑问题到期的能力。
:admin.permissions.descriptions.SET_ISSUE_SECURITY: 在一个问题上设置安全级别的能力，以便该安全级别内的人可以查看问题。
:admin.permissions.descriptions.SYSTEM_ADMIN: 能够执行所有管理功能。必须至少有一组有此权限。
:admin.permissions.descriptions.TRANSITION_ISSUES: 转换问题的能力。
:admin.permissions.descriptions.USE: 能够登录到 Jira。它们是 “用户”。任何新创建的用户将自动加入这些团体, 除非这些团体的
  Jira 系统管理员或 Jira 的管理员权限。
:admin.permissions.descriptions.USER_PICKER: 能够选择用户或组弹出窗口以及能够使用的 “分享” 问题的功能。有此权限的用户还将可以看到所有的用户和组在系统中。
:admin.permissions.descriptions.VIEW_VERSION_CONTROL: 允许用户查看-发展有关的信息查看屏幕上的问题, 要提交、审查和构建信息。
:admin.permissions.descriptions.VIEW_VOTERS_AND_WATCHERS: 查看一个问题的决策人和关注人的能力。
:admin.permissions.descriptions.WORKFLOW_VIEW_READONLY: 拥有这个权限的用户可以查看工作流的只读版本。
:admin.permissions.descriptions.DELETE_ALL_WORKLOGS: 删除问题上所有工作日志的能力。
:admin.permissions.descriptions.DELETE_OWN_WORKLOGS: 删除问题上所工作日志的能力。
:admin.permissions.descriptions.EDIT_ALL_WORKLOGS: 编辑问题上所有工作日志的能力。
:admin.permissions.descriptions.EDIT_OWN_WORKLOGS: 编辑问题上自己工作日志的能力。
:admin.permissions.descriptions.WORK_ISSUES: 登录能力的工作的问题。只有有用的如果时间跟踪是打开的。
:admin.permissions.errors.customfieldnotindexed: 自定义域 “{0}”没有为搜索创建索引 - 请为这个自定义域添加搜索模板。
:admin.permissions.errors.fillradio: 请填写有效数据单选按钮旁的文字框内。
:admin.permissions.errors.invalid.combination: 该权限''{0}''不可授予''{1}''。
:admin.permissions.errors.mustselectpermission: 您必须选择需要添加的权限。
:admin.permissions.errors.mustselectscheme: 您必须选择一种方案要添加的许可权限。单击 “权限” 链接在左侧导航栏中的选择之一。
:admin.permissions.errors.mustselecttype: 您必须为这个权限选择一个类型。
:admin.permissions.errors.permissiondoesnotexist: 有关键字“{0}”的权限不存在。
:admin.permissions.errors.please.select.application.role: 请选择有效的应用程序访问权限。
:admin.permissions.errors.please.select.group.customfield: 请选择一个有效的组自定义域。
:admin.permissions.errors.please.select.project.role: 请选择一个有效的项目角色。
:admin.permissions.errors.please.select.user: 请选择一个有效的用户
:admin.permissions.errors.please.select.user.customfield: 请选择一个有效的用户自定义域。
:admin.permissions.extendedadmin.feedback.flag.feedback.link: 当然，我将帮助
:admin.permissions.extendedadmin.feedback.flag.message: 我们想了解更多有关您禁用此选项的原因。
:admin.permissions.extendedadmin.feedback.flag.title: 扩展的项目管理已禁用
:admin.permissions.extendedadmin.feedback.granterror: 我们无法授予延长项目管理权。您可以刷新此页面，然后重试。
:admin.permissions.extendedadmin.feedback.revokeerror: 我们无法废除延长项目管理权。您可以刷新此页面，然后重试。
:admin.permissions.feedback.feedbackerror.desc: 这些许可和人员组合不能用
:admin.permissions.feedback.feedbackerror.title.multiple: 我们未做任何更新
:admin.permissions.feedback.feedbackerror.title.single: 这种权限和人员组合形式不能用
:admin.permissions.feedback.successfuldelete.multiple: 做\! 好了我们已为您删除权限。
:admin.permissions.feedback.successfuldelete.single: 做\! 好了我们已为您删除权限。
:admin.permissions.feedback.successfulgrant.multiple: 做\! 好了 我们已授予您权限。
:admin.permissions.feedback.successfulgrant.single: 做\! 好了我们已授予您权限。
:admin.permissions.feedback.unspecifiederror.description: 可能由于各种原因，像是一个网络或应用程序的错误。您可以再试一次，或刷新页面，然后重试。
:admin.permissions.feedback.unspecifiederror.title: 我们不能够完成您最后行动
:admin.global.permissions.administer: Jira 管理员
:admin.global.permissions.bulk.change: 批量更改
:admin.global.permissions.create.shared.filter: 创建共享的对象
:admin.global.permissions.manage.group.filter.subscriptions: 管理组筛选器订阅
:admin.global.permissions.system.administer: Jira 系统管理员
:admin.global.permissions.system.admin: Jira 系统管理员
:admin.global.permissions.use: Jira 用户
:admin.global.permissions.user.picker: 浏览用户
:admin.global.permissions.create.shared.objects: 创建共享的对象
:admin.global.permissions.global.browse.archive: 浏览归档
:admin.globalpermissions.add.a.new.permission: 在下面添加权限。
:admin.globalpermissions.add.permission: 添加权限
:admin.globalpermissions.admins.note: "{0}注意\\:{1} 这个权限下的用户允许登录Jira。"
:admin.globalpermissions.confirm.question: 您确实要从权限<strong>{1}</strong>中删除组 <strong>{0}</strong>
  ?
:admin.globalpermissions.confirm.title: 删除全局权限
:admin.globalpermissions.description: 这些权限适用于所有的项目。它们是独立的项目特定的权限。
:admin.globalpermissions.external.manage.link: 配置许可权限的用户管理
:admin.globalpermissions.global.permissions: 全局权限
:admin.globalpermissions.instruction: 如果您希望设置权限的项目由--项目的基础上你可以将它们设置在{0}的许可计划{1}。要允许用户登录,
  他们必须为{2}的应用程序访问{1}
:admin.globalpermissions.jira.permissions: Jira 权限
:admin.globalpermissions.please.select.a.group: 请选择一个组
:admin.globalpermissions.please.select.a.permission: 请选择一个权限
:admin.globalpermissions.title: 全局项目权限
:admin.globalpermissions.use.note: "{0}注意\\:{1}登录Jira的所有用户必须拥有这个权限，而不管此用户是否拥有其它的权限。"
:admin.globalpermissions.user.limit.multiple.error: 您的许可证” 的用户超出限制时。用户可能无法创建的问题, 直到您有{2}升级您的许可证{3}或{0}数量减少的用户{1}。
:admin.globalpermissions.user.limit.single.error: 您的{0}的许可证的用户超过极限值。用户使用此许可证只能无法创建的问题,
  直到您有{3}升级您的许可证{4}或{1}数量减少的用户{2}。
:admin.globalpermissions.view.users: 查看用户
