# frozen_string_literal: true

module AasOracleImporter
  # CSV 客户端
  class CsvClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(file_path, args = {})
      encode         = args[:encode] || 'r:bom|utf-8'
      drop_lines     = args[:drop_lines] || 0
      headers_enable = args[:headers_enable] || false

      file = File.open(file_path, encode)
      CSV.parse(file.readlines.drop(drop_lines).join, headers: headers_enable)
    end

    def csv_path
      @path
    end

    private

    def initialize_driver
      @path = Pathname.new(database_info['path'])
    end
  end
end
