module AasOracleImporter
  class HsWaibaotaImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      super
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_additional_permissions
      import_acccounts_menus
      import_menus_roles

      import_ledgers(HsWaibaota::Account)
    end

    def destroy_exist_datas
      accounts = HsWaibaota::Account.where(quarter_id: @quarter_id)
      HsWaibaota::AccountsRoles.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HsWaibaota::Role.where(quarter_id: @quarter_id).destroy_all
      HsWaibaota::Menu.where(quarter_id: @quarter_id).destroy_all
      HsWaibaota::AdditionalPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select u.vc_user_code,
               u.vc_user_name,
               u.c_status
          from
               #{@table_space}FM_USER#{@sid_suffix} u
          order by u.vc_user_name
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            HsWaibaota::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i == 1
            )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_group_id, vc_group_name from #{@table_space}FM_GROUP#{@sid_suffix}
      SQL
    end

    # 考虑到历史情况，之前guohu_ta角色是没有编码的，很多客户又限制数据中心的字段，所以现在依然只导入名称为编码
    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          HsWaibaota::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          vc_menu_no, vc_menu_name
        from
          #{@table_space}FM_MENUITEM#{@sid_suffix}
        where
          c_menu_type = 1
        order by
          l_order
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          HsWaibaota::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_additional_permissions_sql
      <<-SQL
        select
          vc_menu_no,
          c_menu_right,
          vc_menu_right_name
        from
          #{@table_space}FM_EXTRAMENURIGHT#{@sid_suffix}
      SQL
    end

    def import_additional_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_additional_permissions_sql) do |r|
          menu = HsWaibaota::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          HsWaibaota::AdditionalPermission.create(
            quarter_id: @quarter_id,
            sub_code:   r[1],
            name:       r[2],
            menu_id:    menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_GROUP#{@sid_suffix} g
        where t.l_group_id = g.l_group_id
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = HsWaibaota::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          menu = HsWaibaota::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            HsWaibaota::MenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              HsWaibaota::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              HsWaibaota::AdditionalPermissionsRoles.create(
                role_id:                  role.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_acccounts_menus_sql
      <<-SQL
        select
          u.vc_user_code,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_USER#{@sid_suffix} u
        where t.l_user_id = u.l_user_id
      SQL
    end

    def import_acccounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_acccounts_menus_sql) do |r|
          account = HsWaibaota::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = HsWaibaota::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            HsWaibaota::AccountsMenus.create(menu_id: menu.id, account_id: account.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              HsWaibaota::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              HsWaibaota::AccountsAdditionalPermissions.create(
                account_id:               account.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          u.vc_user_code
        from
          #{@table_space}FM_GROUPUSER#{@sid_suffix} gu,
          #{@table_space}FM_GROUP#{@sid_suffix} g,
          #{@table_space}FM_USER#{@sid_suffix} u
        where
          gu.l_group_id = g.l_group_id
          and gu.l_user_id = u.l_user_id
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = HsWaibaota::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = HsWaibaota::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            HsWaibaota::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end
  end
end
