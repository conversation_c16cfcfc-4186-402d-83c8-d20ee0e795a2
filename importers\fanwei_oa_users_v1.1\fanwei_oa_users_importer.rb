module AasOracleImporter
  class FanweiOaUsersImporter < ImporterBase

    def config
      @bs_id              = importer_config['bs_id']
      @table_space        = importer_config['table_space']
      @sid_suffix         = importer_config['sid_suffix']
      @clear_name_regexp  = importer_config['clear_name_regexp']
      @ignore_company_ids = importer_config['ignore_company_ids'] || []
      @position_users     = [] # 拥有岗位的用户
      initialize_tables
    end

    def initialize_tables
      @table_companies = "#{@table_space}hrmsubcompany#{@sid_suffix}"
      @table_departments = "#{@table_space}hrmdepartment#{@sid_suffix}"
      @table_users = "#{@table_space}HrmResource#{@sid_suffix}"
      @table_jobs = "#{@table_space}HrmJobTitles#{@sid_suffix}"
    end

    def import_to_do
      @jobs = []
      @departments = []
      @users = []

      import_hr
      create_or_update_companies
      create_or_update_departments
      create_or_update_users
      link_manager
      update_user_name_clear

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
      import_positions
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    HrData =
      Struct.new(:department_code, :department_name, :user_id,
                 :user_code, :user_name, :email, :cellphone,
                 :position, :status_string, :manager_id, :job_code, :company_code, :login_name) do

        def inservice
          # 0：试用
          # 1：正式
          # 2：临时
          # 3：试用延期
          # 4：解聘
          # 5：离职
          # 6：退休
          # 7：无效
          [0, 1, 2, 3].include? status_string.to_i
        end

        # 只能判断user_name,离职员工会清掉loginname，也就是user_code
        def present?
          user_code.present? && user_name.present?
        end
      end

    def import_hr_sql
      <<-SQL
        SELECT
          d.id,
          d.departmentname,
          u.id,
          u.loginid,
          u.lastname,
          j.jobtitlename,
          u.status,
          u.managerid,
          j.id,
          u.subcompanyid1
        FROM #{@table_users} u
        left join  #{@table_departments} d on u.departmentid = d.id
        left join  #{@table_jobs} j on u.jobtitle = j.id
      SQL
    end

    def import_hr
      @data = []
      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(import_hr_sql) do |row|
        @data << create_user_struct(row)
      end
      # 测试服务器的员工会出现重复现象
      @data = @data.uniq { |x| x.user_id }
    end

    def create_user_struct(row)
      department_code, department_name, obj_id, login_name, name, position, status_string, manager_code, job_code, company_code = row

      user                 = HrData.new
      user.department_code = department_code.blank? ? nil : "department_#{department_code&.to_i&.to_s}"
      user.department_name = department_name
      user.user_id         = obj_id.to_s
      user.user_code       = obj_id.to_s
      user.user_name       = name
      user.email           = get_email(login_name)
      user.position        = position
      user.job_code        = job_code
      user.status_string   = status_string
      user.manager_id      = manager_code&.to_s if manager_code && manager_code != 0
      user.company_code    = company_code.blank? ? nil : "company_#{company_code&.to_i&.to_s}"
      user.login_name      = login_name
      # 最后返回结构体
      user
    end

    # id为21是顶层，不获取
    # company code 记录以company开头
    def import_company_sql
      "select id, subcompanyname, supsubcomid from #{@table_companies}"
    end

    def create_or_update_companies
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code).select { |code| code.start_with?('company_') }.uniq
      new_codes       = []
      company_data    = []

      @database.exec(import_company_sql) do |row|
        company_code = "company_#{row[0]&.to_i&.to_s}"
        parent_company_code = row[2].blank? ? nil : "company_#{row[2]&.to_i&.to_s}"
        new_codes << company_code
        company_data << [company_code, row[1], parent_company_code, row[0]&.to_i]
      end

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          row = company_data.find { |x| x[0] == code }
          next if @ignore_company_ids.include?(row[3])

          Department.create(
            code:      row[0],
            name:      row[1],
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动，注意更新状态为存在
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          row = company_data.find { |x| x[0] == code }

          department.update(name: row[1], inservice: true)
        end
      end

      # 更新所有部门的parent_id
      Department.all.each do |d|
        # 获取当前部门的row记录
        row = company_data.find { |r| r[0] == d.code }
        # 如果当前row记录的parent_code不会空
        next unless !row.nil? && !row[2].to_s.empty?

        p = Department.find_by(code: row[2])
        d.update_column(:parent_id, p.id) if p
      end

      # # 更新层级
      # root_departments = Department.where(parent_id: nil)
      # root_departments.update_all(level: 1)
      # root_departments.each_with_index do |department|
      #   update_departments_level(department.children, 2)
      # end
    end

    def import_department_sql
      "select id, departmentname, supdepid, subcompanyid1 from #{@table_departments}"
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code).select { |code| code.start_with?('department_') }.uniq
      new_codes       = []
      department_data = []
      # new_codes       = @data.map(&:department_code).uniq

      @database.exec(import_department_sql) do |row|
        department_code = "department_#{row[0]&.to_i&.to_s}"
        # 如果上级部门为空或者0，则全部算作空
        parent_department_code = row[2].blank? || row[2]&.to_i == 0 ? nil : "department_#{row[2]&.to_i&.to_s}"
        parent_company_code = row[3].blank? ? nil : "company_#{row[3]&.to_i&.to_s}"
        new_codes << department_code
        department_data << [department_code, row[1], parent_department_code, parent_company_code]
      end

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          row = department_data.find { |x| x[0] == code }
          Department.create(
            code:      row[0],
            name:      row[1],
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动，注意更新状态为存在
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          row        = department_data.find { |x| x[0] == code }
          department.update(name: row[1], inservice: true)
        end
      end

      # 更新所有部门的parent_id
      # rows = []
      # ActiveRecord::Base.transaction do
      #   @database.exec(import_department_sql) do |row|
      #     rows << [row[0]&.to_i&.to_s, row[1], row[2]&.to_i&.to_s]
      #   end
      # end
      Department.all.each do |d|
        # 获取当前部门的row记录
        row = department_data.find { |r| r[0] == d.code }
        # 如果当前row记录的parent_code不会空

        next unless !row.nil? && (!row[2].to_s.empty? || !row[3].to_s.empty?)

        parent_code = row[2].blank? ? row[3] : row[2]
        p = Department.find_by(code: parent_code)
        next if p.nil?

        d.update_column(:parent_id, p.id)
      end

      # 更新层级
      root_departments = Department.where(parent_id: nil)
      root_departments.update_all(level: 1)
      root_departments.each_with_index do |department|
        update_departments_level(department.children, 2)
      end

      # 把所有名称中带有停用的全部置为false
      Department.where('name like ?', '%停用%').update_all(inservice: false)
    end

    def update_departments_level(departments, level)
      departments.update_all(level: level)
      level += 1
      departments.each do |department|
        children_departments = department.children
        next if children_departments.empty?

        update_departments_level(children_departments, level)
      end
    end

    def create_or_update_users
      @departments = Department.all.reload.to_a
      old_users   = User.all.to_a
      old_codes   = old_users.map(&:code)
      new_codes   = @data.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          user_struct = @data.find { |x| x.user_code == code }
          department  = @departments.find { |x| x.code == user_struct.department_code }
          department  = @departments.find { |x| x.code == user_struct.company_code } if department.nil?

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            position:      user_struct.position,
            email:         user_struct.email,
            inservice:     user_struct.inservice,
            department_id: department&.id,
            source_id:     user_struct.user_id,
            login_name:    user_struct.login_name
          )
          @position_users << user if user.position?
        end
      end

      # 已有的更新
      # 这里通过source_id来获取员工，因为员工离职后user_code会删掉
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @data.find { |x| x.user_id.to_s == user.source_id.to_s }
          next unless user_struct&.present?

          department = @departments.find { |x| x.code == user_struct.department_code }
          department  = @departments.find { |x| x.code == user_struct.company_code } if department.nil?

          options = {
            name:          user_struct.user_name,
            position:      user_struct.position,
            email:         user_struct.email,
            inservice:     user_struct.inservice,
            department_id: department&.id
          }
          options[:login_name] = user_struct.login_name if user_struct.login_name.present?
          user.update(options)
          @position_users << user if user.position?
        end
      end

      @users = User.all
    end

    def link_manager
      all_users = User.all.reload.to_a

      ActiveRecord::Base.transaction do
        @data.each do |struct|
          next unless struct.manager_id

          user = all_users.find { |x| x.source_id.to_s == struct.user_id.to_s }
          next unless user

          manager_s = @data.find { |x| x.user_id == struct.manager_id }
          next unless manager_s

          manager = all_users.find { |x| x.source_id.to_s == manager_s.user_id.to_s }
          user.update(manager_id: manager&.id)
        end
      end
    end

    def update_user_name_clear
      return if @clear_name_regexp.blank?

      regexp    = Regexp.new(@clear_name_regexp)
      all_users = User.all.reload.to_a
      ActiveRecord::Base.transaction do
        all_users.each do |user|
          clear_name = user.name.gsub(regexp, '')
          user.update(name: clear_name)
        end
      end
    end

    def import_jobs_sql
      <<-EOF
        SELECT id, jobtitlename, jobdepartmentid FROM #{@table_jobs}
      EOF
    end

    # 导入岗位
    def import_jobs
      @job_departments = get_job_departments
      @before_job_codes = Job.all.pluck(:code)
      rows = []
      @database.exec(import_jobs_sql) do |r|
        rows << r
      end
      create_jobs(rows)
      delete_job_codes = @before_job_codes - @jobs.pluck(:code)
      # 找出被删除的岗位
      Job.where(code: delete_job_codes).update_all(inservice: false)
    end

    # job_departments为岗位部门信息，格式为json
    def create_jobs(rows)
      rows.each { |row| find_or_create_job(row) }
    end

    def find_or_create_job(r)
      code = r[0]&.to_s
      name = r[1]
      name = '行政管理' if name == 'Default' # Default要改为行政管理
      departments = @job_departments[code] || []
      return nil if name.nil? || (name == '') || code.nil? || (code == '')

      departments.each do |department|
        job_code = "#{department.code}-#{code}"
        job = Job.find_or_create_by(code: job_code)
        # department_job_name = "#{department.name}-#{name}"
        job.update(name: name, inservice: true, department_id: department&.id)
        @jobs << job
      end
    end

    # 获取岗位和部门关系
    # 这里要获取员工的岗位和部门信息，因为需要把每个岗位设置为部门->岗位
    def get_job_departments
      job_departments = {}
      job_department_data = []
      @database.exec(import_job_user_sql) do |r|
        job_code = r[0].to_s
        department_code = "department_#{r[2]&.to_i&.to_s}"
        company_code = "company_#{r[3]&.to_i&.to_s}"
        job_department_data << [job_code, department_code, company_code]
      end
      job_codes = job_department_data.map { |x| x[0] }.uniq
      job_codes.each do |job_code|
        departments = []
        # 获取岗位下的部门信息
        department_data = job_department_data.select { |x| x[0] == job_code }
        department_data.each do |row|
          department  = @departments.find { |x| x.code == row[1] }
          department  = @departments.find { |x| x.code == row[2] } if department.nil?
          next if department.nil?

          departments << department
        end
        job_departments[job_code] = departments.uniq
      end
      job_departments
    end

    def import_job_user_sql
      <<-SQL
        SELECT
          j.id,
          u.id,
          u.departmentid,
          u.subcompanyid1
        FROM
          #{@table_users} u,
          #{@table_jobs} j
        WHERE
          u.jobtitle = j.id
        AND
          u.id is not null
      SQL
    end

    def import_job_users
      data = []
      # 当前导入员工岗位信息
      @database.exec(import_job_user_sql) do |r|
        department_code = "department_#{r[2]&.to_i&.to_s}"
        company_code = "company_#{r[3]&.to_i&.to_s}"
        data << [r[0], r[1], department_code, company_code]
      end
      # 用于判断是否存在，一次性搜索出全部，避免n+1
      # 数据库员工岗位信息
      job_user_source_ids = JobUser.includes(:job, :user).map { |o| [o.job.code, o.user.source_id] }
      import_job_user_lines(data, job_user_source_ids)
    end

    def import_job_user_lines(data, job_user_source_ids)
      # 获取当前导入员工编码
      user_source_ids = data.map { |x| x[1] }.uniq.compact
      user_source_ids.each do |user_source_id|
        job_codes = []
        # 获取员工相关岗位信息
        user_job_data = data.select { |o| o[1].to_s == user_source_id.to_s }
        user_job_data.each do |row|
          department  = @departments.find { |x| x.code == row[2] }
          department  = @departments.find { |x| x.code == row[3] } if department.nil?
          next if department.nil?

          job_codes << "#{department.code}-#{row[0]}"
        end
        # 获取当前导入员工的岗位编码
        # job_codes = data.select { |o| o[1] == user_code }.map(&:first)
        set_job_user_line(user_source_id, job_codes, job_user_source_ids) if user_source_id.present? && job_codes.present?
      end
    end

    def set_job_user_line(user_source_id, job_codes, job_user_source_ids)
      user = @users.find { |x| x.source_id.to_s == user_source_id.to_s }
      return unless user

      before_job_user_source_ids = job_user_source_ids.select { |o| o.last.to_s == user_source_id.to_s }
      before_job_codes      = before_job_user_source_ids.map(&:first)
      add_job_codes         = job_codes - before_job_codes
      delete_job_codes      = before_job_codes - job_codes

      # 如果用户岗位已经记录，则跳过
      return if job_codes == before_job_codes

      params = {
        user_id:          user.id,
        quarter_id:       @quarter_id,
        log_time:         Time.now,
        log_type:         'message',
        operation:        'change',
        operation_target: '岗位'
      }
      delete_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code.to_s }
        next unless job

        JobUser.find_by(user_id: user.id, job_id: job.id)&.delete
        # 去掉岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位去掉「#{job.name}」"))
      end
      add_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code.to_s }
        next unless job

        JobUser.create(user_id: user.id, job_id: job.id)
        # 新增岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位新增「#{job.name}」"))
      end
    end

    def import_positions
      user_ids = JobUser.pluck(:user_id).uniq
      users = User.where(id: user_ids)
      job_users = JobUser.includes(:job, :user)
      users.each do |user|
        jobs = job_users.select { |job_user| job_user.user_id == user.id }.map(&:job)
        position = jobs.empty? ? nil : jobs.pluck(:name).join('、')
        user.update_column(:position, position) if user.position != position
      end
    end

    protected

    def get_email(code)
      return nil if code.blank?

      "#{code.to_s.strip}@sczq.com.cn"
    end
  end
end