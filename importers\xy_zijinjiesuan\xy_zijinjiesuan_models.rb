# frozen_string_literal: true

module <PERSON>y<PERSON><PERSON>njies<PERSON>
  def self.table_name_prefix
    'xy_zijinjiesuan_'
  end
end

class XyZijinjiesuan::Account < ActiveRecord::Base; end
class XyZijinjiesuan::Menu    < ActiveRecord::Base; end
class XyZijinjiesuan::Role    < ActiveRecord::Base; end
class XyZijinjiesuan::Department < ActiveRecord::Base; end
class XyZijinjiesuan::AccountsRoles < ActiveRecord::Base; end
class XyZijinjiesuan::MenusRoles    < ActiveRecord::Base; end
class XyZijinjiesuan::AccountsMenus < ActiveRecord::Base; end