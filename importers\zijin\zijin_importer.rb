module AasOracleImporter
  class ZijinImporter < ImporterBase
    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_agency         = importer_config['table_agency']
      @table_roles          = importer_config['table_roles']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus          = importer_config['table_menus']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_ledgers(Zijin::Account)
    end

    def import_accounts
      Zijin::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT
          a.loginname,
          a.name,
          b.code,
          b.name,
          a.email,
          a.flag
        FROM
          #{@table_accounts} a,
          #{@table_agency} b
        WHERE
          a.orgid = b.id
        ORDER BY
          a.LOGINNAME
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] && r[1]
            status = r[5].to_i == 1
            Zijin::Account.create(
              quarter_id:   @quarter_id,
              code:         r[0],
              name:         r[1].strip,
              email:        r[4]&.strip,
              company_code: r[2]&.strip,
              company_name: r[3]&.strip,
              status:       status
            )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles
      Zijin::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT code, name FROM #{@table_roles} WHERE flag = 1 ORDER BY code
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Zijin::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menus
      Zijin::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT
          a.loginname,
          e.code,
          d.id,
          d.name
        FROM
          #{@table_accounts} a,
          #{@table_accounts_roles} c,
          #{@table_menus} d,
          #{@table_roles} e
        WHERE
          a.flag = 0
          AND a.status = 1
          AND a.id = c.userid
          AND c.agency_duty_id = d.id
          AND d.dutyid = e.id
        ORDER BY
          a.loginname
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = Zijin::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Zijin::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            Zijin::Menu.create(
              quarter_id: @quarter_id,
              account_id: account.id,
              role_id:    role.id,
              code:       r[2],
              name:       r[3]
            )
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}"    unless role
          end
        end
      end
    end
  end
end
