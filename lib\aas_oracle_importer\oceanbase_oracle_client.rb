# frozen_string_literal: true
module AasOracleImporter
  # Ocenbase Oracle模式 客户端
  class OceanbaseOracleClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
      @drb_url = database_info['drb_url']
      @db_host = oceanbase_client_params[:db_host]
      @db_port = oceanbase_client_params[:db_port]
      @db_name = oceanbase_client_params[:db_name]
      @db_user = oceanbase_client_params[:db_user]
      @db_pass = oceanbase_client_params[:db_pass]
    end

    # 返回数组
    def exec(sql)
      rows = query(sql).map { |x| x.values }
      if block_given?
        rows.each do |row|
          yield row
        end
      else
        rows
      end
    end

    # 返回Hash
    def query(sql)
      server = DRbObject.new_with_uri(@drb_url)
      server.execute_query(@db_host, @db_port, @db_name, @db_user, @db_pass, sql)
    end

    private

    def initialize_driver
      load_driver_gem
    rescue 'OceanbaseOracleAdapter::Error' => e
      raise 'OceanbaseOracleAdapter::Error', message_prefix + e.message
    end

    def oceanbase_client_params
      oceanbase_client_config.update({db_pass: ConvertTools::Cryptology.decrypt_if_env(oceanbase_client_config[:db_pass])})
    end

    def oceanbase_client_config
      {
        db_host: database_info['db_host'],
        db_port: database_info['db_port'] || 2883,
        db_name: database_info['db_name'],
        db_user: database_info['db_user'],
        db_pass: database_info['db_pass']
      }
    end

    def load_driver_gem
      require 'drb'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'drb\'.')
      exit(-127)
    end
  end
end
