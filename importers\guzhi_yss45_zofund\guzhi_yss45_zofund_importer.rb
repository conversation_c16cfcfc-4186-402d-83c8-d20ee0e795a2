module AasOracleImporter

  class GuzhiYss45ZofundImporter < ImporterBase

    def config
      @bs_id           = importer_config['bs_id']
      @table_space     = importer_config['table_space']
      @sid_suffix      = importer_config['sid_suffix']
      @sync_booksets_table  = importer_config['sync_booksets_table']
      @disabled_sys_account = importer_config['disabled_sys_account']
      @post_table_name = importer_config['post_table_name'] || 't_s_user_post_data'
      super
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_roles
      import_booksets
      import_booksets_relation

      import_ledgers(Guzhi45::Account)
    end

    def destroy_exist_data
      Guzhi45::Account.where(quarter_id: @quarter_id).destroy_all
      Guzhi45::Bookset.where(quarter_id: @quarter_id).destroy_all
      Guzhi45::Role.where(quarter_id: @quarter_id).destroy_all
      Guzhi45::BooksetRelation.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select C_USER_CODE, C_USER_NAME, C_DV_STATE from #{@table_space}t_s_user#{@sid_suffix} t
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          next if @disabled_sys_account && r[1].to_s == "SYS"
          if r[0] && r[1]
            Guzhi45::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_s == "ENAB"
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select C_POST_CODE, C_POST_NAME from #{@table_space}t_s_post#{@sid_suffix} t
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          if r[0] && r[1]
            Guzhi45::Role.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1]
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_booksets_sql
      if @sync_booksets_table
        <<-SQL
          select C_PORT_CODE, C_PORT_NAME
          from #{@table_space}t_p_ab_port#{@sid_suffix}
        SQL
      else
        <<-SQL
          select C_DATA_CODE, C_DATA_NAME
          from #{@table_space}#{@post_table_name}#{@sid_suffix}
          where c_data_type = '1'
        SQL
      end
    end

    def import_booksets
      ActiveRecord::Base.transaction do
        @database.exec(import_booksets_sql) do |r|
          bookset = Guzhi45::Bookset.find_or_create_by(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_booksets_relation_sql
      <<-SQL
        select C_USER_CODE, C_DATA_CODE, C_DATA_NAME, C_POST_CODE
        from #{@table_space}#{@post_table_name}#{@sid_suffix}
        where c_data_type = '1'
      SQL
    end

    def import_booksets_relation
      ActiveRecord::Base.transaction do
        @database.exec(import_booksets_relation_sql) do |r|
          if r[0] && r[1]
            bookset = Guzhi45::Bookset.find_by(
              quarter_id: @quarter_id,
              code:       r[1]
            )

            # unless bookset
            #   bookset = Guzhi45::Bookset.create(
            #     quarter_id: @quarter_id,
            #     code:       r[1],
            #     name:       r[2]
            #   )
            # end
            account = Guzhi45::Account.find_by(quarter_id: @quarter_id, code: r[0])
            role    = Guzhi45::Role.find_by(quarter_id: @quarter_id, code: r[3])
            if account && role && bookset
              cbr               = Guzhi45::BooksetRelation.new
              cbr.quarter_id    = @quarter_id
              cbr.account = account
              cbr.role    = role
              cbr.bookset = bookset
              cbr.save
            end
          end
        end
      end
    end

  end
end



