module Guzhi25
  def self.table_name_prefix
    'guzhi25_'
  end
end

class Guzhi25::Account < ActiveRecord::Base
  has_many :bookset_relations
  has_many :roles,    -> { distinct }, through: :bookset_relations
  has_many :booksets, -> { distinct }, through: :bookset_relations
end

class Guzhi25::Bookset < ActiveRecord::Base; end

class Guzhi25::Subsystem < ActiveRecord::Base; end

class Guzhi25::BooksetRelation < ActiveRecord::Base
  belongs_to :account
  belongs_to :role
  belongs_to :bookset
  belongs_to :subsystem
end

class Guzhi25::Role < ActiveRecord::Base; end
