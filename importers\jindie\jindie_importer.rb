module AasOracleImporter

  class JindieImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix = importer_config['sid_suffix'] #''
    end

    def import_to_do
      import_accounts_and_roles
      import_account_role
      import_permissions
      import_account_role_permission
      import_ledgers(Jindie::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def import_accounts_and_roles
      ActiveRecord::Base.transaction do
        sql = "select FUserID,FName,FForbidden from t_user"
        @database.query(sql).each do |r|
          if r['FUserID'].to_i > 10000
            Jindie::Account.create(quarter_id: @quarter_id, code:r['FUserID'], name: r['FName'], status: !r['FForbidden'])
          else
            Jindie::Role.create(quarter_id: @quarter_id, code:r['FUserID'], name: r['FName'])
          end
        end
      end
    end

    def import_account_role
      ActiveRecord::Base.transaction do
        sql = "select * from t_group"
        @database.query(sql).each do |r|
          account = Jindie::Account.find_by(quarter_id: @quarter_id, code:r['FUserID'])
          role = Jindie::Role.find_by(quarter_id: @quarter_id, code:r['FGroupID'])
          if account && role
            account.roles << role
          end
        end
      end
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        sql = "select * from t_groupaccesstype"
        @database.query(sql).each do |r|
          Jindie::Permission.create(quarter_id: @quarter_id, code:r['FGroupID'], name: "#{r['FSubSys']}-#{r['FAccess']}", permission_type: "系统权限")
        end
      end
    end

    def import_account_role_permission
      ActiveRecord::Base.transaction do
        sql = "select * from t_groupaccess"
        @database.query(sql).each do |r|
          permission = Jindie::Permission.find_by(quarter_id: @quarter_id, code:r['FGroupID'])
          if permission
            account = Jindie::Account.find_by(quarter_id: @quarter_id, code:r['FUserID'])
            role = Jindie::Role.find_by(quarter_id: @quarter_id, code:r['FUserID'])
            if r['FUserID'].to_i > 10000 && account
              Jindie::AccountsRolesPermission.find_or_create_by(
                quarter_id: @quarter_id,
                permission_id: permission.id,
                account_id: account.id
              )
            elsif role
              Jindie::AccountsRolesPermission.find_or_create_by(
                quarter_id: @quarter_id,
                permission_id: permission.id,
                role_id: role.id
              )
            end
          end
        end
      end
    end

  end
end



