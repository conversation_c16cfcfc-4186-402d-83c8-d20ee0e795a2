# frozen_string_literal: true

module AasOracleImporter
  # 直销中心系统导入
  class ZhixiaoZhongxinImporter < ZhixiaoV11Importer
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @sub_system  = importer_config['sub_system']

      initialize_tables
      initialize_classes
    end

    def initialize_classes
      @account_class          = ZhixiaoZhongxin::Account
      @role_class             = ZhixiaoZhongxin::Role
      @menu_class             = ZhixiaoZhongxin::Menu
      @other_permission_class = ZhixiaoZhongxin::OtherPermission

      @account_role_associate_class   = ZhixiaoZhongxinAccountsRoles
      @account_menu_associate_class   = ZhixiaoZhongxinAccountsMenus
      @role_menu_associate_class      = ZhixiaoZhongxinMenusRoles
      @role_other_permission_class    = ZhixiaoZhongxinOtherPermissionsRoles
      @account_other_permission_class = ZhixiaoZhongxinAccountsOtherPermissions
    end
  end
end



