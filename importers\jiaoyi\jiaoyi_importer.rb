module AasOracleImporter
  class JiaoyiImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @temporary   = importer_config['temporary']
      @display_status = importer_config['display_status']
      @old_fund_temp_check = importer_config['old_fund_temp_check']
      @c_menu_type = importer_config['c_menu_type']
      @history_temp = importer_config['history_temp']
      @disable_status = importer_config['disable_status']
      @accounts = []
      @menus    = []
      @roles    = []
      @menu_additions = []
      @table_log = @table_space + 'thissystemlog' + @sid_suffix
      @table_password_security = @table_space + 'tsysparameter' + @sid_suffix
      @table_station = "#{@table_space}topstation#{@sid_suffix}"
      @table_accounts = "#{@table_space}toperator#{@sid_suffix}"
      @table_tworkstations = "#{@table_space}tworkstation#{@sid_suffix}"
      @table_log = "#{@table_space}thissystemlog#{@sid_suffix}"
      @table_password_security = "#{@table_space}tsysparameter#{@sid_suffix}"
      @accounts = []
      @menus    = []
      @roles    = []
      @menu_additions = []
      @last_quarter_id = Quarter.pluck(:id)[-2]
      super
    end

    def import_to_do
      destroy_old_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_sub_systems
      import_menus
      import_menu_additions
      import_accounts_menus
      import_menus_roles
      import_funds
      import_accounts_funds

      # 导入临时权限
      import_temp_menu_rights
      import_temp_fund_rights
      import_temp_fund_unit_rights
      import_temp_fund_combi_rights

      import_time_controls if importer_config['time_control']
      cpic_custom_importer if importer_config['cpic_custom_importer']
      trade_type_importer if importer_config['trade_type_importer']
      station_importer if importer_config['station_importer']
      station_user_importer if importer_config['station_user_importer']
      import_temp_all_caches
      import_password_securities
      import_logs
      import_last_login_at_data
      import_ledgers(Jiaoyi::Account)
    end

    def destroy_old_datas
      Jiaoyi::Account.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::Role.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::Menu.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::System.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::MenuPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::MenuAdditionPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::Fund.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::FundUnit.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::FundCombination.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::FundPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::FundUnitPermission.where(quarter_id: @quarter_id).destroy_all
      Jiaoyi::FundCombinationPermission.where(quarter_id: @quarter_id).destroy_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all if @display_status
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_station_sql
      <<~SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,VC_STATION_NO
        from
          #{@table_station}
      SQL
    end

    def station_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_station_sql) do |r|
          Jiaoyi::TopStation.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            station_code: r[2]
          )
        end
      end
    end

    def station_user_importer_sql
      <<-SQL
        select distinct
          c.vc_operator_name 操作员名称,
          a.l_operator_no    操作员代码,
          b.vc_station_name  计算机名称,
          b.vc_mac           MAC地址,
          b.l_login_date     登录日期
        from
          #{@table_space}topstation#{@sid_suffix} a,
          #{@table_space}tworkstation#{@sid_suffix} b,
          #{@table_space}toperator#{@sid_suffix} c
        where a.vc_station_no = b.vc_station_no
          and a.l_operator_no = c.l_operator_no
          and c.c_operator_status = 1
        order by a.l_operator_no
      SQL
    end

    def station_user_importer
      ActiveRecord::Base.transaction do
        @database.exec(station_user_importer_sql) do |r|
          Jiaoyi::TopStation.create(
            quarter_id:   @quarter_id,
            account_code: r[1],
            station_code: r[3],
            name:         r[2]
          )
        end
      end
    end

    def import_trade_type_sql
      <<-SQL
        select
          L_SERIAL_ID,VC_TRADE_NO,VC_TRADE_NAME,L_PARENT_ID
        from
          #{@table_space}ttradetypes#{@sid_suffix}
      SQL
    end

    def trade_type_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_trade_type_sql) do |r|
          Jiaoyi::TradeType.create(
            quarter_id:  @quarter_id,
            l_serial_id: r[0],
            code:        r[1],
            name:        r[2],
            parent_id:   r[3]
          )
        end

        @database.exec(import_top_trade_type_sql) do |r|
          Jiaoyi::TopTradeType.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            l_serial_id:  r[2],
            vc_right:     r[3],
            c_status:     r[4]
          )
        end
      end
    end

    def import_top_trade_type_sql
      <<-SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,L_SERIAL_ID,VC_RIGHT,C_STATUS
        from
          #{@table_space}toptradetype#{@sid_suffix}
      SQL
    end

    def import_accounts_sql
      vc_operator_no = importer_config['operator_no_importer'] ? ', vc_operator_no' : ''
      vc_domain_name = importer_config['vc_domain_name'] ? ', vc_domain_name' : ''

      <<-SQL
        select   l_operator_no,
                vc_operator_name,
                 c_operator_status,
                 l_register_date,
                 l_cancel_date #{vc_operator_no} #{vc_domain_name}
        from #{@table_accounts}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Jiaoyi::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     @disable_status ? !@disable_status.include?(r[2].to_i) : r[2].to_i == 1
            )
            @accounts << account
            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       'Jiaoyi::Account',
                account_code:       account.code,
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[2]&.to_s
              )
            end

            if importer_config['register_date']
              account.register_date = r[3]
              account.cancel_date   = r[4]
              account.save
            end

            if importer_config['operator_no_importer']
              account.operator_no = r[5]
              account.save
            end

            if importer_config['vc_domain_name']
              if importer_config['operator_no_importer']
                account.objid = r[6].to_s.match(%r{(?<=/).*}).to_s
              else
                account.objid = r[5].to_s.match(%r{(?<=/).*}).to_s
              end
              account.save
            end
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_role_id, vc_role_name, vc_remarks
        from #{@table_space}trole#{@sid_suffix}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          role = Jiaoyi::Role.create(
            quarter_id:  @quarter_id,
            code:        r[0],
            name:        r[1],
            description: r[2]
          )
          @roles << role
        end
      end
    end

    def menus_sql_no_type
      <<-SQL
        select
          vc_menu_no, vc_menu_name, c_subsystem_no
        from
          #{@table_space}tmenuitem#{@sid_suffix}
        where
          vc_menu_name <> '-'
      SQL
    end

    def import_menus_sql
      menus_sql_with_type = menus_sql_no_type
      unless @c_menu_type.blank?
        menus_sql_with_type = menus_sql_no_type + " AND c_menu_type in (#{@c_menu_type.join(',')})"
      end
      menus_sql_with_type
    end

    def import_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          sub_system = Jiaoyi::System.find_by(quarter_id: @quarter_id, code: r[2])
          next unless sub_system

          menu = Jiaoyi::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1],
            system_id:  sub_system.id
          )
          @menus << menu
        end
      end
    end

    def import_sub_systems_sql
      <<-SQL
        select
          c_lemma_item, vc_item_name
        from
          #{@table_space}tdictionary#{@sid_suffix}
        where
          l_dictionary_no = '10002'
        and
          c_lemma_item <> '!'
      SQL
    end

    def import_sub_systems
      ActiveRecord::Base.transaction do
        @database.exec(import_sub_systems_sql) do |r|
          Jiaoyi::System.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menu_additions_sql
      <<-SQL
        select
          vc_menu_no,
          c_rights_id,
          vc_remarks
        from
          #{@table_space}tmenurights#{@sid_suffix}
      SQL
    end

    def import_menu_additions
      ActiveRecord::Base.transaction do
        @database.exec(import_menu_additions_sql) do |r|
          menu = Jiaoyi::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          menu_addition = Jiaoyi::MenuAddition.create(
            quarter_id: @quarter_id,
            sub_code:   r[1],
            name:       r[2].to_s,
            menu_id:    menu.id
          )
          @menu_additions << menu_addition
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          l_role_id,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_operator_no = -1
      SQL
    end

    def import_menus_roles
      data = []
      @database.exec(import_menus_roles_sql) do |r|
        data << r
      end
      menu_role_data = []
      menu_addition_data = []
      data.each do |x|
        @logger.info "#{self.class}.#{__method__} #{x}"
        role = @roles.find { |r| r.code.to_s == x[0].to_s }
        menu = @menus.find { |m| m.code.to_s == x[1].to_s }
        if menu && role
          menu_role_data << { menu_id: menu.id, role_id: role.id }
          next unless x[2]

          additional_permission_codes = x[2].chars
          @logger.info "#{self.class}.#{__method__} additional_permission_codes: #{additional_permission_codes}"
          aps = @menu_additions.select { |menu_addition| menu_addition.menu_id == menu.id && additional_permission_codes.include?(menu_addition.sub_code) }
          aps.each do |ap|
            menu_addition_data << { menu_addition_id: ap.id, role_id: role.id }
          end
        else
          @logger.warn "#{self.class}.#{__method__}: not found role #{x[0]}" unless role
          @logger.warn "#{self.class}.#{__method__}: not found menu #{x[1]}" unless menu
        end
      end
      JiaoyiMenusRoles.bulk_insert(:menu_id, :role_id) do |obj|
        obj.set_size = 1000
        menu_role_data.each do |x|
          obj.add [x[:menu_id], x[:role_id]]
        end
      end

      JiaoyiMenuAdditionsRoles.bulk_insert(:menu_addition_id, :role_id) do |obj|
        obj.set_size = 1000
        menu_addition_data.each do |x|
          obj.add [x[:menu_addition_id], x[:role_id]]
        end
      end
    end

    def import_accounts_menus_sql
      <<-SQL
        select
          l_operator_no,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_role_id = -1
      SQL
    end

    def import_accounts_menus
      data = []
      menu_permission_data = []
      menu_addition_data = []

      @database.exec(import_accounts_menus_sql) do |r|
        data << r
      end

      @accounts = Jiaoyi::Account.where(quarter_id: @quarter_id)
      @menus    = Jiaoyi::Menu.where(quarter_id: @quarter_id)
      @menu_additions = Jiaoyi::MenuAddition.where(quarter_id: @quarter_id)

      data.each do |x|
        account = @accounts.find { |a| x[0].to_s == a.code.to_s }
        menu    = @menus.find { |m| x[1].to_s == m.code.to_s }

        if menu && account
          @logger.info "#{self.class}.#{__method__} #{x}"
          menu_permission_data << { menu_id: menu.id, account_id: account.id }
          next unless x[2]

          additional_permission_codes = x[2].chars
          @logger.info "#{self.class}.#{__method__} additional_permission_codes: #{additional_permission_codes}"
          aps = @menu_additions.select { |menu_addition| menu_addition.menu_id == menu.id && additional_permission_codes.include?(menu_addition.sub_code) }
          aps.each do |ap|
            menu_addition_data << { menu_addition_id: ap.id, account_id: account.id }
          end
        else
          @logger.warn "#{self.class}.#{__method__}: not found account #{x[0]}" unless account
          @logger.warn "#{self.class}.#{__method__}: not found menu #{x[1]}" unless menu
        end
      end

      time = Time.now
      Jiaoyi::MenuPermission.bulk_insert(:quarter_id, :menu_id, :account_id, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        menu_permission_data.each do |x|
          obj.add [@quarter_id, x[:menu_id], x[:account_id], time, time]
        end
      end

      Jiaoyi::MenuAdditionPermission.bulk_insert(:quarter_id, :menu_addition_id, :account_id, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        menu_addition_data.each do |x|
          obj.add [@quarter_id, x[:menu_addition_id], x[:account_id], time, time]
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select l_operator_no, l_role_id
        from #{@table_space}toprolerights#{@sid_suffix}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Jiaoyi::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Jiaoyi::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            JiaoyiAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_funds_sql
      <<-SQL
        select l_fund_id, vc_fund_code, vc_fund_name, vc_fund_caption, c_fund_status
        from #{@table_space}tfundinfo#{@sid_suffix}
      SQL
    end

    def import_fund_asset_sql
      <<-SQL
        select l_fund_id, l_asset_id, vc_asset_name
        from #{@table_space}tasset#{@sid_suffix}
      SQL
    end

    def import_fund_combi_sql
      <<-SQL
        select l_asset_id, l_combi_id, vc_combi_name
        from #{@table_space}tcombi#{@sid_suffix}
      SQL
    end

    def import_funds
      @logger.info "#{self.class}.#{__method__}"
      time = Time.now

      ActiveRecord::Base.transaction do
        fund_data = []
        @database.exec(import_funds_sql) do |r|
          # 长盛客户code是浮点型，例如123.0
          code = r[0].to_s.sub('.0', '')
          fund_data << { code: code, fund_code: r[1], name: r[2], fund_caption: r[3], inservice: r[4].to_i == 2 }
        end

        Jiaoyi::Fund.bulk_insert(:quarter_id, :code, :fund_code, :name, :fund_caption, :inservice, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_data.each do |x|
            obj.add [@quarter_id, x[:code], x[:fund_code], x[:name], x[:fund_caption], x[:inservice], time, time]
          end
        end
      end
      funds = Jiaoyi::Fund.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        fund_unit_data = []
        @database.exec(import_fund_asset_sql) do |r|
          fund = funds.find { |fund| fund.code.to_s == r[0].to_s }
          next unless fund

          fund_unit_data << { fund_id: fund.id, code: r[1], name: r[2] }
        end

        Jiaoyi::FundUnit.bulk_insert(:quarter_id, :fund_id, :code, :name, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_unit_data.each do |x|
            obj.add [@quarter_id, x[:fund_id], x[:code], x[:name], time, time]
          end
        end
      end

      fund_units = Jiaoyi::FundUnit.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        fund_combination_data = []
        @database.exec(import_fund_combi_sql) do |r|
          unit = fund_units.find { |fund_unit| fund_unit.code.to_s == r[0].to_s }
          next unless unit

          fund_combination_data << { fund_unit_id: unit.id, code: r[1], name: r[2] }
        end

        Jiaoyi::FundCombination.bulk_insert(:quarter_id, :fund_unit_id, :code, :name, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_combination_data.each do |x|
            obj.add [@quarter_id, x[:fund_unit_id], x[:code], x[:name], time, time]
          end
        end
      end
    end

    # MARK: 在投资交易中，要咨询客户是否有通过角色进行授权基金的情况，根据表结构，在 O32 系统中其是支持的。
    # 但是多数客户都是直接授权给人。
    def import_accounts_funds_sql
      <<-SQL
        select
          l_operator_no,
          l_fund_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_fund_id <> -1
        and l_asset_id = -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_uniq_sql
      <<-SQL
        select
          l_operator_no,
          l_asset_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_asset_id <> -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_combi_sql
      <<-SQL
        select
          l_operator_no,
          l_basecombi_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_basecombi_id <> -1
      SQL
    end

    def import_accounts_funds
      @logger.info "#{self.class}.#{__method__}"
      time = Time.now

      funds = Jiaoyi::Fund.where(quarter_id: @quarter_id)
      fund_units = Jiaoyi::FundUnit.where(quarter_id: @quarter_id)
      fund_combinations = Jiaoyi::FundCombination.where(quarter_id: @quarter_id)

      ActiveRecord::Base.transaction do
        fund_permission_data = []
        @database.exec(import_accounts_funds_sql) do |r|
          account = @accounts.find { |x| x.code.to_s == r[0].to_s }
          fund    = funds.find { |x| x.code.to_s == r[1].to_s }
          next unless account && fund

          fund_permission_data << { account_id: account.id, fund_id: fund.id, permission: r[2] }
        end

        Jiaoyi::FundPermission.bulk_insert(:quarter_id, :account_id, :fund_id, :permission, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_permission_data.each do |x|
            obj.add [@quarter_id, x[:account_id], x[:fund_id], x[:permission], time, time]
          end
        end
      end

      ActiveRecord::Base.transaction do
        fund_unit_permission_data = []
        @database.exec(import_accounts_uniq_sql) do |r|
          account = @accounts.find { |x| x.code.to_s == r[0].to_s }
          unit    = fund_units.find { |x| x.code.to_s == r[1].to_s }
          next unless account && unit

          fund_unit_permission_data << { account_id: account.id, fund_unit_id: unit.id, permission: r[2] }
        end

        Jiaoyi::FundUnitPermission.bulk_insert(:quarter_id, :account_id, :fund_unit_id, :permission, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_unit_permission_data.each do |x|
            obj.add [@quarter_id, x[:account_id], x[:fund_unit_id], x[:permission], time, time]
          end
        end
      end

      ActiveRecord::Base.transaction do
        fund_combination_data = []
        @database.exec(import_accounts_combi_sql) do |r|
          account = @accounts.find { |x| x.code.to_s == r[0].to_s }
          combi   = fund_combinations.find { |x| x.code.to_s == r[1].to_s }
          next unless account && combi

          fund_combination_data << { account_id: account.id, fund_combination_id: combi.id, permission: r[2] }
        end

        Jiaoyi::FundCombinationPermission.bulk_insert(:quarter_id, :account_id, :fund_combination_id, :permission, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          fund_combination_data.each do |x|
            obj.add [@quarter_id, x[:account_id], x[:fund_combination_id], x[:permission], time, time]
          end
        end
      end
    end

    def import_temp_menu_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '1'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_accounts} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
      SQL
    end

    def import_temp_menu_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_menu_rights_sql
        sql = sql + ' union ' + import_temp_menu_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, menu_code, addi_codes = r
          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)

          next if today > end_date

          account       = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          menu          = Jiaoyi::Menu.find_by(quarter_id: @quarter_id, code: menu_code)

          next unless account && menu

          perm = Jiaoyi::MenuPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    menu.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Jiaoyi::MenuPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              menu_id:    menu.id
            )
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            update_temp_right(perm, start_date, end_date, grant_account)
          end

          next unless addi_codes

          additional_permission_codes = addi_codes.chars
          aps                         =
            Jiaoyi::MenuAddition.where(
              quarter_id: @quarter_id,
              menu_id:    menu.id,
              sub_code:   additional_permission_codes
            )

          aps.each do |ap|
            # map: MenuAdditionPermission 的缩写
            map_record = Jiaoyi::MenuAdditionPermission.find_or_initialize_by(
              quarter_id:       @quarter_id,
              account_id:       account.id,
              menu_addition_id: ap.id
            )

            # 如果已经存在临时授权了，可能是授权重复
            if map_record.is_temp
              new_perm = Jiaoyi::MenuAdditionPermission.new(
                quarter_id:       @quarter_id,
                account_id:       account.id,
                menu_addition_id: ap.id
              )
              update_temp_right(new_perm, start_date, end_date, grant_account)
            else
              update_temp_right(map_record, start_date, end_date, grant_account)
            end
          end
        end
      end
    end

    def import_temp_fund_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_accounts} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id = -1
          and t.l_basecombi_id = -1
      SQL
    end

    def import_temp_fund_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_rights_sql
        sql = sql + ' union ' + import_temp_fund_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, fund_code, perm_codes, _asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          if @old_fund_temp_check
            old_account = Jiaoyi::Account.find_by(quarter_id: @last_quarter_id, code: account_code)
            old_fund = Jiaoyi::Fund.find_by(quarter_id: @last_quarter_id, code: fund_code)
            if old_account && old_fund
              old_perm = Jiaoyi::FundPermission.find_by(
                quarter_id: @last_quarter_id,
                account_id: old_account.id,
                fund_id:    old_fund.id
              )
              next if old_perm && !old_perm.is_temp # 如果昨天有永久权限，就不算做临时授权
            end
          end

          account       = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          fund          = Jiaoyi::Fund.find_by(quarter_id: @quarter_id, code: fund_code)

          next unless account && fund

          perm = Jiaoyi::FundPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id:    fund.id
          )

          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Jiaoyi::FundPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              fund_id:    fund.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_unit_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_accounts} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id = -1
      SQL
    end

    def import_temp_fund_unit_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_unit_rights_sql
        sql = sql + ' union ' + import_temp_fund_unit_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          unit          = Jiaoyi::FundUnit.find_by(quarter_id: @quarter_id, code: asset_code)

          next unless account && unit

          if @old_fund_temp_check
            old_account = Jiaoyi::Account.find_by(quarter_id: @last_quarter_id, code: account_code)
            old_unit = Jiaoyi::FundUnit.find_by(quarter_id: @last_quarter_id, code: asset_code)
            if old_account && old_unit
              old_perm = Jiaoyi::FundUnitPermission.find_by(
                quarter_id:   @last_quarter_id,
                account_id:   old_account.id,
                fund_unit_id: old_unit.id
              )
              next if old_perm && !old_perm.is_temp # 如果昨天有永久权限，就不算做临时授权
            end
          end

          perm = Jiaoyi::FundUnitPermission.find_or_initialize_by(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            fund_unit_id: unit.id
          )

          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Jiaoyi::FundUnitPermission.new(
              quarter_id:   @quarter_id,
              account_id:   account.id,
              fund_unit_id: unit.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_combi_rights_sql(table_name = 'toptempright')
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}#{table_name}#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_accounts} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id <> -1
      SQL
    end

    def import_temp_fund_combi_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        sql = import_temp_fund_combi_rights_sql
        sql = sql + ' union ' + import_temp_fund_combi_rights_sql('THISOPTEMPRIGHT') if @history_temp
        @database.exec(sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, _asset_code, combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Jiaoyi::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          combi         = Jiaoyi::FundCombination.find_by(quarter_id: @quarter_id, code: combi_code)

          next unless account && combi

          if @old_fund_temp_check
            old_account = Jiaoyi::Account.find_by(quarter_id: @last_quarter_id, code: account_code)
            old_combi = Jiaoyi::FundCombination.find_by(quarter_id: @last_quarter_id, code: combi_code)
            if old_account && old_combi
              old_perm = Jiaoyi::FundCombinationPermission.find_by(
                quarter_id:          @last_quarter_id,
                account_id:          old_account.id,
                fund_combination_id: old_combi.id
              )
              next if old_perm && !old_perm.is_temp # 如果昨天有永久权限，就不算做临时授权
            end
          end

          perm = Jiaoyi::FundCombinationPermission.find_or_initialize_by(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            fund_combination_id: combi.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Jiaoyi::FundCombinationPermission.new(
              quarter_id:          @quarter_id,
              account_id:          account.id,
              fund_combination_id: combi.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_all_caches
      return true unless @temporary

      @destroy_datas = get_destroy_datas if importer_config['temp_delete_record']

      days_of_data = (Time.now - importer_config['days_of_data'].to_i.days).strftime('%Y%m%d')

      history_temp_sql = ''

      if importer_config['history_temp']
        history_temp_sql = <<-SQL
          union
          select
            L_OPERATOR_NO,
            C_RIGHT_TYPE,
            C_RIGHT_OPERATOR,
            VC_RIGHT,
            L_ASSET_ID,
            L_BASECOMBI_ID,
            VC_RIGHTS_ID,
            L_BEGIN_DATE,
            L_END_DATE,
            L_GRANT_OPERATOR,
            L_INPUT_OPERATOR,
            L_GRANT_DATE,
            L_GRANT_TIME,
            C_STATUS,
            L_BEGIN_TIME,
            L_END_TIME
          from
            #{@table_space}THISOPTEMPRIGHT#{@sid_suffix}
          where
            L_BEGIN_DATE >= #{days_of_data}
        SQL
      end

      sql = <<-SQL
        select
          L_OPERATOR_NO,
          C_RIGHT_TYPE,
          C_RIGHT_OPERATOR,
          VC_RIGHT,
          L_ASSET_ID,
          L_BASECOMBI_ID,
          VC_RIGHTS_ID,
          L_BEGIN_DATE,
          L_END_DATE,
          L_GRANT_OPERATOR,
          L_INPUT_OPERATOR,
          L_GRANT_DATE,
          L_GRANT_TIME,
          C_STATUS#{importer_config['history_temp'] ? ', L_BEGIN_TIME, L_END_TIME' : ''}
        from
          #{@table_space}toptempright#{@sid_suffix}
        where
          L_BEGIN_DATE >= #{days_of_data}
        #{history_temp_sql}
      SQL

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 当授权时间不存在时，跳出后面操作
          next unless r[7] && r[8]

          g_time = r[12].to_s
          g_time = '0' + g_time while g_time.size < 6

          temporary = Jiaoyi::Temporary.find_or_create_by(
            account_code:            r[0],
            authorizer_account_code: r[9],
            operator_account_code:   r[10],
            operation_type:          r[2],
            permission_type:         r[1].to_i,
            temp_start_date:         Date.parse(r[7].to_s),
            temp_end_date:           Date.parse(r[8].to_s),
            authorize_time:          Time.parse(r[11].to_s + g_time),
            permission_code:         r[3],
            unit_code:               r[4],
            combination_code:        r[5],
            addition_code:           r[6]
          )

          temporary.l_begin_time = r[14]
          temporary.l_end_time   = r[15]

          if importer_config['temp_delete_record'] && r[13].to_i == 5
            destroy_data                    = find_destroy_data(temporary)
            temporary.destroy_time          = destroy_data[:L_TIME] if destroy_data
            temporary.last_authorize_status = temporary.authorize_status
          end

          temporary.update(
            authorize_status: r[13]
          )
        end
      end
    end

    # 导入日志sql语句
    # L_DATE, L_TIME 是number类型
    def customer_audit_sql
      start_at = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      start_date = start_at.split(' ').first.delete('-')
      start_time = start_at.split(' ').last.delete(':').to_i

      where_sql = "where (L_DATE = '#{start_date}' AND L_TIME > '#{start_time}') OR (L_DATE > '#{start_date}')"
      <<-EOF
        select L_SERIAL_NO, L_OPERATOR_NO, L_DATE, L_TIME, VC_REMARKS, VC_OPCONTENT
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        next if r[2].nil? || r[3].nil?

        operation_at = "#{r[2]} #{change_time(r[3])}".to_time
        data << {
          'source_id'          => r[0],
          'account_code'       => r[1],
          'account_name'       => nil,
          'operation_at'       => operation_at,
          'operation_category' => r[4],
          'operation'          => r[5],
          'bs_id'              => @bs_id,
          'ip_address'         => nil
        }
      end
    end

    def password_security_sql
      "select L_SERIAL_NO, VC_VALUE, VC_DESCRIBE from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        # 0 表示不冻结
        login_failure_number = data.find { |x| x[0].to_s == '55014' }&.[](1)&.to_i
        # L_SERIAL_NO为62728的 VC_VALUE 表示允许密码不过期
        is_password_valid_time = data.find { |x| x[0].to_s == '62728' }&.[](1)&.to_s == '1'
        password_valid_time = is_password_valid_time ? 0 : data.find { |x| x[0].to_s == '60762' }&.[](1)&.to_i
        json['bs_id'] = @bs_id
        json['password_length'] = data.find { |x| x[0].to_s == '60761' }&.[](1)
        json['is_uppercase'] = nil
        json['is_lowercase'] = nil
        json['is_number'] = nil
        json['is_character'] = nil
        json['login_failure_number'] = login_failure_number
        json['password_valid_time'] = password_valid_time
      end
    end

    # def last_login_at_sql
    #   <<-SQL
    #     select distinct c.vc_operator_name,
    #       a.l_operator_no,
    #       b.l_login_date
    #     from #{@table_station} a, #{@table_tworkstations} b, #{@table_accounts} c
    #     where a.vc_station_no = b.vc_station_no
    #       and a.l_operator_no = c.l_operator_no
    #       and c.c_operator_status = 1
    #     order by a.l_operator_no
    #   SQL
    # end

    # L_TIME是number类型，101111、 91111，前者应该大于后者，但是直接max后者会大于前者，所以需要补齐0
    def last_login_at_sql
      if @import_last_login_at.present?
        start_at = (Date.current - @import_last_login_at.days).strftime('%Y%m%d')
        where_sql = "where L_DATE >= '#{start_at}'"
      else
        where_sql = nil
      end

      <<-EOF
      select L_OPERATOR_NO, max(concat(floor(L_DATE), LPAD(L_TIME, 6, 0)))
        from #{@table_log}
        #{where_sql}
        and vc_remarks like '登录系统成功%'
        group by L_OPERATOR_NO
      EOF
    end

    def import_last_login_at_data
      # table_names = [@table_station, @table_tworkstations, @table_accounts]
      # import_last_login_at(table_names, last_login_at_sql) do |data, x|
      #   last_login_at = x[2].is_a?(Date) ? x[2] : x[2]&.to_i&.to_s&.to_date
      #   data << {
      #     'code' => x[1],
      #     'last_login_at' => last_login_at
      #   }
      # end

      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        next if x[1].nil?

        account = Jiaoyi::Account.find_by(code: x[0], quarter_id: @quarter_id)
        next if account.nil?

        data << {
          'account'       => account,
          'code'          => account.code,
          'last_login_at' => x[1].to_time
        }
      end
    end

    def find_destroy_data(temporary)
      if temporary.permission_type == 1
        t_data = [
          temporary.account_code,
          '2',
          temporary.permission_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.delete('-'),
          (temporary.temp_end_date.to_s.delete('-').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..6] == t_data }
      elsif temporary.permission_type == 0
        t_data = [
          temporary.account_code,
          '1',
          temporary.permission_code,
          temporary.unit_code,
          temporary.combination_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.delete('-'),
          (temporary.temp_end_date.to_s.delete('-').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..8] == t_data }
      end
    end

    def get_destroy_datas
      sql = <<-SQL
        select
          L_SERIAL_NO, L_OPERATOR_NO, L_DATE, L_TIME, VC_REMARKS
        from
          #{@table_space}thissystemlog#{@sid_suffix}
        where
          VC_OPCONTENT = '删除临时授权,[复核通过]'
        ORDER BY L_SERIAL_NO ASC
      SQL
      output_datas = []
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[4].to_s.include?('[续前一条]')
            output_datas.last[:VC_REMARKS] = output_datas.last[:VC_REMARKS] + r[4].gsub('[续前一条]', '')
          else
            g_time = r[3].to_s
            g_time = '0' + g_time while g_time.size < 6
            output_datas << {
              L_SERIAL_NO:   r[0],
              L_OPERATOR_NO: r[1],
              L_TIME:        Time.parse(r[2].to_s + g_time),
              VC_REMARKS:    r[4]
            }
          end
        end
      end

      destroy_datas_list = []
      output_datas.each do |output_data|
        output_data[:VC_REMARKS].split('][').each do |remark|
          destroy_datas_list << {
            L_SERIAL_NO:   output_data[:L_SERIAL_NO],
            L_OPERATOR_NO: output_data[:L_OPERATOR_NO],
            L_TIME:        output_data[:L_TIME],
            VC_REMARK:     remark.split(',').map { |y| y.split('：')[1].to_s }
          }
        end
      end

      destroy_datas_list
    end

    def import_time_controls_sql
      <<-SQL
        select * from #{@table_space}toptimecontrol#{@sid_suffix}
      SQL
    end

    def import_time_controls
      if importer_config['time_control']
        ActiveRecord::Base.transaction do
          @database.exec(import_time_controls_sql) do |r|
            Jiaoyi::TimeControl.create(
              quarter_id:   @quarter.id,
              role_code:    r[0],
              account_code: r[1],
              system_code:  r[2],
              menu_code:    r[3],
              begin_date:   r[4].to_s,
              begin_time:   r[5],
              end_date:     r[6].to_s,
              end_time:     r[7]
            )
          end
        end
      end
    end

    def update_temp_right(perm, start_date, end_date, grant_account)
      perm.is_temp                 = true
      perm.temp_start_date         = start_date
      perm.temp_end_date           = end_date
      perm.authorizer_account_id   = grant_account.id
      perm.authorizer_account_name = grant_account.name
      perm.save
    end

    # 国联安特殊的登录时间生成 excel
    def cpic_custom_importer
      require 'axlsx'
      sql = <<-SQL
        select distinct (x.l_operator_no) 账号编码,
        y.vc_operator_name 账号名称,
        case
        when y.c_operator_status = 1 then
        '正常'
        when y.c_operator_status = 2 then
        '冻结'
        end as 账号状态,
        case
        when l_optime_control = 1 then
        '有限制'
        /*else '无'*/
        end as 登录限制
        from (select distinct (b.l_operator_no) as l_operator_no
        from trade32.topmenurights t, trade32.toprolerights b
        where ((t.vc_menu_no = '2501' and
        (t.vc_menu_rights like '%4%' or t.vc_menu_rights like '%5%')) or
        t.vc_menu_no in ('1509', '2802'))
        and t.l_role_id ！ = -1
        and t.l_role_id = b.l_role_id
        and b.l_operator_no != 0
        union all
        select distinct (t.l_operator_no) as l_operator_no
        from trade32.topmenurights t
        where ((t.vc_menu_no in ('2501') and
        (t.vc_menu_rights like '%4%' or t.vc_menu_rights like '%5%')) or
        t.vc_menu_no in ('1509', '2802'))
        and t.l_role_id = -1) x,
        (select a.l_operator_no,
        a.vc_operator_name,
        d.l_optime_control,
        a.c_operator_status
        from trade32.toperator a
        left outer join (select distinct (c.l_operator_no) as l_operator_no,
        1 as l_optime_control
        from trade32.toptimecontrol c) d
        on a.l_operator_no = d.l_operator_no) y
        where x.l_operator_no = y.l_operator_no
        and y.c_operator_status！ = '3'
        and x.l_operator_no > 1000
        group by x.l_operator_no,
        y.vc_operator_name,
        y.l_optime_control,
        y.c_operator_status
        order by x.l_operator_no
      SQL

      export_excel                    = Axlsx::Package.new
      export_excel.use_shared_strings = true
      title_style                     = export_excel.workbook.styles.add_style({ sz: 10, border: Axlsx::STYLE_THIN_BORDER })
      content_style                   = export_excel.workbook.styles.add_style({ sz: 10, border: Axlsx::STYLE_THIN_BORDER, alignment: { vertical: :center } })
      export_excel.workbook.add_worksheet(name: 'sheet') do |sheet|
        i = 1
        sheet.add_row ['', '账号编码', '账号名称', '账号状态', '登录限制'], style: title_style
        @database.exec(sql) do |r|
          sheet.add_row [i, r[0], r[1].split('(')[0].split('-')[0], r[2], r[3]], style: content_style
          i += 1
        end
      end

      `mkdir /opt/data/export/excel/#{@quarter_id}/`
      file_path = "/opt/data/export/excel/#{@quarter_id}/用户可登录时间.xlsx"
      export_excel.serialize(file_path)
    end

    def class_exists?(class_name)
      klass = Module.const_get(class_name)
      klass.first
      klass.is_a?(Class)
    rescue NameError
      false
    end
  end
end
