module AasOracleImporter

  class JiraImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @email_suffix = importer_config['email_suffix']
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_project_permissions
      import_ledgers(Jira::Account)
    end

    def destroy_exist_datas
      Jira::Account.where(quarter_id: @quarter_id).destroy_all
      Jira::Role.where(quarter_id: @quarter_id).destroy_all
      Jira::ProjectPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts
      sql = <<-EOF
        select user_name, display_name, email_address, active from cwd_user
      EOF

      # MARK: active 字段在 jira 4.4.5 版本中不生效。这里只要已存在用户，status 就设置为真

      ActiveRecord::Base.transaction do
        @database.query(sql).each(:as => :array) do |r|
          # 只获取邮箱后缀为 grealife.cn 的用户
          next unless %r{@#{@email_suffix}}.match? r[2]
          Jira::Account.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1].strip,
            email:      r[2],
            status:     r[3].to_i == 1
          )
        end
      end
    end

    def import_roles

      sql = <<-EOF
        select id, group_name from cwd_group where group_type = 'GROUP'
      EOF

      ActiveRecord::Base.transaction do
        @database.query(sql).each(:as => :array) do |r|
          Jira::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select parent_id, child_name from cwd_membership where membership_type = 'GROUP_USER'
      EOF

      @roles    = Jira::Role.where(quarter_id: @quarter_id).to_a
      @accounts = Jira::Account.where(quarter_id: @quarter_id).to_a

      @database.query(sql).each(:as => :array) do |r|
        role    = @roles.find    {|x| x.code == r[0].to_s }
        account = @accounts.find {|x| x.code == r[1].to_s }

        unless role and account
          @logger.warn "#{self.class}: not found role_code '#{r[0]}' or account_code '#{r[1]}' in #{r.join}"
          next
        end

        account.roles << role unless account.roles.exists? role.id
      end
    end

    def import_project_permissions
      @projects      = @database.query('select id, pname from project').to_a
      @project_roles = @database.query('select id, name  from projectrole').to_a

      sql = <<-SQL
        select pid, projectroleid, roletype, roletypeparameter from projectroleactor
      SQL

      @database.query(sql).each do |row|
        next unless row['pid']

        project = @projects.find {|x| x['id'] == row['pid']}
        project_role = @project_roles.find {|x| x['id'] == row['projectroleid']}

        unless project and project_role
          @logger.warn "#{self.class}: not found project '#{row['pid']}' or project_role '#{row['projectroleid']}' in #{row.ro_a.join}"
          next
        end

        permission =
          Jira::ProjectPermission.create(
            project:      project['pname'],
            project_role: project_role['name'],
            quarter_id:   @quarter_id
          )

        case row['roletype']
        when 'atlassian-group-role-actor'
          role = @roles.find {|x| x.name == row['roletypeparameter']}

          unless role
            @logger.warn "#{self.class}: not found role row['roletypeparameter'] in #{row.to_a.join}"
            next
          end
          role.project_permissions << permission

        when 'atlassian-user-role-actor'
          account = @accounts.find {|x| x.code == row['roletypeparameter']}

          unless account
            @logger.warn "#{self.class}: not found account row['roletypeparameter'] in #{row.to_a.join}"
            next
          end
          account.project_permissions << permission
        end
      end
    end
  end
end
