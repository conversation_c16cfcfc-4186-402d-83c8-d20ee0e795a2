module G<PERSON>hi
  def self.table_name_prefix
    'guzhi_'
  end
end
class Guzhi::Account < ActiveRecord::Base
  has_many :bookset_relations
  has_and_belongs_to_many :roles
  has_many :account_role_menus, class_name: 'Guzhi::AccountRoleMenu', dependent: :destroy
  has_many :menus, through: :account_role_menus
  has_many :account_additional_permissions, class_name: 'Guzhi::AccountRoleAdditionalPermission', dependent: :destroy
  has_many :additional_permissions, through: :account_additional_permissions
  has_many :booksets, -> { distinct }, through: :bookset_relations
end

class Guzhi::Bookset < ActiveRecord::Base; end

class Guzhi::BooksetRelation < ActiveRecord::Base
  belongs_to :account
  belongs_to :role
  belongs_to :bookset
end

class Guzhi::Role < ActiveRecord::Base
  belongs_to :quarter
  has_many :bookset_relations
  has_and_belongs_to_many :accounts
  has_many :account_role_menus, class_name: 'Guzhi::AccountRoleMenu', dependent: :destroy
  has_many :menus, through: :account_role_menus
  has_many :account_additional_permissions, class_name: 'Guzhi::AccountRoleAdditionalPermission', dependent: :destroy
  has_many :additional_permissions, through: :account_additional_permissions
end


class Guzhi::Menu < ActiveRecord::Base
  has_ancestry cache_depth: true

  belongs_to :quarter
end

class Guzhi::AdditionalPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :menu, foreign_key: :menu_id, class_name: 'Guzhi::Menu'
end


class Guzhi::AccountRoleAdditionalPermission < ActiveRecord::Base
  belongs_to :account, class_name: 'Guzhi::Account', foreign_key: :account_id, optional: true
  belongs_to :role, class_name: 'Guzhi::Role', foreign_key: :role_id, optional: true
  belongs_to :additional_permission, class_name: 'Guzhi::AdditionalPermission', foreign_key: :additional_permission_id, optional: true
end

class Guzhi::AccountRoleMenu < ActiveRecord::Base
  belongs_to :account, class_name: 'Guzhi::Account', foreign_key: :account_id, optional: true
  belongs_to :role, class_name: 'Guzhi::Role', foreign_key: :role_id, optional: true
  belongs_to :menu, class_name: 'Guzhi::Menu', foreign_key: :menu_id, optional: true
end
