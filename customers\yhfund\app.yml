# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'yhfund'
  customer: '银华基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_yhfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  workday:
    enable: true
    mode: 'team_records'
    api: http://localhost:3000/api/external/is_workday

  agent:
    oracle:
      # tnsname
      oradb:
        # in tns name
        db_name: 'oradb'
        db_user: 'yhit'
        db_pass: 'yhit#123'
      dcpro2:
        db_name: 'DCPRO2'
        db_user: 'yhit'
        db_pass: 'yhit#123'

  importers:
    # MARK: 按照顺序依次导入

    - name: yhfund_hr
      # 接口对接，没有其他属性, 下面这个为了通过 initialize
      tnsname: dcpro2

    - name: guzhi_yss25
      bs_id: 34
      db_type: oracle
      tnsname: oradb
      table_space: 'yhgm.'
      sid_suffix: '@rgzdb'
      import_booksets_only_this_year: true

    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: oradb
      table_space: 'yhgz.'
      sid_suffix: '@rgzdb'

    # guohu_components/ yhfund_guohu_ta: true
    # 登记过户 4.0
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: oradb
      table_space: 'yhta4.'
      sid_suffix: '@rtadb'

    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: oradb
      table_space: 'yhta2.'
      sid_suffix: '@rtadb'

    - name: guohu_sa_etf
      bs_id: 40
      db_type: oracle
      tnsname: oradb
      table_space: 'yhetf.'
      sid_suffix: '@rtadb'


    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: oradb
      table_space: 'trade.'
      sid_suffix: '@rtradedb'

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/data/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_yhfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
  workday:
    enable: true
    api: http://*************/api/external/is_workday



