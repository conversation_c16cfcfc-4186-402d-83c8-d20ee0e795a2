module Hs<PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'hs_waibaota_'
  end
end

class HsWaibaota::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class HsWaibaota::AdditionalPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class HsWaibaota::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
  has_many :additional_permissions
end

class HsWaibaota::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class HsWaibaota::MenusRoles < ActiveRecord::Base; end
class HsWaibaota::AccountsRoles < ActiveRecord::Base; end
class HsWaibaota::AccountsMenus < ActiveRecord::Base; end
class HsWaibaota::AccountsAdditionalPermissions < ActiveRecord::Base; end
class HsWaibaota::AdditionalPermissionsRoles < ActiveRecord::Base; end
