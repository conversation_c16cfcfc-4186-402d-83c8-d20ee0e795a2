# frozen_string_literal: true

module InitializeHelpers
  # Redis 初始化参数
  module RedisHelper
    module_function

    def initialize_options

      if ENV['REDIS_CONFIG_TYPE'].to_s == 'username' # 开启username方式
         host = ENV['REDIS_HOSTS'] || '127.0.0.1'
        port = ENV['REDIS_PORT'] ? ENV['REDIS_PORT'].to_i : 6379
        db = ENV['REDIS_DB'] ? ENV['REDIS_DB'].to_i : 0
        password = ENV['REDIS_PASSWORD'].present? ? ":" + get_password.to_s + "@" : ''
        username = <PERSON>NV['REDIS_USERNAME'].present? ? ENV['REDIS_USERNAME'].to_s : ''

        redis_url = "redis://#{username}#{password}#{host}:#{port}/#{db}"
      else
        redis_url = ENV['REDIS_URL']
      end

      if ENV['SENTINEL_ENABLE'] == 'true'
        sentinels = ENV['SENTINEL_HOSTS'].split(' ').map do |host|
          { host: host, port: ENV['SENTINEL_PORT'].to_i }
        end
        { url: redis_url, sentinels: sentinels, role: :master, password: get_password }
      else
        { url: redis_url }
      end
    end

    def get_password
      # 为了方便aas_importer调用aas的redis，这里使用ActiveSupport::MessageEncryptory原方法
      ENV['AAS_ENCRYPTED'] == 'true' ? ActiveSupport::MessageEncryptor.new(ENV['SECRET_KEY_BASE'][0..31]).decrypt_and_verify(ENV['REDIS_PASSWORD']) : ENV['REDIS_PASSWORD']
    end
  end
end
