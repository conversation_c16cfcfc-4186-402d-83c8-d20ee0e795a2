# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'gcamc'
  customer: '中意资产'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_zofund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      tradevm:
        # in tns name
        db_name: 'tradevm'
        db_user: 'query'
        db_pass: 'query'
      gzdb:
        db_name: 'gztest'
        db_user: 'query'
        db_pass: 'query'
    mysql:
      xpdb:
        db_host: *************
        db_name: Ei_ids_new
        db_user: readdb
        db_pass: readdb
    csv:
      hr_csv:
        path: /opt/samba_data/zofund_hr

  importers:
    # MARK: 按照顺序依次导入
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: gzdb
      table_space: 'hsfaa20.'

    - name: ji<PERSON>yi
      bs_id: 31
      db_type: oracle
      tnsname: tradevm
      table_space: 'trade.'
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_gcamc_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


