module AasOracleImporter
  class OaRecordImporter < ImporterBase
    def config
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @table_space2 = importer_config['table_space2']
      @sid_suffix   = importer_config['sid_suffix']
      @code_data    = []

      initialize_tables
    end

    def initialize_tables
      @table_file = "#{@table_space2}t_file_v#{@sid_suffix}"
      @table_code = "#{@table_space}t_code#{@sid_suffix}"
      # @table_oa1     = "#{@table_space}U_F_FRQSXTYHQXSPBYYB#{@sid_suffix}"
      @table_oa1  = "#{@table_space}U_F_YWXTYHQXSPDZB#{@sid_suffix}"
      @table_oa2  = "#{@table_space}U_F_YWXTYHQXSPDWBJG#{@sid_suffix}"
      @table_oa3  = "#{@table_space}U_F_GMXTGYXXJQXBGSQ#{@sid_suffix}"
    end

    def import_to_do
      import_codes
      import_oa1
      import_oa2
      import_oa3
      import_oa4
    end

    def import_code_sql
      <<-EOF
        select CODECLASS, CODEDATA, CODEVALUE
        from #{@table_code}
      EOF
    end

    def import_codes
      select_db_datas(import_code_sql).each do |r|
        @code_data << [r[0], r[1], r[2]]
      end
    end

    def import_oa1_sql
      <<-EOF
        select a.ID, a.YGBH, a.XM2, a.BMMC2, a.SQXTMC, a.LSQXQX, a.LSQXQX1, b.title, a.SQRQ, a.SQLX, a.SQQXNRJMS
        from #{@table_oa1} a, #{@table_file} b
        where a.id = b.id and b.objectclass='YWXTYHQXSPDZB' and b.status = 2 and a.SQLB like '%2%'
      EOF
    end

    # SQLB 为 '1,2' 的数据中有部分没有开始日期和结束日期，需要过滤掉
    def import_oa1
      select_db_datas(import_oa1_sql).each do |r|
        start_at = r[5]
        end_at = r[6]
        next if end_at.blank?

        start_at, end_at = end_at, start_at if start_at.present? && start_at > end_at
        bs_codes = r[4].to_s.split(',').map(&:strip)
        category_codes = r[9].to_s.split(',').map(&:strip)
        bs_name = get_values('SQXTMC', bs_codes).join(',')
        apply_category = get_values('SQLX', category_codes).join(',')
        OaRecord
          .create_with(
            usercode:        r[1],
            username:        r[2],
            department_name: r[3],
            bs_name:         bs_name,
            start_at:        start_at,
            end_at:          end_at,
            title:           r[7],
            apply_at:        r[8],
            apply_name:      nil,
            apply_category:  apply_category,
            apply_content:   r[10],
            status:          0
          )
          .find_or_create_by(category: 'ywzb', source_id: r[0])
      end
    end

    def import_oa2_sql
      <<-EOF
        select a.ID, a.XM1, a.XM2, a.JGMC2, a.SQXTMC, a.LSQXQX, a.LSQXQX1, b.title, a.SQRQ, a.SQLX, a.SQQXNRJMS, a.JBR2
        from #{@table_oa2} a, #{@table_file} b
        where a.id = b.id and b.objectclass='YWXTYHQXSPDWBJG' and b.status = 2 and a.SQLB like '%2%'
      EOF
    end

    def import_oa2
      select_db_datas(import_oa2_sql).each do |r|
        start_at = r[5]
        end_at = r[6]
        next if end_at.blank?

        start_at, end_at = end_at, start_at if start_at.present? && start_at > end_at
        bs_codes = r[4].to_s.split(',').map(&:strip)
        category_codes = r[9].to_s.split(',').map(&:strip)
        bs_name = get_values('SQXTMC', bs_codes).join(',')
        apply_category = get_values('SQLX', category_codes).join(',')
        OaRecord
          .create_with(
            usercode:        r[1],
            username:        r[2],
            department_name: r[3],
            bs_name:         bs_name,
            start_at:        start_at,
            end_at:          end_at,
            title:           r[7],
            apply_at:        r[8],
            apply_name:      r[11],
            apply_category:  apply_category,
            apply_content:   r[10],
            status:          0
          )
          .find_or_create_by(category: 'ywwb', source_id: r[0])
      end
    end

    # 缺少标题
    def import_oa3_sql
      <<-EOF
        select a.ID, a.JYHM, a.JYXM, a.BMMC2, a.SSXT, a.YXQX1, a.YXQX2, b.title, a.SQRQ, a.SQLB, a.SQYYHNRSM, a.FQRY2, a.SSXT2
        from #{@table_oa3} a, #{@table_file} b
        where a.id = b.id and b.objectclass='GMXTGYXXJQXBGSQ' and b.status = 2 and a.SQLB like '%2%' and a.YXQX2 is not null
      EOF
    end

    def import_oa3
      select_db_datas(import_oa3_sql).each do |r|
        start_at = r[5]
        end_at = r[6]
        next if end_at.blank?

        start_at, end_at = end_at, start_at if start_at.present? && start_at > end_at
        bs_codes = r[4].to_s.split(',').map(&:strip)
        second_bs_codes = r[12].to_s.split(',').map(&:strip)
        category_codes = r[9].to_s.split(',').map(&:strip)
        bs_name1 = get_values('GM_SSXT', bs_codes).join(',')
        bs_name2 = get_values('GM_SSXT2', second_bs_codes).join(',')
        bs_name  = [bs_name1, bs_name2].select(&:present?).join(',')
        apply_category = get_values('QXSQLB', category_codes).join(',')
        OaRecord
          .create_with(
            usercode:        r[1],
            username:        r[2],
            department_name: r[3],
            bs_name:         bs_name,
            start_at:        start_at,
            end_at:          end_at,
            title:           r[7],
            apply_at:        r[8],
            apply_name:      r[11],
            apply_category:  apply_category,
            apply_content:   r[10],
            status:          0
          )
          .find_or_create_by(category: 'gm', source_id: r[0])
      end
    end

    def import_oa4_sql
      <<-EOF
          select a.ID, a.JYHM, a.JYXM, a.BMMC2, a.SSXT, a.YXQX1, a.YXQX2, b.title, a.SQRQ, a.SQLB, a.SQYYHNRSM, a.FQRY2, a.SSXT2
          from #{@table_oa3} a, #{@table_file} b
          where a.id = b.id and b.objectclass='FZJGGMXTGYXXJQXBGSQ' and b.status = 2 and a.SQLB like '%2%' and a.YXQX2 is not null
      EOF
    end

    def import_oa4
      select_db_datas(import_oa4_sql).each do |r|
        start_at = r[5]
        end_at = r[6]
        next if end_at.blank?

        start_at, end_at = end_at, start_at if start_at.present? && start_at > end_at
        bs_codes = r[4].to_s.split(',').map(&:strip)
        second_bs_codes = r[12].to_s.split(',').map(&:strip)
        category_codes = r[9].to_s.split(',').map(&:strip)
        bs_name1 = get_values('GM_SSXT', bs_codes).join(',')
        bs_name2 = get_values('GM_SSXT2', second_bs_codes).join(',')
        bs_name  = [bs_name1, bs_name2].select(&:present?).join(',')
        apply_category = get_values('QXSQLB', category_codes).join(',')
        OaRecord
          .create_with(
            usercode:        r[1],
            username:        r[2],
            department_name: r[3],
            bs_name:         bs_name,
            start_at:        start_at,
            end_at:          end_at,
            title:           r[7],
            apply_at:        r[8],
            apply_name:      r[11],
            apply_category:  apply_category,
            apply_content:   r[10],
            status:          0
          )
          .find_or_create_by(category: 'fzgm', source_id: r[0])
      end
    end

    protected

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回code对应的值
    def get_values(category, codes)
      data = @code_data.select { |x| x[0].to_s == category.to_s }
      data.select { |x| codes.include? x[2].to_s }.map { |x| x[1] }
    end
  end
end
