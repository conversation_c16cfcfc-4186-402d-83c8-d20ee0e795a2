module AasOracleImporter
  class KaihuImporter < ImporterBase
    def config
      @bs_id       = 325
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_ledgers(Kaihu::Account)
    end

    def destroy_exist_datas
      accounts = Kaihu::Account.where(quarter_id: @quarter_id)
      Kaihu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Kaihu::Role.where(quarter_id: @quarter_id).delete_all

      Kaihu::Data1Permission.where(quarter_id: @quarter_id).delete_all

      Kaihu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<~EOF
        select
          u.user_id,
          i.staff_no,
          i.user_name,
          u.user_status
        from
          crh_user.operatorinfo i,
          crh_user.users u
        where u.user_id = i.user_id
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          account = Kaihu::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == '0'
          )

          @accounts << account

          QuarterAccountInfo.create(
            account_id:         account.id,
            account_type:       'Kaihu::Account',
            business_system_id: @bs_id,
            quarter_id:         @quarter.id,
            display_status:     r[3]&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<~EOF
        select
          distinct
          r.role_code,
          r.role_code,
          r.role_name
        from
          crh_user.funcrole r
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Kaihu::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<~EOF
        select
          i.en_roles,
          i.user_id
        from
          crh_user.operatorinfo i
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          r[0].split(',').each do |row|
            role    = @roles.find { |x| x.source_id.to_s == row.to_s }
            account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
            next unless account && role

            Kaihu::AccountsRole.create(account_id: account.id, role_id: role.id)
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<~EOF
        select
          m.menu_id,
          m.menu_id,
          m.menu_name
        from
          crh_user.sysmenu m
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data1_permissions << Kaihu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<~EOF
        select
          i.role_code,
          i.en_resource_str
        from
          crh_user.funcrole i

      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          r[1].split(',').each do |row|
            role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
            permission = @data1_permissions.find { |x| x.source_id.to_s == row.to_s }
            next unless permission && role

            Kaihu::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
