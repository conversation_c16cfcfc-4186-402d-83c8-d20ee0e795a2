# frozen_string_literal: true

module AasOracleImporter
  # 富国的信披系统
  class FundDisclosureFgfundImporter < ImporterBase
    include ConvertTools
    CodeTableRecord = Struct.new(:fd_code, :o32_code)

    def config
      @bs_id                   = importer_config['bs_id']
      @table_space             = importer_config['table_space']
      @sid_suffix              = importer_config['sid_suffix']
      @fund_code_table_space   = importer_config['fund_code_table_space']
      @inservice_fund_statuses = importer_config['inservice_fund_statuses']
      @disabled_fund_statuses  = importer_config['disabled_fund_statuses']
      initialize_tables
    end

    def initialize_tables
      @table_user          = "#{@table_space}km_staffinfo#{@sid_suffix}"
      @table_position      = "#{@table_space}af_position#{@sid_suffix}"
      @table_user_position = "#{@table_space}af_posi_role2staff#{@sid_suffix}"
      @table_role_position = "#{@table_space}af_positionrole#{@sid_suffix}"
      @table_fund_position = "#{@table_space}oa_pipublicfundmgr_new#{@sid_suffix}"
      @table_fund          = "#{@table_space}oa_piproduct#{@sid_suffix}"
      @table_fund_role     = "#{@table_space}oa_pipublicfundmgr_new#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_funds
      import_fund_position
      import_ledgers(FundDisclosure::Account)
    end

    def destroy_exist_data
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
       select distinct m.operatorid, m.operatorname, m.vc_status, m.userid
       from #{@table_role_position} n,
            #{@table_position} b,
            #{@table_user_position}  f,
            #{@table_user}  m
        where b.vc_name LIKE '%投资经理%'
          and b.n_parentid  != 0
          and n.n_positionid  = b.n_positionid
          and n.n_busiroleid  = f.n_posiroleid
          and m.operatorid = f.n_operatorid
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          user_code, user_name, user_status, user_objid = r
          next unless user_code

          user_status = user_status != '离职'
          create_account(user_code, user_name, user_status, user_objid)
        end
      end
    end

    def import_funds_sql
      <<-SQL
        select vc_productcode, vc_cnname, vc_type, vc_status, n_pirecid from #{@table_fund}
      SQL
    end

    def import_funds
      ActiveRecord::Base.transaction do
        @database.exec(import_funds_sql) do |r|
          fund_code, fund_name, fund_type, fund_status, fund_objid = r

          create_fund(fund_code, fund_name, fund_type, fund_status, fund_objid)
        end
      end
    end

    def import_fund_position_sql
      <<-SQL
        select  n_mgrid,
                vc_fundcode,
                vc_mgrrole,
                d_entrydate,
                d_leavedate
        from #{@table_fund_position}
      SQL
    end

    def import_fund_position
      ActiveRecord::Base.transaction do
        @database.exec(import_fund_position_sql) do |r|
          user_code, fund_code, role_name, take_post_date, leave_post_date = r

          account = FundDisclosure::Account.find_by(quarter_id: @quarter_id, code: user_code)
          fund    = FundDisclosure::Fund.find_by(quarter_id: @quarter_id, code: fund_code)
          next unless account && fund

          unless role_name
            record_info = "n_mgrid = '#{user_code}' AND vc_fundcode = '#{fund_code}'"
            @logger.warn "#{self.class}: role_name is null in import fund_position where #{record_info}"
            next
          end
          is_blank_role   = role_name.nil? || role_name.empty?
          @logger.warn "信披系统「@table_fund_position」表角色名称「vc_mgrrole」为空，user_code: #{user_code}, fund_code: #{fund_code}" if is_blank_role
          role            = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, name: role_name) unless is_blank_role

          # 这里 take_post_date 会返回 time格式
          take_post_date  = take_post_date.to_date if take_post_date
          leave_post_date = leave_post_date.to_date if leave_post_date

          next unless role && fund

          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  take_post_date,
            leave_post_date: leave_post_date,
            quarter_id:      @quarter_id
          )
        end
      end
    end

    def create_account(code, name, status, obj_id)
      user        = FundDisclosure::Account.new(quarter_id: @quarter_id, code: code)
      user.name   = name
      user.status = status
      user.obj_id = obj_id
      user.save
      user
    end

    def create_fund(code, name, type, status, objid)
      fund_code           = code.to_s
      fund                = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: fund_code)
      fund.name           = name.to_s
      fund.fund_type      = type.to_s
      fund.status_display = status.to_s
      fund.status         = aas_fund_status(status.to_s)
      fund.obj_id         = objid.to_s
      fund.o32_fund_codes = add_o32_fund_codes(fund.o32_fund_codes, fund_code)
      fund.save
      fund
    end

    # 设置基金状态
    # 对应 model 中的 enum status: { disabled: 0, inservice: 1, unknown: 2 }
    def aas_fund_status(status)
      return 0 if @disabled_fund_statuses.include? status
      return 1 if @inservice_fund_statuses.include? status

      2
    end
  end
end
