class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end
module AdGroupYeb
  def self.table_name_prefix
    'ad_group_yeb_'
  end

  class Account < ActiveRecord::Base
    has_and_belongs_to_many :roles
  end

  class Role < ActiveRecord::Base
    has_and_belongs_to_many :accounts
    serialize :data_json, JsonWithSymbolizeNames
  end
end

module ShareYebSharedata
  def self.table_name_prefix
    'share_yeb_sharedata_'
  end

  class Account < ActiveRecord::Base
	  has_and_belongs_to_many :roles
	  validates :code, presence: true
	  validates :quarter_id, presence: true
	  serialize :data_json, JsonWithSymbolizeNames
	end

	class AccountsRolesPermission < ActiveRecord::Base
		belongs_to :quarter
	  belongs_to :permission
	  validates :permission_id, presence: true
	  validates :quarter_id, presence: true
	  serialize :data_json, JsonWithSymbolizeNames
	end

	class Role < ActiveRecord::Base
	  has_and_belongs_to_many :accounts
	  validates :code, presence: true
	  validates :quarter_id, presence: true
	  serialize :data_json, JsonWithSymbolizeNames
	end


	class Permission < ActiveRecord::Base
	  validates :code, presence: true
	  validates :quarter_id, presence: true
	  serialize :data_json, JsonWithSymbolizeNames
	end
end








