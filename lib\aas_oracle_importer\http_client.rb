# frozen_string_literal: true

module AasOracleImporter
  # http 客户端
  class HttpClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def base_uri
      @base_uri = database_info['base_uri']
    end

    private

    def initialize_driver
      load_driver_gem
    end

    def load_driver_gem
      require 'httparty'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'httparty\'.')
      exit(-127)
    end
  end
end
