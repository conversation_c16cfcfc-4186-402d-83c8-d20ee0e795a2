require 'java'
require 'drb'

java_import java.sql.DriverManager
java_import java.lang.Class

# require '/Users/<USER>/app/oceanbase-client-2.4.8.jar' # 替换为Oceanbase JDBC驱动程序的绝对路径

class JdbcConnectServer
  def execute_query(host, port, db_name, username, password, sql)
    # java.lang.ClassLoader.getSystemClassLoader().loadClass("com.oceanbase.jdbc.Driver");
    Java::JavaClass.for_name('com.oceanbase.jdbc.Driver')
    url = "jdbc:oceanbase://#{host}:#{port}/#{db_name}?pool=false"
    conn = DriverManager.getConnection(url, username, password)
    # 执行Oceanbase查询
    stmt = conn.createStatement
    rs = stmt.executeQuery(sql) # 替换为您要查询的表名
    meta_data = rs.meta_data
    c_num = meta_data.column_count
    output_datas = []
    while rs.next do
      output = {}
      for i in 1..c_num do
        column_name = meta_data.get_column_name(i)
        column_value = rs.getString(i)
        output[column_name] = column_value
      end
      output_datas << output
    end
    rs.close
    stmt.close
    conn.close
    output_datas
  end
end

server = JdbcConnectServer.new
DRb.start_service('druby://localhost:3006', server)
DRb.thread.join

# 启动方式（jar包路径需要替换）： jruby -J-cp '/Users/<USER>/app/oceanbase-client-2.4.8.jar' ../jdbc_oceanbase_server.rb