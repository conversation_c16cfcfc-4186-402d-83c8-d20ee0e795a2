module AasOracleImporter
  class TyrzImporter < ImporterBase
    def config
      @bs_id       = 400
      @accounts    = []
      @roles       = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @user_groups = []
      @group_roles = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      initialize_tables
    end

    def initialize_tables
      @table_account    = "#{@table_space}auth_user#{@sid_suffix}"
      @table_role       = "#{@table_space}auth_role#{@sid_suffix}"
      @table_user_group = "#{@table_space}auth_user_group#{@sid_suffix}"
      @table_group_role = "#{@table_space}auth_group_role#{@sid_suffix}"
      @table_menu       = "#{@table_space}auth_privilege#{@sid_suffix}"
      @table_role_menu  = "#{@table_space}auth_role_privilege#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions

      import_ledgers(Tyrz::Account)
    end

    def destroy_exist_datas
      accounts = Tyrz::Account.where(quarter_id: @quarter_id)
      Tyrz::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Tyrz::Role.where(quarter_id: @quarter_id).delete_all
      Tyrz::Data1Permission.where(quarter_id: @quarter_id).delete_all
      Tyrz::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      "select USER_ID, USER_UID, USER_NAME, USER_STATUS from #{@table_account} where USER_USERTYPE=3"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Tyrz::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
        end
      end
    end

    def import_roles_sql
      "select ROLE_ID, ROLE_KEY, ROLE_NAME from #{@table_role}"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << Tyrz::Role.create(
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            name:        r[2]
          )
        end
      end
    end

    def import_user_groups_sql
      "select USER_ID, GROUP_ID from #{@table_user_group}"
    end

    def import_user_groups
      ActiveRecord::Base.transaction do
        select_db_datas(import_user_groups_sql).each do |r|
          @user_groups << [r[0], r[1]]
        end
      end
    end

    def import_group_roles_sql
      "select GROUP_ID, ROLE_ID from #{@table_group_role}"
    end

    def import_group_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_group_roles_sql).each do |r|
          @group_roles << [r[0], r[1]]
        end
      end
    end

    def import_accounts_roles
      import_user_groups
      import_group_roles

      account_roles = []
      account_ids   = @user_groups.map { |x| x[0] }
      account_ids.each do |account_id|
        group_ids = @user_groups.select { |x| x[0] == account_id }.map { |x| x[1] }
        role_ids = @group_roles.select { |x| group_ids.include? x[0] }.map { |x| x[1] }
        role_ids.each do |role_id|
          account_roles << [account_id, role_id]
        end
      end

      account_roles.each do |r|
        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        role    = @roles.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && role

        Tyrz::AccountsRole.create(
          account_id: account.id,
          role_id:    role.id
        )
      end
    end

    def import_data1_permissions_sql
      "select PRIVILEGE_ID, PRIVILEGE_KEY, PRIVILEGE_NAME, PRIVILEGE_PARENT_ID from #{@table_menu} where PRIVILEGE_STATUS = 0"
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          parent_id_value = r[3]
          name = full_name(import_data1_permissions_sql, r[2], parent_id_value, 2, 3)
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << Tyrz::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      "select ROLE_ID, PRIVILEGE_ID from #{@table_role_menu}"
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          Tyrz::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
