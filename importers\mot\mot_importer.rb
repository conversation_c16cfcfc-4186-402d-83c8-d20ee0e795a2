module AasOracleImporter

  class MotImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @table_accounts = importer_config['table_accounts']
      @table_roles = importer_config['table_roles']
      @table_positions = importer_config['table_positions']
      @table_accounts_positions = importer_config['table_accounts_positions']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_positions_roles = importer_config['table_positions_roles']
    end

    def import_to_do
      import_accounts
      import_roles
      import_positions
      import_accounts_roles
      import_accounts_and_roles_positions
      import_ledgers(Mot::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def import_accounts

      Mot::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select MEMBER_ID, MEMBER_NAME from #{@table_accounts}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Mot::Account.create( quarter_id: @quarter_id,code: r[0].to_i, name: r[1] )
        end
      end
    end

    def import_roles

      Mot::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select role_id, role_name from #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Mot::Role.create( quarter_id: @quarter_id,code: r[0].to_i, name: r[1] )
        end
      end
    end

    def import_positions

      Mot::Position.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select RES_ID, RES_LABEL
        from #{@table_positions}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Mot::Position.create( quarter_id: @quarter_id,code: r[0].to_i, name: r[1] )
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select MEMBER_ID,ROLE_ID
        from  #{@table_accounts_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = Mot::Account.where(quarter_id: @quarter_id).find_by_code(r[0].to_i)
          role = Mot::Role.where(quarter_id: @quarter_id).find_by_code(r[1].to_i)

          if account and role
            MotAccountsRoles.create(role_id: role.id, account_id: account.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_accounts_and_roles_positions
      sql = <<-EOF
        select MEMBER_ID,RESOURCE_ID,MEMBER_TYPE
        from #{@table_accounts_positions}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          case r[2].to_i
          when 2 # 代表角色
            
            role = Mot::Role.where(quarter_id: @quarter_id).find_by_code(r[0].to_i)
            position = Mot::Position.where(quarter_id: @quarter_id).find_by_code(r[1].to_i)
            if role and position
              MotPositionsRoles.create(role_id: role.id, position_id: position.id)
            else
              @logger.warn "#{self.class}: not found role #{r[0]}" unless role
              @logger.warn "#{self.class}: not found position #{r[1]}" unless position
            end
          when 1 # 代表用户
            account = Mot::Account.where(quarter_id: @quarter_id).find_by_code(r[0].to_i)
            position = Mot::Position.where(quarter_id: @quarter_id).find_by_code(r[1].to_i)
            if account and position
              MotAccountsPositions.create(position_id: position.id, account_id: account.id)
            else
              @logger.warn "#{self.class}: not found account #{r[0]}" unless account
              @logger.warn "#{self.class}: not found position #{r[1]}" unless position
            end
          end
        end
      end
    end

  end
end



