# frozen_string_literal: true

require 'date'
require_relative 'workday_mode_base'

module AasOracleImporter
  module Workday
    # 指定月份和日期
    class DateInMonth < WorkdayModeBase
      def initialize(date, config)
        super
        config_valid?
      end

      def workday?
        month_allow? && day_allow?
      end

      private

      def config_valid?
        raise ConfigParseError, 'workday config is not a hash' unless config['config'].is_a?(Hash)

        unless months_config.is_a?(String) || months_config.is_a?(Array)
          raise ConfigParseError, 'months config is not a Array or String'
        end

        if months_config.is_a?(String)
          unless permit_commands.include?(months_config)
            raise ConfigParseError, "#{months_config} is not a permit command"
          end
        end

        unless days_config.is_a?(String) || days_config.is_a?(Array)
          raise ConfigParseError, 'dates config is not a Array or String'
        end

        if days_config.is_a?(String)
          unless permit_commands.include?(days_config)
            raise ConfigParseError, "#{days_config} is not a permit command"
          end
        end

        true
      end

      def month_allow?
        return true if permit_commands.include? months_config

        months_config.include? date.month
      end

      def day_allow?
        return true if permit_commands.include? days_config

        days_config.include? date.day
      end

      def months_config
        config['config']['months']
      end

      def days_config
        config['config']['days']
      end

      def permit_commands
        %w[all every]
      end
    end
  end
end
