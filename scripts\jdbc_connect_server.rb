require 'java'
require 'optparse'
require 'drb'

# 添加JDBC驱动程序的路径
driver_path = "/home/<USER>/program/aas-app/scripts/hive-jdbc-3.1.3000.7.1.7.1000-141-standalone.jar"
drb_port = 3006
require driver_path

class JdbcConnectServer
  def execute_query(host, port, db_name, username, password, sql, driver_name = "hive2")
    url = "jdbc:#{driver_name}://#{host}:#{port}/#{db_name}"
    conn = java.sql.DriverManager.getConnection(url, username, password)
    stmt = conn.createStatement
    rs = stmt.executeQuery(sql)
    meta_data = rs.meta_data
    c_num = meta_data.column_count
    output_datas = []
    while rs.next do
      output = []
      for i in 1..c_num do
        output << rs.getString(i)
      end
      output_datas << output
    end

    rs.close
    stmt.close
    conn.close
    return output_datas
  end
end

server = JdbcConnectServer.new
DRb.start_service("druby://localhost:#{drb_port}", server)
DRb.thread.join
