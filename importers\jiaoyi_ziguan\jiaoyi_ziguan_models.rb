module Ji<PERSON>yi<PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'jiaoyi_ziguan_'
  end
end

class JiaoyiZiguan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class JiaoyiZiguan::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class JiaoyiZiguan::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class JiaoyiZiguan::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class JiaoyiZiguan::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class JiaoyiZiguan::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class JiaoyiZiguan::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class JiaoyiZiguan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class JiaoyiZiguan::System < ActiveRecord::Base
  has_many :menus
end

class JiaoyiZiguan::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class JiaoyiZiguan::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class JiaoyiZiguan::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class JiaoyiZiguan::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class JiaoyiZiguan::Temporary < ActiveRecord::Base; end

class JiaoyiZiguanAccountsRoles < ActiveRecord::Base; end
class JiaoyiZiguanMenusRoles < ActiveRecord::Base; end
class JiaoyiZiguanMenuAdditionsRoles < ActiveRecord::Base; end

class JiaoyiZiguan::TimeControl < ActiveRecord::Base; end

class JiaoyiZiguan::TopTradeType < ActiveRecord::Base; end
class JiaoyiZiguan::TopStation < ActiveRecord::Base; end
class JiaoyiZiguan::TradeType < ActiveRecord::Base; end