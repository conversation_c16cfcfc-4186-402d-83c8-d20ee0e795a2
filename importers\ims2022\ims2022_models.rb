module Ims2022
  def self.table_name_prefix
    'ims2022_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class Ims2022::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles, -> { distinct }
  
  
  has_many :data1_accounts_roles_permissions
  
  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end


class Ims2022::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  
  
  has_many :data1_accounts_roles_permissions
  
  serialize :child_codes, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end

class Ims2022::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end



class Ims2022::Data1Permission < ActiveRecord::Base

  validates :code, presence: true
  validates :level1_name, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end


class Ims2022::Data1AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'ims2022_data1_arps'

  belongs_to :data1_permission
  belongs_to :role, optional: true

  validates :data1_permission_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class Ims2022::Fund < ActiveRecord::Base
end

class Ims2022::AccountsFund < ActiveRecord::Base
end

class Ims2022::DataPermission < ActiveRecord::Base
end

class Ims2022::AccountsDataPermission < ActiveRecord::Base
end

class Ims2022::Asset < ActiveRecord::Base
end

class Ims2022::AccountsAsset < ActiveRecord::Base
end

