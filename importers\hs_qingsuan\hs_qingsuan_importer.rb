module AasOracleImporter

  class HsQingsuanImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      super
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_additional_permissions
      import_acccounts_menus
      import_menus_roles

      import_ledgers(HsQingsuan::Account)
    end

    def import_accounts_sql
      <<-SQL
        select u.vc_user_code,
               u.vc_user_name,
               u.c_status
          from
               #{@table_space}FM_USER#{@sid_suffix} u
          order by u.vc_user_name
      SQL
    end

    def import_accounts
      HsQingsuan::Account.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            HsQingsuan::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_i == 1
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select vc_group_name from #{@table_space}FM_GROUP#{@sid_suffix}
      SQL
    end

    def import_roles
      HsQingsuan::Role.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          HsQingsuan::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[0]
            )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          vc_menu_no, vc_menu_name
        from
          #{@table_space}FM_MENUITEM#{@sid_suffix}
        where
          c_menu_type = 1
        order by
          l_order
      SQL
    end

    def import_menus
      HsQingsuan::Menu.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          HsQingsuan::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_additional_permissions_sql
      <<-SQL
        select
          vc_menu_no,
          c_menu_right,
          vc_menu_right_name
        from
          #{@table_space}FM_EXTRAMENURIGHT#{@sid_suffix}
      SQL
    end

    def import_additional_permissions
      HsQingsuan::AdditionalPermission.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_additional_permissions_sql) do |r|
          menu = HsQingsuan::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          HsQingsuan::AdditionalPermission.create(
            quarter_id: @quarter_id,
            sub_code: r[1],
            name: r[2],
            menu_id: menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_GROUP#{@sid_suffix} g
        where t.l_group_id = g.l_group_id
      SQL
    end


    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = HsQingsuan::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          menu = HsQingsuan::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            HsQingsuanMenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              HsQingsuan::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              HsQingsuanAdditionalPermissionsRoles.create(
                role_id: role.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_acccounts_menus_sql
      <<-SQL
        select
          u.vc_user_code,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_USER#{@sid_suffix} u
        where t.l_user_id = u.l_user_id
      SQL
    end

    def import_acccounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_acccounts_menus_sql) do |r|
          account = HsQingsuan::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = HsQingsuan::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            HsQingsuanAccountsMenus.create(menu_id: menu.id, account_id: account.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              HsQingsuan::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id: menu.id,
                sub_code: additional_permission_codes
              )

            aps.each do |ap|
              HsQingsuanAccountsAdditionalPermissions.create(
                account_id: account.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          u.vc_user_code
        from
          #{@table_space}FM_GROUPUSER#{@sid_suffix} gu,
          #{@table_space}FM_GROUP#{@sid_suffix} g,
          #{@table_space}FM_USER#{@sid_suffix} u
        where
          gu.l_group_id = g.l_group_id
          and gu.l_user_id = u.l_user_id
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = HsQingsuan::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = HsQingsuan::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            HsQingsuanAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



