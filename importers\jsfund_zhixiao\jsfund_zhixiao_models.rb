module Jsfund<PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'jsfund_zhixiao_'
  end
end

class JsfundZhixiao::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Jsfund<PERSON><PERSON>xiao::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Jsfund<PERSON><PERSON>xiao::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class JsfundZhixiao::MenusRoles < ActiveRecord::Base; end
class JsfundZhixiao::AccountsRoles < ActiveRecord::Base; end
