account_list:
  url: /apiopen/userService/userList
  pagesize: 999
  argument: page
  body:
  - key: pageSize
    value: 999
  - key: page
    value: 1
  enum:
    status:
      "1": 已启用
      "2": 已锁定
      "3": 未生效
      "4": 已禁用
      "5": 已过期
    isAdmin:
      '1': 是
      '2': 否
role_list:
  url: /apiopen/roleService/roleList
  pagesize: 999
  argument: page
  body:
  - key: pageSize
    value: 999
  - key: page
    value: 1
data1_permissions_list:
  url: /apiopen/ops/opsList
  pagesize: 999
  argument: page
  body:
  - key: pageSize
    value: 999
  - key: page
    value: 1
  enum:
    status:
      "1": 已启用
      "2": 已禁用
      "3": 未生效
      "4": 已过期
      "5": 待完善
# 查询角色信息
data1_account_permissions:
  url: /apiopen/opsStrategyService/userList
  pagesize: 50
  argument: page
  body:
  - key: pageSize
    value: 50
  - key: page
    value: 1
update_accounts:
  url: /apiopen/userService/userDetail
  argument: id
  body:
  - key: id
    value: ''
