module AasOracleImporter
  class YyCaiwuImporter < ImporterBase
    def config
      @bs_id       = 326
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      # import_data1_permissions

      # import_data1_role_permissions

      # import_data2_permissions

      # import_data2_role_permissions

      import_ledgers(YyCaiwu::Account)
    end

    def destroy_exist_datas
      accounts = YyCaiwu::Account.where(quarter_id: @quarter_id)
      YyCaiwu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      YyCaiwu::Role.where(quarter_id: @quarter_id).delete_all

      YyCaiwu::Data1Permission.where(quarter_id: @quarter_id).delete_all

      YyCaiwu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      YyCaiwu::Data2Permission.where(quarter_id: @quarter_id).delete_all

      YyCaiwu::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select cuserid, user_code, user_name, enablestate, islocked from sm_user
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << YyCaiwu::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == '2'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select pk_role, role_code, role_name from sm_role
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << YyCaiwu::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select pk_role, cuserid from sm_user_role
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          YyCaiwu::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, code, name, pid from sm_menu
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index))

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data1_permissions << YyCaiwu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, per_id from sm_role_menu
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          YyCaiwu::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select id, code, name, pid from sm_menu
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data2_permissions << YyCaiwu::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select role_id, per_id from sm_role_menu
      EOF
    end

    def import_data2_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          YyCaiwu::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data2_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      command = 'obclient -h10.55.74.118 -uqxglcj@cwxttest#zszq_xs_test2 -P2883 -pTEst@********# -A cwxt'
      sql = sql.delete(';')
      output_datas = []

      `#{command} -e '#{sql}'`.each_line do |line|
        output_datas << line.strip.split(/\t/)
      end

      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
