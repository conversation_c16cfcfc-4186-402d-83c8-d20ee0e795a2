require 'httparty'

module AasOracleImporter
  class ThfundFundCenterImporter < ImporterBase

    def config
      @bs_id          = 32
      @host    = importer_config['host']
      @visitorId     = importer_config['visitorId']
      @token       = importer_config['token']
      @accounts = {}
      @funds = {}
      @roles = {}
    end

    def import_to_do
      destroy_exist_data
      import_accounts_funds
      import_ledgers(FundDisclosure::Account)
    end

    private

    def destroy_exist_data
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_funds
      body = {
        disableCache: true,
        param: {},
        visitorId: @visitorId,
        token: @token
      }
      #{"PRODUCTTYPE":"公募","ACCOUNT":"zhoukn","STAGE":"拟发行","USERNAME":"周楷宁",
      #"LEAVEOFFICEDATE":null,"PRODUCTNAME":"天弘数字经济混合型发起式证券投资基金","RN":1,
      #"PRODUCTCODE":null,"MANAGERTYPE":"基金经理","INOFFICEDATE":null}  
      uri = "/query/apiService/queryManager"
      datas = post_server(uri, body)
      account = nil
      fund = nil
      datas.each do |data|
        account_code = data['ACCOUNT'].to_s
        if @accounts[account_code].blank?
          account = FundDisclosure::Account.create(
            quarter_id: @quarter_id,
            code: account_code,
            name: data['USERNAME'],
            status: true,
            obj_id: account_code
          )
          @accounts[account_code] = account
        end

        role_code = data['MANAGERTYPE'].to_s
        if @roles[role_code].blank?
          role = FundDisclosure::Role.create(
            quarter_id: @quarter_id,
            code: role_code,
            name: role_code
            )
          @roles[role_code] = role
        end


        fund_code = data['PRODUCTCODE'].to_s
        next if fund_code.blank?
        if @funds[fund_code].blank?
          fund_status = data['STAGE']
          fund = FundDisclosure::Fund.create(
            quarter_id: @quarter_id,
            code: fund_code,
            name: data['PRODUCTNAME'],
            fund_type: data['PRODUCTTYPE'],
            status_display: data['STAGE'],
            status: data['STAGE'] != '拟发行' && data['STAGE'] != '终止',
            obj_id: fund_code,
            o32_fund_codes: [fund_code],
          )
          @funds[fund_code] = fund
        end

        account = @accounts[account_code]
        fund = @funds[fund_code]
        role = @roles[role_code]

        take_post_date = data['INOFFICEDATE']
        leave_post_date = data['LEAVEOFFICEDATE']
        take_post_date  = take_post_date.to_date if take_post_date.present?
        leave_post_date = leave_post_date.to_date if leave_post_date.present?

        next unless account && role && fund

        FundDisclosure::FundPosition.create(
          fund_id:         fund.id,
          account_id:      account.id,
          role_id:         role.id,
          take_post_date:  take_post_date,
          leave_post_date: leave_post_date,
          quarter_id:      @quarter_id
        )
      end
    end


    def post_server(uri, body)

      
      url = "#{@host}#{uri}"
      response = HTTParty.post(url, body: body.to_json, headers: headers)
      result = JSON.parse(response.body)
      return result['data']
    end

    def headers
      {
        'Content-Type' => 'application/json'
      }
    end

  end
end
