# frozen_string_literal: true
module AasOracleImporter
  # Kingbase(人大金仓) 客户端
  class KingbaseClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.exec(sql).each_row do |row|
          yield row
        end
      else
        @database.exec(sql).each_row
      end
    end

    def query(sql)
      @database.query(sql)
    end

    private

    def initialize_driver
      load_driver_gem
      @database = PG.connect(kingbase_client_config)
    rescue PG::Error => e
      raise PG::Error, message_prefix + e.message
    end

    def kingbase_client_params
      kingbase_client_config.update({password: ConvertTools::Cryptology.decrypt_if_env(kingbase_client_config[:password])})
    end

    # 注意此处的配置的key的名称，使用的pg直连，跟activerecord的不一致
    def kingbase_client_config
      {
        host:     database_info['db_host'],
        port:     database_info['db_port'] || 54321,
        dbname:   database_info['db_name'],
        user:     database_info['db_user'],
        password: database_info['db_pass']
      }
    end

    def load_driver_gem
      require 'pg'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'pg\'.')
      exit(-127)
    end
  end
end
