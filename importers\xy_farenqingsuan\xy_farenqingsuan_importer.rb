module AasOracleImporter
  class XyFarenqingsuanImporter < ImporterBase
    def config
      @bs_id       = 254
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @app_id = importer_config['app_id']
      @accounts    = []
      @roles       = []
      @data1_permissions = []
      @app_role_ids = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}users#{@sid_suffix}"
      @table_role         = "#{@table_space}user_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}user_role_rela#{@sid_suffix}"
      @table_menu         = "#{@table_space}menu_def#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}user_role_right#{@sid_suffix}"
      @table_user_menu    = "#{@table_space}user_right#{@sid_suffix}"
      @table_second_user_menu = "#{@table_space}depmanager_right#{@sid_suffix}"
      @table_org = "#{@table_space}department#{@sid_suffix}"
      @table_log = "#{@table_space}user_visit_log_hist#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_all_role_ids
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      import_data1_second_account_permissions
      import_last_login_at_data

      import_ledgers(XyFarenqingsuan::Account)
    end

    def destroy_exist_datas
      XyFarenqingsuan::Department.where(quarter_id: @quarter_id).delete_all
      accounts = XyFarenqingsuan::Account.where(quarter_id: @quarter_id)
      XyFarenqingsuan::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      XyFarenqingsuan::Role.where(quarter_id: @quarter_id).delete_all
      XyFarenqingsuan::Data1Permission.where(quarter_id: @quarter_id).delete_all
      XyFarenqingsuan::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select DEP_ID, DEP_CODE, DEP_NAME, PARENT_DEP_ID from #{@table_org} where DEP_STATUS = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        XyFarenqingsuan::Department.create(
          quarter_id: @quarter_id,
          source_id:  r[0],
          code:       r[1],
          name:       r[2],
          status:     true
        )
      end
      # 建联关联关系
      XyFarenqingsuan::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = XyFarenqingsuan::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end
      # 设置full_name
      XyFarenqingsuan::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end

      @departments = XyFarenqingsuan::Department.where(quarter_id: @quarter_id)
    end

    def import_all_role_ids_sql
      "select distinct role_id from #{@table_role_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}"
    end

    # 获取对应app_id的角色
    # 用于过滤账号角色数据，默认获取的账号角色是所有的app_id的角色
    def import_all_role_ids
      select_db_datas(import_all_role_ids_sql).each do |r|
        @app_role_ids << r[0]&.to_s if r[0].present?
      end
    end

    def import_accounts_sql
      <<-EOF
        select user_code, user_code, user_name, user_status as status, DEP_ID from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          account = XyFarenqingsuan::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '0',
            department_id: department&.id
          )
          @accounts << account

          next unless @display_status

          QuarterAccountInfo.create(
            account_id:         account.id,
            account_type:       'XyFarenqingsuan::Account',
            business_system_id: @bs_id,
            quarter_id:         @quarter_id,
            display_status:     r[3]&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_id, role_id, role_name, DEP_ID from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          department = @departments.find { |x| x.source_id.to_s == r[3].to_s }
          name = department.present? ? department.name + ' - ' + r[2] : r[2]
          @roles << XyFarenqingsuan::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       name
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_code from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        data = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          next unless @app_role_ids.include?(r[0]&.to_s)

          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          data << [account.id, role.id] if account && role
        end
        XyFarenqingsuan::AccountsRole.bulk_insert(:account_id, :role_id) do |obj|
          obj.set_size = 1000
          data.uniq.each do |o|
            obj.add [o[0], o[1]]
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select menu_code, menu_code, menu_name, parent_menu_code from #{@table_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << XyFarenqingsuan::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, menu_code from #{@table_role_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          XyFarenqingsuan::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_user_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_account_permissions_sql).each do |r|
          account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && account

          XyFarenqingsuan::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_second_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_second_user_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_second_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_second_account_permissions_sql).each do |r|
          account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && account

          XyFarenqingsuan::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def last_login_at_sql
      <<-EOF
        select user_code, max(LOGIN_DATE) from #{@table_log} group by user_code
      EOF
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        next if x[1].nil?

        account = XyFarenqingsuan::Account.find_by(code: x[0], quarter_id: @quarter_id)
        next if account.nil?

        data << {
          'account' => account,
          'code' => account.code,
          'last_login_at' => x[1].to_time
        }
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')

      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
