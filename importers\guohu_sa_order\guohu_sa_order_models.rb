module Guohu<PERSON>a<PERSON>rder
  def self.table_name_prefix
    'guohu_sa_order_'
  end
end

class GuohuSaOrder::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaOrder::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaOrder::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaOrder::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaOrderAccountsRoles < ActiveRecord::Base; end


class GuohuSaOrderMenusRoles < ActiveRecord::Base; end
class GuohuSaOrderAccountsMenus < ActiveRecord::Base; end
class GuohuSaOrderAccountsOtherPermissions < ActiveRecord::Base; end
class GuohuSaOrderOtherPermissionsRoles  < ActiveRecord::Base; end
