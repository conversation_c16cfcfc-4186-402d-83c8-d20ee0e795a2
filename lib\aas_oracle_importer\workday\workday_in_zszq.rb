# frozen_string_literal: true

require_relative 'workday_mode_base'
require_relative '../database_client'
require_relative '../config'

module AasOracleImporter
  module Workday
    class WorkdayInZszq < WorkdayModeBase
      def initialize(date, config)
        super
        #@database = database_client
        @date = date.to_date
      end

      def workday?
        TradingDays::Date.workday?(@date)# && get_data_center_status
      end

      def get_data_center_status
        # 未获取到同步完成信息，隔半小时执行一次
        status = data_center_status
        n = config['config']['restart_num'].to_i
        i = 0
        while !status && i <= n
          sleep 1800
          status = data_center_status
          i = i + 1
          return true if status
        end
        return true
      end

      def database_client
        DatabaseClient.new_database('mysql', config['config']['tnsname'])
      end

      def data_center_status_sql
        <<-EOF
          select
            tdp.name as dbname,
            tdp.description,
            tdpd.name as prefix,
            tdpd.release_state,
            tdpd.next_exec_time,
            tdpi.name as taskname,
            tdpi.state as status,
            tdpi.start_time,
            tdpi.end_time,
            tdpi.run_times
          from 
            t_ds_project tdp 
          left join 
            t_ds_process_definition tdpd on tdp.id = tdpd.project_id
          left join 
            t_ds_process_instance tdpi on tdpd.id = tdpi.process_definition_id 
            and DATE_FORMAT(tdpi.start_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') 
        EOF
      end

      def importer_config
        @importer_config ||= AasOracleImporter.config
      end

      def get_error_systems
        error_systems = []
        data_status_arr = []
        @database.exec(data_center_status_sql) do |r|
          data_status_arr << [r[0].to_s.gsub("IMP_",""),r[2], r[6].to_i == 7]
        end
        # 未发现同步完成数据发送邮件通知
        importer_names = importer_config['importers'].select{|x| x['db_type'] == 'jdbc'}.map { |x| x['name'] }
        importer_names.each do |name|
          importer = importer_class_for(name).new(Quarter.new)
          sql_method = importer.methods.find{|x| x.to_s.include?('account') && x.to_s.include?('_sql')}
          sql = importer.send(sql_method.to_sym)
          keys = sql.to_s.gsub("\n","").upcase.split("FROM")[1].to_s.split("_")[0].to_s.gsub(" ","").split(".")
          data_status = data_status_arr.find{|x| x[0] == keys[0] && x[1].to_s.include?(keys[1])}
          if data_status && data_status[2]
            next
          else
            error_systems << name
          end
        end
        error_systems
      end

      def data_center_status
        get_error_systems.blank?
      end

      def importer_class_for(name)
        Object.const_get("AasOracleImporter::#{name.camelize}Importer")
      end

    end
  end
end
