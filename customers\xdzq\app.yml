---
defaults: &defaults
  customer_id: xdzq
  customer: 信达证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  workday:
    enable: true
    mode: 'trading_day'
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: odbc
      conn_str: DRIVER=DM8;SERVER=127.0.0.1;TCP_PORT=5237;UID=******;PWD=******;ENCODING=utf8;
      # adapter: mysql2
      # encoding: utf8
      # database: aas_xdzq_development
      # username: root
      # password: 123456
      # host: 127.0.0.1
  agent:
    # mysql:
    #   dingdian_liangrong_db:
    #     db_host: ************
    #     db_name: a5_manage
    #     db_user: root
    #     db_pass: 111111
    #   dingdian_kuaijiao_db:
    #     db_host: ************
    #     db_name: a5_manage
    #     db_user: root
    #     db_pass: 111111
    #     db_port: 3308
    dm:
      aas_db:
        conn_str: DRIVER=DM8;SERVER=127.0.0.1;TCP_PORT=5237;UID=******;PWD=******;ENCODING=utf8;
    oracle:
      dbcenter:
        db_host: ************
        db_name: cindasc
        db_user: 'INF_CHECK'
        db_pass: 'INF_CHECK'
      # rongzi_u_db:
      #   db_host: ************
      #   db_name: qstest
      #   db_user: 'scan'
      #   db_pass: 'scantest'
      # jz_tougu_db:
      #   db_host: ************
      #   db_name: otcmdb
      #   db_user: scan
      #   db_pass: scanxdzq
    sqlite:
      kr_jisukuaijiao_db:
        db_path: /opt/aas-app/kuanrui.db
  importers:
  - name: xinda_oa
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.coa_
  - name: qingsuan
    version: 1.1
    bs_id: 25
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.swt_
    sid_suffix: ''
    display_status: true
  - name: jinzheng_otc
    bs_id: 47
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.sdp_
    sid_suffix: ''
    display_status: true
  - name: jz_jizhong_jiaoyi
    bs_id: 46
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.cts_
    sid_suffix: ''
    display_status: true
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.O32_
    sid_suffix: ''
    operator_no_importer: false
    trade_type_importer: true
    station_importer: true
    display_status: true
  - name: guzhi
    bs_id: 24
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.eas_
    sid_suffix: ''
    add_on_modules_disable: true
    display_status: true
  # 直销系统
  - name: zhixiao_zhongxin
    bs_id: 27
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.DSP_
    sid_suffix: ''
    sub_system: "CENTER"
    display_status: true
  - name: zhixiao_guitai
    bs_id: 28
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.DSP_
    sid_suffix: ''
    sub_system: "COUNTER"
    display_status: true
  - name: yuancheng_guitai
    bs_id: 29
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.DSP_
    sid_suffix: ''
    sub_system: "REMOTE"
    display_status: true
  - name: hspb
    bs_id: 52
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.opb_
    sid_suffix: ''
    display_status: true
    version: 1.1
  - name: guohu_sa_new
    bs_id: 26
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.sta_
    sid_suffix: ''
    display_status: true
  - name: jz_zhuanrongtong
    bs_id: 250
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.mts_
    user_table_space: ods_bf.ref_
    sid_suffix: ''
    display_status: true
  - name: jz_gupiao
    bs_id: 253
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.ois_
    sid_suffix: ''
    display_status: true
  - name: xy_farenqingsuan
    bs_id: 254
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.css_
    sid_suffix: ''
    display_status: true
  - name: jz_yunying
    bs_id: 255
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.ygt_
    sid_suffix: ''
    display_status: true
  - name: jz_zhanghu
    bs_id: 252
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.uas_
    sid_suffix: ''
    display_status: true
  - name: dingdian_liangrong
    bs_id: 256
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.dms_
    sid_suffix: ''
    display_status: true
  - name: dingdian_kuaijiao
    bs_id: 257
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.hts_
    sid_suffix: ''
    display_status: true
  - name: xt_pb
    bs_id: 258
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.fam_
    sid_suffix: ''
    display_status: true
  - name: xt_qmt
    bs_id: 259
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.qmt_
    sid_suffix: ''
    display_status: true
    ignore_role_ids:
      - 90 # 忽略角色id
  - name: jz_rongzi_u
    bs_id: 260
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.mts_
    sid_suffix: ''
    display_status: true
  - name: jz_tougu
    bs_id: 261
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.iaf_
    sid_suffix: ''
    display_status: true
  - name: ty_changwai
    bs_id: 263
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.bct_
    sid_suffix: ''
    role_menu: 'auth_role_resource_perm'
    display_status: true
  - name: kr_jisukuaijiao
    bs_id: 262
    db_type: sqlite
    tnsname: kr_jisukuaijiao_db
    table_space: ''
    sid_suffix: ''
    display_status: true
  - name: oa_record
    db_type: oracle
    tnsname: dbcenter
    table_space: ods_bf.coa_
    table_space2: inf_check.coa_
    sid_suffix: ''
  - name: external1
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external2
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external3
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external4
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external5
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external6
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external7
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
  - name: external8
    db_type: dm
    tnsname: aas_db
    table_space: 'external_'
    sid_suffix: ''
development:
  <<: *defaults
test:
  <<: *defaults
production:
  <<: *defaults
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: odbc
      conn_str: DRIVER=DM8;SERVER=192.168.120.21;TCP_PORT=5236;UID=xinda;PWD=*********;ENCODING=utf8;
