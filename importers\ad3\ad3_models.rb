class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end
module Ad3
  def self.table_name_prefix
    'ad3_'
  end

  class Account < ActiveRecord::Base
    has_and_belongs_to_many :roles
    validates :code, presence: true
    validates :name, presence: true
    validates :quarter_id, presence: true
  end

  class Role < ActiveRecord::Base
    has_and_belongs_to_many :accounts
    validates :code, presence: true
    validates :name, presence: true
    validates :quarter_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end
end




