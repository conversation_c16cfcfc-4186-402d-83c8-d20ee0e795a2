module AasOracleImporter
  class TpyzqHrImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @position_users = [] # 拥有岗位的用户
      @bs_id = importer_config['bs_id']
      days_of_data = importer_config['days_of_data'] || 30
      @days_of_data = Time.now - days_of_data.to_i.days
    end

    def import_to_do
      import_hr
      create_or_update_departments
      create_or_update_users
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    HrData =
      Struct.new(:department_code, :department_name,
                 :user_code, :user_name, :email, :cellphone,
                 :position, :status_string, :departure_date, :id_number) do
        def inservice
          inservice_user_statuses.include? status_string
        end

        def present?
          user_code.present? && user_name.present? && !not_allow_user_statuses.include?(status_string)
        end

        # 系统中有客户经理，此类用户非正式员工，不录入
        def not_allow_user_statuses
          %w[103 303]
        end

        def inservice_user_statuses
          %w[1012 1011]
        end

        # 状态对照表
        # 客户经理 103
        # 离职-客户经理 303
        # 正式员工非试用期 1012
        # 离职-正式员工非试用期 3012
        # 正式员工试用期 1011
        # 离职-正式员工试用期 3011
        # 离职人员 30
        # 离职-正式员工 301
      end

    def import_hr_sql
      <<-EOF
        select 
          person_id, personname, cellphone, depname, posname, lnature, username 
          from hrmt.v_hr_jh
        where lnature != '103' AND lnature != '303'
      EOF
    end

    def import_hr
      @data = []

      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(import_hr_sql) do |row|
        @data << create_user_struct(row)
      end
    end

    def create_user_struct(row)
      code, name, cellphone, department_name, position, status_string,id_number  = row

      user                 = HrData.new
      user.user_code       = code
      user.user_name       = name
      user.cellphone       = cellphone
      user.department_code = department_name
      user.department_name = department_name
      user.position        = position
      user.status_string   = status_string
      user.id_number       = id_number
      user
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code)
      new_codes       = @data.map(&:department_code).uniq

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          department_struct = @data.find { |x| x.department_code == code }
          Department.create(
            code:      department_struct.department_code,
            name:      department_struct.department_name,
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department        = old_departments.find { |x| x.code == code }
          department_struct = @data.find { |x| x.department_code == code }

          department.update(name: department_struct.department_name)
        end
      end
    end

    def create_or_update_users
      departments = Department.all.reload.to_a
      old_users   = User.all.to_a
      old_codes   = old_users.map(&:code)
      new_codes   = @data.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          user_struct = @data.find { |x| x.user_code == code }
          department  = departments.find { |x| x.code == user_struct.department_code }

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            email:         user_struct.email,
            cellphone:     user_struct.cellphone,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id,
            id_number:     user_struct.id_number
          )

          @position_users << user if user.position?

          TpyzqOaNotify.create(
            user_id: user.id,
            notify_type: 'position_or_department',
            notify_user_ids: [],
            start_time: Time.now,
            content: {
              old_position: nil,
              old_department_id: nil
            },
            send_status: false
          )

        end
      end

      # 已有的更新
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @data.find { |x| x.user_code == user.code }
          next unless user_struct&.present?

          department = departments.find { |x| x.code == user_struct.department_code }

          old_position = user.position
          old_department_id = user.department_id

          user.update(
            name:          user_struct.user_name,
            email:         user_struct.email,
            cellphone:     user_struct.cellphone,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id,
            id_number:     user_struct.id_number
          )

          @position_users << user if user.position?

          if user.position.to_s != old_position.to_s || user.department_id.to_s != old_department_id.to_s
            TpyzqOaNotify.create(
              user_id: user.id,
              notify_type: 'position_or_department',
              notify_user_ids: [],
              start_time: Time.now,
              content: {
                old_position: old_position,
                old_department_id: old_department_id
              },
              send_status: false
            )
          end
        end
      end
    end

    def import_jobs_sql
      <<-EOF
        select distinct posname, lnature from hrmt.v_hr_jh where lnature != '103' AND lnature != '303'
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



