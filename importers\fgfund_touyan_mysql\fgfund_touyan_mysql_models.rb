module Fg<PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'fgfund_touyan_'
  end
end

class FgfundTouyan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :menu_permissions
end

class FgfundTouyan::MenuPermission < ActiveRecord::Base
  belongs_to :account, optional: true
  belongs_to :role, optional: true
  belongs_to :menu
end

class FgfundTouyan::Menu < ActiveRecord::Base
end

class FgfundTouyan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_many :menu_permissions
end

class FgfundTouyanAccountsRoles < ActiveRecord::Base; end
