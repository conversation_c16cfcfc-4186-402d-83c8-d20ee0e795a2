module AasOracleImporter
  class FgfundHrImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @company_map    = importer_config['company_map']
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      import_hr
      create_or_update_departments
      create_or_update_users

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    HrData =
      Struct.new(:user_code, :km_code, :user_name, :status_string, :join_date_string, :disable_date_string,
                 :company, :dep_code, :dep_name, :position) do

        class << self
          attr_accessor :company_map
        end

        def department_code
          dep_code
        end

        # 找到返回 abbr ，没找到返回全称
        def company_name
          map = self.class.company_map.find { |x| x['full_name'] == company }
          map&.[]('abbr') || company
        end

        def department_name
          if company_name == '总公司'
            dep_name
          else
            "#{company_name} / #{dep_name}"
          end
        end

        def inservice
          status_string == '在职'
        end

        def join_date
          Date.parse(join_date_string) if join_date_string.present?
        end

        def disable_date
          Date.parse(disable_date_string) if disable_date_string.present?
        end

        def present?
          user_code.present? && user_name.present?
        end
      end

    def import_hr_sql
      <<-SQL
        select  distinct HRBADGE, 
                KMACCOUNT, 
                NAME, 
                EMPSTATUS, 
                JOINDATE, 
                LEAVEDATE, 
                COMPANY, 
                DEPKMCODE, 
                DEPARTMENT, 
                JOB
        from hr_empl_info
        where TYPE = '主职'
      SQL
    end

    def import_hr
      @data = []
      HrData.company_map = @company_map
      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(import_hr_sql) { |row| @data << HrData.new(*row) }
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code)
      new_codes       = @data.map(&:dep_code).uniq

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          department_struct = @data.find { |x| x.dep_code == code }
          Department.create(
            code:      department_struct.dep_code,
            name:      department_struct.department_name,
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department        = old_departments.find { |x| x.code == code }
          department_struct = @data.find { |x| x.dep_code == code }

          department.update(name: department_struct.department_name)
        end
      end
    end

    def create_or_update_users
      departments = Department.all.reload.to_a
      old_users   = User.all.to_a
      old_codes   = old_users.map(&:code)
      new_codes   = @data.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          # @type [HrData]
          user_struct = @data.find { |x| x.user_code == code }
          department  = departments.find { |x| x.code == user_struct.dep_code }

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            join_date:     user_struct.join_date,
            disable_date:  user_struct.disable_date,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end

      # 已有的更新
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @data.find { |x| x.user_code == user.code }
          next unless user_struct&.present?

          department = departments.find { |x| x.code == user_struct.dep_code }
          user.update(
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            join_date:     user_struct.join_date,
            disable_date:  user_struct.disable_date,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end
    end

    def import_jobs_sql
      <<-EOF
        select distinct JOB from hr_empl_info where TYPE = '主职'
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



