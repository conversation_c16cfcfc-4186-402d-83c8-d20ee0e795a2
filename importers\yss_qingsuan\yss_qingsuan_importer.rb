module AasOracleImporter

  class YssQingsuanImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @table_space    = importer_config['table_space']
      @sid_suffix     = importer_config['sid_suffix']
      @import_bookset = bookset_config_or_default
      super
    end

    def bookset_config_or_default
      # 仅当配置返回 false 的时候才设为 false
      # 配置不存在返回 true
      return false if importer_config['import_bookset'] == false

      true
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :import_set_list_sql if @import_bookset == false
      ignore_list
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_set_permissions
      import_permissions_right
      import_permissions_qs
      import_permissions_menu
      import_ledgers(YssQingsuan::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def import_accounts_sql
      "SELECT FUSER,FUSERNAME,FDISABLED FROM #{@table_space}sysusers where FSETCODE = -1"
    end

    def import_accounts
      YssQingsuan::Account.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          next if r[0].to_s == ''

          YssQingsuan::Account.find_or_create_by(quarter_id: @quarter_id, code: r[0], name: r[1], status: r[2] == 0)
        end
      end
    end

    def import_roles_sql
      "SELECT FROLECODE,FROLENAME,FSYSTEMCODE FROM #{@table_space}sysrole"
    end

    def import_set_list_sql
      "SELECT fsetcode,fsetname FROM #{@table_space}CWSETLIST"
    end

    def import_user_rights_for_role_sql
      "select * from #{@table_space}SYSUSERRIGHT where FRIGHTIND = 'Role'"
    end

    def import_roles
      YssQingsuan::Role.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          system_type = r[2] == 2000 ? "清算" : "财务"
          if r[0].to_s != ''
            YssQingsuan::Role.find_or_create_by(quarter_id: @quarter_id, code: r[0], name: "#{system_type}-#{r[1]}")
          end
        end
      end
    end

    def import_set_permissions
      return unless @import_bookset

      set_list_json = {}
      @database.exec(import_set_list_sql) do |r|
        set_list_json[r[0].to_s] = r[1]
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_user_rights_for_role_sql) do |r|
          import_a_set_permission(r, set_list_json)
        end
      end
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_user_rights_for_role_sql) do |row|
          import_account_role_link(row)
        end
      end
    end

    def import_account_role_link(row)
      account_code, _set_code, role_code = row
      role = YssQingsuan::Role.find_by(quarter_id: @quarter_id, code: role_code)
      return unless role

      account = YssQingsuan::Account.find_by(quarter_id: @quarter_id, code: account_code)
      return unless account

      account.roles << role unless account.roles.include?(role)
    end

    def import_a_set_permission(row, set_list_json)
      account_code, set_code, role_code = row
      role = YssQingsuan::Role.find_by(quarter_id: @quarter_id, code: role_code.to_s)
      return unless role

      account = YssQingsuan::Account.find_by(quarter_id: @quarter_id, code: account_code.to_s)
      return unless account

      return unless set_code.to_i.positive? && set_list_json[set_code.to_s]

      permission =
        YssQingsuan::Permission.find_or_create_by(
          quarter_id:      @quarter_id,
          code:            "set_list-#{set_code}",
          name:            set_list_json[set_code.to_s],
          permission_type: "#{role.name.to_s.split('-')[0]}-套账"
        )

      YssQingsuan::AccountsRolesPermission.find_or_create_by(
        quarter_id:    @quarter_id,
        permission_id: permission.id,
        account_id:    account.id
      )
    end

    def right_type_sql
      "select FRIGHTTYPECODE,FRIGHTTYPENAME,FFUNMODULENAME,FSYSTEMCODE from #{@table_space}SYSRIGHTTYPE"
    end

    # 角色操作权限
    def role_right_sql
      "select * from #{@table_space}SYSROLERIGHT"
    end

    # 账号操作权限
    def user_rights_sql
      "select * from #{@table_space}SYSUSERRIGHT where FRIGHTIND = 'Right'"
    end

    def import_permissions_right
      ActiveRecord::Base.transaction do
        permission_name_json = set_permission_name_json
        @database.exec(right_type_sql) do |r|
          system_type = r[3] == 2000 ? "清算" : "财务"
          if r[0].to_s != ''
            permission = YssQingsuan::Permission.find_or_create_by(quarter_id: @quarter_id, code: "right-#{r[0]}", name: "#{r[2]}-#{r[1]}", permission_type: "#{system_type}-操作权限")
          end
        end

        @database.exec(role_right_sql) do |r|
          role       = YssQingsuan::Role.find_by(quarter_id: @quarter_id, code: r[0])
          permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "right-#{r[1]}")
          if role && permission
            role_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
              quarter_id:    @quarter_id,
              permission_id: permission.id,
              role_id:       role.id
            )
            if true || r[2].to_s != ""
              r[2].split(',').each do |opertypes|
                if permission_name_json[opertypes]
                  additional_permission_list            = role_permission.additional_permission.to_s.split(',')
                  role_permission.additional_permission = (additional_permission_list | [permission_name_json[opertypes]]).sort.join(",")
                  role_permission.save
                end
              end
            end
          end
        end

        #账号操作权限
        @database.exec(user_rights_sql) do |r|
          account    = YssQingsuan::Account.find_by(quarter_id: @quarter_id, code: r[0])
          permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "right-#{r[2]}")
          if account && permission
            account_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
              quarter_id:    @quarter_id,
              permission_id: permission.id,
              account_id:    account.id
            )
            if r[3].to_s != ""
              r[3].split(',').each do |opertypes|
                if permission_name_json[opertypes]
                  additional_permission_list               = account_permission.additional_permission.to_s.split(',')
                  account_permission.additional_permission = (additional_permission_list | [permission_name_json[opertypes]]).sort.join(",")
                  account_permission.save
                end
              end
            end
          end
        end
      end
    end

    # 数据接口权限
    def data_qs_sql
      "select FCUSCFGCODE,FCUSCFGNAME from #{@table_space}QSCUSCONFIG"
    end

    # 角色接口权限
    def role_interface_sql
      "select * from #{@table_space}SYSROLEINTERFACE"
    end

    # 用户接口权限
    def user_interface_sql
      "select * from #{@table_space}SYSUSERINTERFACE"
    end

    def import_permissions_qs
      ActiveRecord::Base.transaction do
        qsgroup_json, group_json = set_qsgroup_json
        @database.exec(data_qs_sql) do |r|
          permission = YssQingsuan::Permission.find_or_create_by(quarter_id: @quarter_id, code: "qs-#{r[0]}", name: "#{qsgroup_json[r[0]]}-#{r[1]}", permission_type: "清算-数据接口权限")
        end

        @database.exec(role_interface_sql) do |r|
          role = YssQingsuan::Role.find_by(quarter_id: @quarter_id, code: r[0])
          if role
            if group_json[r[2]]
              group_json[r[2]].each do |qs|
                permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "qs-#{qs}")
                if permission
                  role_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
                    quarter_id:    @quarter_id,
                    permission_id: permission.id,
                    role_id:       role.id
                  )
                end
              end
            else
              permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "qs-#{r[2]}")
              if permission
                role_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
                  quarter_id:    @quarter_id,
                  permission_id: permission.id,
                  role_id:       role.id
                )
              end
            end
          end
        end

        @database.exec(user_interface_sql) do |r|
          account = YssQingsuan::Account.find_by(quarter_id: @quarter_id, code: r[0])
          if account
            if group_json[r[2]]
              group_json[r[2]].each do |qs|
                permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "qs-#{qs}")
                if permission
                  account_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
                    quarter_id:    @quarter_id,
                    permission_id: permission.id,
                    account_id:    account.id
                  )
                end
              end
            else
              permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "qs-#{r[2]}")
              if permission
                account_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
                  quarter_id:    @quarter_id,
                  permission_id: permission.id,
                  account_id:    account.id
                )
              end
            end
          end

        end
      end
    end

    def menu_sql
      "select FMENUCODE,FMENUNAME,FPARENTCODE,FSYSTEMCODE from #{@table_space}SYSMENU"
    end

    def role_menu_sql
      "select * from #{@table_space}SYSROLEMENU"
    end

    def user_menu_sql
      "select * from #{@table_space}SYSUSERMENU"
    end

    def import_permissions_menu
      ActiveRecord::Base.transaction do

        @database.exec(menu_sql) do |r|
          system_type = r[3] == 2000 ? "清算" : "财务"
          if r[0].to_s != ''
            parent_code = r[2].to_s == '[root]' ? nil : "menu-#{r[2]}"
            permission  = YssQingsuan::Permission.find_or_create_by(quarter_id: @quarter_id, code: "menu-#{r[0]}", name: r[1], parent_code: parent_code, permission_type: "#{system_type}-菜单权限")
          end
        end

        @database.exec(role_menu_sql) do |r|
          role       = YssQingsuan::Role.find_by(quarter_id: @quarter_id, code: r[0])
          permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "menu-#{r[1]}")
          if role && permission
            role_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
              quarter_id:    @quarter_id,
              permission_id: permission.id,
              role_id:       role.id
            )
          end
        end

        @database.exec(user_menu_sql) do |r|
          account    = YssQingsuan::Account.find_by(quarter_id: @quarter_id, code: r[0])
          permission = YssQingsuan::Permission.find_by(quarter_id: @quarter_id, code: "menu-#{r[1]}")
          if account && permission
            account_permission = YssQingsuan::AccountsRolesPermission.find_or_create_by(
              quarter_id:    @quarter_id,
              permission_id: permission.id,
              account_id:    account.id
            )
          end
        end
      end
    end

    def qs_group_sql
      "select FGROUPCODE,FGROUPNAME,FCUSCONFIGCODES from #{@table_space}QSGROUP"
    end

    def set_qsgroup_json
      output_json = {}
      group_json  = {}
      @database.exec(qs_group_sql) do |r|
        next if r[2].nil?

        r[2].split(',').each do |code|
          output_json[code] = r[1]
        end
        group_json[r[0]] = r[2].split(',')
      end
      return output_json, group_json
    end

    def operation_sql
      "select * from #{@table_space}SYSOPERATION"
    end

    def set_permission_name_json
      output_json = {}
      @database.exec(operation_sql) do |r|
        output_json[r[0]] = r[1]
      end
      output_json
    end

  end
end



