# frozen_string_literal: true

module AasOracleImporter
  # 远程柜台系统导入
  class YuanchengGuitaiImporter < ZhixiaoImporter
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @sub_system  = importer_config['sub_system']

      initialize_tables
      initialize_classes
    end

    def initialize_classes
      @account_class          = YuanchengGuitai::Account
      @role_class             = YuanchengGuitai::Role
      @menu_class             = YuanchengGuitai::Menu
      @other_permission_class = YuanchengGuitai::OtherPermission

      @account_role_associate_class   = YuanchengGuitaiAccountsRoles
      @account_menu_associate_class   = YuanchengGuitaiAccountsMenus
      @role_menu_associate_class      = YuanchengGuitaiMenusRoles
      @role_other_permission_class    = YuanchengGuitaiOtherPermissionsRoles
      @account_other_permission_class = YuanchengGuitaiAccountsOtherPermissions
    end
  end
end



