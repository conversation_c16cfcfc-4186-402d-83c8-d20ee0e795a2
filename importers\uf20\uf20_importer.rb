module AasOracleImporter

  class Uf20Importer < ImporterBase

    def config
      @bs_id = 58
      @accounts = []
      @roles = []
      @accounts_roles = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      @data4_permissions = []
      @data4_accounts_roles_permissions = []

      @data5_permissions = []
      @data5_accounts_roles_permissions = []

      @data6_permissions = []
      @data6_accounts_roles_permissions = []

      @table_space = importer_config['table_space']
      @sid_suffix = importer_config['sid_suffix']

      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      @data4_permission_data = get_data4_permission_data
      @account_role_datas = get_account_role_datas
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_role_permissions

      import_data3_permissions
      import_data3_account_permissions

      import_data4_permissions
      import_data4_account_permissions

      import_data5_permissions
      import_data5_account_permissions

      import_data6_permissions
      import_data6_account_permissions

      import_ledgers(Uf20::Account)
    end

    def destroy_exist_datas
      accounts = Uf20::Account.where(quarter_id: @quarter_id)
      Uf20::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Uf20::Role.where(quarter_id: @quarter_id).delete_all
      Uf20::Data1Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data2Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data3Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data3AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data4Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data4AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data5Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data5AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data6Permission.where(quarter_id: @quarter_id).delete_all
      Uf20::Data6AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select operator_no, operator_name,oper_status,id_no from #{@table_space}operators#{@sid_suffix}
      EOF
    end

    def import_accounts

      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Uf20::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            name: get_enum(enums, "name", r[1]),
            status: r[2].to_s == '0',
            id_number: r[3]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select t1.user_id,t1.oper_roles,t2.branch_name from #{@table_space}userright#{@sid_suffix} t1
        left join #{@table_space}allbranch#{@sid_suffix} t2 on t1.branch_no = t2.branch_no
      EOF
    end

    def get_account_role_datas
      output_data = {}
      select_db_datas(import_accounts_roles_sql).each do |r|
        role_str = r[1].to_s
        role_ids = []
        role_str.chars.each_with_index{|v,i| role_ids << i + 1 if v == '1'}
        role_ids.each do |role_id|
          output_data[r[0].to_s] = {} unless output_data[r[0].to_s]
          output_data[r[0].to_s][role_id.to_s] = [] unless output_data[r[0].to_s][role_id.to_s]
          output_data[r[0].to_s][role_id.to_s] |= [r[2]]
        end
      end
      output_data
    end

    def import_roles_sql
      <<-EOF
        select role_id, role_name from #{@table_space}roles#{@sid_suffix}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Uf20::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[0]), name: get_enum(enums, "name", r[1]))
        end
      end
    end

    def import_accounts_roles
      @accounts.each do |account|
        if @account_role_datas[account.source_id.to_s]
          @account_role_datas[account.source_id.to_s].each do |role_id,v|
            role = @roles.find{|x| x.source_id.to_s == role_id.to_s}
            if role
              Uf20::AccountsRole.create(account_id: account.id, role_id: role.id)
            end
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select role_id, role_name from #{@table_space}roles#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data1_permissions << Uf20::Data1Permission.create(json)
        end
      end
    end

    def import_data1_account_permissions
      @account_role_datas.each do |account_id, roles|
        account = @accounts.find{|x| x.source_id.to_s == account_id.to_s}
        roles.each do |role_id, branchs|
          permission = @data1_permissions.find{|x| x.source_id.to_s == role_id.to_s}
          if account && permission
            branchs.each do |branch|
              Uf20::Data1AccountsRolesPermission.create(
                quarter_id: @quarter_id,
                account_id: account.id,
                data1_permission_id: permission.id,
                additional_permission: branch
              )
            end
          end
        end
      end

    end

    def import_data2_permissions_sql
      <<-EOF
        select MENU_ID, MENU_CAPTION from #{@table_space}hsmenu#{@sid_suffix}
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data2_permissions << Uf20::Data2Permission.create(json)
        end
      end

    end

    def import_data2_role_permissions_sql
      <<-EOF
        select t1.MENU_ID,t2.FUNCTION_NAME,t1.ROLE_RIGHTS from #{@table_space}functiontomenu#{@sid_suffix} t1
        left join #{@table_space}hsfunction#{@sid_suffix} t2 on t1.FUNCTION_ID = t2.FUNCTION_ID
      EOF
    end

    def import_data2_role_permissions
      enums = []
      select_db_datas(import_data2_role_permissions_sql).each do |r|
        permission = @data2_permissions.find{|x| x.source_id.to_s == r[0].to_s}
        role_str = r[2].to_s
        role_ids = []
        role_str.chars.each_with_index{|v,i| role_ids << i + 1 if v == '1'}
        role_ids.each do |role_id|
          role = @roles.find{|x| x.source_id.to_s == role_id.to_s}
          if role && permission# && !@data2_accounts_roles_permissions.find{|x| x.role_id == role.id && x.data2_permission_id == permission.id && x.additional_permission == r[1]}
            Uf20::Data2AccountsRolesPermission.create(
              quarter_id: @quarter_id,
              role_id: role.id,
              data2_permission_id: permission.id,
              additional_permission: r[1]
            )
          end
        end
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select b.subentry,b.dict_prompt from #{@table_space}sysdictionary#{@sid_suffix} b where b.dict_entry='1502'
      EOF
    end

    def import_data3_permissions
      enums = []

      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data3_permissions << Uf20::Data3Permission.create(json)
        end
      end

    end

    def import_data3_account_permissions_sql
      <<-EOF
        select USER_ID, operator_rights from #{@table_space}USERRIGHT#{@sid_suffix}
      EOF
    end

    def import_data3_account_permissions
      enums = []
      account_json = {}
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account_json[r[0].to_s] = [] unless account_json[r[0].to_s]
        account_json[r[0].to_s] |= r[1].to_s.chars
      end

      account_json.each do |account_id, permissions|
        account = @accounts.find{|x| x.source_id.to_s == account_id.to_s}
        permissions.each do |permission_id|
          permission = @data3_permissions.find{|x| x.source_id.to_s == permission_id.to_s}
          if account && permission
            Uf20::Data3AccountsRolesPermission.create(
              quarter_id: @quarter_id,
              account_id: account.id,
              data3_permission_id: permission.id
            )
          end
        end
      end
    end

    def import_data4_permissions
      enums = []

      ActiveRecord::Base.transaction do
        @data4_permission_data.each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[1]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[2]
          }
          @data4_permissions << Uf20::Data4Permission.create(json)
        end
      end
    end

    def import_data4_account_permissions_sql
      <<-EOF
        select USER_ID,EN_CLIENT_GROUP, EN_ROOM_CODE, EN_EXCHANGE_TYPE, EN_STOCK_TYPE, EN_ENTRUST_WAY, EN_BANK_NO, EN_MONEY_TYPE, EN_ASSET_PROP from #{@table_space}userright#{@sid_suffix}
      EOF
    end

    def import_data4_account_permissions
      enums = []
      select_db_datas(import_data4_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        i = 1
        while i <= 8
          permission = @data4_permissions.find{|x| x.source_id.to_s == @data4_permission_data[i-1][1].to_s}
          additional_permission = []
          r[i].to_s.split(",").each do |code|
            if @data4_permission_data[i-1][3][code]
              Uf20::Data4AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data4_permission_id: permission.id, additional_permission: @data4_permission_data[i-1][3][code])
            end
          end
          i += 1
        end
      end
    end

    def get_data4_permission_data
      [
        ['EN_CLIENT_GROUP', 1051,'客户分类', get_data4_data(1051)],
        ['EN_ROOM_CODE', 1052,'客户分组', get_data4_data(1052)],
        ['EN_EXCHANGE_TYPE', 1301,'交易类别', get_data4_data(1301)],
        ['EN_STOCK_TYPE', 1206,'证券类别', get_data4_data(1206)],
        ['EN_ENTRUST_WAY', 1201,'委托方式', get_data4_data(1201)],
        ['EN_BANK_NO', 1601,'银行代码', get_data4_data(1601)],
        ['EN_MONEY_TYPE', 1101,'币种类别', get_data4_data(1101)],
        ['EN_ASSET_PROP', 3002,'资产属性', get_data4_data(3002)]
      ]
    end

    def get_data4_data(code)
      output_data = {}
      sql = "select b.subentry,b.dict_prompt from #{@table_space}sysdictionary#{@sid_suffix} b where b.dict_entry='#{code}'"
      select_db_datas(sql).each do |r|
        output_data[r[0]] = r[1]
      end
      output_data
    end

    def import_data5_permissions_sql
      <<-EOF
        select role_id, role_name from #{@table_space}roles#{@sid_suffix}
      EOF
    end

    def import_data5_permissions
      enums = []

      ActiveRecord::Base.transaction do
        select_db_datas(import_data5_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data5_permissions << Uf20::Data5Permission.create(json)
        end
      end

    end

    def import_data5_account_permissions_sql
      <<-EOF
        select t1.USER_ID,t2.branch_name,ROLE_ID,END_DATE from #{@table_space}USERROLESLIMIT#{@sid_suffix} t1
        left join #{@table_space}allbranch#{@sid_suffix} t2 on t1.branch_no = t2.branch_no
      EOF
    end

    def import_data5_account_permissions
      enums = []
      select_db_datas(import_data5_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data5_permissions.find{|x| x.source_id.to_s == r[2].to_s}
        if account && permission
          Uf20::Data5AccountsRolesPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            data5_permission_id: permission.id,
            additional_permission: r[1],
            data_json: { end_date: r[3] }
          )
        end
      end
    end

    def import_data6_permissions_sql
      <<-EOF
        select AUTH_BIND_STATION from #{@table_space}userstation#{@sid_suffix}
      EOF
    end

    def import_data6_permissions
      enums = []

      ActiveRecord::Base.transaction do
        select_db_datas(import_data6_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[0]
          }
          @data6_permissions << Uf20::Data6Permission.create(json)
        end
      end

    end

    def import_data6_account_permissions_sql
      <<-EOF
        select USER_ID, AUTH_BIND_STATION ,BIND_DATE,VALID_DATE from #{@table_space}userstation#{@sid_suffix}
      EOF
    end

    def import_data6_account_permissions
      enums = []
      select_db_datas(import_data6_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data6_permissions.find{|x| x.source_id.to_s == r[1].to_s}
        if account && permission
          Uf20::Data6AccountsRolesPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            data6_permission_id: permission.id,
            data_json: {bind_date: r[2], valid_date: r[3]}
          )
        end
      end
    end



    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
