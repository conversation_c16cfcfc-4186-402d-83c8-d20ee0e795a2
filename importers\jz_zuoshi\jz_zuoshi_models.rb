module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'jz_zuoshi_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string
    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class JzZuoshi::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :data1_accounts_roles_permissions
  has_many :data2_accounts_roles_permissions
  has_many :data3_accounts_roles_permissions
  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end


class JzZuoshi::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_many :data1_accounts_roles_permissions
  has_many :data2_accounts_roles_permissions
  has_many :data3_accounts_roles_permissions
  validates :code, presence: true
  validates :name, presence: true
end


  
  class JzZuoshi::Data1Permission < ActiveRecord::Base
    validates :code, presence: true
    validates :level1_name, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  class Jz<PERSON>uoshi::Data1AccountsRolesPermission < ActiveRecord::Base
    belongs_to :data1_permission
    belongs_to :role, optional: true
    validates :data1_permission_id, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  
  class JzZuoshi::Data2Permission < ActiveRecord::Base
    validates :code, presence: true
    validates :level1_name, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  class JzZuoshi::Data2AccountsRolesPermission < ActiveRecord::Base
    belongs_to :data2_permission
    belongs_to :role, optional: true
    validates :data2_permission_id, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  
  class JzZuoshi::Data3Permission < ActiveRecord::Base
    validates :code, presence: true
    validates :level1_name, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  class JzZuoshi::Data3AccountsRolesPermission < ActiveRecord::Base
    belongs_to :data3_permission
    belongs_to :role, optional: true
    validates :data3_permission_id, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end

  class JzZuoshi::Data4Permission < ActiveRecord::Base
    validates :code, presence: true
    validates :level1_name, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  class JzZuoshi::Data4AccountsRolesPermission < ActiveRecord::Base
    belongs_to :data4_permission
    belongs_to :role, optional: true
    validates :data4_permission_id, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end

  class JzZuoshi::Data5Permission < ActiveRecord::Base
    validates :code, presence: true
    validates :level1_name, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end


  class JzZuoshi::Data5AccountsRolesPermission < ActiveRecord::Base
    belongs_to :data5_permission
    belongs_to :role, optional: true
    validates :data5_permission_id, presence: true
    serialize :data_json, JsonWithSymbolizeNames
  end
