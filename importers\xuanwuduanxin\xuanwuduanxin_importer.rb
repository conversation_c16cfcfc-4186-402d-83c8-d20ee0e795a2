module AasOracleImporter
  class XuanwuduanxinImporter < ImporterBase
    def config
      @bs_id       = 287
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @db_type     = importer_config['db_type']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}gsms_user#{@sid_suffix}"
      @table_role         = "#{@table_space}gsms_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}gsms_user_role#{@sid_suffix}"
      @table_menu         = "#{@table_space}gsms_permission#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}gsms_role_permission#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions

      import_ledgers(Xuanwuduanxin::Account)
    end

    def destroy_exist_datas
      accounts = Xuanwuduanxin::Account.where(quarter_id: @quarter_id)
      Xuanwuduanxin::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Xuanwuduanxin::Role.where(quarter_id: @quarter_id).delete_all
      Xuanwuduanxin::Data1Permission.where(quarter_id: @quarter_id).delete_all
      Xuanwuduanxin::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select id, user_name, link_man, state from #{@table_account} where type = 2 and user_name is not null and link_man is not null
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Xuanwuduanxin::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id, name, role_type from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << Xuanwuduanxin::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            role_type:  get_role_type(r[3])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          Xuanwuduanxin::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, id, menu_name, type, parent_id from #{@table_menu} where display = 1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[4]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, 2, 4))

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: get_type_text(r[3])
          }
          @data1_permissions << Xuanwuduanxin::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, permission_id, data_scope from #{@table_role_menu} where data_scope = 1
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          Xuanwuduanxin::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      limit = (@db_type.to_s == 'oracle' ? 'and rownum = 1' : 'limit 1').to_s
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' #{limit}"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 角色类型
    def get_role_type(code)
      case code.to_s
      when '0' then '实际功能点'
      when '1' then '虚拟功能点'
      when '9' then '超级管理员'
      end
    end

    # 附加权限
    def additional_permission_text(code)
      case code.to_s
      when '1' then '个人'
      when '2' then '本部门'
      when '3' then '本部门及子部门'
      when '4' then '全局'
      end
    end

    # 类型
    def get_type_text(code)
      case code.to_s
      when '0' then '未定义'
      when '1' then '新增'
      when '2' then '修改'
      when '3' then '删除'
      when '4' then '发送'
      when '5' then '审批'
      when '6' then '导入'
      when '7' then '导出'
      when '8' then '登录'
      when '9' then '退出'
      when '10' then '取消'
      end
    end
  end
end
