# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'mszq'
  customer: '民生证券'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_mszq_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    # oracle:
    #   # tnsname
    #   o32db:
    #     db_host: '**********'
    #     db_name: 'trade'
    #     db_user: 'o3query'
    #     db_pass: 'o3query'

    mysql:
      oa_032_db:
        db_host: ************
        db_name: data_center
        db_user: sjzxDB_2021
        db_pass: MszQ1748@#@
      dc_db:
        db_host: ************
        db_name: data_center
        db_user: sjzxDB_2021
        db_pass: MszQ1748@#@
      aas_db:
        db_host: ************
        db_name: aas_mszq_production
        db_user: aasDB_2021
        db_pass: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"

    # sqlserver:
    #   jiayang_hr:
    #     db_host: '********'
    #     db_user: 'hrtest'
    #     db_pass: 'hrtest@123'
    #     db_name: 'MSZQ'

  importers:
    - name: jiayang_hr
      bs_id: 3
      db_type: mysql
      tnsname: oa_032_db
      table_space: 'hr_'
      filter_user: # 过滤用户规则，blank_column空字段，regexp为正则
        blank_column:
          - department_id
        regexp:
          code: ^[S|s].*
          
    - name: didaima
      bs_id: 25
      db_type: mysql
      tnsname: oa_032_db
      table_space: 'cloudpivot_'
      sid_suffix: ''

    - name: jiaoyi
      bs_id: 31
      db_type: mysql
      tnsname: oa_032_db
      table_space: 'o32_'
      sid_suffix: ''
      #temporary: true
      #days_of_data: 90
      # 导入员工号
      operator_no_importer: true
      # 交易类型
      trade_type_importer: true
      # 站点权限
      station_importer: true
      c_menu_type: [1,9]

    # 清算系统
    - name: qingsuan_department
      bs_id: 25
      db_type: mysql
      tnsname: oa_032_db
      table_space: 'cis_'
      sid_suffix: ''

    - name: hspb
      bs_id: 52
      db_type: mysql
      tnsname: oa_032_db
      table_space: hspbdbdg_
      sid_suffix: ''
      trade_type_importer: true
      station_importer: true
    - name: qd_aas
      bs_id: 10104
      db_type: mysql
      tnsname: aas_db
    - name: zhengzhou
      bs_id: 10111
      db_type: excel
    - name: shanghai_jinqiao
      bs_id: 10112
      db_type: excel
    - name: shanghai_zongbu
      bs_id: 10113
      db_type: excel
    - name: yizhuang_dev
      bs_id: 10115
      db_type: excel
    - name: yizhuang_pro
      bs_id: 10114
      db_type: excel

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_mszq_production
      username: aasDB_2021
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: ************




