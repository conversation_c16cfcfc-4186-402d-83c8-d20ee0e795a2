# frozen_string_literal: true

require 'rspec'
require_relative '../lib/aas_oracle_importer/workday/second_workday_in_every_quarter'

describe AasOracleImporter::Workday::SecondWorkdayInEveryQuarter do
  # 这里不能用 let，因为 let 只能在 it 中生效，在 it 外无法访问
  WORKDAYS = %w[
    2020-1-7 2020-4-2 2020-7-2 2020-10-12
    2021-1-5 2021-4-2 2021-7-2 2021-10-11
   ].freeze

  let(:klass) { AasOracleImporter::Workday::SecondWorkdayInEveryQuarter }

  context 'date in WORKDAYS should be workday' do
    WORKDAYS.each do |the_date|
      it "#{the_date} is workday" do
        date = Date.parse(the_date)
        expect(klass.new(date, {}).workday?).to be true
      end
    end
  end

  context 'if date not in workdays, all should not be workday' do
    date_workdays = WORKDAYS.map { |x| Date.parse(x) }
    first_day = Date.parse('2020-1-1')
    last_day  = Date.parse('2021-12-31')
    (first_day..last_day).each do |the_date|
      next if date_workdays.include?(the_date)

      it "#{the_date} is not workday" do
        expect(klass.new(the_date, {}).workday?).to be false
      end
    end
  end
end
