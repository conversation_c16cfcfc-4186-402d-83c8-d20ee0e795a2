module AasOracleImporter
  class YssXinpiImporter < ImporterBase
    def config
      @bs_id       = 307
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account       = "#{@table_space}T_USER#{@sid_suffix}"
      @table_role          = "#{@table_space}T_ACL_ROLE#{@sid_suffix}"
      @table_account_role  = "#{@table_space}T_ACL_USERROLE#{@sid_suffix}"
      @table_menu          = "#{@table_space}T_APPLICATION#{@sid_suffix}"
      @table_menu2         = "#{@table_space}T_APPLICATIONFUNCTION#{@sid_suffix}"
      @table_account_menu  = "#{@table_space}T_PORTAL_APP_CONF#{@sid_suffix}"
      @table_menu3         = "#{@table_space}VB_PORT_BASEINFO#{@sid_suffix}"
      @table_account_menu2 = "#{@table_space}T_USER_PORT_CODE#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_account_permissions
      import_data2_permissions
      import_data2_account_permissions
      import_ledgers(YssXinpi::Account)
    end

    def destroy_exist_datas
      accounts = YssXinpi::Account.where(quarter_id: @quarter_id)
      YssXinpi::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      YssXinpi::Role.where(quarter_id: @quarter_id).delete_all
      YssXinpi::Data1Permission.where(quarter_id: @quarter_id).delete_all
      YssXinpi::Data2Permission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select FID, FLOGIN_CODE, FNAME, FLOCK_STATE, FCHECK_STATE from #{@table_account} where FDELETE_STATE = 0
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          # 通过两个字段判断状态
          status = r[3]&.to_s == '0' && r[4]&.to_s == '1'
          @accounts << YssXinpi::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     status
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select FID, FROLE_CODE, FROLE_NAME from #{@table_role} where FCHECK_STATE = 1
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << YssXinpi::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select FROLE_ID, FUSER_ID from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          YssXinpi::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select FAPP_ID, FID, CONCAT((select FNAME from #{@table_menu} where FID = af.FAPP_ID and rownum <= 1), CONCAT('-', FNAME)) from #{@table_menu2} af
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << YssXinpi::Data1Permission.create(json)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select FUSER_ID, FAPP_ID from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        YssXinpi::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select C_PORT_CODE, C_PORT_CODE, C_PORT_NAME, C_PORT_TYPE from #{@table_menu3} where N_STATE = 1
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: level2_name_text(r[3])
          }
          @data2_permissions << YssXinpi::Data2Permission.create(json)
        end
      end
    end

    # 枚举值
    def level2_name_text(name)
      case name
      when 'QDII' then '环球'
      when 'ZQTZJJ' then '证券投资基金'
      when 'ZCGLJH' then '资产管理计划'
      when 'DX' then '定向'
      when 'SBJJ' then '社保基金'
      else
        name
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select N_USER_ID, C_PORT_CODE from #{@table_account_menu2}
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        YssXinpi::Data2AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data2_permission_id: permission.id
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
