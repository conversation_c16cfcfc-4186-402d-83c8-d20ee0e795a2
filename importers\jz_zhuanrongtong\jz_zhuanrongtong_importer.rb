module AasOracleImporter
  class JzZhuanrongtongImporter < ImporterBase
    def config
      @bs_id       = 250
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      user_table_space    = importer_config['user_table_space']
      table_space         = user_table_space.nil? ? @table_space : user_table_space
      @table_account      = "#{@table_space}OPERATOR#{@sid_suffix}"
      @table_user         = "#{table_space}USERS#{@sid_suffix}"
      @table_role         = "#{@table_space}POST#{@sid_suffix}"
      @table_account_role = "#{@table_space}USER_POST#{@sid_suffix}"
      @table_menu         = "#{@table_space}sys_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}post_menu#{@sid_suffix}"
      @table_account_menu = "#{@table_space}user_menu#{@sid_suffix}"
      @table_org          = "#{@table_space}org#{@sid_suffix}"
      @table_user_org     = "#{@table_space}user_rtobj#{@sid_suffix}"
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_ledgers(JzZhuanrongtong::Account)
    end

    def import_accounts_sql
      <<-EOF
        SELECT A.USER_CODE, A.USER_CODE,A.USER_NAME,B.OP_STATUS FROM #{@table_user} A, #{@table_account} B WHERE A.USER_CODE=B.OP_CODE
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          account = JzZhuanrongtong::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzZhuanrongtong::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        SELECT POST_CODE,POST_CODE,POST_NAME FROM #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzZhuanrongtong::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        SELECT POST_CODE,USER_CODE, RIGHT_TYPE FROM #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          JzZhuanrongtong::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        SELECT MENU_CODE,MENU_CODE,MENU_NAME FROM #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << JzZhuanrongtong::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        SELECT POST_CODE, MENU_CODE, RIGHT_TYPE FROM #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzZhuanrongtong::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id,
            permission_scope:    permission_scope_text(r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        SELECT USER_CODE, MENU_CODE, RIGHT_TYPE FROM #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzZhuanrongtong::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id,
          permission_scope:    r[2]
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select ORG_CODE,ORG_CODE,ORG_NAME from #{@table_org}
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzZhuanrongtong::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        SELECT A.USER_CODE, A.RTOBJ, A.GRANT_TYPE FROM #{@table_user_org} A, #{@table_org} B WHERE A.RTOBJ=to_char(B.ORG_CODE)
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzZhuanrongtong::Data2AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data2_permission_id: permission.id,
          permission_scope:    second_permission_scope_text(r[2])
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def permission_scope_text(name)
      case name.to_s
      when '0' then '执行权限'
      when '1' then '授权权限'
      when '2' then '执行和授权权限'
      when '3' then '执行和导出权限'
      when '4' then '执行、导出、授权'
      end
    end

    def second_permission_scope_text(name)
      case name.to_s
      when '0' then '禁止'
      when '1' then '允许'
      end
    end
  end
end
