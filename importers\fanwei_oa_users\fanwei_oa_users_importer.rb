module AasOracleImporter
  class FanweiOaUsersImporter < ImporterBase

    def config
      @bs_id             = importer_config['bs_id']
      @table_space       = importer_config['table_space']
      @sid_suffix        = importer_config['sid_suffix']
      @clear_name_regexp = importer_config['clear_name_regexp']
      @position_users    = [] # 拥有岗位的用户
      @code_column       = importer_config['code_column']
      @syfund_o32temp_import = importer_config['syfund_o32temp_import']
      @syfund_o32temp_recover_import = importer_config['syfund_o32temp_recover_import']
      @syfund_o32temp_import_day = importer_config['syfund_o32temp_import_day'].to_i
    end

    def import_to_do
      import_hr
      create_or_update_departments
      create_or_update_users
      link_manager
      update_user_name_clear

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
      syfund_o32temp_import if @syfund_o32temp_import
      syfund_o32temp_recover_import if @syfund_o32temp_recover_import
    end

    def syfund_o32temp_import_sql
      <<-SQL
        SELECT
          a.lcbh,
          a.sqr,
          a.bsqr,
          b.sqqsrq,
          b.sqqssj,
          b.sqdqrq,
          b.sqdqsj,
          a.sqpzfw,
          a.cpdm,
          a.bz
        FROM
          #{@table_space}formtable_main_254#{@sid_suffix} a
        left join
          #{@table_space}formtable_main_254_dt1#{@sid_suffix} b
        on a.id = b.mandid

        WHERE
          a.sqqsrq > #{Date.today - @syfund_o32temp_import_day.day}
      SQL
    end

    def syfund_o32temp_import
      @database.exec(syfund_o32temp_import_sql) do |r|
        user = User.find_by(code: r[2])
        authorizer_user = User.find_by(code: r[1])
        next unless user && authorizer_user
        r[8].split(",").each do |fund_code|
          next if fund_code.blank? || r[3].blank?
          flow = Jiaoyi::TemporaryFlow.find_or_create_by(
            user_id:             user.id,
            authorizer_user_id:  authorizer_user.id,
            flow_id:             r[0],
            permission_type:     'fund',
            permission_code:     fund_code,
            #unit_code: nil
            #combination_code: nil
            #addition_code: nil
            start_time: "#{r[3]} #{r[4]}",
            end_time: "#{r[5]} #{r[6]}"
            #recover_start_time: 
            #recover_end_time:
            #flow_status:
            #recover_status:
          )

          account = Jiaoyi::Account.find_by(user_id: user.id).last
          authorizer_account = Jiaoyi::Account.find_by(user_id: authorizer_user.id).last
          fund = Jiaoyi::Fund.where(fund_code: fund_code).last
          temps = Jiaoyi::Temporary.where(
            account_code: account&.code,
            authorizer_account_code: authorizer_account&.code,
            permission_type: 0,
            permission_code: fund&.code,
            temp_start_date: flow.start_time.to_s.to_date,
            temp_end_date: flow.end_time.to_s.to_date,
            flow_id: nil
          )

          temps.each do |temp|
            temp.flow_id = flow.id
            temp.save
          end
        end
      end
      
    end

    def syfund_o32temp_recover_sql
      <<-SQL
        SELECT
          lcbh,
          sqr,
          bsqshr,
          tqshsqksrq,
          tqshsqkssj,
          tqshsqzzrq,
          tqshsqzysj,
          ysqgllc
        FROM
          #{@table_space}formtable_main_266#{@sid_suffix}
        WHERE
          tqshsqksrq > #{Date.today - @syfund_o32temp_import_day.day}
      SQL
    end

    def syfund_o32temp_recover_import
      @database.exec(syfund_o32temp_recover_sql) do |r|
        user = User.find_by(code: r[2])
        authorizer_user = User.find_by(code: r[1])
        next unless user && authorizer_user
        flows = Jiaoyi::TemporaryFlow.where(user_id: user.id, authorizer_user_id: authorizer_user.id, flow_id: r[7])
        recover_start_time = "#{r[3]} #{r[4]}"
        recover_end_time = "#{r[5]} #{r[6]}"
        account = Jiaoyi::Account.find_by(user_id: user.id)
        authorizer_account = Jiaoyi::Account.find_by(user_id: authorizer_user.id)
        flows.each do |flow|
          if flow.recover_start_time != recover_start_time.to_time
            flow.update(
              recover_start_time: recover_start_time,
              recover_end_time: recover_end_time,
              recover_status: true
            )
            temps = Jiaoyi::Temporary.where(flow_id: flow.id)
            temps.each do |temp|
              temp.authorization_status = nil
              temp.save
            end
          end
        end
      end
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    HrData =
      Struct.new(:department_code, :department_name, :user_id,
                 :user_code, :user_name, :email, :cellphone,
                 :position, :status_string, :manager_id) do

        def inservice
          # 0：试用
          # 1：正式
          # 2：临时
          # 3：试用延期
          # 4：解聘
          # 5：离职
          # 6：退休
          # 7：无效
          [0, 1, 2, 3].include? status_string.to_i
        end

        def present?
          user_code.present? && user_name.present?
        end
      end

    def import_hr_sql
      <<-SQL
        SELECT
          d.id,
          d.departmentname,
          u.id,
          #{@code_column || 'u.loginid'},
          u.lastname,
          j.jobtitlename,
          u.status,
          u.managerid
        FROM
          #{@table_space}HrmResource#{@sid_suffix} u,
          #{@table_space}HrmDepartment#{@sid_suffix} d,
          #{@table_space}HrmJobTitles#{@sid_suffix} j 
        WHERE
          u.departmentid = d.id 
          AND u.jobtitle = j.id
      SQL
    end

    def import_hr
      @data = []
      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(import_hr_sql) do |row|
        @data << create_user_struct(row)
      end
    end

    def create_user_struct(row)
      department_code, department_name, obj_id, code, name, position, status_string, manager_code = row

      user                 = HrData.new
      user.department_code = department_code&.to_s
      user.department_name = department_name
      user.user_id         = obj_id
      user.user_code       = code&.to_s
      user.user_name       = name
      user.position        = position
      user.status_string   = status_string
      user.manager_id      = manager_code if manager_code && manager_code != 0
      # 最后返回结构体
      user
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code).uniq
      new_codes       = @data.map(&:department_code).uniq

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          department_struct = @data.find { |x| x.department_code == code }
          Department.create(
            code:      department_struct.department_code,
            name:      department_struct.department_name,
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department        = old_departments.find { |x| x.code == code }
          department_struct = @data.find { |x| x.department_code == code }

          department.update(name: department_struct.department_name)
        end
      end
    end

    def create_or_update_users
      departments = Department.all.reload.to_a
      old_users   = User.all.to_a
      old_codes   = old_users.map(&:code)
      new_codes   = @data.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          user_struct = @data.find { |x| x.user_code == code }
          department  = departments.find { |x| x.code == user_struct.department_code }

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end

      # 已有的更新
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @data.find { |x| x.user_code == user.code }
          next unless user_struct&.present?

          department = departments.find { |x| x.code == user_struct.department_code }

          user.update(
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end
    end

    def link_manager
      all_users = User.all.reload.to_a

      ActiveRecord::Base.transaction do
        @data.each do |struct|
          next unless struct.manager_id

          user = all_users.find { |x| x.code == struct.user_code }
          next unless user

          manager_s = @data.find { |x| x.user_id == struct.manager_id }
          next unless manager_s

          manager = all_users.find { |x| x.code == manager_s.user_code }
          user.update(manager_id: manager&.id)
        end
      end
    end

    def update_user_name_clear
      return if @clear_name_regexp.blank?

      regexp    = Regexp.new(@clear_name_regexp)
      all_users = User.all.reload.to_a
      ActiveRecord::Base.transaction do
        all_users.each do |user|
          clear_name = user.name.gsub(regexp, '')
          user.update(name: clear_name)
        end
      end
    end

    def import_jobs_sql
      <<-EOF
        SELECT distinct jobtitlename FROM #{@table_space}HrmJobTitles#{@sid_suffix}
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



