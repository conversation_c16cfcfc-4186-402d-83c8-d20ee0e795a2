module AasOracleImporter
  class ZofundGuohuTaImporter < ImporterBase
    def config
      @bs_id       = 402
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @additionals = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_accounts       = "#{@table_space}FM_USER#{@sid_suffix}"
      @table_roles          = "#{@table_space}FM_GROUP#{@sid_suffix}"
      @table_accounts_roles = "#{@table_space}FM_GROUPUSER#{@sid_suffix}"
      @table_menus          = "#{@table_space}FM_MENUITEM#{@sid_suffix}"
      @table_menus_roles    = "#{@table_space}FM_USERMENURIGHTS#{@sid_suffix}"
      @table_menus_accounts = "#{@table_space}FM_USERMENURIGHTS#{@sid_suffix}"
      @table_additionals    = "#{@table_space}FM_EXTRAMENURIGHT#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_additional_permissions
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_ledgers(ZofundGuohuTa::Account)
    end

    def destroy_exist_datas
      accounts = ZofundGuohuTa::Account.where(quarter_id: @quarter_id)
      ZofundGuohuTa::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      ZofundGuohuTa::Role.where(quarter_id: @quarter_id).delete_all
      ZofundGuohuTa::Data1Permission.where(quarter_id: @quarter_id).delete_all
      ZofundGuohuTa::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select l_user_id, vc_user_code, vc_user_name, c_status from #{@table_accounts}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << ZofundGuohuTa::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '1'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select l_group_id, l_group_id, vc_group_name from #{@table_roles}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << ZofundGuohuTa::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select l_group_id, l_user_id from #{@table_accounts_roles}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          ZofundGuohuTa::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_additional_permissions_sql
      "select VC_MENU_NO, C_MENU_RIGHT, VC_MENU_RIGHT_NAME from #{@table_additionals}"
    end

    def import_additional_permissions
      select_db_datas(import_additional_permissions_sql).each do |r|
        @additionals << [r[0], r[1]&.to_s, r[2]]
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select vc_menu_no, vc_menu_no, vc_menu_name, VC_PARENTMENU_NO from #{@table_menus} where c_menu_type = 1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[3]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, 2, 3)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << ZofundGuohuTa::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select L_GROUP_ID, VC_MENU_NO, VC_MENU_RIGHTS from #{@table_menus_roles}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          additional_codes = r[2].to_s.chars
          ZofundGuohuTa::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: get_additional_text(permission, additional_codes)
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select L_USER_ID, VC_MENU_NO, VC_MENU_RIGHTS from #{@table_menus_accounts}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        additional_codes = r[2].to_s.chars
        ZofundGuohuTa::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: get_additional_text(permission, additional_codes)
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}'"
      select_db_datas(new_sql).each_with_index do |r, index|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
        break if index >= 0
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def get_additional_text(permission, codes)
      return nil if codes.blank?

      @additionals
        .select { |x| x[0] == permission.code }
        .select { |x| codes.include?(x[1]) }
        .map { |x| x[2] }
        .join(',')
    end
  end
end
