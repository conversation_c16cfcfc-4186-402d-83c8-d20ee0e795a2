module Risk
  def self.table_name_prefix
    'risk_'
  end
end

class Risk::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Risk::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Risk::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class Risk::MenusRoles < ActiveRecord::Base; end
class Risk::AccountsRoles < ActiveRecord::Base; end
