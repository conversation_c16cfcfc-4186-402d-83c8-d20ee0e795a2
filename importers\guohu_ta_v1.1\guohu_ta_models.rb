module <PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'guohu_ta_'
  end
end

class GuohuTa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class GuohuTa::AdditionalPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuTa::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
  has_many :additional_permissions
end

class GuohuTa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class GuohuTaMenusRoles < ActiveRecord::Base; end
class GuohuTaAccountsRoles < ActiveRecord::Base; end
class GuohuTaAccountsMenus < ActiveRecord::Base; end
class GuohuTaAccountsAdditionalPermissions < ActiveRecord::Base; end
class GuohuTaAdditionalPermissionsRoles  < ActiveRecord::Base; end
