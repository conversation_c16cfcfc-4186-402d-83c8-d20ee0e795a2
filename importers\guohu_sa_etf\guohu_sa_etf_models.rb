module GuohuSaEtf
  def self.table_name_prefix
    'guohu_sa_etf_'
  end
end

class GuohuSaEtf::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaEtf::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaEtf::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaEtf::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaEtfAccountsRoles < ActiveRecord::Base; end


class GuohuSaEtfMenusRoles < ActiveRecord::Base; end
class GuohuSaEtfAccountsMenus < ActiveRecord::Base; end
class GuohuSaEtfAccountsOtherPermissions < ActiveRecord::Base; end
class GuohuSaEtfOtherPermissionsRoles  < ActiveRecord::Base; end
