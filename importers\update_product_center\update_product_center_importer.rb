module AasOracleImporter
  class UpdateProductCenterImporter < ImporterBase
    def config
      @bs_id                  = importer_config['bs_id']
      @table_space            = importer_config['table_space']
      @sid_suffix             = importer_config['sid_suffix']
      @accounts = ProductCenter::Account.where(quarter_id: @quarter_id)

      initialize_tables
    end

    def initialize_tables
      @table_account = "#{@table_space}staff#{@sid_suffix}"
    end

    def import_to_do
      update_accounts
    end

    def update_accounts_sql
      <<-EOF
        select user_id, employee_number from #{@table_account}
      EOF
    end

    def update_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(update_accounts_sql).each do |r|
          @account = @accounts.find_by(ledger_rele: r[0])
          next unless @account

          @account.ledger_rele = r[1]
          @account.save
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end
  end
end
