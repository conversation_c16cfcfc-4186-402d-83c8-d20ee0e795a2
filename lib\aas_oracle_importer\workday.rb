# frozen_string_literal: true

require 'date'
require_relative 'config'
require_relative 'workday/second_workday_in_every_quarter'
require_relative 'workday/workday_in_team_records'
require_relative 'workday/date_in_month'
require_relative 'workday/workday_in_mfcteda'
require_relative 'workday/workday_in_zszq'


module AasOracleImporter
  # 工作日设置
  module Workday
    class NotFoundModeForWorkday < StandardError; end
    class ConfigParseError < StandardError; end

    extend self

    def allow_import?(date = Date.today)
      @date = date
      @logger = AasOracleImporter.initialize_logger
      return true unless enable?

      workday?
    end

    private

    def enable?
      result = config && config['enable']
      @logger.info { "workday: enable is #{result || 'false'}" }
      result
    end

    def workday?
      @logger.info { "workday mode is #{config['mode']}" }

      case config['mode']
      when 'team_records'
        WorkdayInTeamRecords.new(@date, config).workday?
      when 'every_quarter_second_workday'
        SecondWorkdayInEveryQuarter.new(@date, config).workday?
      when 'date_in_month'
        DateInMonth.new(@date, config).workday?
      when 'mfcteda'
        WorkdayInMfcteda.new(@date, config).workday?
      when 'zszq'
        WorkdayInZszq.new(@date, config).workday?
      when 'trading_day'
        TradingDays::Date.workday?(@date)
      else
        raise NotFoundModeForWorkday, "Not found mode named '#{config['mode']}'"
      end
    end

    def config
      AasOracleImporter.config['workday']
    end
  end
end
