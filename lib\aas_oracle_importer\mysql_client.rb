# frozen_string_literal: true
module AasOracleImporter
  # mysql 客户端
  class MysqlClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.query(sql).each(as: :array).each do |row|
          yield row
        end
      else
        @database.query(sql).each(as: :array)
      end
    end

    def query(sql)
      @database.query(sql)
    end

    private

    def initialize_driver
      load_driver_gem
      @database = Mysql2::Client.new mysql_client_params
    rescue Mysql2::Error::ConnectionError => e
      raise Mysql2::Error::ConnectionError, message_prefix + e.message
    end

    def mysql_client_params
      mysql_client_config.update({password: ConvertTools::Cryptology.decrypt_if_env(mysql_client_config[:password])})
    end

    def mysql_client_config
      {
        host:     database_info['db_host'],
        port:     database_info['db_port'] || 3306,
        database: database_info['db_name'],
        username: database_info['db_user'],
        password: database_info['db_pass']
      }
    end

    def load_driver_gem
      require 'mysql2'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'mysql2\'.')
      exit(-127)
    end
  end
end
