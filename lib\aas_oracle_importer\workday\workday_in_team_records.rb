# frozen_string_literal: true

require_relative 'workday_mode_base'

module AasOracleImporter
  module Workday
    # 在 TeamRecords 系统中
    class WorkdayInTeamRecords < WorkdayModeBase
      def initialize(date, config)
        super
        require 'httparty'
      rescue LoadError
        logger.error('LoadError: Not found gem \'httparty\'.')
        exit(-127)
      end

      def workday?
        query_params = { date: @date.strftime('%Y-%m-%d') }
        response     = ::HTTParty.get(config['api'], query: query_params)
        logger.info { "workday: is_workday? #{@date} response: #{response.body}" }
        response.body == 'true'
      end
    end
  end
end
