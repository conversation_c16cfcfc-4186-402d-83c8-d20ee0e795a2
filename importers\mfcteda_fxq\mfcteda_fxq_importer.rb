module AasOracleImporter
  class MfctedaFxqImporter < ImporterBase
    def config
      @bs_id       = 311
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_accounts             = "#{@table_space}fm_user#{@sid_suffix}"
      @table_roles                = "#{@table_space}fm_group#{@sid_suffix}"
      @table_account_roles        = "#{@table_space}fm_groupuser#{@sid_suffix}"
      @table_menus                = "#{@table_space}fm_menuitem#{@sid_suffix}"
      @table_extramenuright       = "#{@table_space}fm_extramenuright#{@sid_suffix}"
      @table_user_menus           = "#{@table_space}fm_usermenurights#{@sid_suffix}"
      @table_user_extramenurights = "#{@table_space}fm_userextraright#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_data1_account_permissions

      import_data2_permissions

      import_data2_role_permissions

      import_data2_account_permissions

      import_ledgers(MfctedaFxq::Account)
    end

    def destroy_exist_datas
      accounts = MfctedaFxq::Account.where(quarter_id: @quarter_id)
      MfctedaFxq::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      MfctedaFxq::Role.where(quarter_id: @quarter_id).delete_all

      MfctedaFxq::Data1Permission.where(quarter_id: @quarter_id).delete_all

      MfctedaFxq::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      MfctedaFxq::Data2Permission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select l_user_id, vc_user_code, vc_user_name, c_status from #{@table_accounts}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '1', 'enum_value' => '正常', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << MfctedaFxq::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select l_group_id, l_group_id, vc_group_name from #{@table_roles}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << MfctedaFxq::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select l_group_id, l_user_id from #{@table_account_roles}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          MfctedaFxq::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<~EOF
        select
          m.vc_menu_no,
          m.vc_menu_no,
          m.vc_menu_name,
          m.vc_parentmenu_no
        from
          #{@table_menus} m
         where
          m.vc_subsystem_no = 'AML' union
        select
          concat( e.vc_menu_no, e.c_menu_right ),
          concat( e.vc_menu_no, e.c_menu_right ),
          e.vc_menu_right_name,
          e.vc_menu_no
        from
          #{@table_extramenuright} e
         where
          e.vc_subsystem_no = 'AML'
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(parent_menu_sql, name, parent_id_value, level1_name_index,
                                                     parent_id_index))

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data1_permissions << MfctedaFxq::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select l_group_id, vc_menu_no, vc_menu_rights from #{@table_user_menus}
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          if r[2].present?
            r[2].each_char do |char|
              menu_source_id = "#{r[1]}#{char}"

              carate_role_permission(r[0].to_s, menu_source_id)
            end
          end
          carate_role_permission(r[0].to_s, r[1].to_s)
        end
      end
    end

    def carate_role_permission(role_source_id, permission_source_id)
      role       = @roles.find { |x| x.source_id.to_s == role_source_id }
      permission = @data1_permissions.find { |x| x.source_id.to_s == permission_source_id }
      return unless permission && role

      MfctedaFxq::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                      data1_permission_id: permission.id)
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select
          r.l_user_id,
          r.vc_menu_no,
          r.vc_menu_rights
        from
          #{@table_user_menus} r
        where r.vc_subsystem_no = 'AML'
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        if r[2].present?
          r[2].each_char do |char|
            menu_source_id = "#{r[1]}#{char}"

            carate_account_permission(r[0].to_s, menu_source_id)
          end
        end

        carate_account_permission(r[0].to_s, r[1].to_s)
      end
    end

    def carate_account_permission(role_source_id, permission_source_id)
      account    = @accounts.find { |x| x.source_id.to_s == role_source_id }
      permission = @data1_permissions.find { |x| x.source_id.to_s == permission_source_id }
      return unless account && permission

      MfctedaFxq::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id,
                                                      data1_permission_id: permission.id)
    end

    def import_data2_permissions_sql
      <<-EOF
        select e.vc_right_value, e.vc_right_value, e.vc_right_value from #{@table_user_extramenurights} e where e.l_grant_user = 0
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data2_permissions << MfctedaFxq::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select e.l_group_id, e.vc_right_value from #{@table_user_extramenurights} e where e.vc_extra_value = 1 and e.l_group_id != -1
      EOF
    end

    def import_data2_role_permissions
      enums = []
      select_db_datas(import_data2_role_permissions_sql).each do |r|
        role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        return unless permission && role

        MfctedaFxq::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                        data2_permission_id: permission.id)
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select e.l_user_id, e.vc_right_value from #{@table_user_extramenurights} e where e.vc_extra_value = 1 and e.l_group_id = -1
      EOF
    end

    def import_data2_account_permissions
      enums = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        MfctedaFxq::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id,
                                                        data2_permission_id: permission.id)
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引

    def parent_menu_sql
      <<~EOF
        select
          m.vc_menu_no,
          m.vc_menu_no,
          m.vc_menu_name,
          m.vc_parentmenu_no
        from
          #{@table_menus} m
      EOF
    end

    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank? || parent_id == '-'

      # sql = sql.downcase

      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " where vc_menu_no = '#{parent_id}' and rownum <= 1"

      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
