module AasOracleImporter
  class GjzqHrImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @users = []
      @departments = []
      @position_users = [] # 拥有岗位的用户
    end

    def import_do_do
      import_departments
      import_users
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    def import_departments_sql
      <<-EOF
        select DepID, DepCName, AdminID, Disabled from U_EHR.DEPARTMENT where Disabled = 0
      EOF
    end

    def import_departments

      @database.exec(import_departments_sql) do |r|
        department = Department.find_or_create_by(code: r[0])
        department.update(name: r[1], inservice: r[3].to_i == 0)
        @departments << department
      end

      @database.exec(import_departments_sql) do |r|
        parent_department = @departments.find{|x| x.code == r[2]}
        department = @departments.find{|x| x.code == r[0]}
        if parent_department
          department.update(parent_id: parent_department.id)
        end
      end

      codes = @departments.map{|x| x.code}
      Department.all.each do |dept|
        level = 1
        level = level + 1 if dept.parent
        level = level + 1 if dept.parent&.parent
        level = level + 1 if dept.parent&.parent&.parent
        level = level + 1 if dept.parent&.parent&.parent&.parent
        dept.update(level: level)
        dept.update(inservice: false) unless codes.include?(dept.code)
      end
    end

    def import_users_sql
      <<-EOF
        select u.Badge, u.SNum, u.Name, u.DepID, j.jobcname, u.status, u.IDENTIFICATION from u_ehr.v_employee_qxjh u LEFT JOIN U_EHR.JOB j ON j.jobid = u.jobid where u.status != 'LOFF' and u.status != 'RETI'
      EOF
    end

    def import_users
      @database.exec(import_users_sql) do |r|
        department = @departments.find{|x| x.code == r[3]}
        user = User.find_or_create_by(code: r[0])
        user.name = r[2]
        user.department = department if department
        user.inservice = r[5].to_s != 'LOFF' && r[5].to_s != 'RETI'
        user.position = r[4]
        user.organization_code = r[1]
        user.id_number = r[6]
        user.save
        @users << user
        @position_users << user if user.position?
      end

      (User.pluck(:code) - @users.map{|x| x.code}).each do |code|
        User.find_by(code: code).update(inservice: false)
      end
    end

    def import_jobs_sql
      <<-EOF
        select distinct jobcname from U_EHR.JOB
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



