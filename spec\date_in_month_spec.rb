# frozen_string_literal: true
#
require 'rspec'
require_relative '../lib/aas_oracle_importer/workday'
require_relative '../lib/aas_oracle_importer/workday/date_in_month'

describe AasOracleImporter::Workday::DateInMonth do

  let(:klass) { AasOracleImporter::Workday::DateInMonth }
  let(:test_date) { Date.parse('2021-01-21') }
  before do
    # Do nothing
  end

  after do
    # Do nothing
  end

  context 'when parse config' do
    it 'Exception when config is missing' do
      config = {}
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'workday config is not a hash')
    end

    it 'Exception when months is missing' do
      config = { 'config' => { 'days' => 'all' } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'months config is not a Array or String')
    end

    it 'Exception when months in error type' do
      config = { 'config' => { 'months' => 1 } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'months config is not a Array or String')
    end

    it 'Exception when months in error command' do
      config = { 'config' => { 'months' => 'hello' } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'hello is not a permit command')
    end

    it 'exception when days is missing' do
      config = { 'config' => { 'months' => 'all' } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'dates config is not a Array or String')
    end

    it 'Exception when days in error type' do
      config = { 'config' => { 'months' => 'all', 'days' => 1 } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'dates config is not a Array or String')
    end

    it 'Exception when days in error command' do
      config = { 'config' => { 'months' => 'all', 'days' => 'hello' } }
      expect { klass.new test_date, config }.to raise_error(AasOracleImporter::Workday::ConfigParseError,
                                                            'hello is not a permit command')
    end

    it 'no exception then config is right' do
      config = { 'config' => { 'months' => 'every', 'days' => 'every' } }
      expect(klass.new(test_date, config)).to be_an_instance_of(klass)
    end
  end

  context 'when date should be workday' do
    it 'success in every month and every day' do
      config = { 'config' => { 'months' => 'every', 'days' => 'every' } }
      expect(klass.new(test_date, config).workday?).to be true
    end

    it 'success in right month and day' do
      config = { 'config' => { 'months' => [1, 3], 'days' => [20, 21] } }
      expect(klass.new(test_date, config).workday?).to be true
    end

    it 'success in right month and every day' do
      config = { 'config' => { 'months' => [1, 3], 'days' => 'all' } }
      expect(klass.new(test_date, config).workday?).to be true
    end

    it 'success in every month and right day' do
      config = { 'config' => { 'months' => 'every', 'days' => [21] } }
      expect(klass.new(test_date, config).workday?).to be true
    end
  end

  context 'when date should not be workday' do
    it 'false in wrong month' do
      config = { 'config' => { 'months' => [2, 3], 'days' => [21] } }
      expect(klass.new(test_date, config).workday?).to be false
    end

    it 'false in wrong day' do
      config = { 'config' => { 'months' => [1], 'days' => [1] } }
      expect(klass.new(test_date, config).workday?).to be false
    end
  end
end
