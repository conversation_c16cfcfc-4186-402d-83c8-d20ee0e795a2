# frozen_string_literal: true

require 'httparty'
require 'yaml'
require 'logger'
require_relative '../aas_oracle_importer' unless ENV['AAS_PACKAGING']

# 富国的数据中心数据同步任务请求模块
module FgfundDatacenterAsyncTask

  # 任务解析请求
  class DataCenterRequest
    attr_reader :url, :task_code, :user, :secret_key

    def initialize(url, task_code, user, secret_key)
      @url        = url
      @task_code  = task_code
      @user       = user
      @secret_key = secret_key
      @date       = Time.now.strftime('%Y%m%d')
      HTTParty::Basement.default_options.update(verify: false)
    end

    def start
      logger.info { "request for task_code #{@task_code}, task_id #{@task_id}" }
      response = ::HTTParty.post(@url, request_package(start_request))
      logger.debug { "http code: #{response.code}" }
      logger.debug { "response body: #{response.body}" }
      response = JSON.parse response.body
      store_task_id(response)
    end

    def check
      response  = ::HTTParty.post(@url, request_package(check_request))
      @response = JSON.parse response.body

      logger.info { "request for task_code #{@task_code}, task_id #{@task_id}" }
      logger.debug { @response }
      @response
    end

    def task_finish?
      @response['Head']['status'].to_i == 1
    end

    def task_success?
      task_finish? && @response['Head']['resFlag'] == 'S'
    end

    def task_failed?
      task_finish? && @response['Head']['resFlag'] != 'S'
    end

    private

    def start_request
      {
        Head: {
          version:   '0.1',
          user:      user,
          secretKey: secret_key
        },
        Body: {
          paramExternal: {
            "TX_DATE": @date
          },
          "taskNo":      @task_code
        }
      }
    end

    def logger
      @logger ||= FgfundDatacenterAsyncTask.logger
    end

    def check_request
      {
        Head: {
          taskId:    @task_id,
          version:   '0.1',
          user:      user,
          secretKey: secret_key
        },
        Body: {
          paramExternal: {
            "TX_DATE": @date
          },
          "taskNo":      @task_code
        }
      }
    end

    def request_package(request_body)
      {
        body:    request_body.to_json,
        headers: { 'Content-Type' => 'application/json' }
      }
    end

    def store_task_id(response)
      @task_id = response['Head']['taskId']
      logger.info { "store task_id #{@task_id}" }
      @task_id
    end
  end

  # 富国数据中心的配置
  module Config
    module_function

    def config
      @config ||= parse_config_file
    end

    def base_url
      @base_url ||= config['base_url']
    end

    def timeout
      @timeout ||= config['timeout']
    end

    def interval
      @interval ||= config['interval']
    end

    def log_level
      @log_level ||= config['log_level']
    end

    def log_file
      @log_file ||= (config['log_file'] || STDOUT)
    end

    def tasks
      @tasks ||= config['tasks']
    end

    def user
      @user ||= config['user']
    end

    def secret_key
      @secret_key ||= config['secret_key']
    end

    def parse_config_file
      YAML.load_file config_file
    end

    def config_file
      File.join(__dir__, '../../config/fgfund_data_center_tasks.yml')
    end
  end

  module RedisProvider
    def provider
      @provider ||= provider_initialize
    end

    def provider_initialize
      server_path = ::AasOracleImporter.config['server']['path']
      load "#{server_path}/config/initializers/redis.rb"

      ::AasProvider
    end

    def redis_key
      'aas:customer:fgfund:dc_task_status'
    end

    def failed_task_keys
      'aas:customer:fgfund:dc_failed_tasks'
    end

    def change_task_status(status)
      provider.redis.set(redis_key, status)
    end

    def record_failed_tasks(task_codes_string)
      provider.redis.set(failed_task_keys, task_codes_string)
    end
  end

  # 队列任务的相关状态处理
  module TaskQueueStatus
    include RedisProvider

    def return_success_status
      logger.info { 'all tasks run success' }
      change_task_status('success')
      exit 0
    end

    def return_timeout_status
      logger.fatal { 'run tasks timeout' }
      change_task_status('timeout')
      exit 1
    end

    def return_failed_status(failed_task_codes)
      codes_string = failed_task_codes.join(', ')
      logger.fatal { "found failed tasks: #{codes_string}" }
      change_task_status('failed')
      record_failed_tasks(codes_string)
      exit 2
    end

    def return_unknown_status
      logger.fatal { 'found unknown error' }
      change_task_status('unknown')
      exit 3
    end
  end

  # 实际执行的队列检查任务
  class TaskQueue
    include TaskQueueStatus

    def initialize
      @tasks = Config.tasks.map do |task_config|
        DataCenterRequest.new(Config.base_url, task_config['task_code'], Config.user, Config.secret_key)
      end

      @start_time = Time.now
    end

    def run_tasks
      start
      loop_check
    rescue StandardError => e
      logger.error { e.message }
      logger.error { e.backtrace.join("\n") }
      return_unknown_status
    end

    private

    def start
      @tasks.each(&:start)
    end

    def loop_check
      loop do
        @tasks.each(&:check)

        break if all_finish?
        break if exist_failed?

        @tasks.delete_if(&:task_success?)

        now = Time.now
        return_timeout_status if (now - @start_time) >= Config.timeout

        sleep Config.interval
      end

      if all_success?
        return_success_status
      else
        failed_tasks = @tasks.select(&:task_failed?)
        return_failed_status(failed_tasks.map(&:task_code))
      end
    end

    def all_finish?
      @tasks.all?(&:task_finish?)
    end

    def all_success?
      @tasks.all?(&:task_success?)
    end

    def exist_failed?
      @tasks.any?(&:task_failed?)
    end

    def logger
      @logger ||= FgfundDatacenterAsyncTask.logger
    end
  end

  module_function

  def logger
    logger                 = Logger.new(Config.log_file)
    logger.level           = Config.log_level
    logger.progname        = 'FgfundDatacenterAsyncTask'
    logger.datetime_format = '%Y-%m-%dT%H:%M:%S.%L%z '
    logger
  end
end
