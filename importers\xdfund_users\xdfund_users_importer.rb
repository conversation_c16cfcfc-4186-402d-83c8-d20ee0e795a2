module AasOracleImporter
  class XdfundUsersImporter < ImporterBase

    def config
      @departments = []
      @users = []
      @old_department_codes = Department.where.not(code: %w[public disabled]).pluck(:code)
      @old_user_codes = User.pluck(:code)
      @table_space = importer_config['table_space'] || 'hrsysv9.'
    end

    def import_to_do
      import_departments
      import_users
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'OA 系统')
    end

    private

    def import_departments_sql
      <<-SQL
        select dpid,title,dpgrade, PARENTID from #{@table_space}xd_auditdp
      SQL
    end

    def import_departments
      users_json = {}
      @database.exec(import_departments_sql) do |r|
        department = Department.find_or_create_by(code: r[0])
        @old_department_codes.delete(department.code)
        department.update(
          name: r[1],
          inservice: true
          )
      end

      @database.exec(import_departments_sql) do |r|
        department = Department.find_by(code: r[0])
        parent_department = Department.find_by(code: r[3])
        department.update(parent_id: parent_department.id) if parent_department
      end
    end

    def import_users_sql
      <<-SQL
        select eid,name,status,jbid,jbtitle,dpid,dptitle from #{@table_space}xd_auditemp
      SQL
    end

    
    def import_users
      @database.exec(import_users_sql) do |data|

        user = User.find_or_create_by(code: data[0])
        @old_user_codes.delete(user.code)
        department = Department.find_by(code: data[5])
        if data[3].present?
          job_code = department ? "#{department&.name}-#{data[3]}" : data[3]
          job_name = data[4]
          job = Job.find_or_create_by(code: job_code,name: job_name, department_id: department&.id)
          JobUser.where(user_id: user.id).delete_all
          JobUser.create(user_id: user.id, job_id: job.id)
        end
        user.update(
          name: data[1],
          department_id: department&.id,
          position: data[4],
          inservice: data[2].to_i == 1

        )
      end
      User.where(code: @old_user_codes).update_all(inservice: false)
    end

  end
end
