module AasOracleImporter

  class GuzhiYss25Importer < ImporterBase

    def config
      @bs_id      = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @import_booksets_only_this_year = importer_config['import_booksets_only_this_year']
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_subsystems
      import_roles
      import_booksets
      import_accounts_roles_booksets

      import_ledgers(Guzhi25::Account)
    end

    def destroy_exist_datas
      Guzhi25::Account.where(quarter_id: @quarter_id).destroy_all
      Guzhi25::Bookset.where(quarter_id: @quarter_id).destroy_all
      Guzhi25::Role.where(quarter_id: @quarter_id).destroy_all
      Guzhi25::Subsystem.where(quarter_id: @quarter_id).destroy_all
      Guzhi25::BooksetRelation.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts
      sql = <<-EOF
        select fucode, funame, fstate, femail, fdept from #{@table_space}tuser#{@sid_suffix}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            Guzhi25::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_i == 0,
              email: r[3],
              department: r[4]
              )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_subsystems
      sql = <<-EOF
        select
          distinct ur.fby
        from
          #{@table_space}tuser_role#{@sid_suffix} ur
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Guzhi25::Subsystem.create(quarter_id: @quarter_id, name: r[0])
        end
      end
    end

    def import_roles
      sql = <<-EOF
        select
          distinct ur.frname
        from
          #{@table_space}tuser_role#{@sid_suffix} ur
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Guzhi25::Role.create(quarter_id: @quarter_id, code: r[0], name: r[0])
        end
      end
    end

    def import_booksets
      sql = <<-EOF
        select
          distinct ur.fsetcode,
          s.fsetname,
          s.fsetid
        from
          #{@table_space}tuser_role#{@sid_suffix} ur,
          #{@table_space}lsetlist#{@sid_suffix} s
        where
          ur.fsetcode = s.fsetcode
      EOF

      # MARK: 如果账套不转到今年，就是过期的，不用存
      if @import_booksets_only_this_year
        sql = sql + "\n and s.fyear = #{Date.today.year}"
      end

      # MARK: id 有可能重，远程 group by 不出来,这里本地检测去重
      ids = Set.new
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          next if ids.include? r[0]

          Guzhi25::Bookset.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            fund_code: r[2]
          )

          ids << r[0]
        end
      end
    end

    def import_accounts_roles_booksets
      sql = <<-EOF
        select
          ur.fucode,
          ur.frname,
          ur.fby,
          ur.fsetcode
        from
          #{@table_space}tuser_role#{@sid_suffix} ur
      EOF

      booksets = Guzhi25::Bookset.where(quarter_id: @quarter_id).to_a
      accounts = Guzhi25::Account.where(quarter_id: @quarter_id).to_a
      roles    = Guzhi25::Role.where(quarter_id: @quarter_id).to_a
      systems  = Guzhi25::Subsystem.where(quarter_id: @quarter_id).to_a

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|

          bookset   = booksets.find {|x| x.code == r[3]}
          # bookset 可能过期不存在，此时不导入
          next if r[3].present? && bookset.nil?

          account   = accounts.find {|x| x.code == r[0]}
          role      = roles.find    {|x| x.name == r[1]}
          subsystem = systems.find  {|x| x.name == r[2]}

          next unless account

          Guzhi25::BooksetRelation.create(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            role_id:      role&.id,
            bookset_id:   bookset&.id,
            subsystem_id: subsystem&.id
          )
        end
      end
    end

  end
end



