module AasOracleImporter

  class BoseraOaImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix = importer_config['sid_suffix'] #''
    end

    def import_to_do
      import_users
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def import_users

      Department.all.each do |department|
        department.inservice = false
      end

      sql = <<-EOF
        select code, yhxm, bm, ygzt
        from omss.doc_RLZY_YGDJ
        where sys_recycleflag = 0
          AND bm != '' AND code IS NOT NULL
          AND code != ''
      EOF

      ActiveRecord::Base.transaction do
        @database.query(sql).each do |r|
          user_code = r['code']
          user_name = r['yhxm']
          department_name = r['bm']
          inservice = r['ygzt'] == 'y'

          department = Department.find_or_create_by(name: department_name)
          department.inservice = true
          department.save

          user = User.find_or_create_by(code: user_code)
          user.name = user_name
          user.department_id = department.id
          user.inservice = inservice
          user.save
        end
      end
    end
  end
end



