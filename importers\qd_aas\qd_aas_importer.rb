module AasOracleImporter
  class QdAasImporter < ImporterBase
    def config
      @bs_id = 10104
      @accounts = []
      @roles = []
      @data = []

      @special_permissions = []
      @system_categories = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      @data4_permissions = []
      @data4_accounts_roles_permissions = []

      @data5_permissions = []
      @data5_accounts_roles_permissions = []

      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_special_permissions
      import_system_categories
      import_data1_permissions
      import_data1_role_permissions
      # import_data2_permissions
      import_data2_account_permissions
      import_data3_permissions
      import_data3_account_permissions
      import_data4_permissions
      import_data4_account_permissions
      import_data5_permissions
      import_data5_role_permissions

      import_ledgers(QdAas::Account)
    end

    def destroy_exist_datas
      accounts = QdAas::Account.where(quarter_id: @quarter_id)
      QdAas::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      QdAas::Role.where(quarter_id: @quarter_id).delete_all

      QdAas::Data1Permission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data2Permission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data3Permission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data4Permission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data5Permission.where(quarter_id: @quarter_id).delete_all
      QdAas::Data5AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select id, code, name, if (disabled_at is null, true,false) as status from admins
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << QdAas::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id as code, name from app_roles where disabled is null
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << QdAas::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select app_role_id, admin_id from admin_app_roles
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          QdAas::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select two.id, two.id, concat(one.name," -> ",two.name) as name from app_module_functions two left join app_modules one on two.app_module_id = one.id where two.disabled = false and one.disabled = false
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << QdAas::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select rela.app_role_id, rela.app_module_function_id from app_role_functions rela left join app_module_functions menu on menu.id = rela.app_module_function_id where menu.disabled = false
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          QdAas::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def data2_permissions
      [[1, 1, '所有系统查询（自动添加新增系统）'],
       [2, 2, '所有系统管理（自动添加新增系统）'],
       [3, 3, '所有部门查询（自动添加新增部门）']]
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2

        data2_permissions.each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << QdAas::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select admin_id, if(all_systems_query = 1, '所有系统查询（自动添加新增系统）', '') as all_systems_query, if(all_systems_maintain = 1, '所有系统管理（自动添加新增系统）', '') as all_systems_maintain, if(all_departments_query = 1, '所有部门查询（自动添加新增部门）', '') as all_departments_query from admin_special_permissions
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        next unless account

        QdAas::Data2AccountsRolesPermission.create(
          quarter_id:                   @quarter_id,
          account_id:                   account.id,
          systems_query_permission:     r[1],
          systems_maintain_permission:  r[2],
          departments_query_permission: r[3]
        )
      end
    end

    def import_system_categories_sql
      'select scp.admin_id, bs.id, scp.query, scp.maintain from admin_system_category_permissions scp, business_system_categories sc, business_systems bs where scp.business_system_category_id = sc.id and bs.business_system_category_id = sc.id and bs.inservice = 1'
    end

    def import_system_categories
      select_db_datas(import_system_categories_sql).each do |r|
        @system_categories << r
      end
    end

    def import_special_permissions_sql
      <<-EOF
        select admin_id, all_systems_query, all_systems_maintain, all_departments_query from admin_special_permissions
      EOF
    end

    def import_special_permissions
      select_db_datas(import_special_permissions_sql).each do |r|
        @special_permissions << r
      end
      # special_permissions 可能存在重复的情况，保留第一个
      @special_permissions = @special_permissions.uniq { |x| x[0] }
    end

    def import_data3_permissions_sql
      <<-EOF
        select id, id as code, name from business_systems where inservice = true
      EOF
    end

    def import_data3_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        select_db_datas(import_data3_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data3_permissions << QdAas::Data3Permission.create(json)
        end
      end
    end

    # 拥有全部部门权限的记录
    # 特殊权限
    def import_data3_specail_permissions
      systems_query_permissions = @special_permissions.select { |x| x[1] == 1 }
      systems_maintain_permissions = @special_permissions.select { |x| x[2] == 1 }
      systems_query_permissions.each do |query_permission|
        account = @accounts.find { |a| a.source_id.to_s == query_permission[0].to_s }
        next unless account

        @data3_permissions.each do |data3_permission|
          @data << [account.id, data3_permission.id, ['查询']]
        end
      end
      # special_permissions 可能存在重复的情况
      @data = @data.uniq
      systems_maintain_permissions.each do |maintain_permission|
        account = @accounts.find { |a| a.source_id.to_s == maintain_permission[0].to_s }
        next unless account

        # 如果已经有了记录，则修改记录，不能添加，这样会重复
        @data3_permissions.each do |data3_permission|
          permission_data = @data.find { |x| x[0] == account.id && x[1] == data3_permission.id }
          if permission_data.present?
            if permission_data[2].is_a?(Array)
              permission_data[2] << '操作'
            else
              permission_data[2] = ['操作']
            end
          else
            @data << [account.id, data3_permission.id, ['操作']]
          end
        end
      end
    end

    def import_data3_category_permissions
      # 系统分类权限
      system_query_categories = @system_categories.select { |x| x[2] == 1 }
      system_maintain_categories = @system_categories.select { |x| x[3] == 1 }

      system_query_categories.each do |query_permission|
        account = @accounts.find { |a| a.source_id.to_s == query_permission[0].to_s }
        next unless account

        # if account.name == '<EMAIL>'
        #   require 'byebug'
        #   byebug
        # end
        # 如果已经有了记录，则修改记录，不能添加，这样会重复
        data3_permission = @data3_permissions.find { |x| x.source_id.to_i == query_permission[1] }
        next if data3_permission.nil?

        permission_data = @data.find { |x| x[0] == account.id && x[1] == data3_permission.id }
        if permission_data.present?
          if permission_data[2].is_a?(Array)
            permission_data[2] << '查询'
          else
            permission_data[2] = ['查询']
          end
        else
          @data << [account.id, data3_permission.id, ['查询']]
        end
      end

      system_maintain_categories.each do |maintain_permission|
        account = @accounts.find { |a| a.source_id.to_s == maintain_permission[0].to_s }
        next unless account

        # 如果已经有了记录，则修改记录，不能添加，这样会重复
        data3_permission = @data3_permissions.find { |x| x.source_id.to_i == maintain_permission[1] }
        next if data3_permission.nil?

        permission_data = @data.find { |x| x[0] == account.id && x[1] == data3_permission.id }
        if permission_data.present?
          if permission_data[2].is_a?(Array)
            permission_data[2] << '操作'
          else
            permission_data[2] = ['操作']
          end
        else
          @data << [account.id, data3_permission.id, ['操作']]
        end
      end
    end

    def import_data3_account_permissions_sql
      <<-EOF
        select rela.admin_id, rela.business_system_id, if(rela.query = 1, '查询', '') as query, if(rela.maintain = 1, '操作', '') as maintain from admin_system_permissions rela left join business_systems bs on rela.business_system_id = bs.id where bs.inservice = true
      EOF
    end

    def import_data3_account_permissions
      import_data3_specail_permissions
      import_data3_category_permissions

      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        permission_data = @data.find { |x| x[0] == account.id && x[1] == permission.id }
        query = r[2]
        maintain = r[3]
        if permission_data
          # 如果授权了所有系统查询，则优先使用查询，没有则看具体的授权
          permission_data[2] << query if query.present? && !permission_data[2].include?(query)
          permission_data[2] << maintain if maintain.present? && !permission_data[2].include?(maintain)
        else
          additional_permissions = [query, maintain]
          @data << [account.id, permission.id, additional_permissions]
        end
      end

      QdAas::Data3AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data3_permission_id,
                                                         :additional_permission) do |obj|
        obj.set_size = 1000
        @data.each do |x|
          additional_permission = x[2].select(&:present?).sort.uniq.join('、')
          obj.add [@quarter_id, x[0], x[1], additional_permission]
        end
      end
    end

    def import_data4_permissions_sql
      <<-EOF
        select id, id as code, name from departments where inservice = true
      EOF
    end

    def import_data4_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        select_db_datas(import_data4_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data4_permissions << QdAas::Data4Permission.create(json)
        end
      end
    end

    def import_data4_account_permissions_sql
      <<-EOF
        select rela.admin_id, rela.department_id, if(rela.query = 1, '查询', '') as query from admin_department_permissions rela left join departments d on rela.department_id = d.id where d.inservice = true
      EOF
    end

    def import_data4_account_permissions
      enums = []
      # 拥有全部部门权限的记录
      special_permissions = @special_permissions.select { |x| x[3] == 1 }

      special_permissions.each do |permission|
        account = @accounts.find { |a| a.source_id.to_s == permission[0].to_s }
        next unless account

        QdAas::Data4AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data4_permission_id,
                                                           :additional_permission) do |obj|
          obj.set_size = 1000
          @data4_permissions.each do |x|
            obj.add [@quarter_id, account.id, x.id, '查询']
          end
        end
      end

      select_db_datas(import_data4_account_permissions_sql).each do |r|
        special_permission = @special_permissions.find { |x| x[0] == r[0] }
        # 如果当前用户设置了全部权限，跳过，上面已经保存了
        next if special_permission.blank? || special_permission[3] == 1

        account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data4_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        QdAas::Data4AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data4_permission_id:   permission.id,
          additional_permission: get_enum(enums, 'additional_permission', r[2])
        )
      end
    end

    def import_data5_permissions_sql
      <<-EOF
        select id, code, name from global_alert_categories where enable = true
      EOF
    end

    def import_data5_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        select_db_datas(import_data5_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data5_permissions << QdAas::Data5Permission.create(json)
        end
      end
    end

    def import_data5_role_permissions_sql
      <<-EOF
        select app_role_id, global_alert_category_id, concat_ws(',', if(query=true,'查询', null), if(maintain=true,'处理', null)) as per from role_alert_permissions
      EOF
    end

    def import_data5_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data5_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data5_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          QdAas::Data5AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data5_permission_id:   permission.id,
            additional_permission: r[2]
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
