---
defaults:
  customer_id: zszq
  customer: 浙商证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_name_is_workday: true
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: &1
    path: "/Users/<USER>/Coding/sdata/account_audit_system"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_development
      username:
      password: 123456
      host: 127.0.0.1
  workday:
    enable: true
    mode: 'zszq'
    config:
      tnsname: datast
      restart_num: 3
  agent: &2
    oracle:
      o32db:
        db_host: ************
        db_name: zgo32test2
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
      gzdb:
        db_host: ************
        db_name: hsfatest1
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
      tadb:
        db_host: ************
        db_name: zjtatest
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
      ufdb:
        db_host: ************
        db_name: fzjygldb
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
      tydb:
        db_host: *************
        db_name: ggjc
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
      khdb:
        db_host: ************
        db_name: fzkh
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
    mysql:
      datast:
        db_host: ***********
        db_name: idp
        db_port: 3306
        db_user: zhqxglcj
        db_pass: hcD5n0TSio0ZqCTC
      seedb:
        db_host: *************
        db_name: acm
        db_port: 3306
        db_user: qxglcj
        db_pass: Zszq@qxgl2023
    jdbc:
      db_center: 
        db_host: ***********
        db_name: default
        db_port: 10001
        db_user: xxxx
        db_pass: xxxx
        drb_url: "druby://localhost:3006"
    http:
      yiban:
        base_uri: https://wechattest.stocke.com.cn/cgi-bin/
  importers: &3
  - name: fake_user
    db_type: http
    tnsname: yiban
    expect_status: success
  - name: yiban
    db_type: http
    tnsname: yiban
    base_uri: https://wechattest.stocke.com.cn/cgi-bin/
    corpid: ww36a41d36d3b099cd
    corpsecret: p9JTDDrNmjWGQaaJHBOvHjx2sScoaZd2ax4xkjw9stw
  - name: jiaoyi
    bs_id: 31
    db_type: jdbc
    tnsname: db_center
    table_space: zgo32.trade_
    sid_suffix: ''
    time_control: true
    temporary: true
    days_of_data: 999
    operator_no_importer: true
    trade_type_importer: true
    station_importer: true
    vc_domain_name: true
    version: zszq
    c_menu_type:
    - 1
    import_log:
      enable: false
      start_at: 5
    import_password_security:
      enable: false
    import_last_login_at:
      enable: true
      start_at: 1
  - name: guzhi
    bs_id: 24
    db_type: jdbc
    tnsname: db_center
    table_space: zgfa.hsfa_
    sid_suffix: ''
    hs_customer_code: 71551
    add_on_modules_table: txtcs_paramter
    add_on_modules_disable: true
    import_fund_code: true
    version: zszq
  - name: guohu_ta
    bs_id: 30
    db_type: jdbc
    tnsname: db_center
    table_space: zgta.hsta_
    sid_suffix: ''
    import_log:
      enable: false
      start_at: 5
    import_password_security:
      enable: false
    import_last_login_at:
      enable: true
      start_at: 1
    version: zszq
  - name: uf20
    bs_id: 58
    db_type: oracle
    tnsname: ufdb
    table_space: hs_user.
    sid_suffix: ''
  - name: hs_touyan
    bs_id: 323
    db_type: oracle
    tnsname: tydb
    table_space: bizframe.
    sid_suffix: ''
  - name: hs_see
    bs_id: 324
    db_type: mysql
    tnsname: seedb
  - name: kaihu
    bs_id: 325
    db_type: oracle
    tnsname: khdb
  - name: yy_caiwu
    bs_id: 326
    db_type: oracle
    tnsname: khdb
    sid_suffix: ''
development:
  customer_id: zszq
  customer: 浙商证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: *1
  agent: *2
  importers: *3
test:
  customer_id: zszq
  customer: 浙商证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: *1
  agent: *2
  importers: *3
production:
  customer_id: zszq
  customer: 浙商证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: "/home/<USER>/program/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: oceanbase
      database: aas_production
      host: <%= ENV['AAS_DATABASE_HOST'] %> 
      pool: 25
      port: <%= ENV['AAS_DATABASE_PORT'] %>
      username: <%= ENV['AAS_DATABASE_USERNAME'] %>
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
  agent: *2
  importers: *3
