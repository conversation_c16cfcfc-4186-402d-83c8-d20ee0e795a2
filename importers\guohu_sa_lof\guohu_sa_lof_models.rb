module Guo<PERSON><PERSON>a<PERSON>of
  def self.table_name_prefix
    'guohu_sa_lof_'
  end
end

class GuohuSaLof::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaLof::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaLof::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaLof::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class <PERSON>huSaLofAccountsRoles < ActiveRecord::Base; end


class GuohuSaLofMenusRoles < ActiveRecord::Base; end
class GuohuSaLofAccountsMenus < ActiveRecord::Base; end
class <PERSON>huSaLofAccountsOtherPermissions < ActiveRecord::Base; end
class <PERSON>huSaLofOtherPermissionsRoles  < ActiveRecord::Base; end
