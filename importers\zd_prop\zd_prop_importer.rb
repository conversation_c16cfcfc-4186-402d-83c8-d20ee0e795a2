require 'spreadsheet'

module AasOracleImporter
  class ZdPropImporter < ImporterBase
    def config
      @bs_id = importer_config['bs_id']
      @accounts = []
      @data1_permissions = []
    end

    def import_to_do
      return if file_names.blank?

      destroy_exist_datas
      import_orgs
      import_accounts
      import_data1_permissions
      import_data1_account_permissions
      import_ledgers(ZdProp::Account)
    end

    def destroy_exist_datas
      ZdProp::Department.where(quarter_id: @quarter_id).delete_all
      ZdProp::Account.where(quarter_id: @quarter_id).delete_all
      ZdProp::Role.where(quarter_id: @quarter_id).delete_all
      ZdProp::Data1Permission.where(quarter_id: @quarter_id).delete_all
      ZdProp::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs
      data = []
      file_paths.each do |file_path|
        book = Spreadsheet.open(file_path)
        sheet1 = book.worksheets[0]
        sheet1.rows[2..-1].each do |row|
          next if row.nil? || row[4].blank?

          data << row[4]
        end
      end
      data.uniq!
      data.each do |name|
        ZdProp::Department.create(
          quarter_id: @quarter_id,
          source_id:  name,
          code:       name,
          name:       name,
          full_name:  name,
          status:     true
        )
      end

      @departments = ZdProp::Department.where(quarter_id: @quarter_id)
    end

    def import_accounts
      data = []
      file_paths.each do |file_path|
        book = Spreadsheet.open(file_path)
        sheet1 = book.worksheets[0]
        sheet1.rows[2..-1].each do |row|
          next if row.nil? || row[1].blank? || row[2].blank?

          data << [row[1], row[2], row[4]]
        end
      end
      data.uniq!

      data.each do |row|
        code = row[0]
        name = row[1]
        department_name = row[2]
        department = @departments.find { |x| x.source_id == department_name }
        json = {
          quarter_id: @quarter.id,
          code:       code,
          name:       name
        }
        @accounts << ZdProp::Account
                       .create_with(
                         source_id:       code,
                         status:          true,
                         department_name: department_name,
                         department_id:   department&.id
                       )
                       .find_or_create_by(json)
      end
    end

    def import_data1_permissions
      data = []
      file_paths.each do |file_path|
        book = Spreadsheet.open(file_path)
        sheet2 = book.worksheets[1]
        sheet2.rows[1..-1].each do |row|
          next if row.nil? || row[1].blank?

          data << [row[1], row[2], row[3]]
        end
      end
      data.uniq!
      data.each do |row|
        code = row[0].to_s
        name = row[0].to_s
        level2_name = row[1].to_s
        level3_name = row[2].to_s

        json = {
          quarter_id:  @quarter_id,
          code:        code,
          level1_name: name,
          level2_name: level2_name,
          level3_name: level3_name
        }
        @data1_permissions << ZdProp::Data1Permission
                              .create_with(source_id: code)
                              .find_or_create_by(json)
      end
    end

    def import_data1_account_permissions
      data = []
      file_paths.each do |file_path|
        book = Spreadsheet.open(file_path)
        sheet2 = book.worksheets[1]
        sheet2.rows[1..-1].each do |row|
          next if row.nil? || row[0].blank? || row[1].blank?

          data << [row[0].to_s, row[1].to_s, row[6]]
        end
      end
      data.uniq!
      data.each do |row|
        account_code = row[0].to_s
        permission_code = row[1].to_s
        account = @accounts.find { |x| x.source_id.to_s == account_code }
        permission = @data1_permissions.find { |x| x.source_id.to_s == permission_code }
        next unless account && permission

        ZdProp::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: row[2]
        )
      end
    end

    protected

    def file_names
      Dir.entries(@path.to_s).select { |x| x.include?('prop权限列表') && x.include?('xls') }
    end

    # 所有文件路径
    def file_paths
      file_names.map { |name| @path.join(name) }
    end
  end
end
