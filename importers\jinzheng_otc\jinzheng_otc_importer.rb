module AasOracleImporter

  class JinzhengOtcImporter < ImporterBase

    def config
      @bs_id       = importer_config["bs_id"]
      @table_space = importer_config["table_space"]
      @sid_suffix  = importer_config["sid_suffix"]
      @accounts = []
      @roles = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      @data3_permissions = []
      @data3_accounts_roles_permissions = []
      
      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_org = "#{@table_space}ORG#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data3_permissions
      import_data3_account_permissions

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions
      import_data2_role_permissions

      import_ledgers(JinzhengOtc::Account)
    end

    def destroy_exist_datas
      JinzhengOtc::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JinzhengOtc::Account.where(quarter_id: @quarter_id)
      JinzhengOtc::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JinzhengOtc::Role.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data3Permission.where(quarter_id: @quarter_id).delete_all
      JinzhengOtc::Data3AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select org_code, org_code, org_name, parent_org from #{@table_org} where org_status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JinzhengOtc::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end
      # 建联关联关系
      JinzhengOtc::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JinzhengOtc::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JinzhengOtc::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts
      @departments = JinzhengOtc::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        sql = "select u.USER_CODE, u.USER_NAME,u.CORP_ORG,u.INT_ORG,u.CLOSE_DATE from #{@table_space}USERS u where u.USER_ROLES = 2"
        select_db_datas(sql).each do |r|
          status = r[4]
          department = @departments.find { |x| x.source_id == r[3]&.to_s }
          account = JinzhengOtc::Account.create(
            quarter_id:    @quarter_id,
            code:          r[0],
            name:          r[1],
            int_org:       r[3],
            corp_org:      r[2],
            status:        status.to_i.to_s == "0",
            department_id: department&.id
          )
          @accounts << account

          # display_status的值包括0.0和********.0「日期」,由于日期太多，所以要转换
          display_status = status.to_s == '0.0' ? status.to_s : '1.0'
          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JinzhengOtc::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     display_status
            )
          end
        end
      end
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas("select POST_CODE, POST_NAME from #{@table_space}POST").each do |r|
          @roles << JinzhengOtc::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, POST_CODE from #{@table_space}USER_POST").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          role = @roles.find{|x| x.code == r[1].to_s}
          if account && role
            account.roles << role
          end
        end
      end
    end
    
    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select RTRES_ID, RTRES_NAME from #{@table_space}SYS_RTRES").each do |r|
          @data1_permissions << JinzhengOtc::Data1Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1])
        end
      end
    end
    
    def import_data1_role_permissions
      org_scope_json = {
        "0" => "本机构",
        "1" => "下属机构",
        "2" => "本机构及下属机构"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.POST_CODE, a.RTRES_ID, a.ORG_CODE, a.ORG_SCOPE from #{@table_space}POST_RTRES a").each do |r|
          role = @roles.find{|x| x.code == r[0].to_s}
          permission = @data1_permissions.find{|x| x.code == r[1].to_s}
          org = @data3_permissions.find{|x| x.code == r[2].to_s}
          org_name = org ? org.name : ''
          org_name = '总部' if r[2].to_s == '0'
          org_name = '未指定机构' if r[2].to_s == '-1'
          next unless permission && role
          JinzhengOtc::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id, additional_permission: org_name , permission_scope: org_scope_json[r[3].to_s] )
        end
      end
    end

    def import_data1_account_permissions
      org_scope_json = {
        "0" => "本机构",
        "1" => "下属机构",
        "2" => "本机构及下属机构"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.USER_CODE, a.RTRES_ID, a.ORG_CODE, a.ORG_SCOPE from #{@table_space}USER_RTRES a").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data1_permissions.find{|x| x.code == r[1].to_s}
          org = @data3_permissions.find{|x| x.code == r[2].to_s}
          org_name = org ? org.name : ''
          org_name = '总部' if r[2].to_s == '0'
          org_name = '未指定机构' if r[2].to_s == '-1'
          next unless permission && account
          JinzhengOtc::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id, additional_permission: org_name , permission_scope: org_scope_json[r[3].to_s] )
        end
      end
    end

    def import_data2_permissions
      menu_type_json = {
        "0" => "父级菜单",
        "1" => "功能菜单",
        "2" => "菜单分割线"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.MENU_CODE,a.MENU_NAME,a.MENU_TYPE from #{@table_space}SYS_MENU a").each do |r|
          @data2_permissions << JinzhengOtc::Data2Permission.create(quarter_id: @quarter_id, code: r[0].to_i.to_s, name: r[1], level1_name: r[1], data_json: {menu_type: menu_type_json[r[2].to_s]})
        end
      end
    end

    def import_data2_account_permissions
      add_json = {
        "0" => "执行权限",
        "1" => "授权权限",
        "2" => "执行和授权"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.USER_CODE, a.MENU_CODE, a.RIGHT_TYPE from #{@table_space}USER_MENU a").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data2_permissions.find{|x| x.code == r[1].to_i.to_s}
          next unless permission && account

          JinzhengOtc::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data2_permission_id: permission.id, additional_permission: add_json[r[2].to_s])
        end
      end
    end

    def import_data2_role_permissions
      add_json = {
        "0" => "执行权限",
        "1" => "授权权限",
        "2" => "执行和授权"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.POST_CODE, a.MENU_CODE, a.RIGHT_TYPE from #{@table_space}POST_MENU a").each do |r|
          role = @roles.find { |x| x.code == r[0].to_s }
          permission = @data2_permissions.find { |x| x.code == r[1].to_i.to_s }
          next unless permission && role

          JinzhengOtc::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data2_permission_id: permission.id, additional_permission: add_json[r[2].to_s])
        end
      end
    end

    def import_data3_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select ORG_CODE, ORG_NAME, PARENT_ORG from #{@table_space}ORG").each do |r|
          @data3_permissions << JinzhengOtc::Data3Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1], level2_name: r[2])
        end
      end
    end

    def import_data3_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, RTOBJ from #{@table_space}USER_RTOBJ").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data3_permissions.find{|x| x.code == r[1].to_s}
          next unless permission && account
          JinzhengOtc::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r
      end
      output_datas
      
    end

  end
end



