module AasOracleImporter
  class DingdianA5Importer < ImporterBase
    def config
      @bs_id       = 284
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}tmc_gyxx#{@sid_suffix}"
      @table_role         = "#{@table_space}tmc_jsxx#{@sid_suffix}"
      @table_account_role = "#{@table_space}tmc_yhjsdy#{@sid_suffix}"
      @table_menu         = "#{@table_space}tmc_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}tmc_jscdqx#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions

      import_ledgers(DingdianA5::Account)
    end

    def destroy_exist_datas
      accounts = DingdianA5::Account.where(quarter_id: @quarter_id)
      DingdianA5::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      DingdianA5::Role.where(quarter_id: @quarter_id).delete_all
      DingdianA5::Data1Permission.where(quarter_id: @quarter_id).delete_all
      DingdianA5::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      "select GYDM, GYDM, XM, ZT from #{@table_account}"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << DingdianA5::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
        end
      end
    end

    def import_roles_sql
      "select JSDM, JSDM, JSMC from #{@table_role}"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << DingdianA5::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      "select JSDM, GYDM from #{@table_account_role} where SHBZ = 1"
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          DingdianA5::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      "select CDDM, CDDM, CDMC from #{@table_menu}"
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << DingdianA5::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      "select JSDM, CDDM, CDSX from #{@table_role_menu} where SHBZ = 1"
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          DingdianA5::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    # 附加权限
    def additional_permission_text(code)
      case code.to_s
      when '0' then '查询'
      when '1' then '维护'
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
