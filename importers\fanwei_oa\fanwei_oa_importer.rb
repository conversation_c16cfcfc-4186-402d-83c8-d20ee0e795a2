module AasOracleImporter

  class FanweiOaImporter < ImporterBase

    def config
      @bs_id       = 340
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_ledgers(FanweiOa::Account)
    end

    def destroy_exist_datas
      accounts = FanweiOa::Account.where(quarter_id: @quarter_id)
      FanweiOa::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      FanweiOa::Role.where(quarter_id: @quarter_id).delete_all
      FanweiOa::Data1Permission.where(quarter_id: @quarter_id).delete_all
      FanweiOa::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    
    def import_accounts_sql
      <<-EOF
      select 
        id,
        lastname,
        status
      from 
        hrmresource
      where 
        seclevel >= 10 and departmentid > 0
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << FanweiOa::Account.create(
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            name: r[1],
            status: r[2].to_s == '1'
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select id,ROLESMARK from hrmroles
      EOF
    end
    

    def import_roles 
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << FanweiOa::Role.create(
              quarter_id: @quarter_id,
              source_id: r[0],
              code: r[0],
              name: r[1]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select 
                        resourceid,roleid
                from 
                        HRMROLEMEMBERS 
      EOF
    end
    

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find{|x| x.source_id.to_s == r[1].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
          next unless account && role

          FanweiOa::AccountsRole.create(account_id: account.id, role_id: role.id  )
        end
      end
      
    end

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
