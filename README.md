# 稽核系统 Oracle 数据导入工具

## 项目目录结构

* customers 放置客户配置文件
* importers 放置不同系统的导入代码，该目录中的代码将打包后 link 到 lib/importers 目录中。每个子目录中必须有一个 `目录名_importer.rb` 的文件，并在代码中包含一个相应的类。

## 环境变量

* RAILS_ENV

## 2. 安装


###  2.3 Agent 安装

安装 Ruby （略）。

#### 2.3.1 安装 oracle-instantclient rpm 包

根据 Oracle 数据库的版本选择相应 instantclient 版本客户端，Oracle 11g 对应的客户端文档是 11.2.0.4。

```
rpm -Uvh oracle-instantclient11.2-basic-11.2.0.4.0-1.x86_64.rpm  \
         oracle-instantclient11.2-devel-11.2.0.4.0-1.x86_64.rpm  \
         oracle-instantclient11.2-sqlplus-11.2.0.4.0-1.x86_64.rpm
```

#### 2.3.2 配置Oracle 环境变量

其中版本根据安装的客户端版本不同稍有变化。TNS_ADMIN 指到实际 tnsnames.ora 文件存放路径。其他基本不变。

```
export ORACLE_HOME=/usr/lib/oracle/11.2/client64/
export LD_LIBRARY_PATH=/usr/lib/oracle/11.2/client64/lib/
export TNS_ADMIN="/opt/oracle/"
export PATH="/usr/lib/oracle/11.2/client64/bin/:$PATH"
export NLS_LANG=AMERICAN_AMERICA.AL32UTF8
```

#### 2.3.3 编译 oci8 gem


```
gem install ./ruby-oci8-2.2.2.gem
```

#### 2.3.4 配置连接 Oracle 数据库

在 oracle 11g 中，对 instantclient 连接的环境有了更高要求。在配置好 tnsnames.ora 文件后，如果还是无法连接数据库，请检查以下配置是否正确。

1. tnsnames.ora 文件中的 service_name 是否与 oracle 中相同。
2. tnsnames.ora 文件中 HOST 不能直接指定数据库的 IP 地址，需要指定域名，相关域名需要配置在 hosts 文件中。
3. 如果数据库做的 RAC ，不仅浮动 IP 要放开网络策略限制，RAC 各节点真实的 IP 也必须放开网络限制。
4. 本机的 hostname 应该在 hosts 文件中存在相应记录。


#### 2.3.5 配置连接稽核系统数据库

打开安装目录中的 `config/database.yml` 文件，填入数据库相关信息。

```yaml
adapter:  mysql2
encoding: utf8
database: show_dev
username: root
password: 密码
host: ip 地址
```


#### 2.3.6 配置应用所需环境变量 (未做)

为了保护 oracle 帐号密码安全，防止未授权访问，所有帐号密码均设置在环境变量中。

在环境变量中设置SI 、红页（HY 开头），挂标数据库（IN 开头）的用户和密码，和 DNS 上传的数据文件存储路径。

* SIUSER
* SIPASS
* HYUSER
* HYPASS
* INUSER
* INPASS
* DNS_DATA_PATH


## 3. 运行

### 开启客户端收集数据

单次执行：

执行 bin/agent 即开始收集数据，收集完成后退出程序。



god 接管：

可以使用 god 管理 agent 进程，目前策略是每 24 小时执行一次。进入项目根目录执行下面命令即可。

```
god -c agent.god
```

启动后可通过命令 `god status` 查看进程是否运行，以 aas 开头的是二期版本客户端，正常会显示 4 个进程都是 up 状态。

odbc配置：

gem 'odbc_adapter'

adapter: odbc
conn_str: DRIVER=DM8;SERVER=127.0.0.1;TCP_PORT=5237;UID=******;PWD=******;ENCODING=utf8;

odbc_adapter:  进去odbc_adapter, rake build 生成odbc_adapter.gem

import_log:
  enable: true # 是否开启导入日志
  start_at: 365 # 第一次导入多少天以前的数据，默认365
import_password_security:
  enable: true # 是否开启导入安全策略
import_last_login_at:
  enable: true # 是否开启导入最后时间
  start_at: 1 # 只通过1天前的日志，计算最后登录时间，默认全部

oceanbase配置：

gem 'oceanbase_adapter'

```
oceanbase:
  aas_db:
    db_host: 127.0.0.1
    db_name: aas_xdzq_development
    db_user: root
    db_pass: 123456
```

pg配置：

gem 'pg', '1.5.4'

```
pg:
  aas_db:
    db_host: 127.0.0.1
    db_name: aas_xdzq_development
    db_user: root
    db_pass: 123456
```



外部系统配置如下，添加agent和importer

```
mysql:
  aas_db:
    db_host: 127.0.0.1
    db_name: aas_xdzq_development
    db_user: root
    db_pass: 123456
```

```
- name: external1
  db_type: mysql
  tnsname: aas_db
  table_space: 'external_'
  sid_suffix: ''
- name: external2
  db_type: mysql
  tnsname: aas_db
  table_space: 'external_'
  sid_suffix: ''
- name: external3
  db_type: mysql
  tnsname: aas_db
  table_space: 'external_'
  sid_suffix: ''
- name: external4
  db_type: mysql
  tnsname: aas_db
  table_space: 'external_'
  sid_suffix: ''
- name: external5
  db_type: mysql
  tnsname: aas_db
  table_space: 'external_'
  sid_suffix: ''
```

oceanbase配置(Oracle模式)：

## 环境安装
安装JRUBY环境

## 启动oceanbase的DRB服务
文件路径：scripts/jdbc_oceanbase_server.rb
启动方式：jruby -J-cp '/Users/<USER>/app/oceanbase-client-2.4.8.jar' jdbc_oceanbase_server.rb
注意：jar包路径需要替换，文件路径也需要替换

```
oceanbase_oracle:
  oceanbase_db:
    drb_url: 'druby://localhost:3006'
    db_host: *************
    db_port: 2883
    db_name: 'SYS'
    db_user: 'SYS@QiDianHH_Oracle#isvtest420' # 用户名@租户#集群
    db_pass: 'QiDianHH12#%'
```

## 多客户端导入支持

```
1：客户端执行导入时，先创建redis key import_system_client，标记当前连接的客户端数量，初始为1，每增加一台加1，会动态判断是要创建新的时间点还是导入当前时间点内的系统，判断标准为redis是否存在key import_quarter，先执行的客户端会创建该key(过期时间两小时)，值为start，同时把所有要导入的系统存入redis，键为"import_system:quarter_id:#{@quarter.id}"，如果redis中已经import_quarter，则直接通过spop的方式消费redis里存的系统，一次消费的数量等于cpu核数，开始导入系统时，标记import_quarter的值为system_task，先导入员工，如果发现员工导入中则不停轮询判断，直到完成导入员工（app.yml指定is_user: true），再导入系统，导入完数据后，判断skip_after_import_rake参数，如果是true，则跳过跑批系统依赖任务，否则按照如下逻辑执行。

1）如果 after_importer_rake参数是'after_import:todo'在导入完系统后就触发after_import:todo_system，并且执行所有系统依赖任务后就执行  after_import:todo_non_system。

2）如果 after_importer_rake不是'after_import:todo'，在导入完系统后不触发after_import:todo_system，直接触发after_importer_rake指定的任务。

2：执行非系统依赖任务时，标记redis的key "after_import:todo_non_system:quarter_id:#{@quarter.id}"为running，其他客户端执行时会判断该键是否存在，如果存在则跳过，非系统依赖任务只能一个客户端执行，执行完后会删除该键。

3：所有任务都执行完时，并且当前客户端数量为0时，要释放key（import_system_client），该键释放必须判断连接的客户端，否则会引发如下问题

1）客户端1导入，客户端2执行ctrl+c，会释放锁，再次执行就会创建新的时间点
2）客户端1导入进行到非系统跑批，客户端2执行发现已经非系统跑批，会释放锁，再次执行会创建新的时间点，但是客户端1跑批还未完成


```

## o32参数导入

```
rake o32_option:import[quarter_id]

quarter_id如果为空，则创建时间点
```
