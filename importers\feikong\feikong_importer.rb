module AasOracleImporter

  class FeikongImporter < ImporterBase

    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_roles          = importer_config['table_roles']
      @table_roles_vl       = importer_config['table_roles_vl']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus          = importer_config['table_menus']
      @table_menus_roles    = importer_config['table_menus_roles']
      @table_descriptions   = importer_config['table_descriptions']
      @table_companies      = importer_config['table_companies']
      @table_employees      = importer_config['table_employees']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_menus_roles
      import_accounts_roles
      import_companies
      import_ledgers(Feikong::Account)
    end

    def import_accounts

      Feikong::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select su.user_name,
             su.description,
             su.frozen_flag,
             su.END_DATE,
             ee.EMPLOYEE_CODE
        from
             #{@table_accounts} su
        left join
             #{@table_employees} ee
        on su.employee_id = ee.employee_id
        order by su.user_name
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            # frozen_flag 为N或者空为有效
            # frozen_flag 为Y 或者 END_DATE存在并且小于等于当前日期是已冻结
            status = r[2] == 'Y' || r[3].present? && r[3] <= Date.current ? false : true
            Feikong::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1].strip,
              status:     status,
              objid:      r[4]
            )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      Feikong::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select role_code, role_name from #{@table_roles_vl}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Feikong::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
          )
        end
      end
    end

    def import_menus

      Feikong::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select function_code, description from #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Feikong::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus_roles

      sql = <<-EOF
        select a.role_code, f.function_code
         from #{@table_roles_vl} a,
              #{@table_menus_roles} rf,
              #{@table_menus} f
        where a.role_id = rf.role_id
          and rf.function_id = f.function_id
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = Feikong::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Feikong::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            FeikongMenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select DISTINCT su.user_name,
               b.role_code
          from #{@table_accounts_roles} a,
               #{@table_roles} b,
               #{@table_accounts} su
         where a.role_id = b.role_id
           and su.user_id = a.user_id
           and a.end_date is null
           and (sysdate >= a.start_date and sysdate < a.end_date or a.end_date is null)
           order by su.user_name
      EOF
      @accounts = Feikong::Account.where(quarter_id: @quarter_id)
      @roles = Feikong::Role.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = @accounts.find { |x| x.code&.to_s == r[0].to_s }
          role = @roles.find { |x| x.code&.to_s == r[1].to_s }
          if account and role
            FeikongAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_companies
      Feikong::Company.where(quarter_id: @quarter_id).destroy_all
      sql = <<-EOF
        select su.user_name,
               b.role_code ,
               fc.company_code,
               fc.company_short_name
          from #{@table_accounts_roles} a,
               #{@table_roles} b,
               #{@table_companies} fc,
               #{@table_accounts} su
         where a.role_id = b.role_id
           and a.company_id = fc.company_id(+)
           and su.user_id = a.user_id
           and su.end_date is null
           and a.end_date is null
           order by su.user_name
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = Feikong::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Feikong::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            Feikong::Company.create(
              quarter_id: @quarter_id,
              account_id: account.id,
              role_id: role.id,
              code: r[2]&.strip,
              name: r[3]&.strip
              )
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



