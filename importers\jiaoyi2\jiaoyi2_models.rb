module Jiaoyi2
  def self.table_name_prefix
    'jiaoyi2_'
  end
end

class Jiaoyi2::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class Jiaoyi2::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class Jiaoyi2::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class Jiaoyi2::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class Jiaoyi2::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class Jiaoyi2::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class Jiaoyi2::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class Jiaoyi2::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class Jiaoyi2::System < ActiveRecord::Base
  has_many :menus
end

class Jiaoyi2::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Jiaoyi2::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class Jiaoyi2::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class Jiaoyi2::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class Jiaoyi2::Temporary < ActiveRecord::Base; end

class Jiaoyi2AccountsRoles < ActiveRecord::Base; end
class Jiaoyi2MenusRoles < ActiveRecord::Base; end
class Jiaoyi2MenuAdditionsRoles < ActiveRecord::Base; end

class Jiaoyi2::TimeControl < ActiveRecord::Base; end

class Jiaoyi2::TopTradeType < ActiveRecord::Base; end
class Jiaoyi2::TopStation < ActiveRecord::Base; end
class Jiaoyi2::TradeType < ActiveRecord::Base; end
