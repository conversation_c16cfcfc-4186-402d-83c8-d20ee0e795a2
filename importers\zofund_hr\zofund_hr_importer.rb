# frozen_string_literal: true

module AasOracleImporter
  # 中欧 HR 系统导入
  class ZofundHrImporter < ImporterBase
    def config
      @bs_id                = importer_config['bs_id']
      @filename_departments = importer_config['file_departments']
      @filename_users       = importer_config['file_users']
      @filename_managers    = importer_config['file_managers']
      @filename_all_users   = importer_config['file_all_users']
      @position_users       = [] # 拥有岗位的用户
    end

    def import_to_do
      import_departments
      import_users
      update_user_columns
      import_managers
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    def departments_file
      @path.join @filename_departments
    end

    def user_file
      @path.join @filename_users
    end

    def all_user_file
      @path.join @filename_all_users
    end

    def department_manager_file
      @path.join @filename_managers
    end

    def parse_csv_file(file_path)
      file = File.open(file_path, 'r:bom|utf-8')
      CSV.parse(file.readlines.drop(2).join, headers: true)
    end

    def import_departments
      unless File.exist? departments_file
        @logger.error "CSV: not found file #{departments_file}"
        return
      end

      Department.update_all(inservice: false)

      csv = parse_csv_file(departments_file)
      csv.each do |row|
        import_one_department(row)
      end
    end

    def import_one_department(row)
      department = Department.find_by(code: row['Department id'])
      return unless row['Company id'].to_i == 1

      if department
        department.update(name: row['Department'], inservice: true)
      else
        Department.create(code: row['Department id'], name: row['Department'], inservice: true)
      end
    end

    def import_users
      unless File.exist? all_user_file
        @logger.error "CSV: not found file #{all_user_file}"
        return
      end

      @exist_users = User.all.to_a

      csv = parse_csv_file(all_user_file)
      csv.each do |row|
        import_one_user(row)
      end

      # 存在的都删除掉之后 exist_users 里剩余的就是离职的人了
      @exist_users.each { |u| u.update(inservice: false) }
    end

    def import_one_user(row)
      return unless row['Company id'].to_i == 1

      user = @exist_users.find { |x| x.code == row['Username'] }

      if user
        user.update(name: row['Name'], inservice: row['status'].match?(/Active/))
        @exist_users.delete_if { |x| x.code == user.code }
      else
        user = User.create(code: row['Username'], name: row['Name'], inservice: row['status'].match?(/Active/))
      end
      create_zofund_hr(user)
    end

    def create_zofund_hr(user)
      account = ZofundHr::Account.create(
        code:       user.code,
        name:       user.name,
        status:     user.inservice,
        quarter_id: @quarter_id,
        user_id:    user.id
      )
      import_ledger_to_account(account, user)
    end

    def update_user_columns
      unless File.exist? user_file
        @logger.error "CSV: not found file #{user_file}"
        return
      end

      @users       = User.all.to_a
      @departments = Department.all.to_a

      csv = parse_csv_file(user_file)
      csv.each do |row|
        update_one_user(row)
      end
    end

    def update_one_user(row)
      return unless row['Company id'].to_i == 1

      user = @users.find { |x| x.code == row['Username'] }

      unless user
        @logger.error "CSV: not found user when update user columns for #{row['Username']}"
        return
      end

      user.update(
        department_id: @departments.find { |x| x.code == row['Department id'] }&.id,
        position:      row['Title']
      )
      @position_users << user if user.position?
    end

    def import_managers
      unless File.exist? user_file
        @logger.error "CSV: not found file #{user_file}"
        return
      end

      @users = User.all.to_a
      csv    = parse_csv_file(user_file)
      csv.each do |row|
        import_one_manager row
      end
    end

    def import_one_manager(row)
      return unless row['Company id'].to_i == 1

      user    = @users.find { |u| u.code == row['Username'] }
      manager = @users.find { |u| u.code == row['Manager'] }

      unless user
        @logger.warn "import manager: not found user #{row['Username']}"
        return
      end

      unless manager
        @logger.warn "import manager: not found manager #{row['Manager']}"
        return
      end
      user.update(manager_id: manager.id)
    end

    # hr 的比较特殊，台账可以自动关联
    def import_ledger_to_account(account, user)
      # TODO: 这里默认就是 code
      ledger =
        Ledger.find_or_create_by(
          business_system_id: @bs_id,
          account_code_field: 'code',
          account_code_value: account.code
        )
      unless ledger.user_id
        ledger.update(
          user_id:         account.user_id,
          user_code_field: 'code',
          user_code_value: user.code
        )
      end
    end

    # 导入岗位
    def import_jobs
      unless File.exist? user_file
        @logger.error "CSV: not found file #{user_file}"
        return
      end

      csv = parse_csv_file(user_file)
      positions = []
      csv.each do |row|
        positions << row['Title']
      end
      positions = positions.uniq.compact
      positions.each do |position|
        Job.find_or_create_by(name: position)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end
