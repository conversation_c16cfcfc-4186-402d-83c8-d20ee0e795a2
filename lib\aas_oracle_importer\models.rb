require 'active_record'
require 'activerecord-jdbc-adapter' if defined? JRUBY_VERSION
require_relative 'config'
require 'yaml'
require 'ancestry'
require 'bulk_insert'

Dir[File.dirname(__FILE__) + '/../models/*.rb'].each { |file| require file }

dbconfig = AasOracleImporter.config['server']['database']
dbconfig.update(
  { 'password' => ConvertTools::Cryptology.decrypt_if_env(dbconfig['password']) }
)

ActiveRecord::Base.default_timezone = :local
ActiveRecord::Base.establish_connection(dbconfig)

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class Ledger < ActiveRecord::Base; end
class QuarterBusinessSystemImportStatus < ActiveRecord::Base
  before_create :setup_start_at

  def setup_start_at
    self.start_at = created_at if start_at.nil?
  end

  enum status: { pending: 0, processing: 1, success: 2, failed: 3, skipped: 4 }
end
class QuarterUserImportStatus < ActiveRecord::Base
  before_create :setup_start_at

  def setup_start_at
    self.start_at = created_at if start_at.nil?
  end

  enum status: { pending: 0, processing: 1, success: 2, failed: 3, skipped: 4 }
end
class QuarterSystemTaskStatus < ActiveRecord::Base
  belongs_to :quarter

  enum status: { not_start: 0, running: 1, success: 2, failed: 3 }
end

class Quarter < ActiveRecord::Base; end
class AdminAuditLog < ActiveRecord::Base; end
class BusinessSystem < ActiveRecord::Base
  serialize :model_names, Array

  def account_class_name
    # "FeikongAccount" or "Feikong::Account"
    model_names.find do |x|
      %W[#{prefix.camelize}Account #{prefix.camelize}::Account].include?(x)
    end
  end

  def account_class
    account_class_name.safe_constantize
  end
end
class SystemAccountsBaseline < ActiveRecord::Base; end
class QuarterAccountInfo < ActiveRecord::Base; end
class PublicAccount < ActiveRecord::Base; end
class Tag < ActiveRecord::Base; end
class PublicAccountType < ActiveRecord::Base; end
# 岗位
class Job < ActiveRecord::Base
  has_ancestry cache_depth: true

  has_many :job_users, dependent: :delete_all
  has_many :users, through: :job_users
end

class JobUser < ActiveRecord::Base
  belongs_to :job
  belongs_to :user
end

class UserLog < ActiveRecord::Base
end

class CustomerAuditLog < ActiveRecord::Base
end

class CustomerPasswordSecurity < ActiveRecord::Base
end

class User < ActiveRecord::Base
  belongs_to :department, optional: true
end

class Department < ActiveRecord::Base
  has_many :users
  has_many :children, class_name: 'Department', foreign_key: 'parent_id'
  belongs_to :parent, class_name: 'Department', optional: true
end

class O32OptionQuarter < ActiveRecord::Base; end
class O32Option < ActiveRecord::Base
  serialize :department_ids, JsonWithSymbolizeNames
  serialize :department_names, JsonWithSymbolizeNames
end
class O32OptionConfig < ActiveRecord::Base
  serialize :department_ids, JsonWithSymbolizeNames
  serialize :department_names, JsonWithSymbolizeNames

  belongs_to :department, optional: true
end