# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'zyfund'
  customer: '中银基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_zyfund_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      tradedb:
        db_host: ************
        db_name: hfttrade
        db_user: dc_query
        db_pass: dc_query
      guzhidb:
        db_host: ************
        db_name: fav45
        db_user: dc_query
        db_pass: DC_QUEYR
    http:
      oa:
        base_uri: http://***********:9264/api-gateway/sys
  importers:
    - name: zyfund_hr
      bs_id: 1
      db_type: http
      tnsname: oa
      user: ''
      password: ''
      base_uri: http://***********:9264/api-gateway/sys
      department_root_id: ''
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'hstrade.'
      sid_suffix: ''
      days_of_data: 90
      temporary: true
      temp_delete_record: true
      history_temp: true

    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: guzhidb
      table_space: hftgzv45.
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: <%= ENV['AAS_DATABASE_NAME'] %>
      host: <%= ENV['AAS_DATABASE_HOST'] %>
      port: <%= ENV['AAS_DATABASE_PORT'] %>
      username: <%= ENV['AAS_DATABASE_USER'] %>
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1



