# frozen_string_literal: true
module AasOracleImporter
  # 创造季度等相关方法
  module QuarterMethods
    private

    def generate_quarter(time = Time.now)
      time = time_after_reduce(time)
      # 为了支持多个客户端共同导入系统，会判断如果最后一个时间点是当天的，要判断redis里最后时间点是否还有未导入的系统，有的话执行最后时间点的导入
      # todo 改为通过redis 添加一个key来实现任务抢占的问题，两个小时，ctrl+c的自动注销redis
      # 页面下发导入任务强制创建时间点
      if AasOracleImporter::Agent.new.exist_import_quarter_key
        quarter = Quarter.last
      else
        AasOracleImporter::Agent.new.import_quarter_value('start')
        quarter = Quarter.create(name: quarter_name(time), created_at: time)
        # 审计日志
        AasOracleImporter.audit_log_quarter_created(quarter)
      end
      quarter
    end

    # 时间节点名称，如果有重复，后面加（数字）
    def quarter_name(time)
      quarter_format = importer_config['quarter_format'] || '%Y年%m月%d日'

      if quarter_format == 'previous_quarter'
        name_is_the_previous_quarter(time)
      else
        name = time.strftime(quarter_format)
        regexp = Regexp.new("^#{name}（*\\d*）*$")
        names = Quarter.where('name LIKE ?', "%#{name}%").pluck(:name)
        quarter_names = names.select { |x| x.match(regexp) }
        return name if quarter_names.length.zero?

        "#{name}（#{quarter_names.length + 1}）"
      end
    end

    # 每年的第几季度
    def name_is_the_previous_quarter(time)
      if time.month <= 3
        "#{time.year - 1}年第四季度"
      elsif time.month <= 6
        "#{time.year}年第一季度"
      elsif time.month <= 9
        "#{time.year}年第二季度"
      else
        "#{time.year}年第三季度"
      end
    end

    # quarter 创建时减掉的秒数，用于第二天创建稽核前一天的权限，创建的时间，应该取前一天的日期
    def quarter_reduce_seconds
      @quarter_reduce_seconds = importer_config['quarter_reduce_seconds'].to_i
      @logger.info { "quarter_reduce_seconds: #{@quarter_reduce_seconds}" }

      @quarter_reduce_seconds
    end

    def time_after_reduce(time)
      time = time - quarter_reduce_seconds
      # 如果工作日导入，自动找到上一个工作日计算时间
      if importer_config['quarter_name_is_workday']
        min_time = time - 30.day
        while TradingDays::Date.holiday?(time.to_date) && time >= min_time
          time = time - 1.day
          raise 'worday not find' if time < min_time
        end
      end
      time
    end

    def newest_quarter
      Quarter.last
    end

    def specify_quarter(quarter_id)
      Quarter.find(quarter_id)
    end
  end
end
