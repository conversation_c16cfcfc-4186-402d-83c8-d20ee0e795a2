require 'httparty'

module AasOracleImporter
  class ThfundUsersImporter < ImporterBase

    def config
      @host    = importer_config['host']
      @visitorId     = importer_config['visitorId']
      @token       = importer_config['token']
      @departments = []
      @users = []
      @old_department_codes = Department.where.not(code: %w[public disabled]).pluck(:code)
      @old_user_codes = User.pluck(:code)
    end

    def import_to_do
      import_departments
      import_users
    end

    private

    def import_departments
      body = {
        disableCache: true,
        param: {},
        visitorId: @visitorId,
        token: @token
      }

      uri = "/query/apiService/account/account_ehr_org_info"
      datas = post_server(uri, body)
      datas.each do |data|
        department = Department.find_or_create_by(code: data['department_code'])
        @old_department_codes.delete(department.code)
        department.update(
          name: data['department_name'],
          inservice: true
          )
      end

      datas.each do |data|
        department = Department.find_by(code: data['department_code'])
        parent_department = Department.find_by(code: data['leader_department_code'])
        department.update(parent_id: parent_department.id) if parent_department
      end

      Department.where(code: @old_department_codes).update_all(inservice: false)
    end

    def import_users
      body = {
        disableCache: true,
        param: {},
        visitorId: @visitorId,
        token: @token
      }

      uri = "/query/apiService/account/account_ehr_user_info"
      datas = post_server(uri, body)
      datas.each do |data|

        user = User.find_or_create_by(code: data['c_gonghao'])
        @old_user_codes.delete(user.code)
        department = Department.find_by(code: data['c_p_org'])
        if data['c_posname'].present?
          job_code = department ? "#{department&.name}-#{data['c_posname']}" : data['c_posname']
          job_name = data['c_posname']
          job = Job.find_or_create_by(code: job_code,name: job_name, department_id: department&.id)
          JobUser.where(user_id: user.id).delete_all
          JobUser.create(user_id: user.id, job_id: job.id)
        end
        user.update(
          name: data['c_personname'],
          department_id: department&.id,
          position: data['c_posname'],
          inservice: data['c_ryzt'].to_s == "在职",
          disable_date: data['c_p_to'],
          join_date: data['c_p_from']

        )
      end

      datas.each do |data|
        user = User.find_by(code: data['c_gonghao'])
        manager = User.find_by(code: data['c_pgonghao'])
        user.update(manager_id: manager.id) if manager
      end

      User.where(code: @old_user_codes).update_all(inservice: false)
    end

    def post_server(uri, body)

      
      url = "#{@host}#{uri}"
      response = HTTParty.post(url, body: body.to_json, headers: headers)
      result = JSON.parse(response.body)
      return result['data']
    end

    def headers
      {
        'Content-Type' => 'application/json'
      }
    end


  end
end
