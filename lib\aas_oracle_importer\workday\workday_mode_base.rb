# frozen_string_literal: true

require_relative '../config'

module AasOracleImporter
  module Workday
    # 各种 Workday 的父类，提供共用变量和方法
    class WorkdayModeBase
      attr_reader :date, :config, :logger

      def initialize(date, config)
        @date   = date
        @config = config
        @logger = AasOracleImporter.initialize_logger
      end

      def workday?
        # 由子类实现
      end
    end
  end
end
