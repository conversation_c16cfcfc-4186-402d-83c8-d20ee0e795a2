---
defaults:
  customer_id: fzfb
  customer: 方正富邦
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: &1
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_development
      username:
      password: 123456
      host: 127.0.0.1
  agent: &2
    oracle:
      o32db:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      gzdb:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      tadb:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      sadb:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
      zxdb:
        db_host: 
        db_name: 
        db_user: 
        db_pass: 
    mysql:
      seedb:
        db_host: 
        db_name: 
        db_port: 
        db_user: 
        db_pass: 
  importers: &3
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: o32_db
    table_space: HSTRADE.
    sid_suffix: ''
    days_of_data: 30
    temporary: true
    time_control: true
    temp_delete_record: true
    history_temp: true
    station_user_importer: true
    trade_type_importer: true
    old_fund_temp_check: true
    c_menu_type:
    - 1
    import_log:
      enable: false
      start_at: 5
    import_password_security:
      enable: false
    import_last_login_at:
      enable: false
      start_at: 1
    disable_status:
    - 3
  - name: guzhi_yss45
    bs_id: 24
    db_type: oracle
    tnsname: gzdb
    table_space: 'gz45.'
    sid_suffix: ''
    display_status: true
  - name: guohu_ta
    bs_id: 402
    db_type: oracle
    tnsname: tadb
    table_space: hsta4.
    sid_suffix: ''
  - name: guohu_sa
    bs_id: 26
    db_type: oracle
    tnsname: sadb
    table_space: subta_20210714.
    sid_suffix: ''
  - name: zhixiao_zhongxin
    bs_id: 27
    db_type: oracle
    tnsname: zxdb
    table_space: 'ds.'
    sid_suffix: ''
    sub_system: "CENTER"
  - name: fund_disclosure
    bs_id: 32
    db_type: oracle
    tnsname: fund_disclosure_yss_db
    table_space: ''
    sid_suffix: ''
    inservice_fund_statuses:
      - 募集期
      - 存续期
      - 已到期 
    disabled_fund_statuses:
      - 已清算
      - 已关帐

development:
  customer_id: fzfb
  customer: 方正富邦
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server: *1
  agent: *2
  importers: *3
production:
  customer_id: fzfb
  customer: 方正富邦
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: oceanbase
      encoding: utf8
      database: "<%= ENV['AAS_DATABASE_NAME'] %>"
      host: ************
      port: "<%= ENV['AAS_DATABASE_PORT'] %>"
      username: "<%= ENV['AAS_DATABASE_USER'] %>"
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
  agent: *2
  importers: *3
