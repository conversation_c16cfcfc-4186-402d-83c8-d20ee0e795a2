# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: zofund
  customer: 中欧基金
  ipaddr: localhost
  log_level: DEBUG
  quarter_reduce_seconds: 28800
  agent:
    oracle:
      xedmdb:
        db_name: xedm
        db_user: di_right
        db_pass: di_right
    csv:
      hr_csv:
        path: "/opt/samba_data/zofund_hr"
  importers:
    - name: zofund_hr_db
      bs_id: 1
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_trd.'
      sid_suffix: ''
      departments_table: 'sf_department_component1'
      users_table: 'sf_employeedata_component1'
      all_users_table: 'sf_employeedata_component'
      managers_table: ''
    - name: fund_disclosure_oracle
      bs_id: 32
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_edge.txyxp_
      sid_suffix: ''
      enable_fund_code_table: true
      fund_inservice: true
      mail_fund_import: true
      fund_code_table_space: xedm_ods.
    - name: huiyan
      bs_id: 10008
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_trd.di_
      sid_suffix: ''
      import_report: true
    - name: yunying_guanli
      bs_id: 10007
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_trd.di_edge_
      sid_suffix: ''
      import_report: true
      import_report_xmls:
        - /opt/xmls/Setting_GM.xml
        - /opt/xmls/Setting_TA.xml
        - /opt/xmls/Setting_ZC.xml
        - /opt/xmls/Setting_ZH.xml
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_edge.to32_
      sid_suffix: ''
      days_of_data: 30
      temporary: true
      c_menu_type:
        - 1
    - name: touzi_fengkong
      bs_id: 10006
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_trd.
      sid_suffix: ''
    - name: touzi_shuju
      bs_id: 10002
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_trd.
      sid_suffix: ''
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.thsgz_'
      sid_suffix: ''
      add_on_modules_disable: true
    - name: qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tzjqs_'
      sid_suffix: ''
    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tsubta_'
      sid_suffix: ''
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "CENTER"
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "COUNTER"
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tds_'
      sid_suffix: ''
      sub_system: "REMOTE"
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: xedmdb
      table_space: 'xedm_edge.tda4_'
      sid_suffix: ''
    - name: guzhi_yss45_zofund
      bs_id: 56
      db_type: oracle
      tnsname: xedmdb
      table_space: xedm_trd.di_edge_gz_
      sid_suffix: ''
      disabled_sys_account: true
      sync_booksets_table: true
  server:
    path: "/opt/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_zofund_production
      username: sdata
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: *************
      port: 3310
development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
