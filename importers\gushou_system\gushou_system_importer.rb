module AasOracleImporter

  class GushouSystemImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix  = importer_config['sid_suffix'] #''
    end

    def import_to_do
      import_accounts
      import_roles
      import_account_role
      import_permissions
      import_role_permissions
      import_ledgers(GushouSystem::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def account_sql
      "SELECT ACCOUNT_ID,ACCOUNT_NAME,VALID_FLAG,PK_SERIAL FROM #{@table_space}system_user_info where SYSTEM_ID = 'FIBMS'"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(account_sql) do |r|
          if r[0].to_s != ''
            GushouSystem::Account.create(quarter_id: @quarter_id, code: r[0], name: r[1], status: r[2].to_i == 0, objid: r[3])
          end
        end
      end
    end

    def roles_sql
      "SELECT PK_SERIAL,ROLE_NAME FROM #{@table_space}system_role_info where SYSTEM_ID = 'FIBMS' and VALID_FLAG = 0"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(roles_sql) do |r|
          if r[0].to_s != ''
            role = GushouSystem::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
          end
        end
      end
    end

    def account_role_sql
      "SELECT ACCOUNT_ID, ROLE_ID FROM #{@table_space}user_role_info where VALID_FLAG = 0"
    end

    def import_account_role
      ActiveRecord::Base.transaction do
        @database.exec(account_role_sql) do |r|
          if r[0].to_s != ''
            account = GushouSystem::Account.find_by(quarter_id: @quarter_id, objid: r[0])
            role = GushouSystem::Role.find_by(quarter_id: @quarter_id, code: r[1])
            if account && role
              account.roles << role
            end
          end
        end
      end
    end

    def permission_sql
      "SELECT PK_SERIAL, MENU_NAME, MENU_LEVEL FROM #{@table_space}system_menu_info where SYSTEM_ID = 'FIBMS' and VALID_FLAG = 0"
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.exec(permission_sql) do |r|
          if r[0].to_s != ''
            GushouSystem::Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], permission_type: '菜单权限', data_json: {level: r[2]})
          end
        end
      end
    end

    def role_permission_sql
      "SELECT MENU_ID, ROLE_ID FROM #{@table_space}menu_role_info where VALID_FLAG = 0"
    end

    def import_role_permissions
      ActiveRecord::Base.transaction do
        @database.exec(role_permission_sql) do |r|
          if r[0].to_s != ''
            permission = GushouSystem::Permission.find_by(quarter_id: @quarter_id, code: r[0])
            role = GushouSystem::Role.find_by(quarter_id: @quarter_id, code: r[1])
            if permission && role
              GushouSystem::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id: permission.id, role_id: role.id)
            end
          end
        end
      end
    end

  end
end



