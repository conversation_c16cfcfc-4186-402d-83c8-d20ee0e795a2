module QdAas
  def self.table_name_prefix
    'qd_aas_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class QdAas::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles, -> { distinct }
  
  
  has_many :data1_accounts_roles_permissions
  
  
  has_many :data2_accounts_roles_permissions
  
  
  has_many :data3_accounts_roles_permissions
  
  
  has_many :data4_accounts_roles_permissions


  has_many :data5_accounts_roles_permissions

  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end


class QdAas::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  
  
  has_many :data1_accounts_roles_permissions
  
  
  has_many :data2_accounts_roles_permissions
  
  
  has_many :data3_accounts_roles_permissions
  
  
  has_many :data4_accounts_roles_permissions
  

  has_many :data5_accounts_roles_permissions
  

  validates :code, presence: true
  validates :name, presence: true
end

class QdAas::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end


  
  class QdAas::Data1Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class QdAas::Data1AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'qd_aas_data1_arps'
    belongs_to :data1_permission
    belongs_to :role, optional: true

    validates :data1_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  
  class QdAas::Data2Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class QdAas::Data2AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'qd_aas_data2_arps'
    belongs_to :data2_permission
    belongs_to :role, optional: true

    # validates :data2_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  
  class QdAas::Data3Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class QdAas::Data3AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'qd_aas_data3_arps'
    belongs_to :data3_permission
    belongs_to :role, optional: true

    validates :data3_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  
  class QdAas::Data4Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class QdAas::Data4AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'qd_aas_data4_arps'
    belongs_to :data4_permission
    belongs_to :role, optional: true

    validates :data4_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end  


  class QdAas::Data5Permission < ActiveRecord::Base

    validates :code, presence: true
    validates :level1_name, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end


  class QdAas::Data5AccountsRolesPermission < ActiveRecord::Base
    self.table_name = 'qd_aas_data5_arps'
    belongs_to :data5_permission
    belongs_to :role, optional: true

    validates :data5_permission_id, presence: true

    serialize :data_json, JsonWithSymbolizeNames
  end

