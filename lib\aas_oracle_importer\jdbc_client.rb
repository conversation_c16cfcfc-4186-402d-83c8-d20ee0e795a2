# frozen_string_literal: true

module AasOracleImporter
  # CSV 客户端
  class JdbcClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
      @host = database_info['db_host']
      @port = database_info['db_port'].blank? ? 10001 : database_info['db_port']
      @db_name = database_info['db_name'].blank? ? 'default' : database_info['db_name']
      @db_user = database_info['db_user']
      @db_pass = database_info['db_pass']
      @drb_url = database_info['drb_url']
    end

    def exec(sql)
      if block_given?
        query(sql).each do |row|
          yield row
        end
      else
        query(sql)
      end
    end

    def query(sql)
      require 'drb'
      DRb.start_service
      server = DRbObject.new_with_uri(@drb_url)
      server.execute_query(@host, @port, @db_name, @db_user, @db_pass, sql)
    end

    private

    def initialize_driver

    end
  end
end
