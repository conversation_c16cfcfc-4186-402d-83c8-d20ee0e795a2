module AasOracleImporter
  class JzGupiaoImporter < ImporterBase
    def config
      @bs_id       = 253
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}USERS#{@sid_suffix}"
      @table_operator     = "#{@table_space}OPERATOR#{@sid_suffix}"
      @table_role         = "#{@table_space}POST#{@sid_suffix}"
      @table_account_role = "#{@table_space}USER_POST#{@sid_suffix}"
      @table_menu         = "#{@table_space}SYS_MENU#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}POST_MENU#{@sid_suffix}"
      @table_account_menu = "#{@table_space}USER_MENU#{@sid_suffix}"
      @table_org          = "#{@table_space}ORG#{@sid_suffix}"
      @table_user_org     = "#{@table_space}USER_RTOBJ#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_ledgers(JzGupiao::Account)
    end

    def destroy_exist_datas
      JzGupiao::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JzGupiao::Account.where(quarter_id: @quarter_id)
      JzGupiao::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzGupiao::Role.where(quarter_id: @quarter_id).delete_all
      JzGupiao::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzGupiao::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JzGupiao::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JzGupiao::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select org_code, org_code, org_name, parent_org from #{@table_org} where org_status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzGupiao::Department.create(
          quarter_id: @quarter_id,
          source_id:  r[0],
          code:       r[1],
          name:       r[2],
          status:     true
        )
      end
      # 建联关联关系
      JzGupiao::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzGupiao::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzGupiao::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts_sql
      <<-EOF
        SELECT A.USER_CODE,A.USER_CODE,A.USER_NAME, B.OP_STATUS OP_STATUS, A.INT_ORG FROM #{@table_account} A,#{@table_operator} B WHERE A.USER_CODE=B.OP_CODE
      EOF
    end

    def import_accounts
      @departments = JzGupiao::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          account = JzGupiao::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '0',
            department_id: department&.id
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzGupiao::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        SELECT POST_CODE,POST_CODE,POST_NAME FROM #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzGupiao::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        SELECT POST_CODE,USER_CODE,RIGHT_TYPE FROM #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          JzGupiao::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        SELECT MENU_CODE,MENU_CODE,MENU_NAME FROM #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << JzGupiao::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        SELECT POST_CODE,MENU_CODE, RIGHT_TYPE FROM #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzGupiao::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id,
            permission_scope:    permission_scope_text(r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        SELECT USER_CODE,MENU_CODE,RIGHT_TYPE FROM #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzGupiao::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id,
          permission_scope:    permission_scope_text(r[2])
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select ORG_CODE,ORG_CODE,ORG_NAME from #{@table_org}
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzGupiao::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        SELECT A.USER_CODE,A.RTOBJ FROM #{@table_user_org} A,#{@table_org} B WHERE A.RTOBJ=to_char(B.ORG_CODE)
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzGupiao::Data2AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data2_permission_id: permission.id,
          permission_scope:    permission_scope_text(r[2])
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def permission_scope_text(name)
      case name.to_s
      when '0' then '执行权限'
      when '1' then '授权权限'
      when '2' then '执行和授权权限'
      when '3' then '执行和导出权限'
      when '4' then '执行、导出、授权'
      end
    end
  end
end
