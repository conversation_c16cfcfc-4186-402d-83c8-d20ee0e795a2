module AasOracleImporter
  class HengtaiZhaiquanImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @permissions = []
      @accounts = []
      @roles = []
    end

    def import_to_do
      destroy_all_datas
      import_accounts
      import_roles
      import_permissions
      import_ledgers(HengtaiZhaiquan::Account)
    end

    private

    def destroy_all_datas
      HengtaiZhaiquan::Account.where(quarter_id: @quarter_id).destroy_all
      HengtaiZhaiquan::Role.where(quarter_id: @quarter_id).destroy_all
      HengtaiZhaiquan::Permission.where(quarter_id: @quarter_id).destroy_all
      HengtaiZhaiquan::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      "SELECT U_ID,U_NAME,U_STATE FROM #{@table_space}TSYS_USER"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            @accounts << HengtaiZhaiquan::Account.find_or_create_by(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i.zero?
            )
          end
        end
      end
    end

    def import_roles_sql
      "SELECT R_ID,R_FLAG,R_NAME FROM #{@table_space}TSYS_ROLE"
    end

    def import_accounts_roles_sql
      "SELECT R_ID,U_ID FROM #{@table_space}TSYS_USER_ROLE_MAPPING"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          if r[0].to_s != '' && r[1].to_s != '' && r[2].to_s != ''
            @roles << HengtaiZhaiquan::Role.find_or_create_by(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[2],
              role_type:  r[1]
            )
          end
        end
        @database.exec(import_accounts_roles_sql) do |r|
          role = @roles.find { |x| x.code.to_s == r[0].to_s }
          account = @accounts.find { |x| x.code.to_s == r[1].to_s }
          next unless account && role

          account.roles << role unless account.roles.include?(role)
        end
      end
    end

    def import_modules_sql
      "SELECT SM_ID,SM_PID,SM_CODE,SM_NAME FROM #{@table_space}TSYS_MODULE"
    end

    def import_role_modules_sql
      "SELECT R_ID,SM_ID,V_FLAG FROM #{@table_space}TSYS_ROLE_MODULE_MAPPING"
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_modules_sql) do |r|
          if r[0].to_s != '' && r[2].to_s != '' && r[3].to_s != ''
            @permissions << HengtaiZhaiquan::Permission.create(
              quarter_id:      @quarter_id,
              code:            r[0],
              name:            r[3],
              parent_code:     r[1],
              permission_type: '菜单权限'
            )
          end
        end
        @database.exec(import_role_modules_sql) do |r|
          role = @roles.find { |x| x.code.to_s == r[0].to_s }
          permission = @permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless role && permission

          HengtaiZhaiquan::AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            permission_id:         permission.id,
            role_id:               role.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    def additional_permission_text(code)
      case code.to_s
      when '0' then '读'
      when '1' then '写'
      else
        code
      end
    end
  end
end
