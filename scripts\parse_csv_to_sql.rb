
require 'csv'

def puts_select(row)
  puts "#{row[1]}: "
  puts <<-EOF
    select #{row[2].split(/(,\s+|\n)/).reject{|x| x == '' || x.match?(/\s+/)}.map(&:upcase).join(', ')} from #{row[0].downcase}
  EOF
  puts
  puts
end

def insert_column(field)
  "\t`#{field.upcase}` varchar(255) DEFAULT NULL"
end

def puts_create_table(row)
  columns = row[2].split(/(,\s+|\n)/).reject{|x| x == '' || x.match?(/\s+/)}
  puts <<-EOF
    DROP TABLE IF EXISTS `#{row[0].downcase}`;
    CREATE TABLE `#{row[0].downcase}` (
    #{columns.map{|x| insert_column(x)}.join(",\n")}
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
  EOF
  puts
end

file_path ='zx.csv'
file = File.open(file_path, 'r:bom|utf-8')
csv = CSV.parse(file.readlines.drop(0).join, headers: true)

csv.each do |row|
  #puts_create_table row
  puts row[0]
end




