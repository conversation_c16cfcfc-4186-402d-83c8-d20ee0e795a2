module AasOracleImporter

  class YhfundHrImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      dcpro_datas
      create_or_update_departments
      create_or_update_users
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    DcproData =
      Struct.new( :department_code, :department_name,
                  :user_code, :user_name, :email, :phone,
                  :position, :status_string, :departure_date) do
        def inservice
          status_string != '离职'
        end

        def present?
          user_code.present? && user_name.present?
        end
      end

    def dcpro_datas


      @datas = []

      sql = <<-EOF
        WITH T_TMP_1 AS
         (SELECT A.SK_ORG,
                 B.ORG_NAME,
                 A.SK_INVPTY,
                 C.EMP_NAME AS INVPTY_NAME,
                 RANK() OVER(PARTITION BY A.SK_INVPTY ORDER BY A.INSERTTIME DESC) AS RN,
                 C.OFFICE_EMAIL,
                 C.OFFICE_PHONE,
                 huoe.posdesc,
                 c.dk_emp_status,c.departure_date
            FROM YHODS.ORG_ORGEMP_RELA A
            LEFT JOIN YHODS.ORG_ORGINFO B
              ON (A.SK_ORG = B.SK_ORG AND B.DK_ORG_TYPE = '01' AND
                 B.SETUP_DATE <= TO_NUMBER(TO_CHAR(SYSDATE, 'yyyymmdd')) AND
                 B.END_DATE > TO_NUMBER(TO_CHAR(SYSDATE, 'yyyymmdd')))
            LEFT JOIN YHODS.ORG_EMPINFO C
              ON (A.SK_INVPTY = C.SK_INVPTY)
            left join yhstage.hr_uvw_oa_employee huoe on (c.hrno = huoe.empcode)
           WHERE A.DK_ORGEMP_RELA = '01'
             AND A.EFFECTIVE_FROM <= TO_NUMBER(TO_CHAR(SYSDATE, 'yyyymmdd'))
             AND A.EFFECTIVE_TO > TO_NUMBER(TO_CHAR(SYSDATE, 'yyyymmdd'))
             /*AND C.DK_EMP_STATUS = '1'*/)
        SELECT SK_ORG, ORG_NAME, SK_INVPTY, INVPTY_NAME, OFFICE_EMAIL, OFFICE_PHONE,posdesc,k.dict_desc,departure_date
          FROM T_TMP_1 left join yhods.comm_dict k on (t_tmp_1.dk_emp_status = k.dict_code and k.dict_type = upper('dk_emp_status'))
         WHERE RN = 1
      EOF

      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(sql) { |r| @datas << DcproData.new(*r.map(&:to_s)) }
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: ['public', 'disabled']).to_a
      old_codes = old_departments.map(&:code)
      new_codes = @datas.map(&:department_code).uniq

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          deparment_struct = @datas.find {|x| x.department_code == code }
          Department.create(
            code: deparment_struct.department_code,
            name: deparment_struct.department_name,
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find {|x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department       = old_departments.find {|x| x.code == code }
          deparment_struct = @datas.find {|x| x.department_code == code }

          department.update(name: deparment_struct.department_name)
        end
      end
    end

    def create_or_update_users
      departments = Department.all.reload.to_a
      old_users = User.all.to_a
      old_codes = old_users.map(&:code)
      new_codes = @datas.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          user_struct = @datas.find { |x| x.user_code == code }
          department  = departments.find {|x| x.code == user_struct.department_code }

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            email:         user_struct.email,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end

      # 已有
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @datas.find {|x| x.user_code == user.code }

          if user_struct && user_struct.present?
            department  = departments.find {|x| x.code == user_struct.department_code }

            user.update(
              name:          user_struct.user_name,
              email:         user_struct.email,
              position:      user_struct.position,
              inservice:     user_struct.inservice,
              department_id: department&.id
            )
            @position_users << user if user.position?
          end
        end
      end
    end

    def import_jobs_sql
      <<-EOF
        SELECT distinct posdesc FROM T_TMP_1
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



