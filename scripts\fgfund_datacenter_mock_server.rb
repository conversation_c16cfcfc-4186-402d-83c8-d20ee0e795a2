require 'sinatra'
require 'json'
require 'pp'

# 模拟富国数据中心接口，做出响应
class Check
  class << self
    def count
      @count ||= Hash.new(0)
    end

    def check(task_id, task_code)
      check_one_failed_mock(task_id, task_code)
    end

    private

    def check_success_mock(task_id, _task_code)
      return start_success_response if task_id.nil? || task_id == ''

      return finish_success_response if count[task_id] >= 5

      count[task_id] += 1
      not_finish_response
    end

    def check_all_failed_mock(task_id, _task_code)
      return start_success_response if task_id.nil? || task_id == ''

      return finish_not_success_response if count[task_id] >= 5

      count[task_id] += 1
      not_finish_response
    end

    def check_one_failed_mock(task_id, task_code)
      return start_success_response if task_id.nil? || task_id == ''

      if task_code == 'TASK20211019150627896582'
        return finish_not_success_response if count[task_id] >= 2
      elsif count[task_id] >= 10
        finish_success_response
      end

      count[task_id] += 1
      not_finish_response
    end

    def unknown_response
      ""
    end

    def start_success_response
      {
        Body: {},
        Head: {
          resFlag: 'S',
          status:  '0',
          taskId:  random_id,
          txnDate: Date.today.strftime('%Y%m%d'),
          txnTime: Time.now.strftime('%H%M%S')
        }
      }
    end

    def finish_success_response
      {
        Body: {
          const:            10_000,
          log:              "asdfasdf\n\nasdfasdf\nasdfasdf\nasdfasdf\nasdfasdf\n",
          logFile:          '/aaa/bbb/ccc.log',
          totalCount:       0,
          DF_ORACLE_TO_DAT: {
            checkStepName:   '文本文件输出',
            checkStepResult: {
              output:   974,
              input:    0,
              read:     974,
              rejected: 0,
              written:  974,
              updated:  0,
              errors:   0
            }
          },
          paramExternal:    {
            TX_DATE: '20210915'
          }
        },
        Head: {
          mac:     'mac',
          resFlag: 'S', # 失败置为 F
          status:  '1',
          taskId:  random_id,
          txnDate: Date.today.strftime('%Y%m%d'),
          txnTime: Time.now.strftime('%H%M%S')
        }
      }
    end

    def finish_not_success_response
      {
        Body: {
          const:            10_000,
          log:              "asdfasdf\n\nasdfasdf\nasdfasdf\nasdfasdf\nasdfasdf\n",
          logFile:          '/aaa/bbb/ccc.log',
          totalCount:       0,
          DF_ORACLE_TO_DAT: {
            checkStepName:   '文本文件输出',
            checkStepResult: {
              output:   974,
              input:    0,
              read:     974,
              rejected: 0,
              written:  974,
              updated:  0,
              errors:   0
            }
          },
          paramExternal:    {
            TX_DATE: '20210915'
          }
        },
        Head: {
          mac:     'mac',
          resFlag: 'F', # 失败置为 F
          status:  '1',
          taskId:  random_id,
          txnDate: Date.today.strftime('%Y%m%d'),
          txnTime: Time.now.strftime('%H%M%S')
        }
      }
    end

    def not_found_response
      {
        Body: {},
        Head: {
          msgCode: 'DATAAPI500',
          msgInfo: '发生异常, 详细信息，该任务不存在',
          resFlag: 'F',
          status:  '1',
          taskId:  random_id,
          txnDate: Date.today.strftime('%Y%m%d'),
          txnTime: Time.now.strftime('%H%M%S')
        }
      }
    end

    def not_finish_response
      {
        Body: {},
        Head: {
          resFlag: 'S',
          status:  '0',
          taskId:  random_id,
          txnDate: Date.today.strftime('%Y%m%d'),
          txnTime: Time.now.strftime('%H%M%S')
        }
      }
    end

    def random_id
      "PL#{(0...15).map { (rand(48..57)).chr }.join}"
    end
  end
end

set :default_content_type, 'application/json'
post '/asyncRequest' do
  request.body.rewind
  data = JSON.parse request.body.read
  pp 'request:'
  pp data
  task_id   = data['Head']['taskId']
  task_code = data['Body']['taskNo']
  result    = Check.check(task_id, task_code)
  pp 'response:'
  pp result
  result.to_json
end
