require 'httparty'
require 'openssl'
# require 'sm2_crypto'
require 'base64'
require_relative '../../lib/sm2_crypto'


module AasOracleImporter

  class Ims2022Importer < ImporterBase

    def config
      @bs_id       = 362
      @accounts    = {}
      @roles       = {}
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @base_url = importer_config['base_url'] #glajj.
      @token = importer_config['token'] #''
      @reqSys = importer_config['reqSys']
      @time = Time.now
      @data1_permissions = {}
      @data1_permissions_row = nil
      @acc_user = importer_config['acc_user']
      @acc_pass = importer_config['acc_pass']
      @acc_public_key_url = importer_config['acc_public_key_url']
      @acc_login_url = importer_config['acc_login_url']
      @data_permission_url = importer_config['data_permission_url']
      @acc_sys_code = importer_config['acc_sys_code']
      @data_permissions = {}
      @acc_token = get_acc_token
      @all_data = {}
      @all_asset = {}
      @data_enum_json = {}
      @fund_code_json = {}
      @all_fund = {}

      @ims_db_client = OracleClient.new('oracle', importer_config['ims_tns'])
      initialize_tables
    end



    def initialize_tables
    end

    def import_to_do
      log_memory_usage("开始导入")

      destroy_exist_datas
      log_memory_usage("清理完成")

      import_accounts
      force_gc_if_needed
      log_memory_usage("账户导入完成")

      import_roles
      force_gc_if_needed
      log_memory_usage("角色导入完成")

      import_accounts_roles
      force_gc_if_needed
      log_memory_usage("账户角色关系导入完成")

      import_assets
      force_gc_if_needed
      log_memory_usage("资产导入完成")

      import_accounts_assets
      force_gc_if_needed
      log_memory_usage("账户资产关系导入完成")

      import_data_permissions
      force_gc_if_needed
      log_memory_usage("数据权限导入完成")

      import_accounts_data_permissions
      force_gc_if_needed
      log_memory_usage("账户数据权限关系导入完成")

      import_data1_permissions
      force_gc_if_needed
      log_memory_usage("菜单权限导入完成")

      import_data1_role_permissions
      force_gc_if_needed
      log_memory_usage("角色菜单权限关系导入完成")

      import_funds
      force_gc_if_needed
      log_memory_usage("基金导入完成")

      import_accounts_funds
      force_gc_if_needed
      log_memory_usage("账户基金关系导入完成")

      import_ledgers(Ims2022::Account)
      log_memory_usage("导入完成")
    end

    # 内存监控方法
    def log_memory_usage(stage)
      if defined?(GC) && GC.respond_to?(:stat)
        memory_mb = GC.stat[:heap_live_slots] * 40 / 1024 / 1024  # 粗略估算
        @logger.info "#{stage} - 内存使用: ~#{memory_mb}MB, 对象数: #{GC.stat[:heap_live_slots]}"
      end
    end

    # 强制垃圾回收
    def force_gc_if_needed
      if defined?(GC)
        GC.start
        @logger.debug "执行垃圾回收"
      end
    end

    # 清理不再需要的缓存数据
    def clear_cache_if_needed(cache_name)
      case cache_name
      when :accounts_after_roles
        # 在角色导入完成后，可以考虑清理部分账户缓存
        @logger.debug "保留账户缓存用于后续关联"
      when :data_enum_cache
        # 清理数据枚举缓存
        @data_enum_json.clear if @data_enum_json.size > 1000
        @logger.debug "清理数据枚举缓存" if @data_enum_json.empty?
      when :temp_permissions
        # 清理临时权限数据
        @data1_permissions_row = nil
        @logger.debug "清理临时权限数据"
      end
    end

    def sm2(password, public_key)
      public_key = [public_key].pack('H*')
      password = Base64.encode64(password)
      data = SM2Crypto.encrypt(public_key, password.gsub("\n", ""), cipher_mode: 0)
      data.unpack1('H*')
    end

    def get_acc_token
      headers = {
        'X-Forwarded-For' => ENV['AAS_DOMAIN'],
        'Content-Type' => 'application/json'
      }
      response = HTTParty.get(@acc_public_key_url, headers)
      public_key = JSON.parse(response.body)['data']
    


      body = {
        userId: @acc_user,
        password: sm2(@acc_pass, public_key),
        sysCode: @acc_sys_code
      }

      response = HTTParty.post(@acc_login_url, body: body.to_json, headers: headers)

      result = JSON.parse(response.body)['data']['token']
    end

    def get_data_enum(code)
      return @data_enum_json[code.to_s] if @data_enum_json[code.to_s]
      headers = {
        'X-Forwarded-For' => ENV['AAS_DOMAIN'],
        'Content-Type' => 'application/json',
        'User-Token' => @acc_token
      }
      url = "#{@data_permission_url}/api/ims-acc/web/passtoken/dict/list?dictType=#{code}"
      response = HTTParty.get(url, headers)
      result = JSON.parse(response.body)['data'].first
      @data_enum_json[code.to_s] = result.blank? ? code : result['dictName']
      return @data_enum_json[code.to_s]
    end

    def data_models_sql
      <<-EOF
        select DISTINCT s_datamodel from t_fw_data_right
      EOF
    end

    def import_data_permissions
      process_db_rows(data_models_sql) do |r|
        headers = {
          'X-Forwarded-For' => ENV['AAS_DOMAIN'],
          'Content-Type' => 'application/json',
          'User-Token' => @acc_token
        }
        body = {
          dataModels: [r[0]]
        }
        url = "#{@data_permission_url}/api/ims-acc/web/data/dataModel/metaData"
        response = HTTParty.post(url, body: body.to_json, headers: headers)
        datas = JSON.parse(response.body)['data']
        import_data(datas)
      end
    end

    def import_data(datas)
      datas.each do |data|
        next if @all_data[data['dataId'].to_s]
        @all_data[data['dataId'].to_s] = Ims2022::DataPermission.create(
          quarter_id: @quarter_id, 
          model_p_code: data['dataModel'], 
          model_p_name: data['dataModelName'],
          code: data['dataId'],
          name: data['dataName'],
          parent_code: data['parentId']
        )
      end
    end

    def import_assets
      # 先导入基金
      import_assets_sql = "select L_FUND_ID fundId, F_CODE code, F_NAME name, T_END_DATE endDate from idc.t_fmn_fundinfo t where t.s_fund_status not in ('4','5')"
      @ims_db_client.exec(import_assets_sql) do |r|
        code = "fund_#{r[1]}"
        next if @all_asset[code]
        @all_asset[code] = Ims2022::Asset.create(
          quarter_id: @quarter_id, 
          asset_type: "基金", 
          code: code,
          name: r[2]
        )
      end

      # 资产
      headers = {
        'X-Forwarded-For' => ENV['AAS_DOMAIN'],
        'Content-Type' => 'application/json',
        'User-Token' => @acc_token
      }
      body = {
        labelGroupCodeList: ["assettype"]
      }
      url = "#{@data_permission_url}/api/ims-pa/external/label/sys/query"
      response = HTTParty.post(url, body: body.to_json, headers: headers)
      assets = JSON.parse(response.body)['data'][0]['labelList']

      assets.each do |asset|
        code = "asset_#{asset['labelId']}"
        next if @all_asset[code]
        @all_asset[code] = Ims2022::Asset.create(
          quarter_id: @quarter_id, 
          asset_type: "资产", 
          code: code,
          name: asset['labelName']
        )
      end

    end

    def import_accounts_assets_sql
      <<-EOF
        SELECT
         u.S_USERID 用户ID,
         u.S_USERNAME 用户姓名,
         '用户' 对象类型,
         drp.S_OWNERID 对象ID,
         u.S_USERNAME 对象名称,
         CASE drp.S_RIGHTTYPE
        WHEN 'SQ' THEN
         '授权权限'
        WHEN 'SY' THEN
         '使用权限'
        ELSE
         drp.S_RIGHTTYPE
        END 操作类型,
         CASE drp.S_DATAMODEL
        WHEN 'FUND' THEN
         '基金'
        WHEN 'ASSET' THEN
         '资产'
        ELSE
         drp.S_DATAMODEL
        END 数据类型,
         CASE drp.S_RIGHT_CODE
        WHEN 'READ' THEN
         '查看'
        ELSE
         drp.S_RIGHT_CODE
        END 权限类型,
         drp.S_DATAID 数据ID,
         drp.L_ID 主键ID
        FROM
         T_FW_DATA_RIGHT_POST drp
        INNER JOIN t_fw_user u ON u.S_USERID = drp.S_OWNERID
        AND S_OWNERTYPE = 'USER'
        WHERE
         drp.L_STATUS = 1
        AND u.L_STATUS = 1
        AND u.S_IS_INNER = 'Y'
        AND u.S_JOB_STATUS = 'ZZ'
      EOF
    end

    def import_accounts_assets
      stream_db_datas(import_accounts_assets_sql, batch_size: 500) do |batch|
        datas = []
        batch.each do |r|
          account = @accounts[r[0].to_s]
          code = r[6] == "基金" ? "fund_#{r[8]}" : "asset_#{r[8]}"
          permission = @all_asset[code]
          if account && permission
            datas << [@quarter_id, account.id, permission.id, r[5], r[7], @time, @time]
          end
        end

        # 批量插入当前批次的数据
        if datas.any?
          Ims2022::AccountsAsset.bulk_insert(:quarter_id, :account_id, :asset_id, :operation_type, :permission_type, :created_at, :updated_at) do |obj|
            obj.set_size = 500
            datas.each do |data|
              obj.add data
            end
          end
        end

        # 清理临时数据
        datas.clear
      end
    end


    def import_accounts_data_permissions_sql
      <<-EOF
        SELECT
         u.S_USERID 用户ID,
         u.S_USERNAME 用户姓名,
         '用户' 对象类型,
         dr.S_OWNERID 对象ID,
         u.S_USERNAME 对象名称,
         CASE dr.S_RIGHTTYPE
        WHEN 'SQ' THEN
         '授权权限'
        WHEN 'SY' THEN
         '使用权限'
        ELSE
         dr.S_RIGHTTYPE
        END 操作类型,
         dr.S_DATAMODEL 数据模型代码,
         dm.S_DATA_MODEL_NAME 数据模型名称,
         dr.S_DATAID 数据ID,
         dr.S_RIGHT_CODE 权限类型代码
        FROM
         t_fw_data_right dr
        INNER JOIN t_fw_user u ON u.S_USERID = dr.S_OWNERID
        AND dr.S_OWNERTYPE = 'USER'
        INNER JOIN t_fw_data_model dm ON dm.S_DATA_MODEL = dr.S_DATAMODEL
        WHERE
         dr.L_STATUS = 1
        AND u.L_STATUS = 1
        AND dm.L_STATUS = 1
        AND u.S_JOB_STATUS = 'ZZ'
        AND dm.S_PLATFORM_CODE = 'EFUNDS'
        UNION ALL
         -- 群组用户的数据权限
         SELECT
          u.S_USERID 用户ID,
          u.S_USERNAME 用户姓名,
          '群组' 对象类型,
          dr.S_OWNERID 对象ID,
          g.S_GROUPNAME 对象名称,
          CASE dr.S_RIGHTTYPE
         WHEN 'SQ' THEN
          '授权权限'
         WHEN 'SY' THEN
          '使用权限'
         ELSE
          dr.S_RIGHTTYPE
         END 操作类型,
         dr.S_DATAMODEL 数据模型代码,
         dm.S_DATA_MODEL_NAME 数据模型名称,
         dr.S_DATAID 数据ID,
         dr.S_RIGHT_CODE 权限类型代码
        FROM
         T_FW_DATA_RIGHT dr
        INNER JOIN T_FW_GROUP g ON CONVERT (g.L_GROUPID, CHAR) = dr.S_OWNERID
        AND dr.S_OWNERTYPE = 'GROUP'
        INNER JOIN T_FW_USER_RELATIONSHIP ur ON ur.L_REL_ID = g.L_GROUPID
        AND ur.S_REL_TYPE = 'GROUP'
        INNER JOIN T_FW_USER u ON u.S_USERID = ur.S_USERID
        AND ur.S_REL_TYPE = 'GROUP'
        INNER JOIN t_fw_data_model dm ON dm.S_DATA_MODEL = dr.S_DATAMODEL
        WHERE
         dr.L_STATUS = 1
        AND g.L_STATUS = 1
        AND ur.L_STATUS = 1
        AND u.L_STATUS = 1
        AND dm.L_STATUS = 1
        AND u.S_JOB_STATUS = 'ZZ'
        AND dm.S_PLATFORM_CODE = 'EFUNDS'
        UNION ALL
         -- 角色用户的数据权限
         SELECT
          u.S_USERID 用户ID,
          u.S_USERNAME 用户姓名,
          '角色' 对象类型,
          dr.S_OWNERID 对象ID,
          r.S_ROLENAME 对象名称,
          CASE dr.S_RIGHTTYPE
         WHEN 'SQ' THEN
          '授权权限'
         WHEN 'SY' THEN
          '使用权限'
         ELSE
          dr.S_RIGHTTYPE
         END 操作类型,
         dr.S_DATAMODEL 数据模型代码,
         dm.S_DATA_MODEL_NAME 数据模型名称,
         dr.S_DATAID 数据ID,
         dr.S_RIGHT_CODE 权限类型代码
        FROM
         T_FW_DATA_RIGHT dr
        INNER JOIN t_fw_ROLE r ON CONVERT (r.L_ROLEID, CHAR) = dr.S_OWNERID
        AND dr.S_OWNERTYPE = 'ROLE'
        INNER JOIN T_FW_USER_RELATIONSHIP ur ON ur.L_REL_ID = r.L_ROLEID
        AND ur.S_REL_TYPE = 'ROLE'
        INNER JOIN T_FW_USER u ON u.S_USERID = ur.S_USERID
        AND ur.S_REL_TYPE = 'ROLE'
        INNER JOIN t_fw_data_model dm ON dm.S_DATA_MODEL = dr.S_DATAMODEL
        WHERE
         dr.L_STATUS = 1
        AND r.L_STATUS = 1
        AND ur.L_STATUS = 1
        AND u.L_STATUS = 1
        AND dm.L_STATUS = 1
        AND u.S_JOB_STATUS = 'ZZ'
        AND dm.S_PLATFORM_CODE = 'EFUNDS';
      EOF
    end

    def import_accounts_data_permissions
      stream_db_datas(import_accounts_data_permissions_sql, batch_size: 500) do |batch|
        datas = []
        batch.each do |r|
          account = @accounts[r[0].to_s]
          permission = @all_data[r[8].to_s]
          if account && permission
            permission_type = get_data_enum(r[9].to_s)
            datas << [@quarter_id, r[2],r[3],r[4], account.id, permission.id, r[5], permission_type, @time, @time]
          end
        end

        # 批量插入当前批次的数据
        if datas.any?
          Ims2022::AccountsDataPermission.bulk_insert(:quarter_id,:obj_type, :obj_code, :obj_name, :account_id, :data_permission_id, :operation_type, :permission_type, :created_at, :updated_at) do |obj|
            obj.set_size = 500
            datas.each do |data|
              obj.add data
            end
          end
        end

        # 清理临时数据
        datas.clear
      end
    end



    def destroy_exist_datas
      accounts = Ims2022::Account.where(quarter_id: @quarter_id)
      Ims2022::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Ims2022::Role.where(quarter_id: @quarter_id).delete_all
      
      
      Ims2022::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      Ims2022::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
      Ims2022::Fund.where(quarter_id: @quarter_id).delete_all

      Ims2022::AccountsFund.where(quarter_id: @quarter_id).delete_all

      Ims2022::DataPermission.where(quarter_id: @quarter_id).delete_all
      Ims2022::AccountsDataPermission.where(quarter_id: @quarter_id).delete_all

      Ims2022::Asset.where(quarter_id: @quarter_id).delete_all
      Ims2022::AccountsAsset.where(quarter_id: @quarter_id).delete_all

      
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_funds
      # 导入基金信息
      sql = "select l_fund_id,f_code, f_name, s_fund_type, s_fund_status from ims.t_fmn_fundinfo"
      @ims_db_client.exec(sql) do |r|
        code = "fund_#{r[1]}"
        f_id = "fund_#{r[0]}"
        @fund_code_json[r[1].to_s] = r[0]
        next if @all_fund[f_id]
        @all_fund[f_id] = Ims2022::Fund.create(
          quarter_id: @quarter_id,
          fund_type: r[3],
          code: code,
          name: r[2],
          fund_status: r[4],
          parent_code: nil,
          source_id: f_id,
          permission_type: "基金"
        )
      end

      # 强制垃圾回收
      force_gc_if_needed

      # 导入资产信息
      sql = "select l_asset_id, l_fund_id, s_asset_no, s_asset_name, s_asset_status from ims.t_fmn_assetinfo"
      @ims_db_client.exec(sql) do |r|
        code = "asset_#{r[2]}"
        f_id = "fund_#{r[1]}"
        fund = @all_fund[f_id]
        a_id = "asset_#{r[0]}"
        next if @all_fund[a_id] || fund.blank?
        @all_fund[a_id] = Ims2022::Fund.create(
          quarter_id: @quarter_id,
          fund_type: fund.fund_type,
          code: code,
          name: "#{fund.name} / #{r[3]}",
          fund_status: r[4],
          parent_code: fund.id,
          source_id: a_id,
          permission_type: "资产单元"
        )
      end

      # 强制垃圾回收
      force_gc_if_needed

      # 导入组合信息
      sql = "select l_combi_id, l_asset_id, s_combi_no, s_combi_name, s_combi_status, l_fund_id from ims.t_fmn_combiinfo"
      @ims_db_client.exec(sql) do |r|
        code = "combi_#{r[2]}"
        a_id = "asset_#{r[1]}"
        asset = @all_fund[a_id]
        c_id = "combi_#{r[0]}"
        f_id = "fund_#{r[5]}"
        fund = @all_fund[f_id]
        next if @all_fund[c_id] || asset.blank? || fund.blank?
        @all_fund[c_id] = Ims2022::Fund.create(
          quarter_id: @quarter_id,
          fund_type: fund.fund_type,
          code: code,
          name: "#{asset.name} / #{r[3]}",
          fund_status: r[4],
          parent_code: asset.id,
          source_id: c_id,
          permission_type: "组合"
        )
      end
    end

    def fund_status_array
      @fund_status_array ||= ['unknown','inservice','disabled']
    end

    def import_accounts_funds_sql
      <<-EOF
        SELECT
         L_ID 主键ID,
         S_REL_ID 用户ID,
         CASE S_DATAMODEL WHEN 'FUND' THEN '基金' WHEN 'ASSET' THEN '资产单元' WHEN 'COMBI' THEN '组合' ELSE S_DATAMODEL END 数据类型,
          S_DATAID 数据ID,
          S_PARENT_DATAID 父数据ID,
          CASE S_RIGHTTYPE WHEN 'SQ' THEN '授权权限' WHEN 'SY' THEN '使用权限' ELSE S_RIGHTTYPE END 操作类型,
          CASE S_RIGHT_CODE WHEN 'READ' THEN '查看' ELSE S_RIGHT_CODE END 权限类型
        FROM t_fw_pa_fund_data_right
        WHERE L_STATUS = 1
          AND S_PLATFORM_CODE = 'EFUNDS'
          AND S_REL_TYPE = 'USER'
      EOF
    end

    def import_accounts_funds
      ActiveRecord::Base.transaction do
        stream_db_datas(import_accounts_funds_sql, batch_size: 500) do |batch|
          datas = []
          batch.each do |r|
            case r[2]
            when "基金"
              f_id = "fund_#{@fund_code_json[r[3]]}"
            when "资产单元"
              f_id = "asset_#{r[3]}"
            when "组合"
              f_id = "combi_#{r[3]}"
            else
              next
            end

            fund = @all_fund[f_id]
            next unless fund

            account = @accounts[r[1].to_s]
            next unless account

            datas << [@quarter_id, account.id, fund.id, r[5], r[6], @time, @time]
          end

          # 批量插入当前批次的数据
          if datas.any?
            Ims2022::AccountsFund.bulk_insert(:quarter_id, :account_id, :fund_id, :operation_type, :permission_type, :created_at, :updated_at) do |obj|
              obj.set_size = 500
              datas.each do |data|
                obj.add data
              end
            end
          end

          # 清理临时数据
          datas.clear
        end
      end
    end

    
    def import_accounts_sql
      <<-EOF
        SELECT
   u.S_USERID AS 账号ID,
   u.S_USERID AS 账号编码,
   u.S_USERNAME AS 账号名称,
   u.L_STATUS AS 账号状态,
   u.S_JOB_STATUS,
   u.S_ISLOCK
  FROM
   t_fw_user u
  WHERE
   u.L_STATUS = 1
  AND u.S_IS_INNER = 'Y'
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        process_db_rows(import_accounts_sql) do |r|
          next if @accounts[r[0].to_s]
          account = Ims2022::Account.create(
            quarter_id: @quarter_id,
            source_id: r[0].to_s,
            code: r[1].to_s,
            name: r[2].to_s,
            status: r[4] != 'LZ' && r[5] != "Y"
          )
          @accounts[r[0].to_s] = account
        end
      end
    end

    

    
    def import_roles_sql
      <<-EOF
        SELECT
 r.L_ROLEID AS 角色id,
 r.L_ROLEID AS 角色code,
 r.S_ROLENAME AS 角色名称,
 r.S_ROLETYPE AS 角色类型,
 r.L_PARENTID
FROM
 t_fw_role r,
 t_fw_board_info b
WHERE
 r.L_STATUS = 1
AND b.L_STATUS = 1
AND r.L_BOARDID = b.L_BOARDID
AND b.S_PLATFORM_CODE = 'EFUNDS'
      EOF
    end
    

    def import_roles
      ActiveRecord::Base.transaction do
        process_db_rows(import_roles_sql) do |r|
          next if @roles[r[0].to_s]
          role = Ims2022::Role.create(
              quarter_id: @quarter_id,
              source_id: r[0],
              code: r[1],
              name: r[2],
              full_name: r[2],
              role_type: r[3],
              parent_code: r[4],
              child_codes: []
          )

          @roles[r[0].to_s] = role
        end

        @roles.each do |key, value|
          set_child_codes(value)
        end
      end
    end

    def set_child_codes(role)
      child_codes = []
      full_name = role.full_name
      while parent_role = @roles[role.parent_code.to_s]
        child_codes = parent_role.child_codes | [role.source_id]
        full_name = "#{parent_role.name} / #{full_name}"
        parent_role.update(child_codes: child_codes)
        role.update(full_name: full_name)
        role = parent_role
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        SELECT
 r.L_ROLEID AS 角色id,
 ur.s_USERID AS 账号ID
FROM
 t_fw_user_relationship ur,
 t_fw_role r,
 t_fw_board_info b
WHERE
 ur.L_STATUS = 1
AND r.L_STATUS = 1
AND b.L_STATUS = 1
AND ur.S_REL_TYPE = 'ROLE'
AND ur.L_REL_ID = r.L_ROLEID
AND r.L_BOARDID = b.L_BOARDID
AND b.S_PLATFORM_CODE = 'EFUNDS'
      EOF
    end
    

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        stream_db_datas(import_accounts_roles_sql, batch_size: 500) do |batch|
          datas = []
          batch.each do |r|
            role    = @roles[r[0].to_s]
            account = @accounts[r[1].to_s]
            next unless account && role
            datas << [account.id, role.id]
          end

          # 批量插入当前批次的数据
          if datas.any?
            Ims2022::AccountsRole.bulk_insert(:account_id, :role_id) do |obj|
              obj.set_size = 500
              datas.each do |data|
                obj.add data
              end
            end
          end

          # 清理临时数据
          datas.clear
        end
      end
    end

    
    
    
    
    def import_data1_permissions_sql
      <<-EOF
        SELECT
 m.L_MENUID AS 菜单id,
 m.S_MENU_UNICODE AS 菜单code,
 m.S_MENUNAME AS 菜单名称,
 p.S_PROD_NAME AS 系统名称,
 m.S_NODE_TYPE AS 菜单类型,
 m.L_PARENTID AS 上级菜单id
FROM
 t_fw_menu m,
 t_fw_productinfo p
WHERE
 m.L_STATUS = 1
AND p.L_STATUS = 1
AND p.S_PLATFORM_CODE = 'EFUNDS'
AND m.S_SYSCODE = p.S_PROD_CODE
UNION ALL
SELECT
 ma.L_ACTIONID AS 菜单id,
 ma.S_ACTIONCODE AS 菜单code,
 m.S_MENUNAME AS 菜单名称,
 p.S_PROD_NAME AS 系统名称,
 ma.S_ACTIONTYPE AS 菜单类型,
 m.L_PARENTID AS 上级菜单id
FROM
 t_fw_menu_action ma,
 t_fw_menu m,
 t_fw_productinfo p
WHERE
 ma.L_STATUS = 1
AND m.L_STATUS = 1
AND p.L_STATUS = 1
AND ma.L_MENUID = m.L_MENUID
AND p.S_PLATFORM_CODE = 'EFUNDS'
AND m.S_SYSCODE = p.S_PROD_CODE
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 5
        source_id_index   = 0

        # 预加载权限行数据，但只加载一次
        @data1_permissions_row ||= get_permissions_row(import_data1_permissions_sql)

        # 使用流式处理
        process_db_rows(import_data1_permissions_sql) do |r|
          next if @data1_permissions[r[0].to_s]

          level1_name = replace_blank_name(full_name(@data1_permissions_row, r, source_id_index, parent_id_index, level1_name_index))

          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: level1_name,
            level2_name: r[3],
            level3_name: r[4],
            data_json: {menu_code: r[1]}
          }
          permission = Ims2022::Data1Permission.create(json)
          @data1_permissions[r[0].to_s] = permission
        end

        # 清理临时数据
        @data1_permissions_row = nil
      end
    end
    
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        SELECT
 mr.L_ROLEID AS 角色id,
 mr.L_MENU_ID AS 菜单id,
 mr.S_RIGHTTYPE AS 权限类型
FROM
 t_fw_menu_right mr,
 t_fw_role r,
 t_fw_board_info b
WHERE
 mr.L_STATUS = 1
AND r.L_STATUS = 1
AND b.L_STATUS = 1
AND mr.L_ROLEID = r.L_ROLEID
AND r.L_BOARDID = b.L_BOARDID
AND b.S_PLATFORM_CODE = 'EFUNDS'
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        stream_db_datas(import_data1_role_permissions_sql, batch_size: 500) do |batch|
          datas = []
          batch.each do |r|
            role       = @roles[r[0].to_s]
            permission = @data1_permissions[r[1].to_s]
            next unless permission && role
            datas << [@quarter_id, role.id, permission.id, r[2]]
          end

          # 批量插入当前批次的数据
          if datas.any?
            Ims2022::Data1AccountsRolesPermission.bulk_insert(:quarter_id, :role_id, :data1_permission_id, :additional_permission) do |obj|
              obj.set_size = 500
              datas.each do |data|
                obj.add data
              end
            end
          end

          # 清理临时数据
          datas.clear
        end
      end
    end
    

    
    def send_request(uri)
      require 'httparty'
      body = {
        "reqSys" => @reqSys,
        "page" => 1,
        "size" => 100000
      }

      response = HTTParty.post("#{@base_url}#{uri}", 
                                  headers: headers,
                                  body: body.to_json
                                )
      result = JSON.parse response.body
      if result['retCode'].to_i != 0 || result['data'].size == 0
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers
      { 
        'Esb-Token' => @token,
        'Content-Type' => 'application/json' 
      }
    end
    

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 流式处理数据库查询，避免将所有数据加载到内存
    def stream_db_datas(sql, batch_size: 1000)
      sql = sql.gsub(';', '')
      batch = []
      @database.exec(sql) do |r|
        batch << r.to_a
        if batch.size >= batch_size
          yield batch
          batch.clear
          # 强制垃圾回收以释放内存
          GC.start if batch_size > 500
        end
      end
      # 处理最后一批数据
      yield batch if batch.any?
    end

    # 直接流式处理，逐行处理数据
    def process_db_rows(sql)
      sql = sql.gsub(';', '')
      @database.exec(sql) do |r|
        yield r.to_a
      end
    end

    # 通过枚举获取值,注意对比的value都是字符串
    # is_combined 是否单字符翻译，默认false，如果是true，1为读、2为写，那么12就是读写
    def get_enum(enums, field, value, is_combined = false)
      codes = is_combined ? value.to_s.chars : [value&.to_s]
      select_enums = enums.select { |obj| obj['name'] == field && codes.include?(obj['value']) }
      str = select_enums.map { |x| x['enum_value'] }.join('、')
      str.present? ? str : value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(rows, r, source_id_index, parent_id_index, level1_name_index)
      p_name = r[level1_name_index]
      return p_name if r[parent_id_index].blank?
      n_r = rows.find{|x| x[source_id_index] == r[parent_id_index]}
      while n_r
        parent_id = n_r[parent_id_index]
        p_name = "#{n_r[level1_name_index]} -> #{p_name}"
        n_r = rows.find{|x| x[source_id_index] == parent_id}
      end
      p_name
    end

    def get_permissions_row(sql)
      output_data = []
      process_db_rows(sql) do |r|
        output_data << r
      end
      output_data
    end

    def replace_blank_name(name)
      return name if name.blank? || !name.is_a?(String)

      name.gsub('&nbsp;', ' ')
    end

  end
end
