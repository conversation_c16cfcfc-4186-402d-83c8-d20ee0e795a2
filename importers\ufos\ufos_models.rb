module Ufos
  def self.table_name_prefix
    'ufos_'
  end
end

class Ufos::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class Ufos::AdditionalPermission < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter

  validates :name, presence: true
  validates :menu_id, presence: true
  validates :quarter, presence: true
end

class Ufos::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :accounts
  
  belongs_to :quarter
  validates :name, presence: true
  validates :quarter, presence: true
end

class Ufos::Role < ActiveRecord::Base
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :additional_permissions
  belongs_to :quarter

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter, presence: true
end
