module AasOracleImporter
  module SqlCheck
    # 检查表权限的方法
    def check
      return if the_system.nil?

      case database_type
      when 'oracle'
        sql_method_list.each { |sql_method| check_sql_for_oracle sql_method }
      when 'mysql'
        sql_method_list.each { |sql_method| check_sql_for_mysql sql_method }
      when 'oceanbase'
        sql_method_list.each { |sql_method| check_sql_for_oceanbase sql_method }
      when 'oceanbase_oracle'
        sql_method_list.each { |sql_method| check_sql_for_oceanbase_oracle sql_method }
      when 'kingbase'
        sql_method_list.each { |sql_method| check_sql_for_kingbase sql_method }
      when 'pg'
        sql_method_list.each { |sql_method| check_sql_for_pg sql_method }
      when 'jdbc'
        sql_method_list.each { |sql_method| check_sql_for_jdbc sql_method }
      when 'csv'
        path_method_list.each { |path_method| check_path_for_csv path_method }
      when 'xls'
        path_method_list.each { |path_method| check_path_for_xls path_method }
      when 'jruby_jdbc'
        path_method_list.each { |path_method| check_path_for_xls path_method }
      else
        puts "CheckSql: Not support database type #{database_type}"
      end
    end

    def show_sqls
      sql_method_list.each { |sql_method| sql_show sql_method }
    end

    # 此方法不能以 sql 为结尾，check 会加载
    def sql_test(sql_method_name, lines)
      puts 'SQL: '
      sql = send(sql_method_name.to_sym)
      puts sql
      puts
      puts 'Result:'
      case database_type
      when 'oracle'
        cursor = @database.exec(sql)
        lines.times do
          row = cursor.fetch
          break unless row

          pp row
        end
        cursor.close
      when 'mysql'
        @database.exec(sql).take(lines)
      end
    end

    private

    def ignore_sql_method?(sql_method)
      return false unless respond_to? :sql_check_ignore_list

      if send(:sql_check_ignore_list).include? sql_method
        puts message_prefix + "#{sql_method}: check ignore."
        return true
      end

      false
    end

    def sql_method_list
      @check_sql ? (methods + private_methods).select { |m| m.to_s.match?(/.*_sql$/) } : []
    end

    def path_method_list
      (methods + private_methods).select { |m| m.to_s.match?(/.*_file$/) }
    end

    def check_sql_for_mysql(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue Mysql2::Error => e
      puts "Mysql2::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_oceanbase(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue StandardError => e
      puts "Oceanbase::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_oceanbase_oracle(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue StandardError => e
      puts "OceanbaseOracle::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_jdbc(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue StandardError => e
      puts "Jdbc::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_kingbase(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue StandardError => e
      puts "Kingbase::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_pg(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.query send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue PG::Error => e
      puts "PG::Error #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_sql_for_oracle(sql_method)
      return if ignore_sql_method?(sql_method)

      @database.exec send(sql_method)
      puts message_prefix + "#{sql_method}: check OK."
    rescue OCIError => e
      puts "OCIError #{message_prefix} in #{sql_method}: #{e.message}"
    end

    def check_path_for_csv(path_method)
      file_path = send(path_method)
      return puts message_prefix + "#{file_path}: check OK." if File.exist? file_path

      puts "StandardError #{message_prefix}  #{file_path}: No such file or directory."
    end

    def check_path_for_xls(path_method)
      file_path = send(path_method)
      return puts message_prefix + "#{file_path}: check OK." if File.exist? file_path

      puts "StandardError #{message_prefix}  #{file_path}: No such file or directory."
    end

    def sql_show(sql_method)
      puts "\n#{sql_method}:"
      puts send(sql_method)
    end

    def message_prefix
      "[#{the_system.name}] [#{tnsname}]: "
    end
  end
end
