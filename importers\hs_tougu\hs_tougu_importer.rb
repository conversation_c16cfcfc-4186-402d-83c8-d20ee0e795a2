# frozen_string_literal: true

module AasOracleImporter
  # 恒生投顾系统解析
  class HsTouguImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      initialize_tables
      initialize_classes
    end

    def initialize_classes
      @accounts               = []
      @roles                  = []
      @data1_permissions      = []
      @menu_right_permissions = {}
    end

    def initialize_tables
      @table_users           = "#{@table_space}yh_toperator#{@sid_suffix}"
      @table_roles           = "#{@table_space}yh_tcompanyrole#{@sid_suffix}"
      @table_user_roles      = "#{@table_space}yh_toperrole#{@sid_suffix}"
      @table_menus           = "#{@table_space}pub_tmenu#{@sid_suffix}"
      @table_menu_rights     = "#{@table_space}pub_tmenuitemfunc#{@sid_suffix}"
      @table_role_menu_type1 = "#{@table_space}pub_trolemenu#{@sid_suffix}"
      @table_role_menu_type2 = "#{@table_space}yh_tcomrolemenu#{@sid_suffix}"

      #@table_menus           = "#{@table_space}menu_def#{@sid_suffix}"
      @table_user_role_rela  = "#{@table_space}user_role_rela#{@sid_suffix}"
      @table_user_role_right = "#{@table_space}user_role_right#{@sid_suffix}"
    end

    def import_to_do
      ActiveRecord::Base.transaction { import_accounts }
      ActiveRecord::Base.transaction { import_roles }
      ActiveRecord::Base.transaction { import_accounts_roles }
      ActiveRecord::Base.transaction { import_data1_permissions }
      import_data1_role_permissions
      import_ledgers(HsTougu::Account)
    end

    def import_accounts_sql
      <<-SQL
        select operator_no, operator_name, operator_status, operator_code from #{@table_users}
      SQL
    end

    def import_accounts
      @database.exec(import_accounts_sql) do |r|
        @accounts << HsTougu::Account.create(
          quarter_id: @quarter_id,
          code:       r[3],
          name:       r[1],
          status:     r[2].to_i == 1,
          objid:      r[0]
        )
      end
    end

    def import_roles_sql
      <<-SQL
        select role_id, role_name from #{@table_roles}
      SQL
    end

    def import_roles
      @database.exec(import_roles_sql) do |r|
        @roles << HsTougu::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select role_id, operator_no, company_id, sys_role_flag from #{@table_user_roles}
      SQL
    end

    def import_accounts_roles
      @database.exec(import_accounts_roles_sql) do |r|
        role    = @roles.find { |x| x.code == r[0].to_s }
        account = @accounts.find { |x| x.objid == r[1].to_s }
        next unless account && role

        role_scope = r[2].to_i == 300_001 ? '业务账户' : '运维账户'

        HsTougu::AccountsRole.create(
          quarter_id: @quarter_id,
          account_id: account.id,
          role_id:    role.id,
          role_scope: role_scope
        )
      end
    end

    def import_data1_permissions_sql
      <<-SQL
        select menu_id, menu_name from #{@table_menus}
      SQL
    end

    def import_data1_permissions
      @database.exec(import_data1_permissions_sql) do |r|
        @data1_permissions << HsTougu::Data1Permission.create(quarter_id: @quarter_id, code: r[0], level1_name: r[1])
      end
    end

    def role_permissions_type1_sql
      <<-SQL
        select role_id, menu_id, menu_right_id from #{@table_role_menu_type1}
      SQL
    end

    def role_permissions_type2_sql
      <<-SQL
        select role_id, menu_id, menu_right_id from #{@table_role_menu_type2}
      SQL
    end

    def import_data1_role_permissions
      @menu_right_permissions = menu_right_permissions

      ActiveRecord::Base.transaction do
        import_role_perm_from_type(role_permissions_type1_sql)
      end

      ActiveRecord::Base.transaction do
        import_role_perm_from_type(role_permissions_type2_sql)
      end
    end

    def menu_right_permissions_sql
      <<-SQL
        select menu_id, menu_right_id, menu_right_name from #{@table_menu_rights}
      SQL
    end

    # 根据菜单 id 获得对应的附加权限内容
    def menu_right_permissions
      output_json = {}
      @database.exec(menu_right_permissions_sql) do |r|
        menu_id, menu_right_id, menu_right_name = r.map(&:to_s)

        output_json[menu_id] ||= {}

        output_json[menu_id][menu_right_id] = menu_right_name
      end
      output_json
    end

    private

    def import_role_perm_from_type(the_sql)
      @database.exec(the_sql) do |r|
        role_id, menu_id, menu_right_id = r

        role       = @roles.find { |x| x.code == role_id.to_s }
        permission = @data1_permissions.find { |x| x.code == menu_id.to_s }
        next unless permission && role

        menu_right_permission = @menu_right_permissions[menu_id.to_s]

        @logger.warn "not found menu_right_permission for menu_id #{menu_id}" unless menu_right_permission

        HsTougu::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          role_id:               role.id,
          data1_permission_id:   permission.id,
          additional_permission: menu_right_permission&.[](menu_right_id.to_s)
        )
      end
    end
  end
end
