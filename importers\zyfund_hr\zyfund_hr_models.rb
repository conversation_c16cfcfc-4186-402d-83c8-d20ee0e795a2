class User < ActiveRecord::Base
  belongs_to :department, optional: true
  has_many   :subordinates, class_name: "User", foreign_key: "manager_id"
  belongs_to :manager,      class_name: "User", optional: true
  validates :name, presence: true
  validates :code, presence: true

end
class Department < ActiveRecord::Base
  has_many :children, class_name: 'Department', foreign_key: 'parent_id'
  belongs_to :parent, class_name: 'Department', optional: true
  has_many :users

  validates :name, presence: true
  validates :code, presence: true
end


