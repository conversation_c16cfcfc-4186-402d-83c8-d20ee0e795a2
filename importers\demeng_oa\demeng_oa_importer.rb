module AasOracleImporter

  class DemengOaImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix = importer_config['sid_suffix'] #''
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      import_accounts_and_users
      import_roles_relation
      import_permissions
      import_o32_temp_apply
      import_leave_user
      import_fund_manager
      import_ledgers(DemengOa::Account)
      send_oa_mail

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def send_oa_mail
      begin
        Dir.chdir AasOracleImporter.config['server']['path']
        `bundle exec rails after_import:demeng_oa_email[]`
      rescue
        puts "send_oa_mail Send Error"
      end
    end

    def import_fund_manager
      # 所有已完成的基金经理审批
      sql = "select t2.MessageID,t3.Document from #{@table_space}WKF_doc t1 inner join #{@table_space}WKF_Message t2 on t1.MessageID=t2.MessageID inner join #{@table_space}WKF_Worksheet t3 on t1.DocHelperID=t3.worksheetid where t1.DocName = '信息披露文件发布审批表' and t2.MessageStatus = 2"
      # 所有盖章完成的基金经理审批
      sql_2 = "select t2.MessageID,t3.Document from #{@table_space}WKF_doc t1 inner join #{@table_space}WKF_Message t2 on t1.MessageID=t2.MessageID inner join #{@table_space}WKF_Worksheet t3 on t1.DocHelperID=t3.worksheetid inner join #{@table_space}WKF_Node t4 on  t1.MessageID=t4.MessageID where t1.DocName = '信息披露文件发布审批表' and (t4.NodeName='官方媒体发布' or t4.NodeName='盖章' or t4.NodeName='申请人确认') and t4.NodeStatus='3'"
      output_json = []
      ActiveRecord::Base.transaction do
        # 目前先跑两遍往库里插，数据量在70左右，影响不大
        @database.query(sql).each do |row|
          set_fund_manager_json(row)
        end
        @database.query(sql_2).each do |row|
          set_fund_manager_json(row)
        end
      end
    end

    def set_fund_manager_json(row)
      row_document = row['Document'].to_s
      data_json = {
        username: set_row_value('编制人员', row_document),
        department: set_row_value('所属部门', row_document),
        title: set_row_value('公告标题（全称）', row_document),
        disclosure_date: set_row_value('预计披露日期', row_document),
        date: set_row_value('日期', row_document)
      }
      if data_json[:title].to_s.include?("基金经理变更公告")
        DemengOa::Approval.find_or_create_by(
          data_json: data_json,
          approval_type: "fund_manager",
          message_id: row['MessageID']
        )
      end
    end

    def import_leave_user
      sql = "select t2.MessageID,t3.Document from #{@table_space}WKF_doc t1 inner join #{@table_space}WKF_Message t2 on t1.MessageID=t2.MessageID inner join #{@table_space}WKF_Worksheet t3 on t1.DocHelperID=t3.worksheetid where t1.DocName = '离职申请' and t2.MessageStatus = 2"
      output_json = []
      ActiveRecord::Base.transaction do
        @database.query(sql).each do |row|
          set_leave_user_json(row)
        end
      end
    end

    def set_leave_user_json(row)
      row_document = row['Document'].to_s
      DemengOa::LeaveUser.find_or_create_by(
        name: set_row_value('姓名', row_document),
        department_name: set_row_value('部门', row_document),
        position_name: set_row_value('职务', row_document),
        entry_date: set_row_value('入司日期', row_document),
        apply_date: set_row_value('申请日期', row_document),
        permission_data: set_row_value('辞职原因', row_document),
        message_id: row['MessageID']
      )
    end

    def import_o32_temp_apply
      sql = "select t2.MessageID,t3.Document from #{@table_space}WKF_doc t1 inner join #{@table_space}WKF_Message t2 on t1.MessageID=t2.MessageID inner join #{@table_space}WKF_Worksheet t3 on t1.DocHelperID=t3.worksheetid where t1.DocName = '代理申请' and t2.MessageStatus = 2"
      output_json = []
      ActiveRecord::Base.transaction do
        @database.query(sql).each do |row|
          set_row_json(row)
        end
      end
    end

    def set_row_json(row)
      row_document = row['Document'].to_s
      DemengOa::JiaoyiTemp.find_or_create_by(
        account_name: set_row_value('被授权人', row_document),
        authorizer_name: set_row_value('授权人', row_document),
        start_date: set_row_value('授权期限', row_document),
        end_date: set_row_value('至', row_document),
        permission_data: set_row_value('授权事项', row_document),
        message_id: row['MessageID']
      )
    end

    def set_row_value(key,doc)
      slilt_1 = 'Value="'+key.to_s+'"'
      if key == '被授权人帐户'
        doc.to_s.split(slilt_1)[0].to_s.split(' Value=')[-1].to_s.split('"')[1].to_s
      else
        doc.to_s.split(slilt_1)[1].to_s.split(' Value=')[1].to_s.to_s.split('"')[1].to_s
      end
    end

    def import_accounts_and_users
      DemengOa::Account.where(quarter_id: @quarter_id).destroy_all
      DemengOa::Role.where(quarter_id: @quarter_id).destroy_all
      #User.all.each do |user|
      #  user.inservice = false
      #end

      #Department.all.each do |department|
      #  department.inservice = false
      #end
      

      ActiveRecord::Base.transaction do
        sql = "select * from #{@table_space}ORG_Department"
        @database.query(sql).each do |r|
          department = Department.find_or_create_by(code: r['DeptID'])
          department.name = r['DeptName']
          department.inservice = true
          department.save

          role = DemengOa::Role.create(quarter_id: @quarter_id, code: "dept_#{r['DeptID']}", parent_code:"dept_#{r['ParentID']}", name: r['DeptName'], role_type: 'dept')
        end

        sql = <<-EOF
          select t1.EmplID, t1.EmplName, t1.EmplEnabled, t1.DeptID, t2.DeptName from #{@table_space}ORG_Employee t1 join #{@table_space}ORG_Department t2 on t1.DeptID=t2.DeptID
        EOF

        @database.query(sql).each do |r|
          department = Department.find_by(code: r['DeptID'])
          user = User.find_or_create_by(code: r['EmplID'], name: r['EmplName'])
          user.department_id = department.id
          user.inservice = r['EmplEnabled'] == 1
          user.save

          role = DemengOa::Role.find_by(quarter_id: @quarter_id, code: "dept_#{r['DeptID']}")
          account = DemengOa::Account.create(quarter_id: @quarter_id, code: r['EmplID'], name: r['EmplName'], status: r['EmplEnabled'] == 1)
          account.roles << role if !account.roles.include?(role)
          while role = DemengOa::Role.find_by(quarter_id: @quarter_id, code: role.parent_code)
            account.roles << role if !account.roles.include?(role)
          end
        end

        sql = <<-EOF
          select t1.EmplID, t1.DeptID, t2.DeptName, t1.ManagerID, t3.PositionName from #{@table_space}ORG_EmplDept t1 inner join #{@table_space}ORG_Department t2 on t1.DeptID=t2.DeptID inner join #{@table_space}ORG_Position t3 on t1.PosID=t3.PositionID
        EOF

        @database.query(sql).each do |r|
          user = User.find_by(code: r['EmplID'])
          if user
            
            manager = User.find_by(code: r['ManagerID'])
            user.position = r['PositionName']
            user.manager_id = manager.id if manager
            user.save

            @position_users << user if user.position?
          end
        end
      end
    end

    def import_roles_relation

      ActiveRecord::Base.transaction do
        sql = "select RoleID, RoleName from #{@table_space}ORG_Role"
        @database.query(sql).each do |r|
          DemengOa::Role.find_or_create_by(quarter_id: @quarter_id, code: "role_#{r['RoleID']}", name: r['RoleName'], role_type: 'role')
        end

        sql = "select EmplID, RoleID from #{@table_space}ORG_EmplRole"

        @database.query(sql).each do |r|
          account =  DemengOa::Account.find_by(quarter_id: @quarter_id, code: r['EmplID'])
          role = DemengOa::Role.find_by(quarter_id: @quarter_id, code: "role_#{r['RoleID']}")
          if account && role && !account.roles.include?(role)
            account.roles << role
          end
        end
      end
    end

    def import_permissions
      DemengOa::Permission.where(quarter_id: @quarter_id).destroy_all
      DemengOa::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
      permission_json = set_permission_json
      ActiveRecord::Base.transaction do

        set_object_type_json.each do |object_type, data|
          sql = "select * from #{@table_space}#{data[:table_name]}"
          begin
            @database.query(sql).each do |r|
              if object_type == 'app'
                name = r['AppCustomizedName'] ? r['AppCustomizedName'] : r['AppName']
              else
                name = r[data[:name_column]]
              end
              if data[:name] != '工作流程' && data[:name] != '工作流程模板' && data[:name] != '文件'
                DemengOa::Permission.create(quarter_id: @quarter_id, code: "#{object_type}_#{r[data[:id_column]]}", name: name, permission_type: data[:name])
              end
            end
          rescue
            puts "not table #{data[:table_name]}"
          end
        end

        sql = "select t1.PermissionCode, t2.ObjectType, t2.ObjectID, t2.SecurityID, t2.SecurityType from #{@table_space}ORG_PermissionValue t1 inner join #{@table_space}ORG_Permission t2 on t1.PermissionID=t2.PermissionID"
        
        permission_list_json = {}
        DemengOa::Permission.where(quarter_id: @quarter_id).each do |permission|
          permission_list_json[permission.code] = permission.id
        end

        account_list_json = {}
        DemengOa::Account.where(quarter_id: @quarter_id).each do |account|
          account_list_json[account.code] = account.id
        end

        role_list_json = {}
        DemengOa::Role.where(quarter_id: @quarter_id).each do |role|
          role_list_json[role.code] = role.id
        end

        @database.query(sql).each do |r|
          permission_id = permission_list_json["#{r['ObjectType']}_#{r['ObjectID']}"]
          if permission_id
            case r['SecurityType']
            when 'user'
              account_id = account_list_json[r['SecurityID']]
              if account_id
                #ar_permission = DemengOa::AccountsRolesPermission.find_or_create_by(quarter_id: @quarter_id, permission_id:permission_id, account_id: account.id)
                ar_permission = DemengOa::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id:permission_id, account_id: account_id, additional_permission: permission_json["#{r['ObjectType']}_#{r['PermissionCode']}"])
              end
            when 'dept'
              role_id = role_list_json["dept_#{r['SecurityID']}"]
              if role_id
                ar_permission = DemengOa::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id:permission_id, role_id: role_id, additional_permission: permission_json["#{r['ObjectType']}_#{r['PermissionCode']}"])
              end
            when 'role'
              role_id = role_list_json["role_#{r['SecurityID']}"]
              if role_id
                ar_permission = DemengOa::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id:permission_id, role_id: role_id, additional_permission: permission_json["#{r['ObjectType']}_#{r['PermissionCode']}"])
              end
            end

            if false && ar_permission
              additional_permission = ar_permission.additional_permission.to_s.split(',')
              ar_permission.additional_permission = (additional_permission | []).join(",")
              ar_permission.save
            end

          end
        end
      end
    end

    def set_object_type_json
      output_json = {}
      sql = "select * from #{@table_space}ORG_PermissionObject"
      @database.query(sql).each do |r|
        output_json[r['ObjectType']] = {
          name: r['ObjectTypeName'],
          table_name: r['ObjectTableName'],
          id_column: r['ObjectIDColumn'],
          name_column: r['ObjectNameColumn']
        }
      end
      output_json
    end

    def set_permission_json
      output_json = {}
      sql = "select ObjectType, PermissionCode, PermissionName from #{@table_space}ORG_PermissionObjectCode"
      @database.query(sql).each do |r|
        output_json["#{r['ObjectType']}_#{r['PermissionCode']}"] = r['PermissionName']
      end
      output_json
    end

    def import_jobs_sql
      <<-EOF
        select distinct PositionName from #{@table_space}ORG_Position
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.query(import_jobs_sql).each do |r|
        name = r['PositionName']
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



