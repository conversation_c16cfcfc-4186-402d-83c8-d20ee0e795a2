module AasOracleImporter

  class HsHeguiImporter < ImporterBase

    def config
      @bs_id       = 62
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      
      
      import_data1_permissions
      
      import_data1_role_permissions

      #import_data1_account_permissions

      import_ledgers(HsHegui::Account)
    end

    def destroy_exist_datas
      accounts = HsHegui::Account.where(quarter_id: @quarter_id)
      HsHegui::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HsHegui::Role.where(quarter_id: @quarter_id).delete_all
      
      
      HsHegui::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      HsHegui::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
      
      
    end

    
    def import_accounts_sql
      <<-EOF
        select user_id,user_id,emp_name,status from hscon.uam_users
      EOF
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        
        enums = [{"name"=>"status", "value"=>"1", "enum_value"=>"正常", "label"=>"账号状态"}]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << HsHegui::Account.create(
            quarter_id: @quarter_id,
            
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            
            
            code: get_enum(enums, "code", r[1]),
            
            
            
            name: get_enum(enums, "name", r[2]),
            
            
            
            status: r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
            
            
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select t.role_id,t.role_id,t.role_name,t.parent_role_id from hscon.uam_roles t
      EOF
    end
    

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          next unless is_hegui(r[2])
          @roles << HsHegui::Role.create(
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[1],
            name: r[2],
            parent_code: r[3]
          )
        end
      end
      
    end

    def is_hegui(role_name)
      role_name_json[:share].include?(role_name) || (!role_name.to_s.include?('反洗钱') && !role_name_json[:fanxiqian].include?(role_name))
    end

    
    def import_accounts_roles_sql
      <<-EOF
        select t.role_id,t.user_id from hscon.uam_user_role t
      EOF
    end
    

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        
        
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          next unless account && role

          HsHegui::AccountsRole.create(account_id: account.id, role_id: role.id  )
        end
      end
      
    end

    
    
    
    
    def import_data1_permissions_sql
      <<-EOF
        select menu_id,menu_id,menu_name from hscon.uam_menus
      EOF
    end

    def import_data1_permissions
      
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          
          level1_name = replace_blank_name(name)
          
          json = {
            quarter_id: @quarter_id,
            
            source_id: r[0],
            
            code: r[1],
            
            level1_name: level1_name
            
          }
          @data1_permissions << HsHegui::Data1Permission.create(json)
        end
      end
      
    end
    
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        select role_id,menu_id from hscon.uam_menu_role
      EOF
    end

    def import_data1_role_permissions
      
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          next unless permission && role

          HsHegui::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end
    

    def role_name_json
      # 除了下边这些角色，其余包含反洗钱的角色都为反洗钱, share为共用角色
      {
        fanxiqian: ['分支机构负责人','黑名单查询','合规负责人','监管检查'],
        share: ['分支机构负责人代岗','调度V1.0运维角色','调度V2.0开发角色','调度V2.0运维角色','赋权管理员','反洗钱业务系统管理员','管理员','系统权限管理员','系统最大权限']
      }
    end

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
