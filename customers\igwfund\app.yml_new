# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'igwfund'
  customer: '景顺长城基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_igwfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      fa11g: #ä¼°å¼
        db_name: 'fa11g'
        db_user: 'qxjh'
"config/app.yml" 238L, 5513C
# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'igwfund'
  customer: 'æ¯é¡ºé¿ååºé'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_igwfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      fa11g: #ä¼°å¼
        db_name: 'fa11g'
        db_user: 'qxjh'
# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'igwfund'
  customer: 'æ¯é¡ºé¿ååºé'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_igwfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      fa11g: #ä¼°å¼
        db_name: 'fa11g'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'igwfund'
  customer: 'æ¯é¡ºé¿ååºé'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_igwfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      fa11g: #ä¼°å¼
        db_name: 'fa11g'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      ta: #guohu_ta
        db_name: 'ta'
        db_user: 'qxjh'
        db_pass: 'kli3v9c_'
      o3:
        db_name: 'o3'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      ds: #çé
        db_name: 'ds'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      jzta1:
        db_name: 'jzta1'
        db_user: 'qxjh'
        db_pass: 'kli3v9c_'
      jzta2:
        db_name: 'jzta2'
        db_user: 'qxjh'
        db_pass: 'kli3v9c_'
      fa:
        db_name: 'fa'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      sirmdb:
        db_name: 'sirmdb'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      igwdc:
        db_name: 'igwdc'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      igwdvp:
        db_name: 'igwdvp'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
      webdc:
        db_name: 'webdc'
        db_user: 'qxjh'
        db_pass: 'qxjh_1234'
    ldap:
      dc:
        host: ***********
        port: 389
        base: 'dc=invescogreatwall, dc=com'
        user: svc-qxjh
        password: pass_12345678

      dc2:
        host: ***********
        port: 389
        base: 'dc=igwdealing, dc=com'
        user: szx-qxjh
        password: pass_12345678

    sqlserver:
      jindie:
        db_host: '************'
        db_user: 'sj2020'
        db_pass: 'sj2020'
        tds_v: '8.0'

  importers:
    # MARK: æç
§é¡ºåºä¾¬¡å¯¼å
    - name: xiening_touyan
      bs_id: 38
      db_type: oracle
      tnsname: sirmdb
      table_space: 'sirm.'
      sid_suffix: ''
      version: 2018

    - name: yss_qingsuan
      bs_id: 22
      db_type: oracle
      tnsname: fa
      table_space: 'igwclr40.'
      sid_suffix: ''

    - name: jindie
      bs_id: 3
      db_type: sqlserver
      tnsname: jindie

    - name: ad_group
      bs_id: 205
      db_type: ldap
      tnsname: dc
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name']
      #touyan_sync: true

    - name: ad_group_two
      bs_id: 206
      db_type: ldap
      tnsname: dc2
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: ['dn', 'name', 'cn', 'samaccountname', 'title', 'useraccountcontrol', 'memberof']
      group_filter: '(&(objectclass = group))'
      group_attrs: ['dn', 'name']

    - name: gushou_system
      bs_id: 44
      db_type: oracle
      tnsname: igwdvp
      table_space: 'pubcde.'
      sid_suffix: ''

    - name: crm_system
      bs_id: 37
      db_type: oracle
      tnsname: igwdc
      table_space: 'customer.'
      sid_suffix: ''

    - name: fengkong_system
      bs_id: 45
      db_type: oracle
      tnsname: webdc
      table_space: 'xrisk.'
      sid_suffix: ''

    - name: jinzheng_ta
      bs_id: 32
      db_type: oracle
      tnsname: jzta1
      table_accounts: kdta_com.kd_userid
      table_roles: kdta_com.kd_role
      table_accounts_roles: kdta_com.kd_rolemember
      table_menus: kdta_com.kd_sysmenu
      table_menus_roles: kdta_com.kd_menuright_role
      table_accounts_menus: kdta_com.kd_menuright_user

    - name: jinzheng_ta_two
      bs_id: 33
      db_type: oracle
      tnsname: jzta2
      table_accounts: kdta_com.kd_userid
      table_roles: kdta_com.kd_role
      table_accounts_roles: kdta_com.kd_rolemember
      table_menus: kdta_com.kd_sysmenu
      table_menus_roles: kdta_com.kd_menuright_role
      table_accounts_menus: kdta_com.kd_menuright_user

    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: ta
      table_space: 'HSTA4.'
      sid_suffix: ''

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o3
      table_space: 'trade.'
      sid_suffix: ''
      temporary: false

    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: ds
      table_space: 'ds.'
      sid_suffix: ''
      sub_system: "CENTER"

    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: ds
      table_space: 'ds.'
      sid_suffix: ''
      sub_system: "COUNTER"

    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: ds
      table_space: 'ds.'
      sid_suffix: ''
      sub_system: "REMOTE"

    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: fa11g
      table_space: 'YSSFA45.'
      sid_suffix: ''
 

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_igwfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1

