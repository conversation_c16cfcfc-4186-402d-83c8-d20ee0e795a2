# frozen_string_literal: true

#require 'rubyXL'
require 'csv'
require_relative '../aas_oracle_importer/database_client'
require_relative '../aas_oracle_importer/csv_client'
require_relative '../aas_oracle_importer/xls_client'
require_relative '../aas_oracle_importer/oracle_client'
require_relative '../aas_oracle_importer/mysql_client'
require_relative '../aas_oracle_importer/oceanbase_client'
require_relative '../aas_oracle_importer/oceanbase_oracle_client'
require_relative '../aas_oracle_importer/kingbase_client'
require_relative '../aas_oracle_importer/pg_client'
require_relative '../aas_oracle_importer/jdbc_client'
require_relative '../aas_oracle_importer/sqlite_client'
require_relative '../aas_oracle_importer/dm_client'
require_relative '../aas_oracle_importer/sql_server_client'
require_relative '../aas_oracle_importer/ldap_client'
require_relative '../aas_oracle_importer/sql_check'
require_relative '../aas_oracle_importer/txt_client'
require_relative '../aas_oracle_importer/fake_client'
require_relative '../aas_oracle_importer/http_client'
require_relative '../aas_oracle_importer/jruby_jdbc_client'

require_relative '../aas_oracle_importer/system_import_status'
require_relative '../convert_tools'
require_relative '../redis_client'

module AasOracleImporter
  # Importer 的共用方法类
  class ImporterBase
    include SqlCheck
    attr_reader :import_status

    # check_sql 默认允许rake check importer的sql
    def initialize(quarter, options = {})
      @quarter    = quarter
      @quarter_id = @quarter.id
      @logger     = AasOracleImporter.initialize_logger
      @check_sql  = true
      @display_status = importer_config['display_status']
      initialize_options(options)
      config
      # @import_status 要在 config 之后执行，才能获取到 @bs_id
      @import_status = SystemImportStatus.new(@quarter, the_system) if the_system
      @enable_import_log = importer_config['import_log']&.[]('enable')
      @import_log_start_at = importer_config['import_log']&.[]('start_at') || 365
      @enable_import_password_security = importer_config['import_password_security']&.[]('enable')
      @enable_import_last_login_at = importer_config['import_last_login_at']&.[]('enable')
      # 获取最后登录时间从日志的什么时间开始
      @import_last_login_at = importer_config['import_last_login_at']&.[]('start_at')
      @db_type = importer_config['db_type']
      @db_type2 = importer_config['db_type2']
      @tnsname = importer_config['tnsname']
      @tnsname2 = importer_config['tnsname2']
    end

    # 准备导入过程需要的驱动信息, 此时有数据库连接尝试，如果发生错误，可以捕捉
    def prepare_import
      # initialize_db 应在 config 之后执行，以获取公共的变量
      initialize_db unless @ignore_initialize_db_flag
      initialize_extra_db if extra_dbs.present?
    end

    def import
      return if the_system.nil?

      before_import

      import_exception

      after_import
    end

    # 加一层嵌套，当报错时，能捕捉到错误并设置导入状态为 failed
    def import_exception
      import_to_do
    rescue StandardError => e
      import_status.failed!
      @logger.error(e.message)
      @logger.error(e.backtrace.join("\n"))
      raise e
    end

    def import_to_do
      # 子类自己去定义
    end

    def before_import
      raise DisabledBusinessSystem, 'The system inservice is false' unless the_system.inservice

      import_status.processing!
    end

    def after_import
      is_success, error_msg = check_error_import_bs
      _, warn_msg = check_warn_import_bs
      if is_success
        import_status.success!
      else
        import_status.success! # todo 测试失败，暂时也记录为导入成功，避免线上有大量的导入失败记录
        # import_status.failed!
        import_status.import_status.update(error_msg: error_msg) if error_msg.present?
      end
      import_status.import_status.update(warn_msg: warn_msg) if warn_msg.present?
    end

    # 测试业务系统model，包括User、Account、Role，如果有问题记录导入失败
    def check_error_import_bs
      is_success = true
      error_msgs = []
      model_names = the_system.model_names
      user_class = User if importer_config['is_user']
      account_class = model_names.find { |x| x.include?('Account') }&.constantize
      role_class = model_names.find { |x| x.include?('Role') }&.constantize
      if user_class.present? && User.count.zero?
        error_msgs << '员工导入数据为空'
        is_success = false
      end
      if account_class.present?
        accounts = account_class.where(quarter_id: @quarter_id)
        if accounts.count.zero?
          error_msgs << "「#{account_class}」账号导入数据为空"
          is_success = false
        end
        if account_class.column_names.include?('code')
          repeat_codes = accounts.group(:code).count.select { |_, quantity| quantity > 1 }.keys
          if repeat_codes.count.positive?
            error_msgs << "账号编码「#{account_class}」#{repeat_codes.join('、')}重复"
            is_success = false
          end
        end
      end
      if role_class.present? && role_class.column_names.include?('code')
        roles = role_class.where(quarter_id: @quarter_id)
        repeat_codes = roles.group(:code).count.select { |_, quantity| quantity > 1 }.keys
        if repeat_codes.count.positive?
          error_msgs << "角色编码「#{role_class}」#{repeat_codes.join('、')}重复"
          is_success = false
        end
      end
      error_msg = error_msgs.join('；')
      @logger.error "系统ID：#{the_system.id} 系统名称：#{the_system.name} 错误信息：#{error_msg}" if error_msg.present?
      [is_success, error_msg]
    end

    # 测试业务系统权限model，如果有问题，记录warn msg
    def check_warn_import_bs
      is_success = true
      warn_msgs = []
      model_names = the_system.model_names
      permission_classes = model_names
                             .select { |x| x.match(/Data\d+Permission/).present? }
                             .map { |x| x&.constantize }

      permission_classes.each do |permission_class|
        next unless permission_class.present? && permission_class.column_names.include?('code')

        number = permission_class.to_s.match(/Data(\d+)Permission/)[1] # 权限维度
        permissions = permission_class.where(quarter_id: @quarter_id)
        repeat_codes = permissions.group(:code).count.select { |_, quantity| quantity > 1 }.keys
        if repeat_codes.count.positive?
          warn_msgs << "权限#{number}编码「#{permission_class}」#{repeat_codes.join('、')}重复"
          is_success = false
        end
      end
      warn_msg = warn_msgs.join('；')
      @logger.warn "系统ID：#{the_system.id} 系统名称：#{the_system.name} 告警信息：#{warn_msg}" if warn_msg.present?

      [is_success, warn_msg]
    end

    def config
      # 加载子类的自定义配置
      @source_encode = importer_config['encode']
    end

    def importer_name
      name = ''
      name = Regexp.last_match(1) if self.class.to_s =~ /AasOracleImporter::(.+)Importer/
      name.underscore
    end

    def importer_config
      AasOracleImporter.config['importers'].find { |x| x['name'] == importer_name }
    end

    def initialize_db
      @database ||= DatabaseClient.new_database(database_type, tnsname)

      @database.source_encode = @source_encode if @source_encode
      @path                   = @database.csv_path if @database.respond_to? :csv_path
      @path                   = @database.file_path if @database.respond_to? :file_path

      @database2 ||= DatabaseClient.new_database(database_type2, tnsname2) if database_type2 && tnsname2
    end

    # 额外数据库连接
    # 必须添加为这种格式，数据库名以 extra_db 开头
    # extra_dbs:
    #   extra_db_update:
    #     db_type: mysql
    #     tnsname: aas_db
    #     encode: xxx
    def initialize_extra_db
      extra_dbs.each do |index, value|
        raise 'not support databse name, it must begin with `extra_db`' unless /^extra_db/.match?(index)

        db_client ||= DatabaseClient.new_database(value&.[]('db_type'), value&.[]('tnsname'))
        instance_variable_set(:"@#{index}", db_client)
        instance_variable_get(:"@#{index}").source_encode = value&.[]('encode')
      end
    end

    def initialize_options(options)
      # 不初始化 db
      @ignore_initialize_db_flag = options[:ignore_initialize_db] || false
    end

    def the_system
      return BusinessSystem.find(@bs_id) if @bs_id

      return BusinessSystem.new(name: 'HR 系统')

      raise "#{self.class}: not found @bs_id"
    end

    def database_type
      @db_type || 'oracle'
    end

    attr_reader :tnsname

    def database_type2
      @db_type2 || 'oracle'
    end

    attr_reader :tnsname2

    def extra_dbs
      importer_config['extra_dbs']
    end

    def import_ledgers(account_class)
      ActiveRecord::Base.transaction do
        account_class.where(quarter_id: @quarter_id).each do |account|
          # TODO: 这里默认就是 code
          ledger = Ledger.find_or_create_by(
            business_system_id: @bs_id,
            account_code_field: 'code',
            account_code_value: account.code
          )
          account.update(user_id: ledger.user_id) if ledger.user_id
        end
      end
    end

    def import_baselines(account_class)
      ActiveRecord::Base.transaction do
        account_class.where(quarter_id: @quarter_id).each do |account|
          # TODO: 这里默认就是 code
          SystemAccountsBaseline.find_or_create_by(
            business_system_id: @bs_id,
            account_code_field: 'code',
            account_code_value: account.code
          )
        end
      end
    end

    def check_file_type(file)
      require 'filemagic'

      fm = FileMagic.new
      case fm.file(file.to_s)
      when 'Microsoft Excel 2007+'
        'xlsx'
      when 'Microsoft OOXML'
        'xlsx'
      when 'Zip archive data, at least v1.0 to extract'
        'xlsx'
      when /^(Composite Document File V2 Document|CDF V2 Document|CDFV2 Microsoft Excel)/
        'xls'
      else
        'other'
      end
    end

    def import_job_user_lines
      # 用于判断是否存在，一次性搜索出全部，避免n+1
      user_job_names = JobUser.includes(:job, :user).map { |o| [o.user_id, o.job_id, o.job.name] }
      @position_users.each do |user|
        job_names = user_job_names.select { |o| o[0] == user.id }.map(&:last)
        job_ids   = user_job_names.select { |o| o[0] == user.id }.map { |o| o[1] }
        # 如果岗位发生变化
        # todo 多个岗位的情况
        next if user.position == job_names.sort.join('、')

        JobUser.where(user_id: user.id, job_id: job_ids).delete_all
        job = @jobs.find { |o| o.name == user.position }
        JobUser.create(user_id: user.id, job_id: job.id) if job
      end
    end

    # 表是否存在
    def exist_table?(table)
      return false if table.blank?

      case @database.database_type
      when 'oracle'
        sql = if table.include?('.')
                owner, table_name = table.split('.')
                "select count(*) from ALL_TABLES where owner = upper('#{owner}') and table_name = upper('#{table_name}')"
              else
                "select count(*) from ALL_TABLES where table_name = upper('#{table}')"
              end
        size = @database.exec(sql) { |x| x }
        size >= 1
      else
        sql = "SHOW TABLES LIKE '#{table}'"
        result = @database.exec(sql) { |x| x }
        !result.empty?
      end
    end

    # sql要求按照顺序获取code、last_login_at，clazz为账户类
    # 导入最后登录时间 sql, 要在导入账户之后使用
    def import_last_login_at(table, sql)
      return unless @enable_import_last_login_at

      is_exist = if table.is_a?(Array)
                   table.all? { |t| exist_table?(t) }
                 else
                   exist_table?(table)
                 end
      # 只有表名存在但是表不存在数据库，返回
      # 自定义系统录入的sql没有table，可以传入nil跳过这一步
      return if table.present? && !is_exist
      return if sql.nil?

      data = []
      @database.exec(sql) do |r|
        yield data, r
      end
      account_data = get_account_info_last_login_at(data)
      data += account_data
      insert_last_login_at(data)
    end

    # 通过quarter_account_info获取账户（data里除外）的最后登录时间
    def get_account_info_last_login_at(data)
      bs = BusinessSystem.find_by(id: @bs_id)
      clazz = bs.account_class
      codes = data.map { |x| x['code'] }
      other_codes = clazz.where(quarter_id: @quarter_id).where.not(code: codes).pluck(:code)
      infos = QuarterAccountInfo
                .select('account_code, max(last_login_at) as last_login_at')
                .where(business_system_id: bs.id, account_code: other_codes)
                .group('account_code')
      infos.map do |x|
        {
          'code' => x.account_code,
          'last_login_at' => x.last_login_at
        }
      end
    end

    def insert_last_login_at(data)
      return if data.nil? || data.empty? || !data.is_a?(Array)

      bs = BusinessSystem.find_by(id: @bs_id)
      return if bs.nil?

      clazz = bs.account_class
      account_type = clazz.to_s
      data.each do |x|
        code = x['code']
        last_login_at = x['last_login_at']
        if code && last_login_at
          account = x['account'] || clazz.find_by(code: code, quarter_id: @quarter_id)
          next if account.nil?

          account_info = QuarterAccountInfo.find_or_create_by(
            account_id:         account.id,
            account_type:       account_type,
            account_code:       account.code,
            business_system_id: @bs_id,
            quarter_id:         @quarter_id
          )
          account_info.update(last_login_at: last_login_at.to_s.to_time)
        else
          @logger.warn "#{self.class}: import_last_login_at not found account code '#{code}' or last_login_at '#{last_login_at}' in #{x}"
        end
      end
    end

    # 导入日志
    def import_customer_audit_log(_table, sql)
      # return unless exist_table?(table)
      return unless @enable_import_log

      @logger.info "#{self.class} bs_id: #{@bs_id}，导入日志: #{sql}"
      data = []
      @database.exec(sql) do |r|
        yield data, r
      end
      insert_customer_audit_log(data)
    end

    # 导入业务系统日志
    # data 格式 [
    #   {
    #     "source_id"=>来源ID，用于判断唯一值,
    #     "bs_id"=>系统ID,
    #     "account_code"=>"账户编码",
    #     "operation_at"=>"操作时间",
    #     "account_name"=>"账户名称",
    #     "operation_category"=>"操作类型",
    #     "operation"=>"操作内容",
    #     "ip_address"=>"ip地址"
    #   }
    # ]
    # source_id bs_id operation_at account_code 必填，如有空值会过滤掉
    def insert_customer_audit_log(data)
      return if data.nil? || data.empty? || !data.is_a?(Array)

      # 开始时间提前一小时，用于去重，确保时间早于插入数据的时间
      start_at = get_start_at(@bs_id) - 1.hour
      source_ids = CustomerAuditLog.where('business_system_id = ? && operation_at >= ?', @bs_id, start_at).pluck(:source_id)

      insert_logs = []
      data.each do |x|
        if !x['source_id'].nil? && !x['bs_id'].nil? && !x['operation_at'].nil? && !x['account_code'].nil?
          # 已经存在的不需要再次导入
          insert_logs << x unless source_ids.include?(x['source_id'])
        else
          @logger.warn "#{self.class}: insert_customer_audit_log not found source_id or bs_id or operation_at or account_code '#{x}'"
        end
      end
      time = Time.now
      CustomerAuditLog.bulk_insert(:source_id, :business_system_id, :account_code, :operation_at, :account_name, :operation_category, :operation, :ip_address, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        insert_logs.each do |log|
          obj.add [
            log['source_id'],
            log['bs_id'],
            log['account_code'],
            log['operation_at'],
            log['account_name'],
            log['operation_category'],
            log['operation'],
            log['ip_address'],
            time,
            time
          ]
        end
      end
    end

    # 导入客户系统的密码安全机制数据
    def import_customer_password_security(sql)
      return unless @enable_import_password_security

      @logger.info "#{self.class} bs_id: #{@bs_id}，导入客户系统的密码安全机制数据: #{sql}"
      data = []
      json = {}
      @database.exec(sql) do |r|
        data << r
      end
      yield data, json
      insert_customer_password_security(json)
    end

    # 插入客户系统的密码安全机制数据
    def insert_customer_password_security(json = {})
      return if json.nil? || json.empty?

      CustomerPasswordSecurity.where(quarter_id: @quarter_id, bs_id: @bs_id).delete_all
      CustomerPasswordSecurity.create(
        quarter_id:           @quarter_id,
        bs_id:                json['bs_id'],
        password_length:      json['password_length'],
        is_uppercase:         json['is_uppercase'],
        is_lowercase:         json['is_lowercase'],
        is_number:            json['is_number'],
        is_character:         json['is_character'],
        login_failure_number: json['login_failure_number'],
        password_valid_time:  json['password_valid_time']
      )
    end

    # 动态获取客户操作日志的操作时间，默认import_log_start_at指定的天数
    def get_start_at(bs_id)
      return Time.now unless @enable_import_log

      log = CustomerAuditLog.where(business_system_id: bs_id).order('operation_at DESC').first
      log.nil? ? Time.now - @import_log_start_at.days : log.operation_at
    end

    # 表是否存在
    def exist_table?(table)
      case @database.database_type
      when 'oracle'
        sql = if table.include?('.')
                owner, table_name = table.split('.')
                "select count(*) from ALL_TABLES where owner = upper('#{owner}') and table_name = upper('#{table_name}')"
              else
                "select count(*) from ALL_TABLES where table_name = upper('#{table}')"
              end
        size = @database.exec(sql) { |x| x }
        size >= 1
      else
        sql = "SHOW TABLES LIKE '#{table}'"
        result = @database.exec(sql) { |x| x }
        !result.empty?
      end
    end

    # oracle某些时间14152这样的数据
    def change_time(time)
      output_time = time.to_s
      i           = 6 - time.to_s.size
      while i.positive?
        output_time = '0' + output_time
        i -= 1
      end
      "#{output_time[0..1]}:#{output_time[2..3]}:#{output_time[4..6]}"
    end
  end
end
