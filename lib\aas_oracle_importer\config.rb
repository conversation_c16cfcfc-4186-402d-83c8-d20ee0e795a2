# frozen_string_literal: true

require 'logger'

module AasOracleImporter

  module_function

  def initialize_logger
    logger           = Logger.new(STDOUT)
    logger.progname  = "aas_importer"
    logger.level     = Logger::DEBUG
    logger.formatter = proc do |severity, datetime, progname, msg|
      "#{ datetime.strftime "%Y-%m-%dT%H:%M:%S" } #{ progname } [#{ severity }] #{ msg }\n"
    end
    logger
  end

  def importer_config_file
    config_app_yml = ENV['AAS_IMPORTER_CONFIG'] || '/opt/aas-app/config/importers/app.yml'
    File.expand_path(config_app_yml, __FILE__)
  end

  def environment
    ENV['RAILS_ENV'] || 'development'
  end

  def importer_config
    YAML.load ERB.new(IO.read(importer_config_file)).result
  end

  def config
    @config ||= importer_config[environment]
  end

  # 适配器 mysql2 odbc等
  def adapter
    config&.[]('server')&.[]('database')&.[]('adapter')
  end

  def audit_log_quarter_created(quarter)
    audit_log(
      operation_category: '导入数据管理',
      operation:          '创建时间点',
      comment:            "成功创建时间点「#{quarter.name}」"
    )
  end

  def audit_log_system_import(quarter, the_system, status = :success)
    statuses = { success: '成功', failed: '失败' }
    audit_log(
      operation_category: '导入数据管理',
      operation:          '系统数据导入',
      comment:            "#{quarter.name} => #{the_system.id ? the_system.name : '员工数据同步'} 导入数据#{statuses[status.to_sym]}"
    )
  end

  def audit_log_o32_option_import(quarter, msg, status = :success)
    statuses = { success: '成功', failed: '失败' }
    audit_log(
      operation_category: 'O32参数导入记录',
      operation:          '导入O32参数',
      comment:            "导入O32参数「#{quarter.name}」失败，ID「#{quarter.id}」，具体原因「#{msg}」"
    )
  end

  def audit_log(op)
    op_category = op[:operation_category]
    operation   = op[:operation]
    comment     = op[:comment]

    AdminAuditLog.create(
      admin_id:           config['server']['operator_id'],
      operation:          operation,
      operation_time:     Time.now,
      operation_module:   '后台管理',
      operation_category: op_category,
      comment:            comment,
      ip_address:         config['ipaddr'],
      host:               Socket.gethostname,
      agent:              'aas_importer'
    )
  end
end
