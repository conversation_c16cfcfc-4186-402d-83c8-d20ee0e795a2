# frozen_string_literal: true

module AasOracleImporter
  # AD 域数据导入
  class LdapSystemImporter < ImporterBase
    def config
      @bs_id                      = importer_config['bs_id']
      @account_filter             = importer_config['account_filter']
      @account_attrs              = importer_config['account_attrs']
      @group_filter               = importer_config['group_filter']
      @group_attrs                = importer_config['group_attrs']
      @top_ou                     = importer_config['top_ou']
      @ignore_account_name        = importer_config['ignore_accountname']
      @ignore_ou                  = importer_config['ignore_ou']
      @ignore_ou_but_import_users = importer_config['ignore_ou_but_import_users']
      @regular_field              = importer_config['regular_field']
      @telephonenumber            = importer_config['telephonenumber']
      @update_user_dept           = importer_config['update_user_dept']
      # @type [Hash]
      @user_ou_sequence           = importer_config['user_ou_sequence']
      initialize_classes
    end

    def import_to_do
      destroy_exist_datas

      @top_ou.each do |root_ou|
        import_accounts(root_ou)
        flag_default_ou
      end

      import_ledgers(Ldap::Account)
    end

    def destroy_exist_datas
      Ldap::Account.where(quarter_id: @quarter_id).delete_all
    end

    def the_system
      BusinessSystem.find(@bs_id)
    end

    private

    def initialize_classes
      @default_ou_flag = true
    end

    def flag_default_ou
      @default_ou_flag = false if @default_ou_flag
    end

    def import_accounts(ou)
      @ou = ou
      ActiveRecord::Base.transaction do
        entries = @database.search(base: query_base, filter: @account_filter, attributes: @account_attrs)
        entries.each do |entry|
          import_an_user(entry)
        end
      end
    end

    def query_base
      @ou.present? ? "ou=#{@ou}, #{@database.base}" : @database.base
    end

    def ignore_import_account?(person_struct)
      @ignore_account_name.include?(person_struct.login) || @ignore_ou.include?(person_struct.department)
    end

    def import_an_user(entry)
      person_struct             = LdapPerson.new(entry)
      person_struct.ou_sequence = @user_ou_sequence

      return if ignore_import_account?(person_struct)
      return unless person_struct.code && person_struct.name
      return if @ou != 'Internship' && @telephonenumber && person_struct.telephonenumber.blank?

      department = create_department(person_struct)
      create_user(person_struct, department)
      create_account(person_struct, department)
    end

    def create_department(entry)
      return unless entry.obj.present?

      department           = Department.find_or_initialize_by(name: entry.obj)
      department.code      = entry.obj
      department.level     = 1
      department.inservice = true
      department.save
      department
    end

    def create_user(entry, department)
      user               = User.find_or_initialize_by(code: entry.code)
      user.name          = entry.name
      user.inservice     = entry.inservice
      user.department_id = department&.id
      user.save

      if @ou == "Internship"
        tags = Tag.where(id: 1)
        user.tags = tags
      end

      user
    end

    def create_account(entry, department)
      account = Ldap::Account.find_or_initialize_by(
        quarter_id: @quarter_id,
        code:       entry.code
      )

      account.name            = entry.name
      account.source_id       = entry.code
      account.status          = entry.inservice
      account.objid           = department&.id
      account.department_name = department&.name
      account.save
    end
  end
end
