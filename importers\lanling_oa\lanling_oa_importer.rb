# 长盛专用

module AasOracleImporter
  class LanlingOaImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @ignore_department_codes = importer_config['ignore_department_codes'] || []
      @ignore_all_department_codes = get_ignore_all_department_codes(@ignore_department_codes)
      initialize_tables
    end

    def initialize_tables
      @table_sys_org_person         = "#{@table_space}sys_org_person#{@sid_suffix}"
      @table_sys_org_element        = "#{@table_space}sys_org_element#{@sid_suffix}"
      @table_sys_auth_role          = "#{@table_space}sys_auth_role#{@sid_suffix}"
      @table_sys_auth_ura           = "#{@table_space}sys_auth_ura#{@sid_suffix}"
      @table_sys_auth_rra           = "#{@table_space}sys_auth_rra#{@sid_suffix}"
      @table_sys_auth_category      = "#{@table_space}sys_auth_category#{@sid_suffix}"
      @table_pda_module_config_main = "#{@table_space}pda_module_config_main#{@sid_suffix}"
      @table_job                    = "#{@table_space}SYS_ORG_ELEMENT#{@sid_suffix}"
      @table_job_user               = "#{@table_space}SYS_ORG_POST_PERSON#{@sid_suffix}"
    end

    def import_to_do
      @accounts = []
      @roles = []
      @permissions = []
      @jobs = []
      @users = []
      import_departments
      import_accounts_and_users
      import_roles
      import_accounts_roles
      import_roles_permissions

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
      import_positions
      import_ledgers(LanlingOa::Account)
    end

    private

    def import_department_sql
      <<-SQL
        select fd_id, fd_name, fd_is_available, fd_parentid FROM #{@table_sys_org_element} where fd_org_type = 2
      SQL
    end

    def import_departments
      rows = []
      ActiveRecord::Base.transaction do
        @database.exec(import_department_sql) do |row|
          rows << row
        end
      end
      create_departments(rows)
      # 记录所有部门
      @departments = Department.all
      # 记录所有部门和账户的关联关系
      @d_json = {}
      @departments.each { |x| @d_json[x.id] = [] }
    end

    def create_departments(rows)
      rows.each do |row|
        next if @ignore_all_department_codes.include?(row[0]&.to_s)

        find_or_create_department(row)
      end
      # 更新所有部门的parent_id
      Department.where(parent_id: nil).each do |d|
        # 获取当前部门的row记录
        row = rows.find { |r| r[0] == d.code }
        # 如果当前row记录的parent_code不会空
        next unless !row.nil? && !row[3].to_s.empty?

        p = Department.find_by(code: row[3])
        d.update_column(:parent_id, p.id) if p
      end

      # 更新层级
      root_departments = Department.where(parent_id: nil)
      root_departments.update_all(level: 1)
      root_departments.each_with_index do |department|
        update_departments_level(department.children, 2)
      end
    end

    def update_departments_level(departments, level)
      departments.update_all(level: level)
      level += 1
      departments.each do |department|
        children_departments = department.children
        next if children_departments.empty?

        update_departments_level(children_departments, level)
      end
    end

    def find_or_create_department(r)
      code        = r[0]
      name        = r[1]
      status      = r[2].to_s == '1'
      # parent_code = r[3]
      return nil if name.nil? || (name == '')
      return nil if code.nil? || (code == '')

      d = Department.find_or_create_by(code: code)
      d.update(name: name, inservice: status)
    end

    def import_job_sql
      <<-SQL
        select fd_id, fd_name, fd_org_type, fd_is_available, fd_parentid from #{@table_job} where fd_org_type=4
      SQL
    end

    # 导入岗位
    def import_jobs
      rows = []
      ActiveRecord::Base.transaction do
        @database.exec(import_job_sql) do |r|
          rows << r
        end
      end
      create_jobs(rows)
    end

    def create_jobs(rows)
      jobs        = Job.all
      departments = Department.all
      rows.each do |row|
        next if @ignore_all_department_codes.include?(row[4]&.to_s)

        find_or_create_job(row)
      end
      # 更新所有岗位的parent_id
      Job.find_each do |job|
        # 获取当前部门的row记录
        row = rows.find { |r| r[0] == job.code }
        next if row.nil?

        parent_code = row[4].to_s
        # 如果当前row记录的parent_code不会空
        next unless !row.nil? && !parent_code.empty?

        parent_job = jobs.find { |o| o.code == parent_code }
        department = departments.find { |o| o.code == parent_code }

        job.parent = parent_job if parent_job
        job.department_id = department.id if department
        job.save
      end
      # 子job不直接关联department所以需要通过该job的祖先job的department匹配
      jobs = Job.where(department_id: nil)
      jobs.each do |job|
        root_job = job.root
        next unless root_job && root_job.department_id

        job.update_column(:department_id, root_job.department_id)
      end
    end

    def find_or_create_job(r)
      code   = r[0]
      name   = r[1]
      status = r[3].to_s == '1'
      return nil if name.nil? || (name == '') || code.nil? || (code == '')

      job = Job.find_or_create_by(code: code)
      job.update(name: name, inservice: status)
      @jobs << job
    end

    # 这里不能通过fd_is_available判断在职状态，通过fd_lzsj判断
    # 185569f20a834a5dd7a60854ff5bab6d 是脏数据
    def import_accounts_sql
      <<-SQL
        SELECT
          c.fd_id,
          c.fd_login_name,
          d.fd_name,
          d.dept_name,
          d.fd_is_available,
          d.fd_org_type,
          d.dept_id,
          c.fd_lzsj
        FROM
          #{@table_sys_org_person} c
          LEFT JOIN (
          SELECT
            a.fd_id,
            a.fd_name,
            a.fd_no,
            a.fd_is_available,
            a.fd_org_type,
            b.fd_name as dept_name,
            b.fd_id as dept_id
          FROM
            #{@table_sys_org_element} a
          LEFT JOIN #{@table_sys_org_element} b ON a.fd_parentid = b.fd_id AND a.fd_org_type=8
          ) d ON c.fd_id = d.fd_id
          where c.fd_id != '185569f20a834a5dd7a60854ff5bab6d'
      SQL
    end

    def import_accounts_and_users
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          set_accounts_line(r)
        end
      end
    end

    def set_accounts_line(r)
      # lzrq = format_datetime(r[7])
      # status = lzrq.nil? ? true : lzrq >= Time.now
      # department = find_or_create_department(r[3], r[3])
      status = r[4]&.to_i == 1
      department = Department.find_by(code: r[6])
      user       = find_or_create_user(r[0], r[1], r[2], status, department)
      account    = create_account(r[0], r[1], r[2], status)
      # 保存部门和账号的关联关系，用于记录部门下面的所有账号角色
      @d_json[department.id] << account if department && account
      @users << user
      @accounts << account
    end

    # 长盛离职日期格式为 '31-12月-99 12.00.00.********* 上午'，ruby无法识别，需要转换
    def format_datetime(lzrq)
      return if lzrq.nil? || lzrq.empty?

      lzrq = lzrq.gsub(/月|上午|下午/, '')
      data = lzrq.split('-').map(&:strip)
      data[2] = "20#{data[2]}"
      data.join('-').strip.to_time
    end

    def create_account(objid, code, name, status)
      LanlingOa::Account.create(
        quarter_id: @quarter_id,
        code:       code,
        name:       name,
        status:     status,
        objid:      objid
      )
    end

    def find_or_create_user(source_id, code, name, status, department)
      return nil if name.nil? || (name == '')
      return nil if code.nil? || (code == '')

      user = User.find_by(code: code)
      if user
        user.update(
          name:          name,
          inservice:     status,
          department_id: department&.id,
          source_id:     source_id
        )
      else
        user =
          User.create(
            code:          code,
            name:          name,
            inservice:     status,
            department_id: department&.id,
            source_id:     source_id
          )
      end
      user
    end

    #--------------------------------roles-----------------------------------------------
    def import_roles_sql
      <<-SQL
        select fd_id, fd_name, fd_alias, fd_module_path, fd_category_id from #{@table_sys_auth_role}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          set_roles_line(r)
        end
      end
    end

    def set_roles_line(r)
      if r[3]
        @permissions << LanlingOa::Permission.create(
          quarter_id:      @quarter_id,
          code:            r[0],
          name:            set_permission_name(r[2], r[3]),
          permission_type: 'OA权限'
        )
      else
        @roles << LanlingOa::Role.create(
          quarter_id: @quarter_id,
          code:       r[0],
          name:       r[1],
          role_type:  get_category_json[r&.[](4)]
        )
      end
    end

    def set_permission_name(fd_alias, fd_module_path)
      "#{get_permission_name[fd_module_path]}_#{fd_alias}"
    end

    #-------------------------------------------accounts_roles--------------------------------
    def import_accounts_roles_sql
      <<-SQL
        select fd_roleid, fd_orgelementid from #{@table_sys_auth_ura}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          set_accounts_roles_line(r)
        end
      end
    end

    def set_accounts_roles_line(r)
      account = @accounts.find { |x| x.objid == r[1] }
      department = @departments.find { |x| x.code == r[1] }
      role = @roles.find { |x| x.code == r[0] }
      account.roles << role and return if role && account

      # 获取部门下的账户
      if role && department
        # 获取部门及其子孙部门id
        d_ids = get_department_ids(department)
        d_ids.each do |d_id|
          d_accounts = @d_json[d_id]
          d_accounts.each { |x| x.reload.roles << role }
        end
        return
      end

      permission = @permissions.find { |x| x.code == r[0] }
      return unless permission && account

      LanlingOa::AccountsRolesPermission.create(
        quarter_id:    @quarter_id,
        account_id:    account.id,
        permission_id: permission.id
      )
    end

    def get_department_ids(department, ids = [])
      ids << department.id
      children = department.children
      children.map { |x| get_department_ids(x, ids) }
      ids.flatten.uniq
    end

    # 忽略掉部门编码
    def get_ignore_all_department_codes(department_codes = [])
      return [] if department_codes.blank?

      departments = Department.where(code: department_codes)
      return department_codes if departments.blank?

      Department.where(id: departments.map { |x| get_department_ids(x) }.flatten.uniq).pluck(:code)
    end

    #-------------------------roles_permissions--------------------------------------------
    def import_roles_permissions_sql
      <<-SQL
        select fd_roleid, fd_inhroleid from #{@table_sys_auth_rra}
      SQL
    end

    def import_roles_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_permissions_sql) do |r|
          set_roles_permissions_line(r)
        end
      end
    end

    def import_job_user_sql
      <<-SQL
        select fd_postid, fd_personid from #{@table_job_user}
      SQL
    end

    def import_job_users
      data = []
      # 当前导入员工岗位信息
      @database.exec(import_job_user_sql) do |r|
        data << r
      end
      # 用于判断是否存在，一次性搜索出全部，避免n+1
      # 数据库员工岗位信息
      job_user_codes = JobUser.includes(:job, :user).map { |o| [o.job.code, o.user.source_id] }
      import_job_user_lines(data, job_user_codes)
    end

    def import_job_user_lines(data, job_user_codes)
      # 获取当前导入员工编码
      user_codes = data.map(&:last).uniq.compact
      user_codes.each do |user_code|
        # 获取当前导入员工的岗位编码
        job_codes = data.select { |o| o.last == user_code }.map(&:first)
        set_job_user_line(user_code, job_codes, job_user_codes) if user_code.present? && job_codes.present?
      end
    end

    def set_job_user_line(user_code, job_codes, job_user_codes)
      user = @users.find { |x| x.source_id == user_code }
      return unless user

      before_job_user_codes = job_user_codes.select { |o| o.last == user_code }
      before_job_codes      = before_job_user_codes.map(&:first)
      add_job_codes         = job_codes - before_job_codes
      delete_job_codes      = before_job_codes - job_codes

      # 如果用户岗位已经记录，则跳过
      return if job_codes == before_job_codes

      params = {
        user_id:          user.id,
        quarter_id:       @quarter_id,
        log_time:         Time.now,
        log_type:         'message',
        operation:        'change',
        operation_target: '岗位'
      }
      delete_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code }
        next unless job

        JobUser.find_by(user_id: user.id, job_id: job.id)&.delete
        # 去掉岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位去掉「#{job.name}」"))
      end
      add_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code }
        next unless job

        JobUser.create(user_id: user.id, job_id: job.id)
        # 新增岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位新增「#{job.name}」"))
      end
    end

    def import_positions
      user_ids = JobUser.pluck(:user_id).uniq
      users = User.where(id: user_ids)
      job_users = JobUser.includes(:job, :user)
      users.each do |user|
        jobs = job_users.select { |job_user| job_user.user_id == user.id }.map(&:job)
        position = jobs.empty? ? nil : jobs.pluck(:name).join('、')
        user.update_column(:position, position) if user.position != position
      end
    end

    def set_roles_permissions_line(r)
      role = @roles.find { |x| x.code == r[0] }
      permission = @permissions.find { |x| x.code == r[1] }
      return unless role && permission

      LanlingOa::AccountsRolesPermission.create(
        quarter_id:    @quarter_id,
        role_id:       role.id,
        permission_id: permission.id
      )
    end

    #-------------------------------------category-----------------------
    def get_category_sql
      <<-SQL
        select fd_id, fd_name from #{@table_sys_auth_category}
      SQL
    end

    def get_category_json
      output_json = {}
      ActiveRecord::Base.transaction do
        @database.exec(get_category_sql) do |r|
          output_json[r[0]] = r[1]
        end
      end
      output_json
    end

    def get_permission_name_sql
      <<-SQL
        select fd_name, fd_url_prefix from #{@table_pda_module_config_main}
      SQL
    end

    def get_permission_name
      output_json = {}
      ActiveRecord::Base.transaction do
        @database.exec(get_permission_name_sql) do |r|
          output_json['/' + r[1] + '/'] = r[0] if r[1].present?
        end
      end
      output_json
    end
  end
end
