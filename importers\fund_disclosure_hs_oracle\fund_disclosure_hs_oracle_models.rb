module FundDisclosure
  def self.table_name_prefix
    'fund_disclosure_'
  end
end

# TODO: 这里未来如果其他客户有 User ，实际会重复导入
class User < ActiveRecord::Base
  belongs_to :department, optional: true
  has_many :subordinates, class_name: "User", foreign_key: "manager_id"
  belongs_to :manager, class_name: "User", optional: true
  validates :name, presence: true
  validates :code, presence: true
end


class FundDisclosure::Account < ActiveRecord::Base
  belongs_to :user
end

class FundDisclosure::Fund < ActiveRecord::Base
  serialize :o32_fund_codes, ConvertTools::JsonWithSymbolizeNames
end

class FundDisclosure::FundPosition < ActiveRecord::Base
end

class FundDisclosure::Role < ActiveRecord::Base
end

