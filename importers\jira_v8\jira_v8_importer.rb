require_relative '../../lib/jira_i18n'

module AasOracleImporter
  class JiraV8Importer < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @email_suffix = importer_config['email_suffix']
      @table_space = importer_config['table_space']
      @sid_suffix = importer_config['sid_suffix']
      initialize_tables
    end
 
    def initialize_tables
      @table_account = "#{@table_space}cwd_user#{@sid_suffix}"
      @table_role = "#{@table_space}cwd_group#{@sid_suffix}"
      @table_account_role = "#{@table_space}cwd_membership#{@sid_suffix}"
      @table_menu = "#{@table_space}schemepermissions#{@sid_suffix}"
      @table_node = "#{@table_space}nodeassociation#{@sid_suffix}"
      @table_project = "#{@table_space}project#{@sid_suffix}"
      @table_projectroleactor = "#{@table_space}projectroleactor#{@sid_suffix}"
      @table_projectrole = "#{@table_space}projectrole#{@sid_suffix}"
      @table_menu2 = "#{@table_space}globalpermissionentry#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_all_permissions
      import_data2_permissions
      import_ledgers(JiraV8::Account)
    end

    def destroy_exist_datas
      JiraV8::Account.where(quarter_id: @quarter_id).destroy_all
      JiraV8::Role.where(quarter_id: @quarter_id).destroy_all
      JiraV8::ProjectPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select id, display_name, email_address, active from #{@table_account}
      EOF
    end
    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          # 只获取邮箱后缀为 TODO: 的用户
          # next unless %r{@#{@email_suffix}}.match? r[2]
          name = r[1].match(/[^\[]+/).to_s.strip
          JiraV8::Account.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       name,
            email:      r[2],
            status:     r[3].to_i == 1
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, group_name from #{@table_role} where group_type = 'GROUP'
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          JiraV8::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select parent_id, child_id from #{@table_account_role} where membership_type = 'GROUP_USER'
      EOF
    end

    def import_accounts_roles
      @roles    = JiraV8::Role.where(quarter_id: @quarter_id).to_a
      @accounts = JiraV8::Account.where(quarter_id: @quarter_id).to_a

      @database.exec(import_accounts_roles_sql) do |r|
        role    = @roles.find    {|x| x.code == r[0].to_s }
        account = @accounts.find {|x| x.code == r[1].to_s }

        unless role and account
          @logger.warn "#{self.class}: not found role_code '#{r[0]}' or account_code '#{r[1]}' in #{r.join}"
          next
        end

        account.roles << role unless account.roles.exists? role.id
      end
    end

    def permission_sql
      <<-SQL
        SELECT
          m.perm_type,
          m.perm_parameter,
          m.permission_key,
          n.SOURCE_NODE_ID project_id,
          p.pname as project_name
        FROM
          #{@table_menu} m
          INNER JOIN #{@table_node} n ON n.SINK_NODE_ID = m.SCHEME
          INNER JOIN #{@table_project} p on n.SOURCE_NODE_ID = p.ID
        WHERE
          n.SINK_NODE_ENTITY = 'PermissionScheme' and
          m.perm_parameter is NOT NULL;
      SQL
    end

    def project_role_sql
      <<-SQL
        SELECT
          p2.name project_role_name,
          p1.pid,
          p1.projectroleid,
          p1.roletype,
          p1.roletypeparameter
        FROM
          #{@table_projectroleactor} p1
        INNER JOIN #{@table_projectrole} p2 
        ON p2.ID = p1.projectroleid;
      SQL
    end

    def import_all_permissions
      project_role_actors = select_db_datas(project_role_sql)
      data = select_db_datas(permission_sql)
      data.each do |row|
        permission_name = permission_properties.permission_name(row[2])
        case row[0]
        when 'projectrole'
          project_role = project_role_actors.find { |item| item[2].to_s == row[1].to_s &&
                                                           item[1] == row[3] }
          next unless project_role.present?

          if project_role[3] == 'atlassian-group-role-actor'
            deal_project_role_with_group(row, permission_name, project_role)
          elsif project_role[3] == 'atlassian-user-role-actor'
            deal_project_role_with_user(row, permission_name, project_role)
          end
        when 'group'
          deal_group_permission(row, permission_name)
        when 'user'
          deal_user_permission(row, permission_name)
        end
      end
    end

    def data2_permission_sql
      <<-SQL
        select permission, group_id from #{@table_menu2};
      SQL
    end

    def import_data2_permissions
      data = select_db_datas(data2_permission_sql)
      data.each do |row|
        permission_name = permission_properties.global_permission_name(row[0])
        permission = JiraV8::Data2Permission.find_or_create_by(
          permission_name: permission_name,
          permission_key:  row[0],
          quarter_id:      @quarter.id
        )
        role = @roles.find { |item| item['name'] == row[1] }
        next unless role.present?

        role.data2_permissions << permission
      end
    end

    def select_db_datas(sql)
      output_datas = []
      sql = sql.delete(';')
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    private

    def permission_properties
      @permission_properties ||= AasOracleImporter::JiraI18n.new
    end

    def deal_project_role_with_group(row, permission_name, project_role)
      role = @roles.find { |item| item['name'] == project_role[4] }
      return unless role.present?

      permission =
        JiraV8::ProjectPermission.find_or_create_by(
          project_name:     row[4],
          permission_key:   row[2],
          permission_name:  permission_name,
          permission_type:  '项目角色权限',
          permission_owner: "#{project_role[0]}（#{role.name}）",
          quarter_id:       @quarter_id
        )
      role.project_permissions << permission
    end

    def deal_project_role_with_user(row, permission_name, project_role)
      account = @accounts.find { |item| "JIRAUSER#{item['code']}" == project_role[4] }
      return unless account.present?

      permission =
        JiraV8::ProjectPermission.find_or_create_by(
          project_name:     row[4],
          permission_key:   row[2],
          permission_name:  permission_name,
          permission_type:  '项目角色权限',
          permission_owner: "#{project_role[0]}（#{account.name}）",
          quarter_id:       @quarter_id
        )
      account.project_permissions << permission
    end

    def deal_group_permission(row, permission_name)
      role = @roles.find { |item| item['name'] == row[1] }
      return unless role.present?

      permission =
        JiraV8::ProjectPermission.find_or_create_by(
          project_name:     row[4],
          permission_key:   row[2],
          permission_name:  permission_name,
          permission_type:  '组权限',
          permission_owner: role.name,
          quarter_id:       @quarter_id
        )
      role.project_permissions << permission
    end

    def deal_user_permission(row, permission_name)
      account = @accounts.find { |item| "JIRAUSER#{item['code']}" == row[1].to_s }
      return unless account.present?

      permission =
        JiraV8::ProjectPermission.find_or_create_by(
          project_name:     row[4],
          permission_key:   row[2],
          permission_name:  permission_name,
          permission_type:  '用户权限',
          permission_owner: account.name,
          quarter_id:       @quarter_id
        )
      account.project_permissions << permission
    end
  
  end
end
