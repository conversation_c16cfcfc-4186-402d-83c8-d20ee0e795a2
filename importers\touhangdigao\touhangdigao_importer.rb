module AasOracleImporter

  class TouhangdigaoImporter < ImporterBase

    def config
      @bs_id = 55
      @accounts = []
      @roles = []
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      @data4_permissions = []
      @data4_accounts_roles_permissions = []

      @table_space = importer_config['table_space']
      @sid_suffix = importer_config['sid_suffix']
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      destroy_exist_datas

      @accounts_roles_projects = get_accounts_roles_projects

      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data2_permissions
      import_data2_role_permissions
      import_data3_permissions
      import_data3_account_permissions
      import_data4_permissions
      import_data4_role_permissions
      import_data4_account_permissions
      import_ledgers(Touhangdigao::Account)
    end

    def destroy_exist_datas
      accounts = Touhangdigao::Account.where(quarter_id: @quarter_id)
      Touhangdigao::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Touhangdigao::Role.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data1Permission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data2Permission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data3Permission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data3AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data4Permission.where(quarter_id: @quarter_id).delete_all
      Touhangdigao::Data4AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    
    def import_accounts_sql
      <<-EOF
        select user_id, emp_badge, user_name, on_job from #{@table_space}sys_user#{@sid_suffix}
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Touhangdigao::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            name: get_enum(enums, "name", r[0]),
            status: get_enum(enums, "status", r[3])&.to_s == '1',
            objid: r[1]
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, name, role_type, ignore_dept from #{@table_space}sys_role#{@sid_suffix}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          data_json = {
            role_type: r[2],
            ignore_dept: r[3]
          }
          @roles << Touhangdigao::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[0]), name: "#{r[2].to_i==1 ? '项目角色' : '系统角色'}-#{r[1]}", data_json: data_json)
        end
      end
    end
    
    def import_accounts_roles_sql
      <<-EOF
        select user_id, role_id
        from #{@table_space}sys_role_user#{@sid_suffix}
        union
        (select t2.user_id, t1.id as role_id
         from #{@table_space}sys_role#{@sid_suffix} t1
                  left join #{@table_space}project_member#{@sid_suffix} t2 on t1.id = t2.project_role
         where role_type = 1
           and t2.deleted = 0
         group by t1.id, t2.user_id)
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[1].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
          if account && role
            Touhangdigao::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
    end
    
    def import_data1_permissions_sql
      <<-EOF
        select code, name from #{@table_space}sys_menu#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1]
          }
          @data1_permissions << Touhangdigao::Data1Permission.create(json)
        end
      end
      
    end
    
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, menu_code from #{@table_space}sys_role_menu#{@sid_suffix}
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            Touhangdigao::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select id, name from #{@table_space}sys_operation#{@sid_suffix}
      EOF
    end

    def import_data2_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1]
          }
          @data2_permissions << Touhangdigao::Data2Permission.create(json)
        end
      end
      
    end
    
    
    
    def import_data2_role_permissions_sql
      <<-EOF
        select role_id, operation_id from #{@table_space}sys_role_operation#{@sid_suffix}
      EOF
    end

    def import_data2_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data2_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            Touhangdigao::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data2_permission_id: permission.id)
          end
        end
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select project_code, project_name from #{@table_space}finance_project#{@sid_suffix}
      EOF
    end

    def import_data3_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1]
          }
          @data3_permissions << Touhangdigao::Data3Permission.create(json)
        end
      end
    end

    def data3_account_permissions_sql_with_account(account_code)
      <<-EOF
        select t1.project_code
        from #{@table_space}finance_project#{@sid_suffix} t1
        where t1.status != -1 and 
        (
          exists(
            select id
            from #{@table_space}project_member#{@sid_suffix} t2
            where deleted = 0
            and project_id = t1.id
            and user_id = '#{account_code}'
            and exists(
              select id 
              from #{@table_space}sys_role#{@sid_suffix} t3
              where t3.role_type = 1 and t3.id = t2.project_role
            )
          )

          or exists(
            select id
            from #{@table_space}sys_role_user#{@sid_suffix} t4
            where user_id = '#{account_code}' and (
            exists(
              select id 
              from #{@table_space}sys_role#{@sid_suffix} 
              where t4.role_id = id and ignore_dept = 1
            )
            or exists(
              select id from #{@table_space}sys_role_project_dept#{@sid_suffix}
              where t4.role_id = role_id and dept_code = t1.dept_id
              )
            )
            and exists(
              select id 
              from #{@table_space}sys_role_project_type#{@sid_suffix} 
              where t4.role_id = role_id and t1.project_type_detail = project_type_detail
            )
            and exists(
              select id 
              from #{@table_space}sys_role_menu#{@sid_suffix} 
              where t4.role_id = role_id and menu_code = 'project_list'
            )
          )
        )
      EOF
    end

    def import_data3_account_permissions
      enums = []
      @accounts.each do |account|
        select_db_datas(data3_account_permissions_sql_with_account(account.code)).each do |r|
          permission = @data3_permissions.find{|x| x.source_id.to_s == r[0].to_s}
          if permission
            Touhangdigao::Data3AccountsRolesPermission.create(
              quarter_id: @quarter_id, 
              account_id: account.id, 
              data3_permission_id: permission.id
            )
          end
        end
      end
    end

    def import_data4_permissions_sql
      <<-EOF
        select id, project_name, period, template_id, project_id from #{@table_space}catalog_outline#{@sid_suffix}
      EOF
    end

    def import_data4_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data4_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1],
            level2_name: r[2],
            level3_name: r[3],
            data_json: { project_id: r[4] }
          }
          @data4_permissions << Touhangdigao::Data4Permission.create(json)
        end
      end
    end

    def import_data4_role_permissions_sql
      <<-EOF
        select id, sys_role, project_role 
        from #{@table_space}catalog_outline_template#{@sid_suffix}
      EOF
    end

    def import_data4_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data4_role_permissions_sql).each do |r|
          permissions = @data4_permissions.select{|x| x.level3_name.to_s == r[0].to_s}

          r[1].to_s.split("-").each do |role_id|
            role = @roles.find{|x| x.source_id.to_s == role_id.to_s}
            permissions.each do |permission|
              Touhangdigao::Data4AccountsRolesPermission.create(
                quarter_id: @quarter_id, 
                role_id: role.id, 
                data4_permission_id: permission.id
              )
            end
          end
        end
      end
    end

    def account_roles_projects_sql
      <<-EOF
        select user_id, project_id, project_role 
        from #{@table_space}project_member#{@sid_suffix}
      EOF
    end

    def get_accounts_roles_projects
      output_data = []
      select_db_datas(account_roles_projects_sql).each do |r|
        output_data << r
      end
      output_data
    end

    def import_data4_account_permissions
      select_db_datas(import_data4_role_permissions_sql).each do |r|
        permissions = @data4_permissions.select{|x| x.level3_name.to_s == r[0].to_s}
        r[2].to_s.split("-").each do |role_id|
          role = @roles.find{|x| x.source_id.to_s == role_id.to_s}
          permissions.each do |permission|
            accounts = @accounts_roles_projects.select{|x| x[2].to_s == role.code.to_s && x[1].to_s == permission.data_json[:project_id].to_s}.map{|x| x[0]}
            accounts.each do |account_code|
              account = @accounts.find{|x| x.source_id.to_s == account_code.to_s}
              if account && permission
                Touhangdigao::Data4AccountsRolesPermission.create(
                  quarter_id: @quarter_id,
                  account_id: account.id, 
                  data4_permission_id: permission.id,
                  additional_permission: role.name
                )
              end
            end
          end
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
