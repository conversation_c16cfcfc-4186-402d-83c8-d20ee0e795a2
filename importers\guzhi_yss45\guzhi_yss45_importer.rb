require 'yaml'
module AasOracleImporter
  class GuzhiYss45Importer < ImporterBase
    def config
      @bs_id           = importer_config['bs_id']
      @table_space     = importer_config['table_space']
      @sid_suffix      = importer_config['sid_suffix']
      @sync_booksets_table = importer_config['sync_booksets_table']
      @import_fund_type = importer_config['import_fund_type']
      @role_check      = importer_config['role_check']
      @display_status = importer_config['display_status']
      @post_table_name = importer_config['post_table_name'] || 't_s_user_post_data'
      @table_log       = @table_space+'t_s_oper_log'+@sid_suffix
      @table_password_security = @table_space + 't_s_safe_system' + @sid_suffix
      @accounts = []
      @roles = []
      super
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_roles
      import_booksets
      import_booksets_relation
      import_logs
      import_password_securities
      import_last_login_at_data

      import_ledgers(Guzhi::Account)
    end

    def destroy_exist_data
      Guzhi::Account.where(quarter_id: @quarter_id).destroy_all
      Guzhi::Bookset.where(quarter_id: @quarter_id).destroy_all
      Guzhi::Role.where(quarter_id: @quarter_id).destroy_all
      Guzhi::BooksetRelation.where(quarter_id: @quarter_id).destroy_all
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_accounts_sql
      <<-SQL
        select C_USER_CODE, C_USER_NAME, C_DV_STATE from #{@table_space}t_s_user#{@sid_suffix} t
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Guzhi::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_s == 'ENAB'
            )
            if @display_status
              QuarterAccountInfo.create(
                account_id: account.id, 
                account_type: 'Guzhi::Account',
                business_system_id: @bs_id, 
                quarter_id: @quarter_id, 
                display_status: r[2].to_s
              )
            end
            @accounts << account
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      role_check_sql = ''
      if @role_check
        role_check_sql = 'where t.n_check_state = 1'
      end
      <<-SQL
        select C_POST_CODE, C_POST_NAME from #{@table_space}t_s_post#{@sid_suffix} t #{role_check_sql}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          if r[0] && r[1]
            @roles << Guzhi::Role.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1]
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_booksets_sql
      if @sync_booksets_table
        if @import_fund_type
          <<-SQL
            select
              a.C_PORT_CODE,
              a.C_PORT_NAME,
              b.C_ASSET_CODE, # 产品代码
              b.C_PORT_TYPE # 资产类型
            from
              #{@table_space}t_p_ab_port#{@sid_suffix} a,
              #{@table_space}vb_port_baseinfo#{@sid_suffix} b
            where
             a.C_PORT_CODE = b.C_PORT_CODE
          SQL
        else
          <<-SQL
            select C_PORT_CODE, C_PORT_NAME
            from #{@table_space}t_p_ab_port#{@sid_suffix}
          SQL
        end
      else
        <<-SQL
          select C_DATA_CODE, C_DATA_NAME
          from #{@table_space}#{@post_table_name}#{@sid_suffix}
          where c_data_type = '1'
        SQL
      end
    end

    def import_booksets
      ActiveRecord::Base.transaction do
        config_path = File.join(__dir__, '../../config/fund_type_config.yml')

        fund_types = YAML.load_file config_path
        enums = fund_types&.[]('fund_types')

        @database.exec(import_booksets_sql) do |r|
          bookset = Guzhi::Bookset.find_or_create_by(
            quarter_id: @quarter_id,
            code:       r[0]
          )

          bookset.name      = r[1]
          bookset.fund_type = get_enum(enums, 'fund_type', r[3])
          bookset.fund_code = r[2]
          bookset.save
        end
      end
    end

    def import_booksets_relation_sql
      <<-SQL
        select C_USER_CODE, C_DATA_CODE, C_DATA_NAME, C_POST_CODE
        from #{@table_space}#{@post_table_name}#{@sid_suffix}
        where c_data_type = '1'
      SQL
    end

    def import_booksets_relation
      booksets = Guzhi::Bookset.where(quarter_id: @quarter_id).to_a
      ActiveRecord::Base.transaction do
        @database.exec(import_booksets_relation_sql) do |r|
          if r[0] && r[1]

            bookset = booksets.find{|x| x.code == r[1].to_s}
            # unless bookset
            #   bookset = Guzhi::Bookset.create(
            #     quarter_id: @quarter_id,
            #     code:       r[1],
            #     name:       r[2]
            #   )
            # end
            account = @accounts.find{|x| x.code == r[0].to_s}
            role = @roles.find{|x| x.code == r[3].to_s}
            if account && role && bookset
              cbr               = Guzhi::BooksetRelation.new
              cbr.quarter_id    = @quarter_id
              cbr.account = account
              cbr.role    = role
              cbr.bookset = bookset
              cbr.save
            end
          end
        end
      end
    end

    # 导入日志sql语句
    # c_log_time是VARCHAR2类型
    def customer_audit_sql
      start_at = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      where_sql = "where c_content like '登%' and c_log_time > '#{start_at}'"

      <<-EOF
        select id_s_oper_log, c_user_code, c_log_time, c_oper_type, c_content, c_ip
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        data << {
          'source_id' => r[0],
          'account_code' => r[1],
          'account_name' => nil,
          'operation_at' => r[2],
          'operation_category' => r[3],
          'operation' => r[4],
          'bs_id' => @bs_id,
          'ip_address' => r[5]
        }
      end
    end

    def password_security_sql
      "select C_SAFE_SYS_CODE, C_VALUE, C_SAFE_SYS_NAME from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        # 0 表示不冻结
        login_failure_number = data.find { |x| x[0].to_s == 'A0' }&.[](1)&.to_i
        # 0 表示一直有效
        password_valid_time = data.find { |x| x[0].to_s == 'A6' }&.[](1)&.to_i
        json['bs_id'] = @bs_id
        json['password_length'] = data.find { |x| x[0].to_s == 'A1' }&.[](1)
        json['is_uppercase'] = data.find { |x| x[0].to_s == 'A2' }&.[](1)
        json['is_lowercase'] = data.find { |x| x[0].to_s == 'A3' }&.[](1)
        json['is_number'] = data.find { |x| x[0].to_s == 'A4' }&.[](1)
        json['is_character'] = data.find { |x| x[0].to_s == 'A5' }&.[](1)
        json['login_failure_number'] = login_failure_number
        json['password_valid_time'] = password_valid_time
      end
    end

    def last_login_at_sql
      if @import_last_login_at.present?
        where_sql = case @database.database_type
                    when 'oracle'
                      "where c_content like '登%' and c_log_time > sysdate-#{@import_last_login_at}"
                    else
                      start_at = (Time.now - @import_last_login_at.days).strftime('%Y-%m-%d %H:%M:%S')
                      "where c_content like '登%' and c_log_time > '#{start_at}'"
                    end
      else
        where_sql = nil
      end
      <<-EOF
        select c_user_code, max(c_log_time)
        from #{@table_log}
        #{where_sql}
        group by c_user_code
      EOF
    end

    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        data << {
          'code' => x[0],
          'last_login_at' => x[1]
        }
      end
    end
  end
end
