module GuohuSaYlb
  def self.table_name_prefix
    'guohu_sa_ylb_'
  end
end

class GuohuSaYlb::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaYlb::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaYlb::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuSaYlb::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuSaYlbAccountsRoles < ActiveRecord::Base; end


class GuohuSaYlbMenusRoles < ActiveRecord::Base; end
class GuohuSaYlbAccountsMenus < ActiveRecord::Base; end
class GuohuSaYlbAccountsOtherPermissions < ActiveRecord::Base; end
class GuohuSaYlbOtherPermissionsRoles  < ActiveRecord::Base; end
