# frozen_string_literal: true

module AasOracleImporter
  class GuohuSaImporter < ZhixiaoImporter
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      initialize_tables
      initialize_classes
      super
    end

    def initialize_tables
      @table_accounts         = "#{@table_space}hsi_user#{@sid_suffix}"
      @table_roles            = "#{@table_space}hsi_group#{@sid_suffix}"
      @table_accounts_roles   = "#{@table_space}hsi_usergroup#{@sid_suffix}"
      @table_menus            = "#{@table_space}hsi_menu#{@sid_suffix}"
      @table_menus_roles      = "#{@table_space}hsi_groupright#{@sid_suffix}"
      @table_other_permission = "#{@table_space}hsi_right#{@sid_suffix}"
      @table_sub_system       = "#{@table_space}hsi_system#{@sid_suffix}"
    end

    def initialize_classes
      @account_class          = GuohuSa::Account
      @role_class             = GuohuSa::Role
      @menu_class             = GuohuSa::Menu
      @other_permission_class = GuohuSa::OtherPermission

      @account_role_associate_class   = GuohuSaAccountsRoles
      @account_menu_associate_class   = GuohuSaAccountsMenus
      @role_menu_associate_class      = GuohuSaMenusRoles
      @role_other_permission_class    = GuohuSaOtherPermissionsRoles
      @account_other_permission_class = GuohuSaAccountsOtherPermissions
    end

    def import_menus_sql
      <<-SQL
        select c_menucode, c_menuname from #{@table_menus}
      SQL
    end

    def import_menus
      @menu_class.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          @menu_class.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def check_import_menus_group_sql
      <<-SQL
        select * from #{@table_menus_roles} where rownum = 1
      SQL
    end

    def import_menus_group(group, menu)
      join_class =
        if group == @role_class && menu == @menu_class
          @role_menu_associate_class
        elsif group == @account_class && menu == @menu_class
          @account_menu_associate_class
        elsif group == @role_class && menu == @other_permission_class
          @role_other_permission_class
        elsif group == @account_class && menu == @other_permission_class
          @account_other_permission_class
        end

      case group.to_s
      when @role_class.to_s
        group_name   = 'g.c_groupname,'
        group_from   = "#{@table_roles} g,"
        group_flag   = 0
        group_id     = 'g.l_groupid '
        send_method  = :find_by_name
        role_join_id = :role_id
      when @account_class.to_s
        group_name   = 'u.c_usercode,'
        group_from   = "#{@table_accounts} u,"
        group_flag   = 1
        group_id     = 'u.l_userid'
        send_method  = :find_by_code
        role_join_id = :account_id
      end

      case menu.to_s
      when @menu_class.to_s
        menu_name    = 'm.c_menuname'
        menu_code    = 'm.c_menucode'
        menu_from    = "#{@table_menus} m"
        menu_flag    = 1
        menu_join_id = :menu_id
      when @other_permission_class.to_s
        menu_name    = 'm.c_rightname'
        menu_code    = 'm.c_rightcode'
        menu_from    = "#{@table_other_permission} m"
        menu_flag    = 0
        menu_join_id = :other_permission_id
      end

      sql = <<-SQL
        select
          #{group_name}
          gm.c_rightcode,
          #{menu_name}
        from
          #{@table_menus_roles} gm,
          #{group_from}
          #{menu_from}
        where
          gm.c_flag           = #{group_flag}
          and gm.c_rightclass = #{menu_flag}
          and gm.l_groupid    = #{group_id}
          and gm.c_rightcode  = #{menu_code}
         order by
          gm.c_rightcode
      SQL

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          group_record = group.where(quarter_id: @quarter_id).send(send_method, r[0])
          menu_record  = menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu_record && group_record
            join_class.create(menu_join_id => menu_record.id, role_join_id => group_record.id)
          else
            @logger.warn "#{self.class}: not found #{group} #{r[0]}" unless group_record
            @logger.warn "#{self.class}: not found #{menu} #{r[1]}" unless menu_record
          end
        end
      end
    end
  end
end
