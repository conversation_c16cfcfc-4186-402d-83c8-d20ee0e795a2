module AasOracleImporter

  class JinzhengTaImporter < ImporterBase

    def config
      @bs_id                  = importer_config['bs_id']
      @table_accounts         = importer_config['table_accounts']
      @table_roles            = importer_config['table_roles']
      @table_accounts_roles   = importer_config['table_accounts_roles']
      @table_menus            = importer_config['table_menus']
      @table_menus_roles      = importer_config['table_menus_roles']
      @table_accounts_menus   = importer_config['table_accounts_menus']
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_accounts_menus
      import_menus_roles

      import_ledgers(JinzhengTa::Account)
    end

    def import_accounts

      JinzhengTa::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select u.userid,
               u.username,
               u.status
          from
               #{@table_accounts} u
          order by u.username
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            JinzhengTa::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_s == '0'
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      JinzhengTa::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select roleid, rolename from #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JinzhengTa::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus

      JinzhengTa::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select
          menuid, menupos, menuprompt
        from
          #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JinzhengTa::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: "#{r[2]}-#{r[1]}"
            )
        end
      end
    end

    def import_menus_roles
      sql = <<-EOF
        select
          roleid, menuid
        from
          #{@table_menus_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = JinzhengTa::Role.where(quarter_id: @quarter_id).find_by(code: r[0])
          menu = JinzhengTa::Menu.where(quarter_id: @quarter_id).find_by(code: r[1])

          if menu and role
            role.menus << menu
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end


        end
      end
    end

    def import_accounts_menus
      sql = <<-EOF
        select
          userid, menuid
        from
          #{@table_accounts_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = JinzhengTa::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = JinzhengTa::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and account
            account.menus << menu
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select
          userid, roleid
        from
          #{@table_accounts_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|

          account = JinzhengTa::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = JinzhengTa::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            account.roles << role
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



