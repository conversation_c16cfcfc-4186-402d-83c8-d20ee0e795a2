# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'qingsuan_'
  end
end
class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end
class Qingsuan::Account < ActiveRecord::Base; end
class Qingsuan::Menu    < ActiveRecord::Base; end
class Qingsuan::Role    < ActiveRecord::Base; end
class Qingsuan::Department < ActiveRecord::Base; end
class QingsuanAccountsRoles < ActiveRecord::Base; end
class QingsuanMenusRoles    < ActiveRecord::Base; end
class QingsuanAccountsMenus < ActiveRecord::Base; end
class Qingsuan::Data2Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :level1_name, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end
class Qingsuan::Data2AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'qingsuan_data2_arps'

  belongs_to :data2_permission
  belongs_to :role, optional: true

  validates :data2_permission_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end
class Qingsuan::Data3Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :level1_name, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end
class Qingsuan::Data3AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'qingsuan_data3_arps'

  belongs_to :data3_permission
  belongs_to :role, optional: true

  validates :data3_permission_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end
