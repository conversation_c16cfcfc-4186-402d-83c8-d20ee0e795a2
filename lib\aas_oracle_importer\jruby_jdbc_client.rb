# frozen_string_literal: true
require 'active_record'
require_relative '../jardirs/ojdbc8.jar'
require 'jdbc/mysql'
# require 'activerecord-jdbc-adapter'
require 'java'
java_import 'oracle.jdbc.OracleDriver' if defined?(JRUBY_VERSION)
module AasOracleImporter
  # mysql 客户端
  class JrubyJdbcClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        query(sql).each do |row|
          yield row
        end
      else
        query(sql)
      end
    end

    def query(sql)
      url = client_params[:url]
      props = java.util.Properties.new
      props.put("user", client_params[:user].to_s)
      props.put("password", client_params[:password].nil? ? nil : client_params[:password].to_s)
      # 使用 Properties 对象传递参数，避免参数数量问题
      connection = java.sql.DriverManager.getConnection(url, props)
      statement = connection.createStatement
      result_set = statement.executeQuery(sql)
      # 将获取到的数据转成二维数组
      result_hash = result_set_to_array_of_arrays(result_set)   

      result_hash
    end

    private

    def query_sql
      
    end

    def result_set_to_array_of_arrays(result_set)
      meta = result_set.getMetaData
      column_count = meta.getColumnCount

      rows = []
      while result_set.next
        row = []
        (1..column_count).each do |i|
          value = result_set.getObject(i)
          row << (result_set.wasNull ? nil : value)
        end
        rows << row
      end
      rows
    end
    
    def initialize_driver
    end

    def client_params
      client_config.update({password: ConvertTools::Cryptology.decrypt_if_env(client_config[:password])})
    end

    def client_config
      {
        url: database_info['db_url'],
        user: database_info['db_user'],
        password: database_info['db_pass']
      }
    end
  end
end
