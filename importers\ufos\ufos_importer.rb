# frozen_string_literal: true

module AasOracleImporter
  class UfosImporter < ZhixiaoImporter
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      super
    end

    def import_to_do
      import_accounts
      import_ledgers(Ufos::Account)
    end

    def import_accounts_sql
      <<-SQL
        with t1 as (
        select b.name 登录名,
               b.realname 真实名称,
               case
                 when b.status = 0 then
                  '停用'
                 when b.status = 1 then
                  '启用'
               end  userstatus,
               a.name, a.id
          from ufosdb.role a, ufosdb.userinfo b, ufosdb.userrole c
         where a.id = c.roleid
           and b.name = c.username
           and b.status = 1
         order by b.status desc, b.department, b.realname),
        t2 as (
        select a.name rolename, b.name rname1, d.name rname2, a.id
          from ufosdb.role a, ufosdb.resourceinfo b, ufosdb.resourceinfo d, ufosdb.roleresource c
         where a.id = c.roleid
           and b.id = c.resourceid
           and b.type != 2
           and b.parentid = d.id
         order by a.name, d.id asc)
         select t1.登录名, t1.真实名称, t1.userstatus 状态, t1.name 角色名称, t2.rname1 父菜单, t2.rname2 子菜单 from t1, t2 where t1.id = t2.id  order by 登录名 asc
      SQL
    end

    def import_accounts
      @accounts = []
      @roles = []
      @menus = []
      @account_roles = {}
      @role_menus = {}
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          import_account_line(r)
        end
      end
    end

    def import_account_line(data_array)
      account_code, account_name, account_type, role_name, menu_name, parent_menu_name = data_array

      account_type = account_type == "启用"

      account = @accounts.find{|x| x.code == account_code}
      unless account
        account = Ufos::Account.create(
          code: account_code,
          name: account_name,
          status: account_type,
          quarter_id: @quarter.id
        )
        @accounts << account
        @account_roles[account_code] = []
      end

      role = @roles.find{|x| x.code == role_name}
      unless role
        role = Ufos::Role.create(
          code: role_name,
          name: role_name,
          quarter_id: @quarter.id
        )
        @roles << role
        @role_menus[role_name] = []
      end

      unless @account_roles[account_code].include? role_name
        account.roles << role 
        @account_roles[account_code] << role_name
      end

      menu = @menus.find{|x| x.name == menu_name && x.parent == parent_menu_name}
      unless menu
        menu = Ufos::Menu.create(
          name: menu_name,
          parent: parent_menu_name,
          quarter_id: @quarter.id
        )
        @menus << menu
      end

      unless @role_menus[role_name].include? menu.id
        role.menus << menu
        @role_menus[role_name] << menu.id
      end

    end
  end
end
