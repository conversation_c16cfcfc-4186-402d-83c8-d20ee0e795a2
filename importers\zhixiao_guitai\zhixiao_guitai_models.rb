module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'zhixiao_guitai_'
  end
end

class ZhixiaoGuitai::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class Z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class Z<PERSON>xiaoGuitai::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class Zhixiao<PERSON><PERSON><PERSON>::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class ZhixiaoGuitaiAccountsRoles < ActiveRecord::Base; end


class ZhixiaoGuitaiMenusRoles < ActiveRecord::Base; end
class ZhixiaoGuitaiAccountsMenus < ActiveRecord::Base; end
class ZhixiaoGuitaiAccountsOtherPermissions < ActiveRecord::Base; end
class ZhixiaoGuitaiOtherPermissionsRoles  < ActiveRecord::Base; end
