# frozen_string_literal: true

require_relative 'workday_mode_base'
require_relative '../database_client'
require_relative '../config'

module AasOracleImporter
  module Workday
    class WorkdayInMfcteda < WorkdayModeBase
      def initialize(date, config)
        super
        @database = database_client
        @date = date.to_date
      end

      def workday?
        [1,4,7,10].include?(@date.month) && import_day && data_canter_status
      end

      def database_client
        DatabaseClient.new_database('oracle', config['tnsname'])
      end

      def workday_sql
        <<-EOF
          select a.d_normal_day from dcportal.t_sys_workday a where a.v_sys_code = 'DC' and a.c_flag = 1 and a.d_normal_day >= to_date('#{@date.strftime("%Y%m")}01', 'yyyymmdd') and a.d_normal_day < to_date('#{(@date+1.month).strftime("%Y%m")}01', 'yyyymmdd') order by a.d_normal_day ASC
        EOF
      end

      def status_sql
        <<-EOF
          select count(1) from DCPORTAL.T_LOG_MSG_SEND a where a.v_msg='#{@last_workday.to_s}数据中心处理全部结束!Y'
        EOF
      end

      def data_canter_status
        @database.exec(status_sql) do |r|
          return true if r[0].to_i > 0
        end
        # 未发现同步完成数据发送邮件通知
        Dir.chdir server_config['path']

        system "bundle exec rails 'after_import:mfcteda_workday_importer_error_emails'"
        false
      end

      def import_day
        dates = []
        @database.exec(workday_sql) do |r|
          dates << r[0]
        end
        @last_workday = dates[2].to_date
        @date == dates[3].to_date
      end

      def server_config
        AasOracleImporter.config['server']
      end

    end
  end
end
