# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'dxzq'
  customer: '东兴证券'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_dxzq_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      v6x:
        # in tns name
        db_name: 'v6x_63-40'
        db_user: 'v6xuser'
        db_pass: 'passw0rd'

  importers:
    - name: zhiyuan_oa
      bs_id: 201
      tnsname: v6x

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_dxzq_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1

