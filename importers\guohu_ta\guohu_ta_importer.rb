module AasOracleImporter
  class GuohuTaImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @table_log   = @table_space + 'fm_systemlog' + @sid_suffix
      @table_password_security = @table_space + 'fm_sysparameter' + @sid_suffix
      @table_accounts          = @table_space + 'fm_user' + @sid_suffix
      @display_status = importer_config['display_status']
      super
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_menus
      import_additional_permissions
      import_acccounts_menus
      import_menus_roles
      import_logs
      import_password_securities
      import_last_login_at_data

      import_ledgers(GuohuTa::Account)
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_accounts_sql
      <<-SQL
        select u.vc_user_code,
               u.vc_user_name,
               u.c_status,
               u.l_user_id
          from
               #{@table_accounts} u
          order by u.vc_user_name
      SQL
    end

    # 需要记录l_user_id，通过l_user_id获取import_logs里的日志
    def import_accounts
      GuohuTa::Account.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = GuohuTa::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i == 1,
              obj_id:     r[3]
            )

            if @display_status
              QuarterAccountInfo.create(
                account_id: account.id, 
                account_type: 'GuohuTa::Account', 
                business_system_id: @bs_id, 
                quarter_id: @quarter_id, 
                display_status: r[2].to_s
              )
            end
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select vc_group_name from #{@table_space}FM_GROUP#{@sid_suffix}
      SQL
    end

    # 考虑到历史情况，之前guohu_ta角色是没有编码的，很多客户又限制数据中心的字段，所以现在依然只导入名称为编码
    def import_roles
      GuohuTa::Role.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          GuohuTa::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[0]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          vc_menu_no, vc_menu_name
        from
          #{@table_space}FM_MENUITEM#{@sid_suffix}
        where
          c_menu_type = 1
        order by
          l_order
      SQL
    end

    def import_menus
      GuohuTa::Menu.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          GuohuTa::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_additional_permissions_sql
      <<-SQL
        select
          vc_menu_no,
          c_menu_right,
          vc_menu_right_name
        from
          #{@table_space}FM_EXTRAMENURIGHT#{@sid_suffix}
      SQL
    end

    def import_additional_permissions
      GuohuTa::AdditionalPermission.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_additional_permissions_sql) do |r|
          menu = GuohuTa::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          GuohuTa::AdditionalPermission.create(
            quarter_id: @quarter_id,
            sub_code:   r[1],
            name:       r[2],
            menu_id:    menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_GROUP#{@sid_suffix} g
        where t.l_group_id = g.l_group_id
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = GuohuTa::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          menu = GuohuTa::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            GuohuTaMenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              GuohuTa::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              GuohuTaAdditionalPermissionsRoles.create(
                role_id:                  role.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_acccounts_menus_sql
      <<-SQL
        select
          u.vc_user_code,
          t.vc_menu_no,
          t.vc_menu_rights
         from #{@table_space}FM_USERMENURIGHTS#{@sid_suffix} t,
              #{@table_space}FM_USER#{@sid_suffix} u
        where t.l_user_id = u.l_user_id
      SQL
    end

    def import_acccounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_acccounts_menus_sql) do |r|
          account = GuohuTa::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = GuohuTa::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            GuohuTaAccountsMenus.create(menu_id: menu.id, account_id: account.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps =
              GuohuTa::AdditionalPermission.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              GuohuTaAccountsAdditionalPermissions.create(
                account_id:               account.id,
                additional_permission_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          g.vc_group_name,
          u.vc_user_code
        from
          #{@table_space}FM_GROUPUSER#{@sid_suffix} gu,
          #{@table_space}FM_GROUP#{@sid_suffix} g,
          #{@table_space}FM_USER#{@sid_suffix} u
        where
          gu.l_group_id = g.l_group_id
          and gu.l_user_id = u.l_user_id
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = GuohuTa::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = GuohuTa::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            GuohuTaAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    # 导入日志sql语句
    # L_DATE, L_TIME 是number类型
    def customer_audit_sql
      start_at = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      start_date = start_at.split(' ').first.delete('-')
      start_time = start_at.split(' ').last.delete(':').to_i

      where_sql = "where (L_DATE = '#{start_date}' AND L_TIME > '#{start_time}') OR (L_DATE > '#{start_date}')"
      <<-EOF
        select L_SYSTEMLOG_ID, L_USER_ID, L_DATE, L_TIME, VC_OPCLASS, VC_OPCONTENT
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        next if r[2].nil? || r[3].nil?

        operation_at = "#{r[2]} #{change_time(r[3])}".to_time
        data << {
          'source_id'          => r[0],
          'account_code'       => r[1],
          'account_name'       => nil,
          'operation_at'       => operation_at,
          'operation_category' => r[4],
          'operation'          => r[5],
          'bs_id'              => @bs_id,
          'ip_address'         => nil
        }
      end
    end

    def password_security_sql
      "select L_SERIAL_NO, VC_VALUE, VC_DESCRIBE from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        # 0 表示不冻结
        login_failure_number = data.find { |x| x[0].to_s == '14' }&.[](1)&.to_i
        password_content = data.find { |x| x[0].to_s == '19' }&.[](1).to_s
        # 0 表示一直有效
        password_valid_time = data.find { |x| x[0].to_s == '15' }&.[](1)&.to_i
        json['bs_id'] = @bs_id
        json['password_length'] = data.find { |x| x[0].to_s == '20' }&.[](1)
        json['is_uppercase'] = password_content.include?('2')
        json['is_lowercase'] = password_content.include?('3')
        json['is_number'] = password_content.include?('1')
        json['is_character'] = password_content.include?('4')
        json['login_failure_number'] = login_failure_number
        json['password_valid_time'] = password_valid_time
      end
    end

    # L_TIME是number类型，101111、 91111，前者应该大于后者，但是直接max后者会大于前者，所以需要补齐0
    def last_login_at_sql
      if @import_last_login_at.present?
        start_at = (Date.current - @import_last_login_at.days).strftime('%Y%m%d')
        where_sql = "where L_DATE >= '#{start_at}'"
      else
        where_sql = nil
      end
      <<-EOF
      select L_USER_ID, max(concat(floor(L_DATE), LPAD(L_TIME, 6, 0)))
        from #{@table_log}
        #{where_sql}
        and vc_opcontent = '登录成功'
        group by L_USER_ID
      EOF
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        next if x[1].nil?

        account = GuohuTa::Account.find_by(obj_id: x[0], quarter_id: @quarter_id)
        next if account.nil?

        data << {
          'account'       => account,
          'code'          => account.code,
          'last_login_at' => x[1].to_time
        }
      end
    end
  end
end
