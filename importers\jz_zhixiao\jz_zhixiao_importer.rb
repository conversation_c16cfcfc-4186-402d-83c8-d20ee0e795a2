module AasOracleImporter

  class JzZhixiaoImporter < ImporterBase

    def config
      @bs_id = 49
      @accounts = []
      @roles = []
      @table_space = importer_config['table_space']
      @display_status = importer_config['display_status']
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      import_ledgers(JzZhixiao::Account)
    end

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        sql = <<-EOF
          select userid,username,status from #{@table_space}kd_userid
        EOF
        select_db_datas(sql).each do |r|
          account = JzZhixiao::Account.create(quarter_id: @quarter_id, code: r[0], name: r[1], status: r[2].to_s == '0')
          @accounts << account
          if @display_status
            QuarterAccountInfo.create(
              account_id: account.id, 
              account_type: 'J<PERSON><PERSON><PERSON>xiao::Account', 
              business_system_id: @bs_id, 
              quarter_id: @quarter_id, 
              display_status: r[2].to_s
            )
          end
        end
      end
      
    end

    def import_roles
      
      ActiveRecord::Base.transaction do
        
        sql = <<-EOF
          select roleid,rolename from #{@table_space}kd_role
        EOF
        select_db_datas(sql).each do |r|
          @roles << JzZhixiao::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
      
    end

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        
        sql = <<-EOF
          select roleid, userid from #{@table_space}kd_rolemember
        EOF
        select_db_datas(sql).each do |r|
          role = @roles.find{|x| x.code == r[0].to_s}
          account = @accounts.find{|x| x.code == r[1].to_s}
          if account && role
            JzZhixiao::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
      
    end

    
    
    
    
    def import_data1_permissions
      sql = <<-EOF
        select menuid, menuprompt, menupos from #{@table_space}kd_sysmenu 
      EOF
      ActiveRecord::Base.transaction do
        select_db_datas(sql).each do |r|
          @data1_permissions << JzZhixiao::Data1Permission.create(quarter_id: @quarter_id, code: r[0], level1_name: r[1], level2_name: r[2])
        end
      end
    end
    
    
    
    def import_data1_role_permissions
      sql = <<-EOF
        select roleid, menuid, userflag from #{@table_space}kd_menuright_role
      EOF
      ActiveRecord::Base.transaction do
        select_db_datas(sql).each do |r|
          role = @roles.find{|x| x.code == r[0].to_s}
          permission = @data1_permissions.find{|x| x.code == r[1].to_s}
          additional_permission = []
          additional_permission << '执行' if r[2].to_s != '0'
          if permission && role
            JzZhixiao::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id, additional_permission: additional_permission.join(","))
          end
        end
      end
    end
    
    def import_data1_account_permissions
      sql = <<-EOF
        select userid, menuid, userflag, grantflag, checkflag, rolegrantflag, rolecheckflag from #{@table_space}kd_menuright_user

      EOF
      select_db_datas(sql).each do |r|
        account = @accounts.find{|x| x.code == r[0].to_s}
        permission = @data1_permissions.find{|x| x.code == r[1].to_s}
        additional_permission = []
        additional_permission << '执行' if r[2].to_s != '0'
        additional_permission << '授权' if r[3].to_s != '0'
        additional_permission << '审核' if r[4].to_s != '0'
        additional_permission << '角色授权' if r[5].to_s != '0'
        additional_permission << '角色审核' if r[6].to_s != '0'
        if account && permission
          JzZhixiao::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id, additional_permission: additional_permission.join(","))
        end
      end
    end
    
    

    def select_db_datas(sql)
      
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r
      end
      output_datas
      
    end

  end
end



