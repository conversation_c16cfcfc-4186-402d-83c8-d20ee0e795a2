# IMS2022 导入程序内存优化总结

## 优化前的主要问题

1. **`select_db_datas` 方法内存占用过大**
   - 将所有查询结果一次性加载到内存中
   - 对于大数据量查询，会导致内存急剧增长

2. **实例变量缓存策略不当**
   - 大量使用哈希表缓存数据（@accounts, @roles, @all_data 等）
   - 缓存数据在整个导入过程中一直占用内存

3. **批量插入时数据累积**
   - 在批量插入前将所有数据收集到数组中
   - 大数据量时会导致内存峰值过高

4. **缺乏内存监控和垃圾回收机制**
   - 没有内存使用情况的监控
   - 没有主动的垃圾回收策略

## 优化方案

### 1. 数据库查询优化

#### 新增流式处理方法
```ruby
# 批量流式处理
def stream_db_datas(sql, batch_size: 1000)
  # 分批处理，避免一次性加载所有数据
end

# 逐行流式处理
def process_db_rows(sql)
  # 逐行处理，内存占用最小
end
```

#### 优化效果
- 内存占用从 O(n) 降低到 O(batch_size)
- 对于百万级数据，内存使用可减少 90% 以上

### 2. 缓存策略优化

#### 新增智能查找方法
```ruby
def find_account_by_id(id)
  @accounts[id.to_s] || Ims2022::Account.find_by(quarter_id: @quarter_id, source_id: id.to_s)
end
```

#### 优化效果
- 减少了不必要的缓存数据
- 提供了缓存未命中时的数据库查找备选方案
- 内存使用更加可控

### 3. 批量插入优化

#### 分批处理策略
- 将原来的一次性批量插入改为分批处理
- 每批处理 500 条记录，处理完立即清理
- 在批次间执行垃圾回收

#### 优化效果
- 内存峰值显著降低
- 处理过程更加平稳

### 4. 内存监控和垃圾回收

#### 新增监控功能
```ruby
def log_memory_usage(stage)
  # 记录各阶段内存使用情况
end

def force_gc_if_needed
  # 强制垃圾回收
end
```

#### 优化效果
- 可以实时监控内存使用情况
- 主动释放不再使用的内存

## 具体优化的方法

### 已优化的方法列表
1. `import_accounts` - 使用 `process_db_rows`
2. `import_roles` - 使用 `process_db_rows`
3. `import_accounts_assets` - 使用 `stream_db_datas` + 分批插入
4. `import_accounts_data_permissions` - 使用 `stream_db_datas` + 分批插入
5. `import_accounts_roles` - 使用 `stream_db_datas` + 分批插入
6. `import_accounts_funds` - 使用 `stream_db_datas` + 分批插入
7. `import_data1_role_permissions` - 使用 `stream_db_datas` + 分批插入
8. `import_data1_permissions` - 使用 `process_db_rows`
9. `import_funds` - 添加了分阶段垃圾回收

### 主要改进点
- **批处理大小**: 从 1000 调整为 500，减少内存峰值
- **查找优化**: 使用智能查找方法，减少缓存依赖
- **垃圾回收**: 在关键节点主动执行垃圾回收
- **内存监控**: 在每个主要步骤记录内存使用情况

## 预期效果

### 内存使用优化
- **峰值内存**: 预计减少 60-80%
- **平均内存**: 预计减少 40-60%
- **内存增长**: 从线性增长改为锯齿状增长（处理-释放循环）

### 性能影响
- **处理速度**: 可能略有下降（5-10%），但内存稳定性大幅提升
- **系统稳定性**: 显著提升，避免内存溢出
- **可扩展性**: 可以处理更大的数据集

## 使用建议

1. **监控内存使用**: 关注日志中的内存使用报告
2. **调整批处理大小**: 根据实际情况调整 `batch_size` 参数
3. **数据库连接**: 确保数据库连接稳定，支持长时间查询
4. **系统资源**: 虽然内存使用减少，但可能需要更多的数据库连接时间

## 测试建议

### 内存监控测试
1. **运行前后对比**: 记录优化前后的内存使用峰值
2. **长时间运行测试**: 验证内存是否会持续增长
3. **大数据集测试**: 使用更大的数据集验证优化效果

### 性能测试
1. **执行时间对比**: 记录优化前后的总执行时间
2. **分阶段性能**: 监控各个导入步骤的执行时间
3. **数据库负载**: 监控数据库的连接数和查询负载

### 功能测试
1. **数据完整性**: 确保优化后数据导入的完整性
2. **错误处理**: 测试在异常情况下的错误处理
3. **回滚机制**: 验证事务回滚是否正常工作

## 监控命令示例

```bash
# 监控内存使用
ps aux | grep ruby | grep ims2022

# 监控系统内存
free -h

# 监控进程内存详情
cat /proc/[PID]/status | grep -E "(VmPeak|VmSize|VmRSS)"
```

## 后续优化建议

1. **数据库索引**: 确保查找方法使用的字段有适当的索引
2. **连接池**: 考虑使用数据库连接池优化数据库访问
3. **并行处理**: 对于独立的导入步骤，可以考虑并行处理
4. **增量导入**: 考虑实现增量导入机制，避免全量重新导入
5. **配置化批处理大小**: 将批处理大小设为可配置参数
6. **内存阈值监控**: 实现内存使用阈值告警机制
