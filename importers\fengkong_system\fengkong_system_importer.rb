module AasOracleImporter

  class FengkongSystemImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space'] #glajj.
      @sid_suffix  = importer_config['sid_suffix'] #''
    end

    def import_to_do
      import_accounts
      import_roles
      import_account_role
      import_permissions
      import_role_permissions
      import_ledgers(FengkongSystem::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def account_sql
      "SELECT EM_ID,EM_USERNO,EM_USERNAME,EM_STATE FROM #{@table_space}TSYS_USER"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(account_sql) do |r|
          if r[0].to_s != ''
            FengkongSystem::Account.create(quarter_id: @quarter_id, code: r[1], name: r[2], status: r[3].to_i == 0,objid: r[0])
          end
        end
      end
    end

    def roles_sql
      "SELECT R_ID,R_NAME FROM #{@table_space}TSYS_ROLE"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(roles_sql) do |r|
          if r[0].to_s != ''
            role = FengkongSystem::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
          end
        end
      end
    end

    def account_role_sql
      "SELECT R_ID, EM_ID FROM #{@table_space}TSYS_ROLE_MAPPING"
    end

    def import_account_role
      ActiveRecord::Base.transaction do
        @database.exec(account_role_sql) do |r|
          if r[0].to_s != ''
            account = FengkongSystem::Account.find_by(quarter_id: @quarter_id, objid: r[1])
            role = FengkongSystem::Role.find_by(quarter_id: @quarter_id, code: r[0])
            if account && role
              account.roles << role
            end
          end
        end
      end
    end

    def permission_sql
      "SELECT SM_CODE, SM_NAME, SM_CODE_PARENT FROM #{@table_space}TSYS_MODULE"
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.exec(permission_sql) do |r|
          if r[0].to_s != ''
            FengkongSystem::Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], parent_code: r[2], permission_type: '模块权限')
          end
        end
      end
    end

    def role_permission_sql
      "SELECT R_ID, SM_CODE, FUNCTION_CODE FROM #{@table_space}TSYS_MODULE_MAPPING"
    end

    def import_role_permissions
      ActiveRecord::Base.transaction do
        @database.exec(role_permission_sql) do |r|
          if r[0].to_s != ''
            permission = FengkongSystem::Permission.find_by(quarter_id: @quarter_id, code: r[1])
            role = FengkongSystem::Role.find_by(quarter_id: @quarter_id, code: r[0])
            if permission && role
              FengkongSystem::AccountsRolesPermission.create(quarter_id: @quarter_id, permission_id: permission.id, role_id: role.id, additional_permission: r[2])
            end
          end
        end
      end
    end

  end
end



