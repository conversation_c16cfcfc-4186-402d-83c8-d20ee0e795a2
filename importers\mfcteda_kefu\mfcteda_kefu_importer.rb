module AasOracleImporter
  class MfctedaKefuImporter < ImporterBase
    def config
      @bs_id       = 310
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_accounts        = "#{@table_space}hsi_user#{@sid_suffix}"
      @table_account_status  = "#{@table_space}tfundbroker#{@sid_suffix}"
      @table_roles           = "#{@table_space}hsi_group#{@sid_suffix}"
      @table_account_roles   = "#{@table_space}hsi_usergroup#{@sid_suffix}"
      @table_menus           = "#{@table_space}hsi_menu#{@sid_suffix}"
      @table_role_menus      = "#{@table_space}hsi_groupright#{@sid_suffix}"
      @table_menu_rights     = "#{@table_space}hsi_menuright#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_ledgers(MfctedaKefu::Account)
    end

    def destroy_exist_datas
      accounts = MfctedaKefu::Account.where(quarter_id: @quarter_id)
      MfctedaKefu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      MfctedaKefu::Role.where(quarter_id: @quarter_id).delete_all

      MfctedaKefu::Data1Permission.where(quarter_id: @quarter_id).delete_all

      MfctedaKefu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select
          a.l_userid,
          a.c_usercode,
          a.c_username,
          s.c_careerstatus
        from #{@table_accounts} a
        left join #{@table_account_status} s
        on a.c_usercode = s.c_usercode
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << MfctedaKefu::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == '0'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select l_groupid , l_groupid, c_groupname from #{@table_roles} where c_isuse=1
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << MfctedaKefu::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select l_groupid, l_userid from #{@table_account_roles}

      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          MfctedaKefu::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select
          m.c_menucode,
          m.c_menucode,
          m.c_menuname,
          m.c_parentcode,
          m.c_versiontype
        from #{@table_menus} m
        where m.c_sysname = 'FUNDCRM'
        and m.c_isuse = '1'
        and ( m.c_versiontype != 'trust' or m.c_versiontype is null )
        and m.c_parentcode not in (select c_menucode from #{@table_menus} where c_isuse=0)
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          parent_id_value = r[parent_id_index]
          f_name = full_name(import_data1_permissions_sql, name, parent_id_value,
                             level1_name_index, parent_id_index)
          level1_name = replace_blank_name(f_name)

          next if r[parent_id_index].present? && (name == f_name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level1_name: level1_name

          }
          @data1_permissions << MfctedaKefu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select l_groupid, c_rightcode from #{@table_role_menus} where c_rightclass = 1
      EOF
    end

    # select l_groupid, c_rightcode from #{@table_role_menus} where c_rightclass = 1

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          MfctedaKefu::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                           data1_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      # sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
