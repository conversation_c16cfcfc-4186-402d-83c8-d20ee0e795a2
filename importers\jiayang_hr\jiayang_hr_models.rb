module JiayangHr
  def self.table_name_prefix
    'jiayang_hr_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class User < ActiveRecord::Base; end

class Department < ActiveRecord::Base; end

class JiayangHr::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class JiayangHr::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission

  validates :permission_id, presence: true
  validates :quarter_id, presence: true
  serialize :data_json, JsonWithSymbolizeNames
end

class JiayangHr::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class JiayangHr::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :quarter_id, presence: true 
end

class JiayangHr::PostPermission < ActiveRecord::Base
  validates :code, presence: true
  validates :post_name, presence: true
  validates :department_name, presence: true
  validates :quarter_id, presence: true
end

class JiayangHr::AccountsRolesPostPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :post_permission

  validates :post_permission_id, presence: true
  validates :quarter_id, presence: true
  serialize :data_json, JsonWithSymbolizeNames
end