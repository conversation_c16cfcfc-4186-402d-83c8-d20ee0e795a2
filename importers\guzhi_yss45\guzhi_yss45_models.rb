module G<PERSON>hi
  def self.table_name_prefix
    'guzhi_'
  end
end
class Guzhi::Account < ActiveRecord::Base
  has_many :bookset_relations
  has_many :roles,    -> { distinct }, through: :bookset_relations
  has_many :booksets, -> { distinct }, through: :bookset_relations
end

class Guzhi::Bookset < ActiveRecord::Base; end

class Guzhi::BooksetRelation < ActiveRecord::Base
  belongs_to :account
  belongs_to :role
  belongs_to :bookset
end

class Guzhi::Role < ActiveRecord::Base; end