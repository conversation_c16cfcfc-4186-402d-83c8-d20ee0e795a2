module AasOracleImporter

  class TouziShujuImporter < ImporterBase

    def config
      @bs_id = 10002
      @accounts = []
      @account_roles = []
      @roles = []
      @data1_permissions = []
      @data1_arps = []
      @data2_permissions = []
      @data2_arps = []
      @data3_permissions = []
      @data3_arps = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_account_permissions
      import_data2_permissions
      import_data2_account_permissions
      import_data3_permissions
      import_data3_role_permissions
      import_reports
      import_ledgers(TouziShuju::Account)
    end

    def import_accounts_sql
      <<-EOF
        select ACCOUNT,USER_NAME,STATUS from #{@table_space}di_edge_xedm_user#{@sid_suffix}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << TouziShuju::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            name: get_enum(enums, "name", r[1]),
            status: r[2] == '新建',
          )
        end
      end
    end

    
    def import_funcroles_sql
      <<-EOF
        select ROLE_ID,ROLE_NAME from #{@table_space}di_edge_xedm_user_funcrole#{@sid_suffix}
      EOF
    end

    def import_dataroles_sql
      <<-EOF
        select AUTH_ID,AUTH_NAME from #{@table_space}di_edge_xedm_user_datarole#{@sid_suffix}
      EOF
    end

    def import_indroles_sql
      <<-EOF
        select ROLE_ID,ROLE_NAME from #{@table_space}di_edge_xedm_user_indrole#{@sid_suffix}
      EOF
    end
    

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_funcroles_sql).each do |r|
          @roles << TouziShuju::Role.create(
            quarter_id: @quarter_id, 
            source_id: "func-#{r[0]}", 
            code: "func-#{r[0]}", 
            name: "功能-#{r[1]}"
          ) unless @roles.find{|x| x.code == "func-#{r[0]}"}
        end

        select_db_datas(import_dataroles_sql).each do |r|
          @roles << TouziShuju::Role.create(
            quarter_id: @quarter_id, 
            source_id: "data-#{r[0]}", 
            code: "data-#{r[0]}", 
            name: "数据-#{r[1]}"
          ) unless @roles.find{|x| x.code == "data-#{r[0]}"}
        end

        select_db_datas(import_indroles_sql).each do |r|
          @roles << TouziShuju::Role.create(
            quarter_id: @quarter_id, 
            source_id: "ind-#{r[0]}", 
            code: "ind-#{r[0]}", 
            name: "指标-#{r[1]}"
          ) unless @roles.find{|x| x.code == "ind-#{r[0]}"}
        end
      end
    end

    def import_accounts_func_roles_sql
      <<-EOF
        select ACCOUNT,ROLE_ID from #{@table_space}di_edge_xedm_func#{@sid_suffix}
      EOF
    end

    def import_accounts_data_roles_sql
      <<-EOF
        select ACCOUNT,AUTH_NAME from #{@table_space}di_edge_xedm_data#{@sid_suffix}
      EOF
    end

    def import_accounts_ind_roles_sql
      <<-EOF
        select ACCOUNT,ROLE_ID from #{@table_space}di_edge_xedm_role_user#{@sid_suffix}
      EOF
    end
    

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_func_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == "func-#{r[1]}"}
          account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
          if account && role && !@account_roles.find{|x| x.account_id == account.id && x.role_id == role.id}
            @account_roles << TouziShuju::AccountsRole.create(account_id: account.id, role_id: role.id )
          end
        end
        select_db_datas(import_accounts_data_roles_sql).each do |r|
          role = @roles.find{|x| x.name.to_s == "数据-#{r[1]}"}
          account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
          if account && role && !@account_roles.find{|x| x.account_id == account.id && x.role_id == role.id}
            @account_roles << TouziShuju::AccountsRole.create(account_id: account.id, role_id: role.id )
          end
        end
        select_db_datas(import_accounts_ind_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == "ind-#{r[1]}"}
          account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
          if account && role && !@account_roles.find{|x| x.account_id == account.id && x.role_id == role.id}
            @account_roles << TouziShuju::AccountsRole.create(account_id: account.id, role_id: role.id )
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select ID,TEXT,PARENT_ID,PATH from  #{@table_space}di_edge_xedm_ttrd_auth_menu#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|

          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, r[1], r[2], 1, 2))
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: level1_name,
            level2_name: r[2],
            level3_name: r[3]
          }
          @data1_permissions << TouziShuju::Data1Permission.create(json) unless @data1_permissions.find{|x| x.code == r[0]}
        end
      end
    end
    
    def import_data1_account_permissions_sql
      <<-EOF
        select RESOURCE_ID,ROLE_ID,ACTION_NAME from #{@table_space}di_edge_xedm_func#{@sid_suffix}
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        permission = @data1_permissions.find{|x| x.code.to_s == r[0].to_s}
        role = @roles.find{|x| x.source_id.to_s == "func-#{r[1]}"}
        if role && permission
          unless @data1_arps.find{|x| x.role_id == role.id && x.data1_permission_id == permission.id && x.additional_permission == r[2]}
            @data1_arps << TouziShuju::Data1AccountsRolesPermission.create(
              quarter_id: @quarter_id, 
              role_id: role.id, 
              data1_permission_id: permission.id,
              additional_permission: r[2]
            ) 
          end
        end
      end
    end
    
    def import_data2_permissions_sql
      <<-EOF
        select PORT_CODE,ACCNAME from  #{@table_space}di_edge_xedm_data#{@sid_suffix}
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1]
          }
          @data2_permissions << TouziShuju::Data2Permission.create(json) unless @data2_permissions.find{|x| x.code == r[0]}
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select ACCOUNT,PORT_CODE,AUTH_NAME,STATUS from  #{@table_space}di_edge_xedm_data#{@sid_suffix}
      EOF
    end

    def import_data2_account_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_account_permissions_sql).each do |r|
          account = @accounts.find{|x| x.code.to_s == r[0].to_s}
          permission = @data2_permissions.find{|x| x.code.to_s == r[1].to_s}
          if permission && account
            unless @data2_arps.find{|x| x.account_id == account.id && x.data2_permission_id == permission.id && x.additional_permission == r[3] && x.permission_scope == "数据-#{r[2]}"}
              @data2_arps << TouziShuju::Data2AccountsRolesPermission.create(
                quarter_id: @quarter_id, 
                account_id: account.id, 
                data2_permission_id: permission.id, 
                permission_scope: "数据-#{r[2]}",
                additional_permission: r[3]
              )
            end
          end
        end
      end
    end
    
    def import_data3_permissions_sql
      <<-EOF
        select Q_CODE, Q_NAME from #{@table_space}di_edge_xedm_role_ind#{@sid_suffix}
      EOF
    end

    def import_data3_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data3_permissions << TouziShuju::Data3Permission.create(json) unless @data3_permissions.find{|x| x.code == r[0]}
        end
      end
    end
    
    def import_data3_role_permissions_sql
      <<-EOF
        select ROLE_ID,Q_CODE from  #{@table_space}di_edge_xedm_role_ind#{@sid_suffix}
      EOF
    end

    def import_data3_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == "ind-#{r[0]}"}
          permission = @data3_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            unless @data3_arps.find{ |x| x.role_id == role.id && x.data3_permission_id == permission.id }
              @data3_arps << TouziShuju::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data3_permission_id: permission.id)
            end
          end
        end
      end
    end

    def import_reports_sql
      <<-EOF
        select JOB_ID,JOB_NAME,MAIL_LIST from  #{@table_space}DI_EDGE_XEDM_REPORT_MAIL#{@sid_suffix}
      EOF
    end

    def import_reports
      ActiveRecord::Base.transaction do
        @disabled_reports = FundEmailsCheck::Report.where(business_system_id: @bs_id).pluck(:source_id)
        select_db_datas(import_reports_sql).each do |r|
          import_reports_line(r)
        end
        FundEmailsCheck::Report.where(business_system_id: @bs_id, source_id: @disabled_reports).each{|x| x.update(status: false)}
      end
    end

    def import_reports_line(r)
      report = FundEmailsCheck::Report.find_or_initialize_by(business_system_id: @bs_id,source_id: r[0], report_name: r[1])
      report.receiver_email = set_email(r[2])
      report.status = true
      report.save
      @disabled_reports.delete(r[0].to_s)
    end

    def set_email(emails)
      emails.to_s.gsub("\n","").gsub(" ", "").split(";")
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end



