module Jira
  def self.table_name_prefix
    'jira_'
  end
end

class Jira::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :project_permissions
end

class Jira::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :project_permissions
end

class Jira::ProjectPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end
