# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'tpyzq'
  customer: '太平洋证券'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_tpyzq_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      oadb:
        db_name: oadb
        db_user: jihe
        db_pass: TpyZq_JiHe#
        db_host: **********
      ehrdb:
        db_name: ehrdb.com
        db_user: EHRQXJH
        db_pass: EHRQXJH
        db_host: **********
      jzjydb:
        db_name: UFDB
        db_user: jhcx
        db_pass: jhcx
      bopdb:
        db_name: bopdbbak
        db_user: jhcx
        db_pass: jhcx
        db_host: **********
      o32db:
        db_name: o32db
        db_user: tmpread
        db_pass: tmpread
      tadb:
        db_name: tadb
        db_user: qxgl
        db_pass: qxgl
      sadb:
        db_name: sadb
        db_user: qxgl
        db_pass: qxgl
      crm_db:
        db_name: crmdb
        db_user: qxjh
        db_pass: qxjh2023
        db_host: **********
      zhengtou_db:
        db_name: trade
        db_user: o32jh
        db_pass: o32jhxt
        db_host: ***********
      fanxiqian_db:
        db_name: hgdb
        db_user: jhcx
        db_pass: jhcx
        db_host: *********
      heguidb:
        db_name: hgdb
        db_user: jhcx
        db_pass: jhcx
        db_host: *********
      qingsuandb:
        db_name: ORCL
        db_user: PERMCK
        db_pass: PERMCK
        db_host: ***********
      hspbdb:
        db_name: pbdb
        db_user: qsjh
        db_pass: qsjh
        db_host: **********
      frqsdb:
        db_name: ORCL
        db_user: e_qxjihe
        db_pass: e_qxjihe
        db_host: ***********
      
      hengtaidb:
        db_name: xirdb
        db_user: reader
        db_pass: reader
        db_host: *********
                
    mysql:
      tgdb:
        db_name: dbtrade
        db_user: aqcx
        db_pass: aqcx
        db_host: ***********
    sqlserver:
      zuoshidb:
        db_host: '**********'
        db_user: 'jhcj'
        db_pass: '!@#$1234qwer'
        db_name: 'KSMM'
      jsjydb:
        db_host: '***********'
        db_user: 'qsjh'
        db_pass: 'qsjh'
        db_name: 'YGT'
  importers:
  - name: tpyzq_hr
    db_type: oracle
    tnsname: ehrdb
  - name: tpyzq_oa
    db_type: oracle
    tnsname: oadb
    table_space: 'ecology.'
  - name: hengtai_zhaiquan
    bs_id: 39
    db_type: oracle
    tnsname: hengtaidb
    table_space: xir_app.
    sid_suffix: ''
  - name: hs_hegui
    bs_id: 62
    db_type: oracle
    tnsname: heguidb
    table_space: 'hscon.'
    sid_suffix: ''
  - name: hs_fanxiqian
    bs_id: 63
    db_type: oracle
    tnsname: fanxiqian_db
    table_space: 'hscon.'
    sid_suffix: ''
  - name: xy_farenqingsuan_tpyzq
    bs_id: 254
    db_type: oracle
    tnsname: frqsdb
    table_space: 'essystem.'
    sid_suffix: ''
  - name: jz_jisu_jiaoyi
    bs_id: 59
    db_type: sqlserver
    tnsname: jsjydb
    table_space: ''
    sid_suffix: ''
  - name: jz_zuoshi
    bs_id: 57
    db_type: sqlserver
    tnsname: zuoshidb
    table_space: ''
    sid_suffix: ''
  - name: hspb
    bs_id: 52
    db_type: oracle
    tnsname: hspbdb
    table_space: trade.
    sid_suffix: ''
    trade_type_importer: true
    station_importer: true
  - name: dingdian_crm
    bs_id: 37
    db_type: oracle
    tnsname: crm_db
    table_space: crmii.
    sid_suffix: ''
  - name: zhengtou
    bs_id: 41
    db_type: oracle
    tnsname: zhengtou_db
    table_space: trade.
    sid_suffix: ''
    operator_no_importer: true
    trade_type_importer: true
    station_importer: true
  - name: bop
    bs_id: 54
    db_type: oracle
    tnsname: bopdb
    table_space: hs_user.
    sid_suffix: ''
  - name: hs_tougu
    bs_id: 51
    db_type: mysql
    tnsname: tgdb
    table_space: ''
    sid_suffix: ''
  - name: jizhong_jiaoyi
    bs_id: 43
    db_type: oracle
    tnsname: jzjydb
    table_space: hs_user.
    sid_suffix: ''
  - name: guohu_ta
    bs_id: 30
    db_type: oracle
    tnsname: tadb
    table_space: ta4.
    sid_suffix: ''
  - name: guohu_sa
    bs_id: 26
    db_type: oracle
    tnsname: sadb
    table_space: subta.
    sid_suffix: ''
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: o32db
    table_space: trade.
    sid_suffix: ''
    operator_no_importer: true
    trade_type_importer: true
    station_importer: true


development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_tpyzq_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1



