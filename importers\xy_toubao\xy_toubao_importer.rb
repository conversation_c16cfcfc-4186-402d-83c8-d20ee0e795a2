# frozen_string_literal: true

module AasOracleImporter
  class XyToubaoImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @product_table_space = importer_config['product_table_space'] || @table_space
      @sid_suffix          = importer_config['sid_suffix']
      @app_id              = importer_config['app_id']
      @accounts            = []
      @data1_permissions   = []
      @app_role_ids        = []
      initialize_tables
    end

    def initialize_tables
      @table_departments     = "#{@table_space}department#{@sid_suffix}"
      @table_users           = "#{@table_space}users#{@sid_suffix}"
      @table_user_roles      = "#{@table_space}user_role#{@sid_suffix}"
      @table_menus           = "#{@table_space}menu_def#{@sid_suffix}"
      @table_user_role_rela  = "#{@table_space}user_role_rela#{@sid_suffix}"
      @table_user_role_right = "#{@table_space}user_role_right#{@sid_suffix}"
      @table_user_menu       = "#{@table_space}user_right#{@sid_suffix}"
      @table_second_user_menu = "#{@table_space}depmanager_right#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_records
      import_departments
      import_all_role_ids
      import_accounts
      import_roles
      import_menus
      import_accounts_roles
      import_menus_roles
      import_data1_account_permissions
      import_data1_second_account_permissions

      import_ledgers(XyToubao::Account)
    end

    def destroy_exist_records
      XyToubao::Account.where(quarter_id: @quarter_id).destroy_all
      XyToubao::Department.where(quarter_id: @quarter_id).destroy_all
      XyToubao::Role.where(quarter_id: @quarter_id).destroy_all
      XyToubao::Menu.where(quarter_id: @quarter_id).destroy_all
      XyToubao::AccountsMenus.where(quarter_id: @quarter_id).destroy_all
    end

    def import_departments_sql
      <<-SQL
        select dep_id, dep_code, dep_name, dep_full_name, dep_type, parent_dep_id, dep_status from #{@table_departments}
      SQL
    end

    def import_departments
      ActiveRecord::Base.transaction do
        @database.exec(import_departments_sql) do |r|
          if r[0] && r[1]
            XyToubao::Department.create(
              quarter_id:                  @quarter_id,
              import_department_id:        r[0],
              code:                        r[1],
              name:                        r[2],
              full_name:                   r[3],
              category:                    r[4],
              parent_import_department_id: r[5],
              status:                      r[6].to_i.zero?
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[1]}' or name '#{r[2]}' in #{r.join}"
          end
        end
      end
      @departments = XyToubao::Department.where(quarter_id: @quarter_id)
    end

    def import_all_role_ids_sql
      "select distinct role_id from #{@table_user_role_right} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}"
    end

    # 获取对应app_id的角色
    # 用于过滤账号角色数据，默认获取的账号角色是所有的app_id的角色
    def import_all_role_ids
      @database.exec(import_all_role_ids_sql) do |r|
        @app_role_ids << r[0]&.to_s if r[0].present?
      end
    end

    def import_accounts_sql
      <<-SQL
        select user_code, user_name, user_status, DEP_ID from #{@table_users}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          department = @departments.find { |x| x.import_department_id&.to_s == r[3]&.to_s }
          if r[0] && r[1]
            account = XyToubao::Account.create(
              quarter_id:    @quarter_id,
              code:          r[0],
              name:          r[1],
              status:        r[2].to_i.zero?,
              department_id: department&.id
            )
            @accounts << account
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select role_id, role_name, dep_id from #{@table_user_roles}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          department = XyToubao::Department.find_by(import_department_id: r[2])
          name = department.present? ? department.name + ' - ' + r[1] : r[1]
          XyToubao::Role.create(
            quarter_id:           @quarter_id,
            code:                 r[0],
            name:                 name,
            import_department_id: r[2]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          menu_code,
          menu_name,
          menu_level,
          parent_menu_code
        from
          #{@table_menus}
          #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
        order by
          menu_code
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        menus = []
        @database.exec(import_menus_sql) do |r|
          menus << r
        end
        new_menus = calculate_menus(menus)
        time = Time.now
        XyToubao::Menu.bulk_insert(:quarter_id, :code, :name, :level, :parent_code, :full_name, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          new_menus.each do |menu|
            obj.add [@quarter_id, menu[0], menu[1], menu[2], menu[3], menu[4], time, time]
          end
        end
      end
      @data1_permissions = XyToubao::Menu.where(quarter_id: @quarter_id)
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          user_code, role_id
        from
          #{@table_user_role_rela}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          next unless @app_role_ids.include?(r[1]&.to_s)

          account = XyToubao::Account.where(quarter_id: @quarter_id).find_by(code: r[0])
          role    = XyToubao::Role.where(quarter_id: @quarter_id).find_by(code: r[1])

          if account && role
            XyToubao::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          role_id, menu_code
        from
          #{@table_user_role_right}
          #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = XyToubao::Role.where(quarter_id: @quarter_id).find_by(code: r[0])
          menu = XyToubao::Menu.where(quarter_id: @quarter_id).find_by(code: r[1])

          if menu && role
            XyToubao::MenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}.#{__method__}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_user_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_data1_account_permissions_sql) do |r|
          account    = @accounts.find { |x| x.code.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless permission && account

          XyToubao::AccountsMenus.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    permission.id
          )
        end
      end
    end

    def import_data1_second_account_permissions_sql
      <<-EOF
        select user_code, menu_code from #{@table_second_user_menu} #{@app_id.present? ? "where app_id=#{@app_id}" : ''}
      EOF
    end

    def import_data1_second_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_data1_second_account_permissions_sql) do |r|
          account    = @accounts.find { |x| x.code.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless permission && account

          XyToubao::AccountsMenus.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    permission.id
          )
        end
      end
    end

    # 菜单数据进行运算，计算树形菜单组合的名称
    def calculate_menus(menus)
      menus.map do |menu|
        full_name = get_full_name(menu, menus)
        menu << full_name
      end
    end

    # 菜单全名，包括所有祖先级菜单
    def get_full_name(menu, menus)
      names = []
      names << menu[1]
      while (menu_data = menus.find { |m| menu[3] == m[0] }).present?
        names << menu_data[1]
        menu = menu_data
      end
      names.reverse.join(' -> ')
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
