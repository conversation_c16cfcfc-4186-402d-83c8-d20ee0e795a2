module AasOracleImporter
  class CoremailImporter < ImporterBase
    def config
      @bs_id       = 401
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}td_user#{@sid_suffix}"
      @table_user         = "#{@table_space}cm_user_info#{@sid_suffix}"
      @table_role         = "#{@table_space}td_admin_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}td_system_admin#{@sid_suffix}"
      @table_menu         = "#{@table_space}td_admin_role#{@sid_suffix}"
      @table_account_menu = "#{@table_space}td_system_admin#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_account_permissions

      import_ledgers(Coremail::Account)
    end

    def destroy_exist_datas
      accounts = Coremail::Account.where(quarter_id: @quarter_id)
      Coremail::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      Coremail::Role.where(quarter_id: @quarter_id).delete_all

      Coremail::Data1Permission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      # 测试@table_user表没有相关字段
      #<<-EOF
      #  select
      #    a.user_id,
      #    a.user_id,
      #    u.true_name,
      #    a.user_status,
      #    u.remark
      #  from
      #    #{@table_account} a
      #  left join
      #    #{@table_user} u
      #  on
      #    a.user_id = u.user_id
      #EOF
      # todo 
      "select user_id, user_id as user_code, user_id as user_name, user_status from #{@table_account}"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          # 优先使用true_name，其次是user_id 作为名称
          name = r[2].present? ? r[2] : r[1]
          @accounts << Coremail::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       name,
            status:     r[3]&.to_s == '0',
            objid:      r[4]
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_id, role_id as role_code, role_name, admin_type from #{@table_role} where status = 0
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << Coremail::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            role_type:  get_role_type(r[3])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          Coremail::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select role_id, role_id as role_code, role_name, admin_type from #{@table_menu} where status = 0
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: get_role_type(r[3])
          }
          @data1_permissions << Coremail::Data1Permission.create(json)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_id, role_id from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        Coremail::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def get_role_type(code)
      case code
      when 'SA' then '超级管理员'
      when 'CSA' then '系统管理员'
      when 'OA' then '组织管理员'
      when 'OUA' then '部门管理员'
      when '202' then '自定义的管理员角色'
      end
    end
  end
end
