module AasOracleImporter

  class JzJizhongJiaoyiImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []
      @permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_org = "#{@table_space}branch#{@sid_suffix}"
    end

    def import_to_do
      destroy_all_datas
      import_orgs
      import_accounts
      import_roles
      import_roles_accounts
      import_permissions
      import_account_permissions
      import_role_permissions
      import_ledgers(JzJizhongJiaoyi::Account)
    end

    private

    def destroy_all_datas
      JzJizhongJiaoyi::Department.where(quarter_id: @quarter_id).delete_all
      JzJizhongJiaoyi::Account.where(quarter_id: @quarter_id).destroy_all
      JzJizhongJiaoyi::Role.where(quarter_id: @quarter_id).destroy_all
      JzJizhongJiaoyi::Permission.where(quarter_id: @quarter_id).destroy_all
      JzJizhongJiaoyi::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_orgs_sql
      "select branch, branch_id, brh_name, parent_branch from #{@table_org} where status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzJizhongJiaoyi::Department.create(
          quarter_id: @quarter_id,
          source_id:  r[0],
          code:       r[1],
          name:       r[2],
          status:     true
        )
      end
      # 建联关联关系
      JzJizhongJiaoyi::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzJizhongJiaoyi::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzJizhongJiaoyi::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts_sql
      <<-SQL
        select a.user_code,
               a.user_roles,
               a.user_name,
               b.status,
               c.brh_name,
               c.branch
        from #{@table_space}USERS a
        inner join #{@table_space}OPERATORS b on a.user_code = b.op_code
        left join #{@table_space}branch c on b.open_brh = c.branch
      SQL
    end

    def import_accounts
      @departments = JzJizhongJiaoyi::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          import_account_line(r)
        end
      end
    end

    def import_account_line(row)
      code, roles, name, status, department_name = row
      return if code.to_s == '' || name.to_s == ''

      department = @departments.find { |x| x.source_id == row[5]&.to_s }

      account = JzJizhongJiaoyi::Account.create(
        quarter_id:    @quarter_id,
        code:          code,
        name:          name,
        status:        status.to_s == '0',
        data_json:     {department_name: department_name},
        department_id: department&.id
      )
      @accounts << account

      if @display_status
        QuarterAccountInfo.create(
          account_id:         account.id,
          account_type:       'JzJizhongJiaoyi::Account',
          business_system_id: @bs_id,
          quarter_id:         @quarter_id,
          display_status:     status&.to_s
        )
      end
    end

    def import_roles_sql
      <<-SQL
        select b.GRP_STD,  
               b.GRP_STD_NAME
        from #{@table_space}USER_GRP_STD_SETTING b
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          import_role_line(r)
        end
      end
    end

    def import_role_line(row)
      code, name = row

      @roles << JzJizhongJiaoyi::Role.create(
        quarter_id: @quarter_id,
        code: code,
        name: name
      )
    end

    def import_roles_accounts_sql
      <<-SQL
        select USER_CODE,  
               GRP_STD
        from #{@table_space}USER_GROUP
      SQL
    end

    def import_roles_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_accounts_sql) do |r|
          import_import_role_account_line(r)
        end
      end
    end

    def import_import_role_account_line(row)
      account_code, role_code = row

      account = @accounts.find{|x| x.code == account_code.to_s}
      role = @roles.find{|x| x.code == role_code.to_s}

      if account && role
        account.roles << role
      end
    end

    def import_permissions_sql
      <<-SQL
        select NODE_FUNC,  
               CAPTION
        from #{@table_space}ui_tree
      SQL
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_permissions_sql) do |r|
          import_permission_line(r)
        end
      end
    end
    
    def import_permission_line(row)
      code, name = row

      @permissions << JzJizhongJiaoyi::Permission.create(
        quarter_id: @quarter_id,
        code: code,
        name: name,
        permission_type: '菜单权限'
      )
    end

    def import_account_permissions_sql
      <<-SQL
        select user_code,  
               node_func,
               permit_type
        from #{@table_space}ui_rights
      SQL
    end

    def import_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_account_permissions_sql) do |r|
          import_account_permission_line(r)
        end
      end
    end

    def import_account_permission_line(row)
      account_code, permission_code, permit_type = row
      permission = @permissions.find{|x| x.code == permission_code.to_s}
      account = @accounts.find{|x| x.code == account_code.to_s}
      if account && permission
        JzJizhongJiaoyi::AccountsRolesPermission.create(
          quarter_id: @quarter_id,
          permission_id: permission.id,
          account_id: account.id,
          additional_permission: change_permit_type(permit_type)
        )
      end
    end

    def import_role_permissions_sql
      <<-SQL
        select grp_std,  
               node_func,
               permit_type
        from #{@table_space}UI_GRP_RIGHTS
      SQL
    end

    def import_role_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_role_permissions_sql) do |r|
          import_role_permission_line(r)
        end
      end
    end

    def import_role_permission_line(row)
      role_code, permission_code, permit_type = row
      permission = @permissions.find{|x| x.code == permission_code.to_s}
      role = @roles.find{|x| x.code == role_code.to_s}
      if role && permission
        JzJizhongJiaoyi::AccountsRolesPermission.create(
          quarter_id: @quarter_id,
          permission_id: permission.id,
          role_id: role.id,
          additional_permission: change_permit_type(permit_type)
        )
      end
    end

    def change_permit_type(permit_type)
      case permit_type.to_s
      when '0'
        '执行'
      when '1'
        '授权'
      when '2'
        '执行及授权'
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

  end
end