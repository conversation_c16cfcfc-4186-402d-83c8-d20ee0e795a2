defaults: &defaults
  customer_id: sczq
  customer: 首创证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  workday:
    enable: true
    mode: 'trading_day'
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      # host: *************
      host: localhost
      database: aas_sczq_production
      username: root
      # password: aas_sczq123
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"

  agent:
    # ldap用于佰钧成资管网站导入账号数据
    csv:
      zd_ccnet_db:
        path: '/opt/aas-app/data/import/zd_ccnet'
      zd_dcom_db:
        path: '/opt/aas-app/data/import/zd_dcom'
    xls:
      zd_prop_db:
        path: '/opt/aas-app/data/import/zd_prop'
    mysql:
      xt_qmt_db:
        db_host: *************5
        db_name: ttmgrportal
        db_user: aas_cj
        db_pass: aas_cjSczq123
      dingdian_a5_db:
        db_host: *************
        db_name: A5_manage
        db_user: aas_cj
        db_pass: aas_cjSczq123
      xuanwuduanxin_db:
        db_host: *************
        db_name: ump_gsms_new
        db_user: aas_cj
        db_pass: aas_cjSczq123
      bjc_ziguan_db:
        db_host: *************
        db_name: report_app
        db_user: aas_cj
        db_pass: aas_cjSczq123
    oracle:
      oa_db:
        db_host: *************
        db_name: scdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      htzg_db:
        db_host: *************
        db_name: frqsdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      httg_db:
        db_host: *************
        db_name: frqsdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      htgs_db:
        db_host: *************
        db_name: ncc10
        db_user: aas_cj
        db_pass: aas_cjSczq123
      qingsuan_db:
        db_host: *************
        db_name: TZJQSDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      guzhi_db:
        db_host: *************
        db_name: SCDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      farenqingsuan_db:
        db_host: *************
        db_name: FRQSDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      jiaoyi_db:
        db_host: *************
        db_name: hso32
        db_user: aas_cj
        db_pass: aas_cjSczq123
      jiaoyi_ziguan_db:
        db_host: *************
        db_name: hso32
        db_user: aas_cj
        db_pass: aas_cjSczq123
      dingdian_jiaoyi_db:
        db_host: **************
        db_name: jzjyrac
        db_user: aas_cj
        db_pass: aas_cjSczq123
      dingdian_rzrq_db:
        db_host: **************
        db_name: rzrqrac
        db_user: aas_cj
        db_pass: aas_cjSczq123
      dingdian_cif_db:
        db_host: **************
        db_name: cifdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      dingdian_otc_db:
        db_host: **************
        db_name: otcdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      sidi_cms_db:
        db_host: *************
        db_name: oldtestorcl
        db_user: aas_cj
        db_pass: aas_cjSczq123
      zhixiao_db:
        db_host: *************
        db_name: hsta4
        db_user: aas_cj
        db_pass: aas_cjSczq123
      guohu_ta_db:
        db_host: *************
        db_name: hsta4
        db_user: aas_cj
        db_pass: aas_cjSczq123
      guohu_zd_db:
        db_host: *************
        db_name: oldtestorcl
        db_user: aas_cj
        db_pass: aas_cjSczq123
      hspb_db:
        db_host: *************
        db_name: pbdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      tuoguan_guzhi_db:
        db_host: *************
        db_name: scdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      waibao_guzhi_db:
        db_host: *************
        db_name: tscdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      hs_waibaota_db:
        db_host: *************
        db_name: tscdb
        db_user: aas_cj
        db_pass: aas_cjSczq123
      xy_zijinjiesuan_db:
        db_host: *************
        db_name: FRQSDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      xy_toubao_db:
        db_host: *************
        db_name: FRQSDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      xy_zhifu_db:
        db_host: *************
        db_name: FRQSDB
        db_user: aas_cj
        db_pass: aas_cjSczq123
      jz_baobiao_db:
        db_host: *************
        db_name: oldtestorcl
        db_user: aas_cj
        db_pass: aas_cjSczq123
  importers:
    - name: fanwei_oa_users
      db_type: oracle
      tnsname: oa_db
      table_space: 'oa.'
      version: 1.1
      is_user: true # 标记是否导入员工
      ignore_company_ids:
        - 1
        - 21
    - name: hengtai_ziguan
      bs_id: 91
      db_type: oracle
      tnsname: htzg_db
      table_space: 'xir_app.'
      sid_suffix: ''
    - name: hengtai_tougu
      bs_id: 92
      db_type: oracle
      tnsname: httg_db
      table_space: 'xir_app.'
      sid_suffix: ''
    - name: hengtai_gushou
      bs_id: 90
      db_type: oracle
      tnsname: htgs_db
      table_space: 'xir_app.'
      sid_suffix: ''
    - name: qingsuan_department
      bs_id: 25
      db_type: oracle
      tnsname: qingsuan_db
      table_space: 'es_system.'
      product_table_space: 'ea_am.'
      sid_suffix: ''
      version: 1.1
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: guzhi_db
      table_space: hsfa30.
      sid_suffix: ''
      add_on_modules_disable: true
      import_fund_code: true
    - name: xy_farenqingsuan
      bs_id: 254
      db_type: oracle
      tnsname: farenqingsuan_db
      table_space: 'es_system.'
      sid_suffix: ''
      app_id: 1
    - name: dingdian_jiaoyi
      bs_id: 315
      db_type: oracle
      tnsname: dingdian_jiaoyi_db
      table_space: 'aboss.'
      sid_suffix: ''
    - name: dingdian_rzrq
      bs_id: 316
      db_type: oracle
      tnsname: dingdian_rzrq_db
      table_space: 'aboss.'
      sid_suffix: ''
    - name: dingdian_cif
      bs_id: 317
      db_type: oracle
      tnsname: dingdian_cif_db
      table_space: 'cif.'
      sid_suffix: ''
    - name: dingdian_otc
      bs_id: 318
      db_type: oracle
      tnsname: dingdian_otc_db
      table_space: 'livebos.'
      sid_suffix: ''
    - name: dingdian_a5
      bs_id: 284
      db_type: mysql
      tnsname: dingdian_a5_db
      table_space: ''
      sid_suffix: ''
    - name: sidi_cms
      bs_id: 319
      db_type: oracle
      tnsname: sidi_cms_db
      table_space: 'osoadata.'
      sid_suffix: ''
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: jiaoyi_db
      table_space: 'trade.'
      sid_suffix: ''
      days_of_data: 365
      temporary: false
      temp_delete_record: false
      history_temp: false
      # 交易类型
      trade_type_importer: true
      # 站点权限
      station_importer: true
      version: 1.1
    - name: jiaoyi_ziguan
      bs_id: 403
      db_type: oracle
      tnsname: jiaoyi_ziguan_db
      table_space: 'trade.'
      sid_suffix: ''
      days_of_data: 365
      temporary: false
      temp_delete_record: false
      history_temp: false
      # 交易类型
      trade_type_importer: true
      # 站点权限
      station_importer: tru
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zhixiao_db
      table_space: hsds.
      sid_suffix: ''
      sub_system: CENTER
      version: 1.1
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zhixiao_db
      table_space: hsds.
      sid_suffix: ''
      sub_system: COUNTER
      version: 1.1
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zhixiao_db
      table_space: hsds.
      sid_suffix: ''
      sub_system: REMOTE
      version: 1.1
    - name: zofund_guohu_ta
      bs_id: 402
      db_type: oracle
      tnsname: guohu_ta_db
      table_space: hsta4.
      sid_suffix: ''
    - name: guohu_zd
      bs_id: 270
      db_type: oracle
      tnsname: guohu_zd_db
      table_space: hsta.
      sid_suffix: ''
    - name: xt_qmt
      bs_id: 259
      db_type: mysql
      tnsname: xt_qmt_db
      sid_suffix: ''
    - name: hspb
      bs_id: 52
      db_type: oracle
      tnsname: hspb_db
      table_space: trade.
      sid_suffix: ''
      version: 1.1
    - name: tuoguan_guzhi
      bs_id: 280
      db_type: oracle
      tnsname: tuoguan_guzhi_db
      table_space: sm_hsfa_tgcbs.
      import_fund_code: true
      sid_suffix: ''
    - name: waibao_guzhi
      bs_id: 281
      db_type: oracle
      tnsname: waibao_guzhi_db
      table_space: hsfa_wb.
      import_fund_code: true
      sid_suffix: ''
    - name: hs_waibaota
      bs_id: 290
      db_type: oracle
      tnsname: hs_waibaota_db
      table_space: sczqta.
      sid_suffix: ''
    - name: xy_zijinjiesuan
      bs_id: 282
      db_type: oracle
      tnsname: xy_zijinjiesuan_db
      table_space: es_system.
      sid_suffix: ''
      app_id: 13
    - name: xy_toubao
      bs_id: 283
      db_type: oracle
      tnsname: xy_toubao_db
      table_space: es_system.
      sid_suffix: ''
      app_id: 25
    - name: xy_zhifu
      bs_id: 286
      db_type: oracle
      tnsname: xy_zhifu_db
      table_space: ea_ipmp2.
      sid_suffix: ''
    - name: jz_baobiao
      bs_id: 285
      db_type: oracle
      tnsname: jz_baobiao_db
      table_space: kgrp.
      sid_suffix: ''
    - name: xuanwuduanxin
      bs_id: 287
      db_type: mysql
      tnsname: xuanwuduanxin_db
      table_space: ''
      sid_suffix: ''
    - name: bjc_ziguan
      bs_id: 288
      db_type: mysql
      tnsname: bjc_ziguan_db
      table_space: ''
      sid_suffix: ''
    - name: zd_prop
      bs_id: 1001
      db_type: xls
      tnsname: zd_prop_db
    - name: zd_ccnet
      bs_id: 1002
      db_type: csv
      tnsname: zd_ccnet_db
    - name: zd_dcom
      bs_id: 1003
      db_type: csv
      tnsname: zd_dcom_db

    # - name: ldap_sczq_users
    #   bs_id: 1
    #   db_type: ldap
    #   tnsname: oa
    #   account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
    #   account_attrs:
    #   - dn
    #   - name
    #   - cn
    #   - samaccountname
    #   - title
    #   - useraccountcontrol
    #   - memberof
    #   - objectSid
    #   - primaryGroupID
    #   - description
    #   - department
    #   - mobile
    #   - ipphone
    #   - mail
    #   group_filter: # 不需要过滤了，在top_ou下
    #   group_attrs:
    #   - dn
    #   - name
    #   - memberOf
    #   - objectSid
    #   - primaryGroupID
    #   top_ou:
    #   - 首创证券
    #   ignore_accountname: []
    #   ignore_ou:
    #   - 首创证券
    #   ignore_ou_but_import_users:
    #   # - 用户
    #   user_ou_sequence:
    #     default: 1
    #     用户: 2
    #     离职人员: 1

development:
  <<: *defaults
test:
  <<: *defaults
production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      host: *************
      database: aas_sczq_production
      username: aas_sczq_production
      # password: aas_sczq123
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
