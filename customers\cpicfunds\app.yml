# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'cpicfunds'
  customer: '国联安基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_cpicfunds_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      # tnsname
      saledb1:
        # in tns name
        db_name: 'saledb1'
        db_user: 'tmpqry'
        db_pass: 'cjs*********'
      saledb1_old:
        # in tns name
        db_name: 'saledb1_old'
        db_user: 'query'
        db_pass: 'query'
      sa_db:
        db_name: 'tatest'
        db_user: 'subta'
        db_pass: 'subta'
      etf_db:
        db_name: 'tatest'
        db_user: 'etfta'
        db_pass: 'etfta'
      yanbao:
        db_name: 'yanbao'
        db_user: 'SDATA'
        db_pass: 'sdata'
      o32db:
        db_name: 'o32db'
        db_user: 'query'
        db_pass: 'query'
      zhixiaodb:
        db_name: 'jz_zhixiao'
        db_user: 'query'
        db_pass: 'query'
      fengkongdb:
        db_name: 'xriskdb'
        db_user: 'xrisk_read'
        db_pass: 'Xrisk_read'
        db_host: *************
    sqlserver:
      demeng_oa:
        db_host: '***********'
        db_user: 'sa'
        db_pass: 'msn*20100315'
        tds_v: '8.0'


  importers:
    # MARK: 按照顺序依次导入
    # 德萌阳光oa
    - name: demeng_oa
      bs_id: 1
      db_type: sqlserver
      tnsname: demeng_oa
      table_space: 'glaoa.dbo.'

    - name: fengkong_system
      bs_id: 45
      version: 1.1
      db_type: oracle
      tnsname: fengkongdb
      table_space: ''
      sid_suffix: ''

    - name: jz_zhixiao
      bs_id: 49
      db_type: oracle
      tnsname: zhixiaodb
      table_space: 'kd_sale.'
      sid_suffix: ''
      display_status: true

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o32db
      table_space: 'trade32.'
      sid_suffix: ''
      temporary: true
      days_of_data: 90
      time_control: true
      register_date: true
      cpic_custom_importer: true
      temp_delete_record: true
      history_temp: true
      display_status: true
    # guohu_components/ yhfund_guohu_ta: true
    # 登记过户 4.0
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: saledb1
      table_space: 'hsta4.'
      sid_suffix: ''
      display_status: true
    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: saledb1
      table_space: 'subta.'
      sid_suffix: ''
      version: 2018
      display_status: true

    - name: guohu_sa_etf
      bs_id: 40
      db_type: oracle
      tnsname: saledb1
      table_space: 'etfta.'
      sid_suffix: ''

    - name: guohu_sa_order
      bs_id: 42
      db_type: oracle
      tnsname: saledb1_old
      table_space: 'ossale.'
      sid_suffix: ''

    #- name: guzhi_yss25
    #  bs_id: 34
    #  db_type: oracle
    #  tnsname: saledb1
    #  table_space: 'fundacct25.'
    #  sid_suffix: ''

    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: saledb1
      table_space: 'gz45.'
      sid_suffix: ''
      display_status: true

    - name: xiening_touyan
      bs_id: 38
      db_type: oracle
      tnsname: yanbao
      table_space: 'glajj.'
      sid_suffix: ''

    - name: yss_qingsuan
      bs_id: 22
      db_type: oracle
      tnsname: saledb1
      table_space: 'newtaqs.'
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_cpicfunds_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: *************



