module AasOracleImporter

  class DidaimaImporter < ImporterBase

    def config
      @bs_id = 10085
      @accounts = []
      @roles = []
      @table_space = importer_config['table_space']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      
      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_data3_permissions
      import_data3_account_permissions

      import_ledgers(Didaima::Account)
    end

    
    def import_accounts_sql
      <<-EOF
        select a.id, a.username, a.name, a.deleted, a.enabled, d.name, a.departmentid, a.status from #{@table_space}h_org_user a left join #{@table_space}h_org_department d on a.departmentid = d.id
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Didaima::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            objid: r[1],
            name: get_enum(enums, "name", r[2]),
            status: r[7]=='ENABLE', 
            department_name: r[5],
            organization_code: r[6]
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id,code,name from #{@table_space}h_org_role
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Didaima::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[1]), name: get_enum(enums, "name", r[2]))
        end
      end
      
    end
    
    def import_accounts_roles_sql
      <<-EOF
        select roleid, userid, ouScope from #{@table_space}h_org_role_user
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            Didaima::AccountsRole.create(account_id: account.id, role_id: role.id  , role_scope: get_enum(enums, "role_scope", r[2]) )
          end
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        SELECT
          ad.id '管理员id',
          ad.userId '管理员关联的用户id',
          ad.adminType '管理员类型',
          ad.appManage '数据管理员是否有创建应用的权限',
          ad.dataQuery '数据管理员是否有查看数据的权限',
          ad.dataManage '数据管理员是否有管理数据的权限',
          ad.parentId '关联的父id',
          pas.CODE AS '应用编码',
          pas.NAME AS '应用名称',
          pds.queryCode AS '数据查询编码',
          pds.NAME AS '组织单元名称',
          pds.unitId AS '单元id，部门或用户id',
          pds.unitType as '组织类型'
        FROM
          #{@table_space}h_perm_admin AS ad
          LEFT JOIN #{@table_space}h_perm_apppackage_scope pas ON pas.adminId = ad.id
          LEFT JOIN #{@table_space}h_perm_department_scope pds ON pds.adminId = ad.id
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_account_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[0],
            data_json: {
              adminType: r[2],
              appManage: r[3],
              dataQuery: r[4],
              dataManage: r[5],
              parentId: r[6],
              appCode: r[7],
              appName: r[8],
              queryCode: r[9],
              queryName: r[10],
              unitId: r[11],
              unitType: r[12]
            }
          }
          @data1_permissions << Didaima::Data1Permission.create(json)
        end
      end
      
    end

    def import_data1_account_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_account_permissions_sql).each do |r|
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[0].to_s}
          if permission && account
            Didaima::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id)
          end
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select appCode from #{@table_space}h_perm_app_package
      EOF
    end

    def import_data2_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[0]
          }
          @data2_permissions << Didaima::Data2Permission.create(json) unless @data2_permissions.find{|x| x.source_id == r[0]}
        end
      end
      
    end
    
    
    
    def import_data2_account_permissions_sql
      <<-EOF
        SELECT

          pa.appCode '应用编码',
          pg.name '权限组名称',
          pg.departments '权限组关联的部门或人',
          pg.roles '权限组关联的角色',
          pg.authorType '授权类型',
          pf.functionCode '目录编码',
          pf.schemaCode '数据模型编码',
          pf.visibleType '是否可见',
          pf.creatable '新增',
          pf.importable '导入',
          pf.exportable '导出',
          pf.editable '编辑',
          pf.deleted '删除',
          pf.printAble '打印',
          pf.batchPrintAble '批量打印',
          pf.editOwnerAble '更改拥有者',
          pf.nodeType '类型',
          pf.dataPermissionType '数据权限类型',
          pfc.functionId '数据权限ID',
          pfc.schemaCode '模型编码',
          pfc.propertyCode '数据项编码',
          pfc.operatorType '过滤条件类型',
          pfc.value '数据权限ID'


        FROM
          #{@table_space}h_perm_group pg
          LEFT JOIN #{@table_space}h_perm_app_package pa ON pg.appCode = pa.appCode
          LEFT JOIN #{@table_space}h_perm_biz_function pf ON pf.permissionGroupId = pg.id
          LEFT JOIN #{@table_space}h_perm_function_condition pfc ON pfc.functionId = pf.id
      EOF
    end

    def import_data2_account_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_account_permissions_sql).each do |r|
          accounts = scan_departments(r[2], r[3])
          permission = @data2_permissions.find{|x| x.source_id.to_s == r[0].to_s}
          if permission && accounts.present?
            accounts.each do |account|
              Didaima::Data2AccountsRolesPermission.create(
                quarter_id: @quarter_id, 
                account_id: account.id, 
                data2_permission_id: permission.id,
                additional_permission: scan_permissions(r[8..15]),
                data_json: {
                  authorType: r[4],
                  functionCode: r[5],
                  schemaCode: r[6],
                  visibleType: r[7],
                  nodeType: r[16],
                  dataPermissionType: r[17],
                  functionId: r[18],
                  propertyCode: r[20],
                  operatorType: r[21],
                  value: r[22]
                }
              )
            end
          end
        end
      end
    end

    def scan_permissions(r)
      permission = []
      permission << '新增' if r[0] == 1
      permission << '导入' if r[1] == 1
      permission << '导出' if r[2] == 1
      permission << '编辑' if r[3] == 1
      permission << '删除' if r[4] == 1
      permission << '打印' if r[5] == 1
      permission << '批量打印' if r[6] == 1
      permission << '更改拥有者' if r[7] == 1
      permission.join(",")
    end

    def scan_departments(departments, roles)
      output_accounts = []
      departments = (departments.present?) ? JSON.parse(departments) : []
      departments.each do |item|
        case item['unitType']
        when 1
          output_accounts |= @accounts.select{|x| x.organization_code == item['id']}
        when 2
          role = @roles.find{|x| x.source_id.to_s == item['id']}
          if role
            output_accounts |= role.accounts
          end
        when 3
          account = @accounts.find{|x| x.source_id == item['id']}
          output_accounts |= [account] if account
        end
      end
      roles = roles.present? ? JSON.parse(roles) : []
      roles.each do |role|
        role = @roles.find{|x| x.source_id.to_s == role['id']}
        if role
          output_accounts |= role.accounts
        end
      end
      output_accounts
    end
    
    def import_data3_permissions_sql
      <<-EOF
        SELECT
          unitId '组织id',
          unitType '组织类型',
          workflowCode '流程编码'
        FROM
          #{@table_space}h_workflow_permission  
      EOF
    end

    def import_data3_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[2]),
            code: get_enum(enums, "code", r[2]),
            level1_name: r[2]
          }
          @data3_permissions << Didaima::Data3Permission.create(json) unless @data3_permissions.find{|x| x.source_id == r[2]}
        end
      end
      
    end


    def import_data3_account_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          permission = @data3_permissions.find{|x| x.source_id.to_s == r[2].to_s}
          case r[1]
          when 'DEPARTMENT'
            @accounts.select{|x| x.organization_code == r[0]}.each do |account|
              Didaima::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id, additional_permission: "部门-#{account.department_name}")
            end
          when 'USER'
            account = @accounts.find{|x| x.source_id == r[0]}
            if account.nil?
              @logger.warn "低代码平台导入流程权限时，对应的账号不存在：#{r[0]}"
              next
            end
            Didaima::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id, additional_permission: '个人授权')
          when 'ROLE'
            role = @roles.find{|x| x.source_id.to_s == r[0]}
            if role.nil?
              @logger.warn "低代码平台导入流程权限时，对应的角色不存在：#{r[0]}"
              next
            end
            role.accounts.each do |account|
              Didaima::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id, additional_permission: "角色-#{role.name}")
            end
          end
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.gsub(';','')
      
      output_datas = []
      @database.exec(sql).each do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
      
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end



