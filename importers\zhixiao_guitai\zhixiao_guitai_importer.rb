# frozen_string_literal: true

module AasOracleImporter
  # 直销柜台系统导入
  class ZhixiaoGuitaiImporter < ZhixiaoImporter
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @sub_system  = importer_config['sub_system']

      initialize_tables
      initialize_classes
    end

    def initialize_classes
      @account_class          = ZhixiaoGuitai::Account
      @role_class             = ZhixiaoGuitai::Role
      @menu_class             = ZhixiaoGuitai::Menu
      @other_permission_class = ZhixiaoGuitai::OtherPermission

      @account_role_associate_class   = ZhixiaoGuitaiAccountsRoles
      @account_menu_associate_class   = ZhixiaoGuitaiAccountsMenus
      @role_menu_associate_class      = ZhixiaoGuitaiMenusRoles
      @role_other_permission_class    = ZhixiaoGuitaiOtherPermissionsRoles
      @account_other_permission_class = ZhixiaoGuitaiAccountsOtherPermissions
    end
  end
end



