module JinzhengTaTwo
  def self.table_name_prefix
    'jinzheng_ta_two_'
  end
end

class JinzhengTaTwo::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
end

class JinzhengTaTwo::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class JinzhengTaTwo::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class JinzhengTaTwoMenusRoles < ActiveRecord::Base; end
class JinzhengTaTwoAccountsRoles < ActiveRecord::Base; end
class JinzhengTaTwoAccountsMenus < ActiveRecord::Base; end
