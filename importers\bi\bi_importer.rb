# frozen_string_literal: true

module AasOracleImporter

  # BI 数据库导入
  class BiImporter < ImporterBase
    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_roles          = importer_config['table_roles']
      @table_menus          = importer_config['table_menus']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus_roles    = importer_config['table_menus_roles']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_menus_roles
      import_accounts_roles
      import_ledgers(Bi::Account)
    end

    def import_accounts_sql
      "select usercode, username, comcode,userstate from #{@table_accounts}"
    end

    def import_accounts
      Bi::Account.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          # username有可能为空，为空取usercode作为username
          if r[0]
            name = r[1].present? ? r[1]&.strip : r[0]
            Bi::Account.create(
              quarter_id:   @quarter_id,
              code:         r[0],
              name:         name,
              company_code: r[2]&.strip,
              status:       r[3].to_i == 0
            )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select menugrpcode, menugrpname
        from #{@table_roles}
      SQL
    end

    def import_roles
      Bi::Role.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          Bi::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
            )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select distinct (d.nodecode),
           Case
             When a.nodename is null and b.nodename is null and c.nodename is null then
              d.nodename
             When a.nodename is null and b.nodename is null then
              c.nodename || '->' || d.nodename
             when a.nodename is null then
              b.nodename || '->' || c.nodename || '->' || d.nodename
             else
              a.nodename || '->' || b.nodename || '->' || c.nodename || '->' || d.nodename
           end,
           d.nodeorder
           from #{@table_menus} a, #{@table_menus} b, #{@table_menus} c, #{@table_menus} d
          where a.nodecode(+) = b.parentnodecode
            and b.nodecode(+) = c.parentnodecode
            and c.nodecode(+) = d.parentnodecode
            and d.childflag = '0'
          order by d.nodeorder
      SQL
    end

    def import_menus
      Bi::Menu.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          Bi::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
            )
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select menugrpcode,nodecode
        from #{@table_menus_roles}
      SQL
    end

    def import_menus_roles

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = Bi::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Bi::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            Bi::MenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select usercode,menugrpcode
        from #{@table_accounts_roles}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Bi::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Bi::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account && role
            Bi::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end
  end
end
