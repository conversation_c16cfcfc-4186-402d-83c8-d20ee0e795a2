module AasOracleImporter

  class ImsJiaoyiOracleImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @permissions = {}
      @roles       = {}
      @accounts    = {}
      @account_funds = {}
      @account_roles = {}
      @role_permissions = {}
      @fund_permissions = {}
    end

    def import_to_do
      destroy_old_datas
      import_role_permission_data
      import_account_data
      import_account_role_data
      import_fund_data
      import_ledgers(ImsJiaoyiOracle::Account)
    end

    def destroy_old_datas
      ImsJiaoyiOracle::Account.where(quarter_id: @quarter_id).destroy_all
      ImsJiaoyiOracle::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
      ImsJiaoyiOracle::Permission.where(quarter_id: @quarter_id).destroy_all
      ImsJiaoyiOracle::Role.where(quarter_id: @quarter_id).destroy_all
    end

    def import_role_permission_data_sql
      sql = <<-EOF
        with temp as
(select a.s_rolecode,a.s_rolename, sys_connect_by_path(nvl(a.s_parentcode,a.s_rolecode),',') ||','||a.s_rolecode||',' s_parentcodes
    from ims.t_fw_role a
   start with a.s_parentcode is null
  connect by  a.s_parentcode = prior a.s_rolecode)
SELECT distinct
       t1.s_rolecode 角色编号,
       t1.s_rolename 角色名称,
       decode(t2.n_type, 1, '菜单权限', 2, '控件权限') 权限类型,
       t2.s_rightcode 权限编号,
       t3.s_menuname 权限名称,
       decode(t2.s_systemtype,
              '1',
              '投资管理平台',
              '2',
              '投研助手',
              '3',
              '定时任务框架',
              '4',
              '云投资管理平台',
              '1,4',
              '投资管理平台、云投资管理平台') 系统类型,
       '操作权限' 权限性质,
       t3.s_parentcode
FROM temp t1 ,ims.t_fw_role_right t2,ims.t_fw_menu t3  
  where instr(t1.s_parentcodes,','|| t2.s_rolecode ||',')>0
  and t2.s_rightcode =t3.s_menucode
  and t2.n_type='1'
union
SELECT distinct
       t1.s_rolecode 角色编号,
       t1.s_rolename 角色名称,
       decode(t2.n_type, 1, '菜单权限', 2, '控件权限') 权限类型,
       t2.s_rightcode 权限编号,
       t3.S_BUTTONNAME 权限名称,
       decode(t2.s_systemtype,
              '1',
              '投资管理平台',
              '2',
              '投研助手',
              '3',
              '定时任务框架',
              '4',
              '云投资管理平台',
              '1,4',
              '投资管理平台、云投资管理平台') 系统类型,
       '操作权限' 权限性质,
       '' 上级权限
FROM temp t1 ,ims.t_fw_role_right t2,ims.t_fw_menu_button t3  
  where instr(t1.s_parentcodes,','|| t2.s_rolecode ||',')>0
  and t2.s_rightcode =t3.s_buttoncode
  and t2.n_type='2'
  
union all --union 获取授权权限
 
select a.s_rolecode,
       c.s_rolename,
       decode(a.n_type, 11, '菜单授权权限', 12, '控件授权权限') menutype, --3策略可见权限
       a.s_rightcode,
       d.s_menuname,
       decode(d.s_systype,
              '1',
              '投资管理平台',
              '2',
              '投研助手',
              '3',
              '定时任务框架',
              '4',
              '云投资管理平台',
              '1,4',
              '投资管理平台、云投资管理平台') 系统类型, --如何区分属于ims还是IMCloud？
       '授权权限',
       d.s_parentcode
  from ims.t_fw_role_right_grant a, ims.t_fw_role c, ims.t_fw_menu d
where a.s_rolecode = c.s_rolecode
   and a.s_rightcode = d.s_menucode
   and a.n_type = '11'
union
select a.s_rolecode,
       c.s_rolename,
       decode(a.n_type, 11, '菜单授权权限', 12, '控件授权权限') menutype, --3策略可见权限
       a.s_rightcode,
       d.S_BUTTONCAPTION,
       decode(e.s_systype,
             '1',
              '投资管理平台',
              '2',
              '投研助手',
              '3',
              '定时任务框架',
              '4',
              '云投资管理平台',
              '1,4',
              '投资管理平台、云投资管理平台') 系统类型, --如何区分属于ims还是IMCloud？
      '授权权限',
      e.s_parentcode
  from ims.t_fw_role_right_grant a,
       ims.t_fw_role             c,
       ims.t_fw_menu_button      d,
       ims.t_fw_menu             e
where a.s_rolecode = c.s_rolecode
   and a.s_rightcode = d.s_buttoncode
   and d.s_menucode = e.s_menucode
   and a.n_type = '12'
      EOF
    end

    def import_role_permission_data
      sql = import_role_permission_data_sql
      role_permission_data = []
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role_permission_data << import_role_permission_line(r)
        end

        ImsJiaoyiOracle::AccountsRolesPermission.bulk_insert(:quarter_id, :permission_id, :role_id) do |obj|
          obj.set_size = 1000
          role_permission_data.each do |x|
            obj.add [@quarter_id, x[:permission_id], x[:role_id]] if x[:permission_id] && x[:role_id]
          end
        end
      end
    end
       # t_fw_menu.parent code

    def import_role_permission_line(data_array)
      role_code, role_name, permission_type, permission_code, permission_name, system_type, permission_nature, parent_code = data_array
      role = @roles[role_code.to_s] 
      unless role
        role = ImsJiaoyiOracle::Role.create(quarter_id: @quarter.id, code: role_code, name: role_name)
        @roles[role_code.to_s] = role
      end
      permission = @permissions[permission_code.to_s]
      unless permission
        permission = ImsJiaoyiOracle::Permission.create(quarter_id: @quarter.id, code: permission_code, name: permission_name, permission_type: permission_type, parent_code: parent_code, data_json: { system_type: system_type, permission_nature: permission_nature })
        @permissions[permission_code.to_s] = permission
      end
      {permission_id: permission.id, role_id: role.id}
    end

    def import_account_data_sql
      <<-EOF
        select t.s_usercode 用户代码,
        t.s_username 用户名称,
        t.s_dept 部门,
        decode(t.n_state, '0', '启用', '1', '禁用', '2', '启用') 用户状态
        from ims.t_fw_user t
      EOF
    end

    def import_account_data
      sql = import_account_data_sql

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          import_account_line r
        end
      end
    end

    def import_account_line(data_array)
      account_code, account_name, dept, account_status = data_array
      account = @accounts[account_code.to_s]
      unless account
        account = ImsJiaoyiOracle::Account.create(
          quarter_id: @quarter.id, 
          code: account_code.to_s, 
          name: account_name.to_s, 
          status: account_status == "启用"
        )
        @accounts[account_code.to_s] = account
      end
    end

    def import_account_role_data_sql
      <<-EOF
          select t.s_usercode 用户代码,t.s_rolecode 角色编号,l.s_rolename 角色名称 from ims.t_fw_user_role t,ims.t_fw_role l
          where t.s_rolecode=l.s_rolecode
      EOF
    end

    def import_account_role_data
      sql = import_account_role_data_sql

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          import_account_role_line r
        end
      end
    end

    def import_account_role_line(data_array)
      account_code, role_code, role_name = data_array
      account = @accounts[account_code.to_s]
      unless account
        account = ImsJiaoyiOracle::Account.create(quarter_id: @quarter.id, code: account_code.to_s)
        @accounts[account_code.to_s] = account
      end

      role = @roles[role_code.to_s]
      unless role
        role = ImsJiaoyiOracle::Role.create(quarter_id: @quarter.id, code: role_code.to_s, name: role_name.to_s)
        @roles[role_code.to_s] = role
      end

      if account && role
        account_role = @account_roles[account_code.to_s+role_code.to_s]
        unless account_role
          account.roles << role
          @account_roles[account_code.to_s+role_code.to_s] = true
        end
      end
    end

    def import_fund_data_sql
      <<-EOF
          select a.s_usercode 用户,
       a.s_fundcode 基金代码,
       a.l_fundid 基金序号,
       l.f_name 基金名称,
       a.l_combi_id 组合序号,
       k.s_combi_name 组合名称,
       a.s_source 权限来源,
      a.s_rights 基金权限类型,
       a.s_bussin_rights 基金业务权限类型,
      -- a.s_opt_usercode 赋权操作用户,
       --to_char(a.t_opt_date) 赋权操作时间,
       '基金操作权限' 权限性质
  from ims.t_im_user_fund a, idc.t_fmn_fundinfo l, idc.t_fmn_combiinfo k
where a.s_fundcode = l.f_code
   and a.l_combi_id = k.l_combi_id
   and l.s_fund_status not in ('4','5')
   and trunc(sysdate, 'd') >= a.t_begin_date
   and trunc(sysdate, 'd') <= a.t_end_date
union all --union基金权限授予角色部分
select c.s_usercode,
       b.s_fundcode,
       b.l_fundid,
       l.f_name,
       b.l_combi_id,
       k.s_combi_name,
       b.s_source,
       b.s_rights,
       ' ' 基金业务权限类型,
      -- ' ' 赋权操作用户,
     --  ' ' 赋权操作时间,
       '基金操作权限' 权限性质
  from ims.t_im_role_fund  b,
       ims.t_fw_user_role  c,
       idc.t_fmn_fundinfo  l,
       idc.t_fmn_combiinfo k
where b.s_rolecode = c.s_rolecode
   and b.s_fundcode = l.f_code
   and b.l_combi_id = k.l_combi_id
   and l.s_fund_status not in ('4','5')
union all --union 授权权限
select d.s_usercode,
       d.s_fundcode,
       d.l_fundid,
       l.f_name,
       d.l_combi_id,
       k.s_combi_name,
       d.s_source,
       ' ' 基金权限类型,
       ' ' 基金业务权限类型,
      -- d.s_opt_usercode 赋权操作用户,
      -- to_char(d.t_opt_date) 赋权操作时间,
       '基金授权权限' 权限性质
  from ims.t_im_user_fund_grant d,
       idc.t_fmn_fundinfo       l,
       idc.t_fmn_combiinfo      k
where d.s_fundcode = l.f_code
   and d.l_combi_id = k.l_combi_id
  and l.s_fund_status not in ('4','5')
union all --union基金权限授予角色部分
select f.s_usercode,
       e.s_fundcode,
       e.l_fundid,
       l.f_name,
       e.l_combi_id,
       k.s_combi_name,
       e.s_source,
       ' ' 基金权限类型,
       ' ' 基金业务权限类型,
       --' ' 赋权操作用户,
      -- ' ' 赋权操作时间,
       '基金授权权限' 权限性质
  from ims.t_im_role_fund_grant e,
       ims.t_fw_user_role       f,
       idc.t_fmn_fundinfo       l,
       idc.t_fmn_combiinfo      k
where e.s_rolecode = f.s_rolecode
   and e.s_fundcode = l.f_code
   and e.l_combi_id = k.l_combi_id
      and l.s_fund_status not in ('4','5')
      EOF
    end

    def import_fund_data
      sql = import_fund_data_sql

      ActiveRecord::Base.transaction do
        account_fund_permissions = []
        @database.exec(sql) do |r|
          account_fund_permissions << import_fund_line(r)
        end
        ImsJiaoyiOracle::AccountsRolesPermission.bulk_insert(:quarter_id, :permission_id, :account_id, :additional_permission, :data_json) do |obj|
          obj.set_size = 1000
          account_fund_permissions.compact.each do |x|
            obj.add [@quarter_id, x[:permission_id], x[:account_id], x[:additional_permission], x[:data_json].to_json] if x[:permission_id] && x[:account_id]
          end
        end
      end
    end

    def import_fund_line(data_array)
      account_code, fund_code, fund_id, fund_name, combination_id, combination_name, permission_source, fund_permission, business_permission, permission_nature = data_array
      account = @accounts[account_code.to_s]
      if account && combination_id.to_s != ''
        fund_permission_code = "combination_#{combination_id}"
        permission = @fund_permissions[fund_permission_code]

        unless permission
          permission = ImsJiaoyiOracle::Permission.new(
            quarter_id:      @quarter.id,
            code:            fund_permission_code,
            name:            combination_name,
            permission_type: '基金权限'
          )
          permission.data_json = {
            fund_code:         fund_code,
            fund_name:         fund_name,
            permission_source: permission_source,
            permission_nature: permission_nature
          }
          permission.save
          @fund_permissions[fund_permission_code] = permission
        end

        output_json ={
          quarter_id:            @quarter.id,
          permission_id:         permission.id,
          account_id:            account.id,
          additional_permission: set_fund_permission(fund_permission.to_s),
          data_json:             {
            business_permission: set_business_permission(business_permission.to_s)
          }
        }
        return output_json
      end
      return nil
    end

    def set_fund_permission(permission)
      output_permission = []
      output_permission << "查询" if permission.include?('1')
      output_permission << "操作" if permission.include?('2')
      output_permission << "复核" if permission.include?('3')
      output_permission << "审批" if permission.include?('4')
      output_permission.join(",")
    end

    def set_business_permission(permission)
      output_permission = []
      output_permission << "新股" if permission.include?('1')
      output_permission << "公增" if permission.include?('2')
      output_permission << "定增" if permission.include?('3')
      output_permission << "可转债" if permission.include?('4')
      output_permission << "可交换债" if permission.include?('5')
      output_permission.join(",")
    end

    def class_exists?(class_name)
      klass = Module.const_get(class_name)
      klass.first
      return klass.is_a?(Class)
    rescue NameError
      return false
    end

  end
end



