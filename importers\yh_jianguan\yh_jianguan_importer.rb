module AasOracleImporter
  class YhJianguanImporter < ImporterBase
    def config
      @bs_id       = 306
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      @data3_permissions = []
      @data3_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}Sys_user#{@sid_suffix}"
      @table_role         = "#{@table_space}Sys_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}Sys_user_role#{@sid_suffix}"
      @table_menu         = "#{@table_space}Sys_resource#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}Sys_role_resource#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data2_permissions
      import_data2_role_permissions
      import_data3_permissions
      import_data3_role_permissions
      import_ledgers(YhJianguan::Account)
    end

    def destroy_exist_datas
      accounts = YhJianguan::Account.where(quarter_id: @quarter_id)
      YhJianguan::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      YhJianguan::Role.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data1Permission.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data2Permission.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data3Permission.where(quarter_id: @quarter_id).delete_all
      YhJianguan::Data3AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select name, name as code, real_name, status from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << YhJianguan::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '1'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id as code, name from #{@table_role} where status='E'
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << YhJianguan::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_name from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          YhJianguan::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, id as code, name, parent_id from #{@table_menu} where type=0 and status='E'
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << YhJianguan::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, resource_id from #{@table_role_menu} where status = 'E'
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          YhJianguan::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select id, id as code, name, parent_id from #{@table_menu} where type=1 and status='E'
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 3
        select_db_datas(import_data2_permissions_sql).each do |r|
          name            = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name       = full_name(import_data2_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name     = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << YhJianguan::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select role_id, resource_id from #{@table_role_menu} where status = 'E'
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          YhJianguan::Data2AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data2_permission_id: permission.id
          )
        end
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select id, id as code, uri, action, parent_id from #{@table_menu} where type=2 and status='E'
      EOF
    end

    def import_data3_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 4
        select_db_datas(import_data3_permissions_sql).each do |r|
          name            = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name       = full_name(import_data3_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name     = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: r[3]
          }
          @data3_permissions << YhJianguan::Data3Permission.create(json)
        end
      end
    end

    def import_data3_role_permissions_sql
      <<-EOF
        select role_id, resource_id from #{@table_role_menu} where status = 'E'
      EOF
    end

    def import_data3_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data3_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          YhJianguan::Data3AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data3_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum =  1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
