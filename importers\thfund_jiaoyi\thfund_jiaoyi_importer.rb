require 'httparty'
require 'csv'
module AasOracleImporter

  class ThfundJiaoyiImporter < ImporterBase

    def config
      @bs_id       = 132
      @accounts    = {}
      @roles       = {}
      @accounts_roles_url  = importer_config['accounts_roles_url']
      @roles_menus_csv_path = importer_config['roles_menus_csv_path']
      
      
      @data1_permissions = {}
      @data1_permissions_row = nil
      @data1_accounts_roles_permissions = {}
      
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_account_roles
      import_roles_menus
      
      import_ledgers(ThfundJiaoyi::Account)
    end

    def destroy_exist_datas
      accounts = ThfundJiaoyi::Account.where(quarter_id: @quarter_id)
      ThfundJiaoyi::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      ThfundJiaoyi::Role.where(quarter_id: @quarter_id).delete_all
      ThfundJiaoyi::Data1Permission.where(quarter_id: @quarter_id).delete_all
      ThfundJiaoyi::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end
    

    def import_account_roles
      ActiveRecord::Base.transaction do
        get_accounts.each do |r|
          if @accounts[r['userAccount'].to_s].blank?
            account = ThfundJiaoyi::Account.create(
              quarter_id: @quarter_id,
              source_id: r['userAccount'],
              code: r['userAccount'],
              name: r['userName'],
              status: true
            )
            @accounts[r['userAccount'].to_s] = account
          end
          if @roles[r['roleCode'].to_s].blank?
            role = ThfundJiaoyi::Role.create(
              quarter_id: @quarter_id,
              source_id: r['roleCode'],
              code: r['roleCode'],
              name: r['roleName']
            )
            @roles[r['roleCode'].to_s] = role
          end
          account = @accounts[r['userAccount'].to_s]
          role = @roles[r['roleCode'].to_s]
          account.roles << role if account && role
        end
        if @accounts.blank?
          raise ThfundJiaoyiImporter, "ThfundJiaoyiImporter accounts is null"
        end
      end
    end

    def get_accounts
      url = @accounts_roles_url
      response = HTTParty.get(url, headers: headers)
      result = JSON.parse(response.body)
      return result['data']
    end

    def headers
      {
        'Content-Type' => 'application/json'
      }
    end
    

    def import_roles_menus
      ActiveRecord::Base.transaction do
        enums = []
        get_permissions.each do |r|
          r['菜单权限'].split("、").each do |permission|
            if @data1_permissions[permission.to_s].blank?
              data1_permission = ThfundJiaoyi::Data1Permission.create(
                source_id: permission,
                code: permission,
                quarter_id: @quarter_id,
                level1_name: permission
              )
              @data1_permissions[permission.to_s] = data1_permission
            end
            role = @roles[r['角色代码'].to_s]
            data1_permission = @data1_permissions[permission.to_s]

            if role && data1_permission
              ThfundJiaoyi::Data1AccountsRolesPermission.create(
                quarter_id: @quarter_id,
                data1_permission_id: data1_permission.id,
                role_id: role.id
              )
            end
          end
        end
      end
    end

    def get_permissions
      data = CSV.read(@roles_menus_csv_path, headers: true)
    end

  end
end
