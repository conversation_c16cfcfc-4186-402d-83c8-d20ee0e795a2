# frozen_string_literal: true

module AasOracleImporter
  # 嘉扬人事系统导入
  # noinspection SqlNoDataSourceInspection
  class JiayangHrImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @filter_user = importer_config['filter_user']
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      @accounts                   = []
      @roles                      = []
      @permissions                = []
      @post_permissions           = []
      @menus                      = []
      @posts                      = []
      @accounts_roles             = []
      @accounts_roles_permissions = []

      import_accounts_and_users
      import_roles
      import_accounts_roles
      import_menu_permissions
      import_post_permissions_rela
      import_permissions
      import_post_permissions
      import_roles_permissions
      import_ledgers(JiayangHr::Account)

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    #-----------------------accounts------------------------------------------------------
    def import_accounts_sql
      <<-SQL
        select a.Badge, a.Name, a.Disabled, b.empstatus, a.id, j.title position, b.JoinDate, b.LeaveDate, b.email, b.mobile,
        c1.Title t1, c1.isDisabled t1_is_disabled, c1.xOrder t1_order,
        c2.Title t2, c2.isDisabled t2_is_disabled, c2.xOrder t2_order,
        c3.Title t3, c3.isDisabled t3_is_disabled, c3.xOrder t3_order
        from #{@table_space}skySecUser#{@sid_suffix} a
        left join #{@table_space}eVW_employee#{@sid_suffix} b on a.Badge=b.Badge
        left join #{@table_space}oDepartment#{@sid_suffix} c1 on b.depid1=c1.DepID
        left join #{@table_space}oDepartment#{@sid_suffix} c2 on b.depid2=c2.DepID
        left join #{@table_space}oDepartment#{@sid_suffix} c3 on b.DEPIDBD=c3.DepID
        left join #{@table_space}ojob#{@sid_suffix} j on j.JobID=b.JobID
      SQL
    end

    def import_accounts_and_users
      ActiveRecord::Base.transaction do
        @database.query(import_accounts_sql).each do |row|
          set_accounts_line(row)
        end
      end
    end

    def set_accounts_line(row)
      account_status = convert_account_status(row['Disabled'])
      # 员工状态字段值：1 在职，2离职，3退休
      user_status = row['empstatus'].to_i == 1
      department  = find_or_create_department(row['t1'], row['t1_is_disabled'], row['t1_order'], row['t2'], row['t2_is_disabled'], row['t2_order'], row['t3'], row['t3_is_disabled'], row['t3_order'])
      find_or_create_user(row['Badge'], row['Name'], user_status, department, row['position'], row['JoinDate'], row['LeaveDate'], row['email'], row['mobile'])

      if @accounts.find { |x| x.code == row['Badge'].to_s }
        @logger.warn "#{self.class}: found same user code '#{row['Badge']}'"
        return
      end

      # 根据规则过滤用户
      if @filter_user
        regexp_code   = @filter_user&.[]('regexp')&.[]('code')
        if regexp_code.present?
          if row['Badge'].nil?
            @logger.warn "#{self.class}: found same user code '#{row['Badge']}'"
            return
          end
          if row['Name'].nil?
            @logger.warn "#{self.class}: found same user name '#{row['Name']}'"
            return
          end
          regexp = Regexp.new(regexp_code)
          return if row['Badge'].match?(regexp)
        end
      end
      account = create_account(row['id'], row['Badge'], row['Name'], account_status)
      @accounts << account
    end

    # 民生证券在数据中心同步的时候，改变了 r['Disabled'] 的类型，所以这里要做判断
    # 民生证券是 数字型
    # 原 sqlserver 的库是布尔型
    def convert_account_status(status_value)
      status_value.is_a?(Integer) ? status_value.zero? : !status_value
    end

    def create_account(objid, code, name, status)
      JiayangHr::Account.create(
        quarter_id: @quarter_id,
        code:       code,
        name:       name,
        status:     status,
        objid:      objid
      )
    end

    # 获取或者创建部门，有关联关系
    def find_or_create_department(t1, t1_is_disabled, t1_order, t2, t2_is_disabled, t2_order, t3, t3_is_disabled, t3_order)
      return if t3.nil?

      # is_disabled为1的就是失效部门
      d = Department.find_or_create_by(code: '失效部门', name: '失效部门')
      d.update(level: 1, parent_id: nil)
      d3 = find_or_create_by_department(d, t3, t3_order, t3_is_disabled)
      d3.update(inservice: true) unless d3.inservice
      # t3 不等于 t1的情况下，t2不会出现为空的情况
      if t3 == t1
        d3.update(level: 1)
      else
        d1 = find_or_create_by_department(d, t1, t1_order, t1_is_disabled)
        d2 = find_or_create_by_department(d, t2, t2_order, t2_is_disabled)
        d1.update(inservice: true) unless d1.inservice
        d2.update(inservice: true) unless d2.inservice
        if t3 == t2
          d1.update(level: 1) if t1_is_disabled != 1
          d2.update(level: 2, parent_id: d1.id) if t2_is_disabled != 1
        else
          d1.update(level: 1) if t1_is_disabled != 1
          d2.update(level: 2, parent_id: d1.id) if t2_is_disabled != 1
          d3.update(level: 3, parent_id: d2.id) if t3_is_disabled != 1
        end
      end
      d3
    end

    # 获取或者创建单个部门，没有关联关系
    def find_or_create_by_department(d, t1, t1_order, t1_is_disabled)
      department = Department.find_or_initialize_by(code: t1, name: t1)
      department.update(position: t1_order)
      if t1_is_disabled == 1
        department.update(parent_id: d.id, level: 2)
      end
      department
    end

    def find_or_create_user(code, name, status, department, position, join_date, disable_date, email, mobile)
      return nil if name.nil? || (name == '')
      return nil if code.nil? || (code == '')

      # 根据规则过滤用户
      if @filter_user
        blank_columns = @filter_user&.[]('blank_column')
        regexp_code   = @filter_user&.[]('regexp')&.[]('code')
        return if blank_columns.present? && blank_columns.include?('department_id') && department.nil?

        if regexp_code.present?
          regexp = Regexp.new(regexp_code)
          return if code.match?(regexp)
        end
      end

      user = User.find_by(code: code)
      if user
        user.update(
          name:          name,
          position:      position,
          inservice:     status,
          department_id: department&.id,
          join_date:     join_date,
          email:         email,
          disable_date:  disable_date,
          cellphone:     mobile
        )
      else
        user =
          User.create(
            code:          code,
            name:          name,
            position:      position,
            inservice:     status,
            department_id: department&.id,
            join_date:     join_date,
            email:         email,
            disable_date:  disable_date,
            cellphone:     mobile
          )
      end
      @position_users << user if user.position?
      user
    end

    #--------------------------------roles-----------------------------------------------
    def import_roles_sql
      <<-SQL
        select ID, Title from #{@table_space}skySecRole#{@sid_suffix}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.query(import_roles_sql).each do |r|
          insert_roles_line(r)
        end
      end
    end

    def insert_roles_line(r)
      @roles << JiayangHr::Role.create(
        quarter_id: @quarter_id,
        code:       r['ID'],
        name:       r['Title']
      )
    end

    #-------------------------------------------accounts_roles--------------------------------
    def import_accounts_roles_sql
      <<-SQL
        select ID, RUID, URID from #{@table_space}skySecRoleMember#{@sid_suffix}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.query(import_accounts_roles_sql).each do |r|
          insert_accounts_roles_line(r)
        end
      end
    end

    def insert_accounts_roles_line(r)
      account = @accounts.find { |x| x.objid == r['URID'].to_s }
      role    = @roles.find { |x| x.code == r['RUID'].to_s }
      @accounts_roles << r
      account.roles << role if role && account
    end

    #-------------------------permissions--------------------------------------------

    def import_menu_permissions_sql
      <<-SQL
        select
          r.id as 'role_code',
          r.title as 'role_name',
          m.id as 'menu_code',
          m.title as 'menu_name',
          rm.fmid,
          rm.remark
        from #{@table_space}skySecRole#{@sid_suffix} r,
        #{@table_space}SKYSECDESKTOPMENUS#{@sid_suffix} m,
        #{@table_space}skySecRolePermission#{@sid_suffix} rm
        where r.id = rm.ruid
        and m.id = rm.dmid
        and rm.allowopen = 1
        and rm.fmid=0
        and m.id!=734
        and m.id!=733
        order by rm.remark
      SQL
    end

    def import_menu_permissions
      ActiveRecord::Base.transaction do
        @database.query(import_menu_permissions_sql).each do |r|
          import_menu_permissions_line(r)
        end
      end
    end

    def import_menu_permissions_line(r)
      role = @roles.find { |x| x.code == r['role_code'].to_s }
      menu = @menus.find { |x| x.code == r['menu_code'].to_s }
      unless menu
        menu = JiayangHr::Permission.create(
          quarter_id:      @quarter_id,
          code:            r['menu_code'],
          name:            r['menu_name'],
          permission_type: '菜单权限'
        )
        @menus << menu
      end
      if role && menu
        JiayangHr::AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          role_id:               role.id,
          permission_id:         menu.id,
          additional_permission: r['remark']
        )
      end
    end

    def import_permissions_sql
      <<-SQL
        select ID, Title from #{@table_space}skyEmpZone#{@sid_suffix}
      SQL
    end

    def import_permissions
      ActiveRecord::Base.transaction do
        @database.query(import_permissions_sql).each do |r|
          insert_permissions_line(r)
        end
      end
    end

    def insert_permissions_line(r)
      @permissions << JiayangHr::Permission.create(
        quarter_id:      @quarter_id,
        code:            r['ID'],
        name:            r['Title'],
        permission_type: '权限范围'
      )
    end

    # 兼岗权限
    def import_post_permissions_rela_sql
      <<-SQL
        select 
          id as post_id,
          id as post_code,
          badge as account_id,
          otitle as post_name,
          dtitle as department_name
        from 
          #{@table_space}eVW_PartOrg
      SQL
    end

    def import_post_permissions_rela
      ActiveRecord::Base.transaction do
        @database.query(import_post_permissions_rela_sql).each do |r|
          import_post_permissions_rela_line(r)
        end
      end
    end

    def import_post_permissions_rela_line(r)
      account = @accounts.find { |x| x.code == r['account_id'].to_s }
      post = @post_permissions.find { |x| x.code == r['post_code'].to_s }
      unless post
        post = JiayangHr::PostPermission.create(
          quarter_id:      @quarter_id,
          code:            r['post_code'],
          post_name:       r['post_name'],
          department_name: r['department_name'],
          permission_type: '兼岗权限'
        )
        @post_permissions << post
      end
      if account && post
        JiayangHr::AccountsRolesPostPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          post_permission_id:    post.id
        )
      end
    end

    def import_post_permissions_sql
      <<-SQL
        select id, otitle, dtitle from #{@table_space}eVW_PartOrg
      SQL
    end

    def import_post_permissions
      ActiveRecord::Base.transaction do
        @database.query(import_post_permissions_sql).each do |r|
          insert_post_permissions_line(r)
        end
      end
    end

    def insert_post_permissions_line(r)
      @post_permissions << JiayangHr::PostPermission.create(
        quarter_id:      @quarter_id,
        code:            r['id'],
        post_name:       r['otitle'],
        department_name: r['dtitle'],
        permission_type: '兼岗权限'
      )
    end

    #-------------------------roles_permissions--------------------------------------------
    def import_roles_permissions_sql
      <<-SQL
        select RMID, ZoneKey, ZoneID from #{@table_space}skySecRoleMemberAgent#{@sid_suffix}
      SQL
    end

    def import_roles_permissions
      ActiveRecord::Base.transaction do
        @database.query(import_roles_permissions_sql).each do |r|
          insert_roles_permissions_line(r)
        end
      end
    end

    def insert_roles_permissions_line(r)
      account_role = @accounts_roles.find { |x| x['ID'] == r['RMID'] }
      return if account_role.nil?

      account    = @accounts.find { |x| x.objid == account_role['URID'].to_s }
      role       = @roles.find { |x| x.code == account_role['RUID'].to_s }
      permission = @permissions.find { |x| x.code == r['ZoneID'].to_s }

      if account && role && permission
        a_r_p = @accounts_roles_permissions.find do |x|
          x.account_id == account.id && x.role_id == role.id && x.permission_id == permission.id
        end
        if a_r_p
          @accounts_roles_permissions << JiayangHr::AccountsRolesPermission.create(
            quarter_id:    @quarter_id,
            account_id:    account.id,
            role_id:       role.id,
            permission_id: permission.id
          )
        end
      end
    end

    # def add_permission_json
    #   {
    #     'EZID' => '人事',
    #     'AZID' => '考勤',
    #     'PZID' => '薪资'
    #   }
    # end

    def import_jobs_sql
      <<-EOF
        select title from #{@table_space}ojob#{@sid_suffix}
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.query(import_jobs_sql).each do |r|
        name = r['title']
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end
