module JiaoyiO<PERSON><PERSON>
  def self.table_name_prefix
    'jiaoyi_oracle_'
  end
end

class JiaoyiOracle::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class JiaoyiOracle::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class JiaoyiOracle::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class JiaoyiOracle::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class JiaoyiOracle::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class JiaoyiOracle::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class JiaoyiOracle::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class JiaoyiOracle::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class JiaoyiOracle::System < ActiveRecord::Base
  has_many :menus
end

class JiaoyiOracle::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class JiaoyiOracle::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class JiaoyiOracle::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class JiaoyiOracle::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class JiaoyiOracle::Temporary < ActiveRecord::Base; end

class JiaoyiOracleAccountsRoles < ActiveRecord::Base; end
class JiaoyiOracleMenusRoles < ActiveRecord::Base; end
class JiaoyiOracleMenuAdditionsRoles < ActiveRecord::Base; end

class JiaoyiOracle::TimeControl < ActiveRecord::Base; end