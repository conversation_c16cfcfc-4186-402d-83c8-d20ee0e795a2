# frozen_string_literal: true

module AasOracleImporter
  # 封装的各类型数据库客户端
  class DatabaseClient

    attr_reader :database_type, :tnsname, :logger, :database
    attr_accessor :source_encode

    def initialize(database_type, tnsname)
      @database_type = database_type
      @tnsname       = tnsname
      @logger        = AasOracleImporter.initialize_logger

      check_args

      begin
        initialize_driver
      rescue ActiveSupport::MessageVerifier::InvalidSignature => e
        puts '**************************'
        puts "You must convert database:#{@tnsname} password to ciphertext."
        puts '**************************'
        puts
        raise e
      end
    end

    protected

    def database_info
      info = AasOracleImporter.config['agent'][database_type][tnsname]
      return info if info.present?


      raise "NOT FOUND tnsname called \"#{tnsname}\""
    end

    def message_prefix
      "DatabaseInitialize #{tnsname}: "
    end

    def convert_encoding(row)
      return row unless source_encode

      row.map do |x|
        x.is_a?(String) ? x.force_encoding(source_encode).encode('utf-8', replace: nil) : x
      end
    end

    def check_args
      support_types = %w[oracle mysql csv xls sqlserver ldap txt fake http odbc sqlite dm oceanbase oceanbase_oracle kingbase pg jdbc jruby_jdbc]

      raise ArgumentError, "not support database_type: #{database_type}" unless support_types.include? database_type

      raise ArgumentError, 'tnsname cannot be nil' unless tnsname
    end

    class << self
      def new_database(database_type, tnsname)
        case database_type
        when 'oracle'
          OracleClient.new(database_type, tnsname)
        when 'mysql'
          MysqlClient.new(database_type, tnsname)
        when 'csv'
          CsvClient.new(database_type, tnsname)
        when 'sqlserver'
          SqlServerClient.new(database_type, tnsname)
        when 'ldap'
          LdapClient.new(database_type, tnsname)
        when 'txt'
          TxtClient.new(database_type, tnsname)
        when 'xlsx'
          # XlsClient.new(database_type, tnsname)
        when 'xls'
          XlsClient.new(database_type, tnsname)
        when 'fake'
          FakeClient.new(database_type, tnsname)
        when 'http'
          HttpClient.new(database_type, tnsname)
        when 'sqlite'
          SqliteClient.new(database_type, tnsname)
        when 'dm'
          DmClient.new(database_type, tnsname)
        when 'oceanbase'
          OceanbaseClient.new(database_type, tnsname)
        when 'oceanbase_oracle'
          OceanbaseOracleClient.new(database_type, tnsname)
        when 'kingbase'
          KingbaseClient.new(database_type, tnsname)
        when 'pg'
          PgClient.new(database_type, tnsname)
        when 'jdbc'
          JdbcClient.new(database_type, tnsname)
        when 'jruby_jdbc'
          JrubyJdbcClient.new(database_type, tnsname)
        else
          raise NoSupportDriver, "Not support database type #{database_type}"
        end
      end
    end

  end
end
