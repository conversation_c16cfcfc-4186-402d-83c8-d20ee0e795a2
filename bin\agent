#!/usr/bin/env jruby
# frozen_string_literal: true

begin
  require_relative '../lib/aas_oracle_importer'
  require_relative '../lib/init_customer_importer.rb'
  require 'optparse'
  options = {}

  OptionParser.new do |opts|
    opts.banner = "Usage: ./#{opts.program_name} action"

    # default action
    options[:action] = 'create_quarter'
    options[:skip_after_import_rake] = false
    options[:skip_non_system_after_import_rake] = false
    options[:only_import_user] = false

    opts.on('-c', '--create', 'create quarter and import') do
      options[:action] = 'create_quarter'
    end

    opts.on('-n', '--newest', 'import data into the newest quarter') do
      options[:action] = 'newest_quarter'
    end

    opts.on('-l', '--list', 'list quarters') do
      options[:action] = 'list_quarters'
    end

    opts.on('-s QUARTER_ID', '--specify QUARTER_ID', 'import data to the specify quarter') do |value|
      options[:action] = 'specify_quarter'
      options[:quarter_id] = value
    end

    opts.on('-S systems', '--systems system1,system2', Array, 'import data from systems, WARNING: CANNOT exist SPACE in names, --systems、--system_ids、--only_import_user can only choose one') do |value|
      options[:systems] = value
    end

    opts.on('-I systems_ids', '--system_ids 31,3', Array, 'import data from system_ids, WARNING: CANNOT exist SPACE in system_ids, --systems、--system_ids、--only_import_user can only choose one') do |value|
      options[:system_ids] = value
    end

    opts.on('-r', '--skip_non_system_after_import_rake', 'skip after non system import rake') do |value|
      options[:skip_non_system_after_import_rake] = true
    end

    opts.on('-a', '--skip_after_import_rake', 'skip after import rake') do |value|
      options[:skip_after_import_rake] = true
    end

    opts.on('-i', '--only_import_user', 'only import user, --systems、--system_ids、--only_import_user can only choose one') do |value|
      options[:only_import_user] = true
    end

    opts.on('-f', '--is_force', 'force running，ignore import_quarter_key') do |value|
      options[:is_force] = true
    end
  end.parse!

  params = options.slice(:quarter_id, :systems, :system_ids, :skip_after_import_rake, :skip_non_system_after_import_rake, :only_import_user, :is_force)
  agent = AasOracleImporter::Agent.new(params)

  case options[:action]

  when 'create_quarter'
    agent.run
  when 'newest_quarter'
    agent.run_newest
  when 'list_quarters'
    agent.list_quarters
  when 'specify_quarter'
    agent.run_specify
  end
rescue Exception => e
  puts "加载aas_oracle_importer时出错: #{e.message}"
  puts e.backtrace.join("\n")
  # 可以选择写入日志文件
  File.open('error_log.txt', 'w') do |f|
    f.puts "错误时间: #{Time.now}"
    f.puts "错误消息: #{e.message}"
    f.puts "错误位置:"
    f.puts e.backtrace.join("\n")
  end
  raise
end
