module AasOracleImporter

  class ZhiyuanOaImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @table_space    = importer_config['table_space']
      @import_account = importer_config['import_account']
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      if @import_account
        ZhiyuanOa::Account.where(quarter_id: @quarter_id).destroy_all
        ZhiyuanOa::Role.where(quarter_id: @quarter_id).destroy_all
        ZhiyuanOa::Menu.where(quarter_id: @quarter_id).destroy_all
        import_accounts
        import_ledgers(ZhiyuanOa::Account)
        # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
        return unless Job.table_exists?

        import_jobs
        import_job_users
      else
        import_users
      end
      
    end

    def the_system
      if @bs_id && @import_account
        BusinessSystem.find(@bs_id)
      else
        BusinessSystem.new(name: 'HR 系统')
      end
    end

    def import_users
      sql = <<-SQL
        select m.code, m.name, m.IS_ENABLE, d.id, d.name
        from #{@table_space}org_member m, #{@table_space}org_unit d
        where m.org_department_id=d.id
      SQL

      @database.exec(sql) do |r|
        ActiveRecord::Base.transaction do
          user_code, user_name, status, d_code, d_name = r
          if user_code && user_name
            d = Department.find_or_create_by(code: d_code)
            d.update(inservice: true, name: d_name)

            u = User.find_or_create_by(code: user_code)
            u.update(name: user_name, inservice: status.to_i == 1, department_id: d.id)
          end
        end
      end
    end

    def import_accounts

      sql = <<-SQL
        SELECT
               m.id              "人员id（主键）",
               p1.login_name     "登录名",
               m.name            "姓名",
               m.IS_ENABLE       "人员在职状态",
               p2.name            "主岗名称",
               d.id                "部门 id",
               d.name              "单位/部门",
               r.id                "角色id",
               r.name              "角色名称",
               pm.id               "角色菜单id",
               pm.name             "角色菜单",
               pm.ext7             "角色菜单名称",
               (select p_m.name from priv_menu p_m where p_m.path=substr(pm.path,1,length(pm.path)-3)) "上级菜单",
               (select p_m.id   from priv_menu p_m where p_m.path=substr(pm.path,1,length(pm.path)-3)) "上级菜单id"
        FROM
               #{@table_space}org_member m,
               #{@table_space}org_principal p1,
               #{@table_space}org_unit d,
               #{@table_space}org_post p2,
               #{@table_space}org_relationship rs,
               #{@table_space}priv_role_menu rm,
               #{@table_space}org_role r,
               #{@table_space}priv_menu pm
        WHERE
               m.id=p1.member_id
               AND m.org_department_id=d.id
               AND m.org_post_id=p2.id
               AND m.id=rs.source_id
               AND rs.objective1_id=rm.roleid
               AND r.id=rm.roleid
               AND pm.id=rm.menuid
               AND rs.type='Member_Role'
        ORDER BY d.name,m.name,r.name,pm.id
      SQL

      @database.exec(sql) do |r|
        ActiveRecord::Base.transaction do
          user_code, ad_name, user_name, status, position, d_code, d_name, role_code, role_name, menu_code, not_need1, menu_name, not_need2, parent_code = r

          department = find_or_create_department(d_code, d_name)
          user       = find_or_create_user(user_code, ad_name, user_name, status, position, department)

          next unless user
          next unless department

          account    = find_or_create_account(user)
          role       = find_or_create_role(role_code, role_name)
          menu       = find_or_create_menu(menu_code, menu_name, parent_code)

          next unless account
          next unless role
          next unless menu

          link_account_and_role(account, role)
          link_role_and_menu(role, menu)
        end
      end

    end

    def find_or_create_department(code, name)
      return nil if name.nil? or name == ''
      return nil if code.nil? or code == ''

      d = Department.find_by(code: code, name: name)
      if d
        d.update(inservice: true, d_type: 2, level: 2)
      else
        d = Department.create(code: code, name: name, inservice: true, d_type: 2, level: 2)
      end
      d
    end

    def find_or_create_user(code, ad_name, name, status, position, department)
      return nil if name.nil? or name == ''
      return nil if code.nil? or code == ''

      user = User.find_by(code: code)
      if user
        user.update(
          name: name,
          ad_name: ad_name,
          inservice: status,
          position: position,
          department_id: department&.id,
        )
      else
        user =
          User.create(
            code: code,
            name: name,
            ad_name: ad_name,
            inservice: status,
            position: position,
            department_id: department&.id
          )
      end
      @position_users << user if user.position?
      user
    end

    def find_or_create_account(user)

      account = ZhiyuanOa::Account.find_by(code: user.code, quarter_id: @quarter_id)
      if account
        account.update(name: user.name, status: user.inservice, user_id: user.id)
      else
        account =
          ZhiyuanOa::Account.create(
            code: user.code,
            name: user.name,
            status: user.inservice,
            user_id: user.id,
            quarter_id: @quarter_id
          )
      end
      account
    end

    def find_or_create_role(role_code, role_name)
      return nil if role_code.nil? or role_code == ''

      if role_name.nil? or role_name == ''
        role_name = 'null'
      end

      role = ZhiyuanOa::Role.find_by(code: role_code, quarter_id: @quarter_id)
      if role
        role.update(name: role_name)
      else
        role =
          ZhiyuanOa::Role.create(
            code: role_code,
            name: role_name,
            quarter_id: @quarter_id
          )
      end
      role
    end

    def find_or_create_menu(menu_code, menu_name, parent_code)
      return nil if menu_code.nil? or menu_code == ''

      if menu_name.nil? or menu_name == ''
        menu_name = 'null'
      end

      menu        = ZhiyuanOa::Menu.find_by(code: menu_code, quarter_id: @quarter_id)
      parent_menu = ZhiyuanOa::Menu.find_by(code: parent_code, quarter_id: @quarter_id)

      unless parent_menu
        @logger.warn "#{self.class}: not found parent_menu code '#{parent_code}'"
      end

      if menu
        menu.update(name: menu_name, parent_id: parent_menu&.id)
      else
        menu =
          ZhiyuanOa::Menu.create(
            code: menu_code,
            name: menu_name,
            parent_id: parent_menu&.id,
            quarter_id: @quarter_id
          )
      end
      menu
    end

    def link_account_and_role(account, role)
      account.roles << role unless account.roles.exists? role.id
    end

    def link_role_and_menu(role, menu)
      role.menus << menu unless role.menus.exists? menu.id
    end

    def import_jobs_sql
      <<-EOF
        SELECT name FROM #{@table_space}org_post
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end
