module AasOracleImporter

  class XbrlXinpiImporter < ImporterBase

    def config
      @bs_id = 10057
      @accounts = []
      @roles = []
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      
      
      @data3_permissions = []
      @data3_accounts_roles_permissions = []
      
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      
      
      import_data1_permissions
      
      import_data1_role_permissions
      
      
      
      
      import_data2_permissions
      
      
      import_data2_account_permissions
      
      
      
      import_data3_permissions
      
      
      import_data3_account_permissions
      
      
      import_ledgers(XbrlXinpi::Account)
    end

    def destroy_exist_datas
      accounts = XbrlXinpi::Account.where(quarter_id: @quarter_id)
      XbrlXinpi::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      XbrlXinpi::Role.where(quarter_id: @quarter_id).delete_all
      
      
      XbrlXinpi::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      XbrlXinpi::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
      
      
      
      XbrlXinpi::Data2Permission.where(quarter_id: @quarter_id).delete_all
      
      
      
      XbrlXinpi::Data3Permission.where(quarter_id: @quarter_id).delete_all
      
      
    end

    
    def import_accounts_sql
      <<-EOF
        select a.pk_id, a.pk_id, a.real_name, case a.user_status when '1' then 1 else 0 end from xbrlnew.x27_user a
      EOF
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << XbrlXinpi::Account.create(
            quarter_id: @quarter_id,
            
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            
            
            code: get_enum(enums, "code", r[1]),
            
            
            
            name: get_enum(enums, "name", r[2]),
            
            
            
            status: get_enum(enums, "status", r[3])&.to_s == '1.0',
            
            
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select role_id, role_id, role_name from xbrlnew.x27_roles
      EOF
    end
    

    def import_roles
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << XbrlXinpi::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[1]), name: get_enum(enums, "name", r[2]))
        end
      end
      
    end

    
    def import_accounts_roles_sql
      <<-EOF
        select c.role_id, b.pk_id
from xbrlnew.x27_join_user_role a, 
     xbrlnew.x27_user b, 
     xbrlnew.x27_roles c 
where    a.fk_user_id = b.pk_id 
and      a.fk_role_id = c.role_id 
      EOF
    end
    

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            XbrlXinpi::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
      
    end

    
    
    
    
    def import_data1_permissions_sql
      <<-EOF
        select privil_id, privil_id, privil_name from xbrlnew.x27_privilege
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])
          
          level1_name = replace_blank_name(name)
          
          json = {
            quarter_id: @quarter_id,
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            code: get_enum(enums, "code", r[1]),
            
            level1_name: level1_name
            
          }
          @data1_permissions << XbrlXinpi::Data1Permission.create(json)
        end
      end
      
    end
    
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        select b.role_id, c.privil_id from xbrlnew.x27_join_role_privilege a, xbrlnew.x27_roles b, xbrlnew.x27_privilege c where a.fk_privil_id = c.privil_id and a.fk_role_id = b.role_id
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        data = select_db_datas(import_data1_role_permissions_sql).uniq
        data.each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            XbrlXinpi::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end
    

    
    
    
    
    
    
    def import_data2_permissions_sql
      <<-EOF
        select b.id, b.id, b.postname
from xbrlnew.post_template a, 
     xbrlnew.postmanagement b,
     xbrlnew.taxonomy_conf c
where a.post_id = b.id
  and a.taxonomy_conf_id = c.id

      EOF
    end

    def import_data2_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = nil
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])
          
          level1_name = replace_blank_name(name)
          
          json = {
            quarter_id: @quarter_id,
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            code: get_enum(enums, "code", r[1]),
            
            level1_name: level1_name
            
          }
          @data2_permissions << XbrlXinpi::Data2Permission.create(json)
        end
      end
      
    end
    
    

    
    
    
    def import_data2_account_permissions_sql
      <<-EOF
        select distinct b.pk_id, d.id
from xbrlnew.x27_user b, 
     xbrlnew.product c,
     xbrlnew.postmanagement d,
     xbrlnew.join_user_pro_post a
where a.user_id = b.pk_id
and   c.ids = a.product_id
and   d.id = a.postmanagement_id
      EOF
    end

    def import_data2_account_permissions
      enums = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data2_permissions.find{|x| x.source_id.to_s == r[1].to_s}
        if account && permission
          XbrlXinpi::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data2_permission_id: permission.id)
        end
      end
    end
    
    
    
    
    
    def import_data3_permissions_sql
      <<-EOF
        select b.id, b.id, b.postname
from xbrlnew.post_template a, 
     xbrlnew.postmanagement b,
     xbrlnew.taxonomy_conf c
where a.post_id = b.id
  and a.taxonomy_conf_id = c.id
      EOF
    end

    def import_data3_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = nil
        select_db_datas(import_data3_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])
          
          level1_name = replace_blank_name(name)
          
          json = {
            quarter_id: @quarter_id,
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            code: get_enum(enums, "code", r[1]),
            
            level1_name: level1_name
            
          }
          @data3_permissions << XbrlXinpi::Data3Permission.create(json)
        end
      end
      
    end
    
    

    
    
    
    def import_data3_account_permissions_sql
      <<-EOF
        select b.pk_id, c.id from (
	select * 
	from xbrlnew.join_user_pro_post t 
	where t.postmanagement_id in (
		select ID 
		from xbrlnew.postmanagement 
		where type = '8080-0000' 
		and not id like 'ziPing%' 
	)
) a, xbrlnew.x27_user b, xbrlnew.postmanagement c
where a.user_id = b.pk_id 
and a.postmanagement_id = c.id
      EOF
    end

    def import_data3_account_permissions
      enums = []
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data3_permissions.find{|x| x.source_id.to_s == r[1].to_s}
        if account && permission
          XbrlXinpi::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id)
        end
      end
    end
    
    

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
