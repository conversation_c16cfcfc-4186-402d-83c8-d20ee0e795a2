module YssQingsuan
  def self.table_name_prefix
    'yss_qingsuan_'
  end
end

class YssQingsuan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class YssQingsuan::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission
  validates :permission_id, presence: true
  validates :quarter_id, presence: true
end

class YssQingsuan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end


class YssQingsuan::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :permission_type, presence: true 
  validates :quarter_id, presence: true 
end

