require 'httparty'

module AasOracleImporter
  class ZxzqUsersImporter < ImporterBase
    include HTT<PERSON><PERSON>y

    def config
      @bs_id          = importer_config['bs_id']
      @table_space    = importer_config['table_space']
      @sid_suffix     = importer_config['sid_suffix']
      @base_url       = importer_config['base_uri']
      @app_key        = importer_config['app_key']
      @invoke_key     = importer_config['invoke_key']
      @jobs = []
      @departments = []
      @users = []
      @old_departments = Department.where.not(code: %w[public disabled]).to_a
    end

    def import_to_do
#       get_users(1000) { |x| puts x }

      create_or_update_departments
      import_hr
#      create_or_update_users
      link_manager
      update_user_name_clear
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    HrData =
      Struct.new(:department_code, :user_id, :department,
                 :user_code, :user_name, :email, :cellphone,
                 :position, :status_string, :manager_id, :job_code, :updated_at) do
        # 0 正常、1 未启用、2 停用、3 锁定、4 失效、5 未知，这里的锁定是输入密码错误触发的，所以算正常
        def inservice
          [0, 3].include? status_string.to_i
        end

        def present?
          user_code.present? && user_name.present?
        end
      end

    def import_hr
      @data = []
      @limit_users = []
      get_users(500) do |user|
        user_struct = create_user_struct(user)
        @data << user_struct
        @limit_users << user_struct
        if @limit_users.size == 1000
          create_or_update_users
          @limit_users = []
        end
      end
      create_or_update_users
    end

    def create_user_struct(row)
      duty = row['userDuty'] || ''
      job_code, position = duty.split('-').map(&:strip)
      user                 = HrData.new
      user.department_code = row['userDepart']
      user.user_id         = row['userId']
      user.user_code       = row['userUid']
      user.user_name       = row['userName'].gsub!(/\d/, '')
      user.email           = row['userEmail']
      user.cellphone       = row['userPreferredMobile']
      user.updated_at      = row['updateTime']&.to_time
      user.job_code        = job_code
      user.position        = position
      user.status_string   = row['userStatus']
      department  = @departments.find { |x| x.code == user.department_code }
      user.department = department
      # 最后返回结构体
      user
    end

    def create_or_update_departments
      old_codes       = @old_departments.map(&:code)
      department_data = []

      get_departments do |dept|
        id = dept['orgId']
        name = dept['orgName']
        parent_id = dept['orgParentId']
        code = dept['orgCode']
        status = dept['orgStatus']

        next unless status.to_s == '0'

        department_data << [id, name, parent_id, code]
      end
      new_codes = department_data.map { |x| x[3] }

      # 新增的 inservice 为 true
        (new_codes - old_codes).each do |code|
          row = department_data.find { |x| x[3] == code }
          Department.create(
            code:      row[3],
            name:      row[1],
            obj_id:    row[0],
            inservice: true
          )
        end

      # 没有查到的部门注销
        (old_codes - new_codes).each do |code|
          department = @old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end

      # 现有的可能名称有变动，注意更新状态为存在
        (old_codes & new_codes).each do |code|
          department = @old_departments.find { |x| x.code == code }
          row        = department_data.find { |x| x[3] == code }
          department.update(name: row[1], inservice: true, obj_id: row[0])
        end

      Department.all.each do |x|
        # 获取当前部门的row记录
        row = department_data.find { |r| r[3] == x.code }
        # 如果当前row记录的parent_code不会空

        next unless !row.nil? && !row[2].to_s.empty?

        parent_id = row[2]
        p = Department.find_by(obj_id: parent_id)
        next if p.nil? || x.parent_id == p.id

        x.update_column(:parent_id, p.id)
      end

      # 更新层级
      root_departments = Department.where(parent_id: nil)
      root_departments.update_all(level: 1)
      root_departments.each do |department|
        update_departments_level(department.children, 2)
      end

      @departments = Department.all.reload.to_a
    end

    def update_departments_level(departments, level)
      departments.update_all(level: level)
      level += 1
      departments.each do |department|
        children_departments = department.children
        next if children_departments.empty?

        update_departments_level(children_departments, level)
      end
    end

    def create_or_update_users
      old_users    = User.all.to_a
      old_codes    = User.pluck(:code)
      new_codes    = @limit_users.map(&:user_code)
      data = []

      # 新增
        (new_codes - old_codes).each do |code|
          user_struct = @limit_users.find { |x| x.user_code == code }

          next unless user_struct.present?

          data << user_struct
        end

        User.bulk_insert(:code, :name, :position, :email, :cellphone, :inservice, :department_id, :import_updated_at, :created_at, :updated_at) do |obj|
          obj.set_size = 200
          data.uniq.each do |x|
            obj.add [x.user_code, x.user_name, x.position, x.email, x.cellphone, x.inservice, x.department&.id, x.updated_at, Time.now, Time.now]
          end
        end

      # 已有的更新
        old_users.each do |user|
          user_struct = @limit_users.find { |x| x.user_code == user.code }
          next unless user_struct&.present?

          department = @departments.find { |x| x.code == user_struct.department_code }

          user.update(
            name:          user_struct.user_name,
            position:      user_struct.position,
            email:         user_struct.email,
            cellphone:     user_struct.cellphone,
            inservice:     user_struct.inservice,
            department_id: department&.id,
            import_updated_at: user_struct.updated_at
          )
        end

      @users = User.all
    end

    def link_manager
      all_users = User.all.reload.to_a

      ActiveRecord::Base.transaction do
        @data.each do |struct|
          next unless struct.manager_id

          user = all_users.find { |x| x.code == struct.user_code }
          next unless user

          manager_s = @data.find { |x| x.user_id == struct.manager_id }
          next unless manager_s

          manager = all_users.find { |x| x.code == manager_s.user_code }
          user.update(manager_id: manager&.id)
        end
      end
    end

    def update_user_name_clear
      return if @clear_name_regexp.blank?

      regexp    = Regexp.new(@clear_name_regexp)
      all_users = User.all.reload.to_a
      ActiveRecord::Base.transaction do
        all_users.each do |user|
          clear_name = user.name.gsub(regexp, '')
          user.update(name: clear_name)
        end
      end
    end

    # 导入岗位
    # 岗位数据存在员工数据里，员工数据是增量获取，所以不能通过对比来删除岗位
    def import_jobs
      # @before_job_codes = Job.all.pluck(:code)
      job_data = @data.map { |x| [x.job_code, x.position] }.uniq { |x| x&.[](0) }
      create_jobs(job_data)
      # delete_job_codes = @before_job_codes - @jobs.pluck(:code)
      # 找出被删除的岗位
      # Job.where(code: delete_job_codes).update_all(inservice: false)
    end

    def create_jobs(job_data)
      job_data.each do |x|
        find_or_create_job(x)
      end
    end

    def find_or_create_job(x)
      code = x[0]
      name = x[1]
      return nil if name.nil? || name == '' || code.nil? || code == ''

      job = Job.find_or_create_by(code: code)
      job.update(name: name, inservice: true)
    end

    def import_job_users
      data = []
      # 当前导入员工岗位信息
      @data.each do |x|
        data << [x.job_code, x.user_code]
      end
      # 用于判断是否存在，一次性搜索出全部，避免n+1
      # 数据库员工岗位信息
      job_user_codes = JobUser.includes(:job, :user).map { |o| [o.job.code, o.user.code] }
      import_job_user_lines(data, job_user_codes)
    end

    def import_job_user_lines(data, job_user_codes)
      # 获取当前导入员工编码
      user_codes = data.map { |x| x[1] }.uniq.compact
      user_codes.each do |user_code|
        job_codes = []
        # 获取员工相关岗位信息
        user_job_data = data.select { |o| o[1] == user_code }
        user_job_data.each do |row|
          job_codes << row[0]
        end
        set_job_user_line(user_code, job_codes, job_user_codes) if user_code.present? && job_codes.present?
      end
    end

    def set_job_user_line(user_code, job_codes, job_user_codes)
      user = @users.find { |x| x.code == user_code }
      return unless user

      before_job_user_codes = job_user_codes.select { |o| o.last == user_code }
      before_job_codes      = before_job_user_codes.map(&:first)
      add_job_codes         = job_codes - before_job_codes
      delete_job_codes      = before_job_codes - job_codes

      # 如果用户岗位已经记录，则跳过
      return if job_codes == before_job_codes

      params = {
        user_id:          user.id,
        quarter_id:       @quarter_id,
        log_time:         Time.now,
        log_type:         'message',
        operation:        'change',
        operation_target: '岗位'
      }
      delete_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code.to_s }
        next unless job

        JobUser.find_by(user_id: user.id, job_id: job.id)&.delete
        # 去掉岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位去掉「#{job.name}」"))
      end
      add_job_codes.each do |job_code|
        job = @jobs.find { |x| x.code == job_code.to_s }
        next unless job

        JobUser.create(user_id: user.id, job_id: job.id)
        # 新增岗位
        UserLog.create(params.merge(name: job.name, content: "员工岗位新增「#{job.name}」"))
      end
    end

    # def import_positions
    #   user_ids = JobUser.pluck(:user_id).uniq
    #   users = User.where(id: user_ids)
    #   job_users = JobUser.includes(:job, :user)
    #   users.each do |user|
    #     jobs = job_users.select { |job_user| job_user.user_id == user.id }.map(&:job)
    #     position = jobs.empty? ? nil : jobs.pluck(:name).join('、')
    #     user.update_column(:position, position) if user.position != position
    #   end
    # end

    def get_users(per_page = 100)
      api_name = 'getUsers'
      start_at = get_start_at(api_name)
      page = 1
      loop do
        result = send_request(api_name, start_at, page, per_page)
        quantity = (page-1)*per_page + result.count
        puts "已获取员工数: #{quantity}"
        result.each do |x|
          yield x
        end
        page += 1
        break if result.size.zero?
      end
    end

    def get_departments
      api_name = 'getOrganization'
      start_at = get_start_at(api_name)
      page = 1
      loop do
        result = send_request(api_name, start_at, page)
        result.each do |x|
          yield x
        end
        page += 1
        break if result.size.zero?
      end
    end

    # 注意: 搜索updateTime和queryTime之间的数据
    # maxSize 最多设置20条，超过无效
    def send_request(api_name, start_at, page = 1, per_page = 100)
      body = <<EOF
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <ns2:#{api_name} xmlns:ns2="http://service.interfaceService.xlkh.com/">
      <arg0>#{@app_key}</arg0>
      <arg1>#{@invoke_key}</arg1>
      <arg2>#{start_at}</arg2>
      <arg3>#{per_page}</arg3>
      <arg4>#{Time.now.localtime.to_s(:db)}.000</arg4>
      <arg5>#{page}</arg5>
    </ns2:#{api_name}>
  </soap:Body>
</soap:Envelope>
EOF
      @logger.info("请求#{@base_url}, body: #{body}")
      response = self.class.post(@base_url, { body: body, headers: headers, verify: false })
      begin
        result = response['Envelope']['Body']

        if result.keys.include?('Fault')
          @logger.error "#{@method} 接口响应错误"
          @logger.error result
          return
        end
        return [] if result["#{api_name}Response"].nil?
        result["#{api_name}Response"][api_name]
      rescue => e
        @logger.error "#{@method} 接口错误，#{e.message}"
        @logger.error e.backtrace.join("\n")
      end
    end

    def headers
      { 'Content-Type' => 'application/x-www-form-urlencoded', 'charset' => 'utf-8' }
    end

    # 动态获取api起始时间
    # 员工是增量获取，所以员工获取最新更新时间
    def get_start_at(api_name)
      default_time = '1970-01-01 00:00:00.000'
      return default_time unless api_name == 'getUsers'

      last_user = User.order('import_updated_at DESC').first
      last_at = last_user&.import_updated_at&.localtime&.to_s(:db)
      last_at.nil? ? default_time : "#{last_at}.000"
    end
  end
end
