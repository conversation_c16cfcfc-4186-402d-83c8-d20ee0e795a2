# frozen_string_literal: true

module <PERSON>y<PERSON>ou<PERSON>
  def self.table_name_prefix
    'xy_toubao_'
  end
end

class XyToubao::Account < ActiveRecord::Base; end
class XyToubao::Menu    < ActiveRecord::Base; end
class XyToubao::Role    < ActiveRecord::Base; end
class XyToubao::Department < ActiveRecord::Base; end
class XyToubao::AccountsRoles < ActiveRecord::Base; end
class XyToubao::MenusRoles    < ActiveRecord::Base; end
class XyToubao::AccountsMenus < ActiveRecord::Base; end