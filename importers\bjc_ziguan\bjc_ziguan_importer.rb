module AasOracleImporter
  class BjcZiguanImporter < ImporterBase
    def config
      @bs_id       = 288
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @account_data = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    # 这里的账号表其实是账号角色关联表，去重后获取账号表
    def initialize_tables
      @table_account      = "#{@table_space}t_user_role_relation#{@sid_suffix}"
      @table_role         = "#{@table_space}t_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}t_user_role_relation#{@sid_suffix}"
      @table_menu         = "#{@table_space}t_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}t_role_menu_relation#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions

      import_ledgers(BjcZiguan::Account)
    end

    def destroy_exist_datas
      accounts = BjcZiguan::Account.where(quarter_id: @quarter_id)
      BjcZiguan::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      BjcZiguan::Role.where(quarter_id: @quarter_id).delete_all
      BjcZiguan::Data1Permission.where(quarter_id: @quarter_id).delete_all
      BjcZiguan::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select spell_name, user_name, del_flag from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @account_data << r
        end

        account_data = @account_data.uniq { |x| x[0] }
        account_data.each do |x|
          # 只要有任何一条存在的数据就是有效的
          status = @account_data.select { |a| a[0] == x[0] }.map { |a| a[2] }.uniq.include?('1')
          @accounts << BjcZiguan::Account.create(
            quarter_id: @quarter_id,
            source_id:  x[0],
            code:       x[0],
            name:       x[1],
            status:     status
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_id, role_id, role_name from #{@table_role} where status = 1 and del_flag = 1
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << BjcZiguan::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, spell_name from #{@table_account_role} where del_flag = 1
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          BjcZiguan::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select menu_id, menu_id, menu_name, menu_type from #{@table_menu} where del_flag = 1 and status = 1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])
          level2_name = get_category_name(r[3])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: level2_name
          }
          @data1_permissions << BjcZiguan::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, menu_id from #{@table_role_menu} where del_flag = 1
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          BjcZiguan::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def get_category_name(code)
      case code.to_s
      when '1' then '目录'
      when '2' then '菜单'
      when '3' then '按钮'
      end
    end
  end
end
