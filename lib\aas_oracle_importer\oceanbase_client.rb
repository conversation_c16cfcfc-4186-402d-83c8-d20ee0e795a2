# frozen_string_literal: true
module AasOracleImporter
  # Ocenbase 客户端
  class OceanbaseClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.query(sql).each do |row|
          yield row
        end
      else
        @database.query(sql)
      end
    end

    def query(sql)
      @database.query(sql)
    end

    private

    def initialize_driver
      load_driver_gem
      @database = ActiveRecord::Base.oceanbase_connection oceanbase_client_params
    rescue OceanbaseAdapter::Error => e
      raise OceanbaseAdapter::Error, message_prefix + e.message
    end

    def oceanbase_client_params
      oceanbase_client_config.update({password: ConvertTools::Cryptology.decrypt_if_env(oceanbase_client_config[:password])})
    end

    def oceanbase_client_config
      {
        host:     database_info['db_host'],
        port:     database_info['db_port'] || 2881,
        database: database_info['db_name'],
        username: database_info['db_user'],
        password: database_info['db_pass']
      }
    end

    def load_driver_gem
      require 'oceanbase_adapter'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'oceanbase_adapter\'.')
      exit(-127)
    end
  end
end
