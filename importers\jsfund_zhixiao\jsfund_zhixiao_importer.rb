module AasOracleImporter

  class JsfundZhixiaoImporter < ImporterBase

    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_roles          = importer_config['table_roles']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus          = importer_config['table_menus']
      @table_menus_roles    = importer_config['table_menus_roles']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_menus_roles
      import_accounts_roles
      import_ledgers(JsfundZhixiao::Account)
    end

    def import_accounts

      JsfundZhixiao::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT C_USERCODE, C_USERNAME, C_STATUS FROM #{@table_accounts}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            JsfundZhixiao::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1].strip,
              status: r[2].to_i == 0 ? true : false
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      JsfundZhixiao::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT L_GROUPID, C_GROUPNAME FROM #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JsfundZhixiao::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus

      JsfundZhixiao::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT C_RIGHTCODE, C_RIGHTNAME, C_SYSNAME FROM #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          JsfundZhixiao::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1],
            sub_system: r[2]
            )
        end
      end
    end

    def import_menus_roles

      sql = <<-EOF
        SELECT L_GROUPID, C_RIGHTCODE from #{@table_menus_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = JsfundZhixiao::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = JsfundZhixiao::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            JsfundZhixiao::MenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        SELECT
          a. C_usercode,
          b.L_GROUPID
        from
          #{@table_accounts} a,
          #{@table_accounts_roles} b
        where
         a.L_USERID = b.L_USERID
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = JsfundZhixiao::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = JsfundZhixiao::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            JsfundZhixiao::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



