module AasOracleImporter
  class XyZhifuImporter < ImporterBase
    def config
      @bs_id       = 286
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @db_type     = importer_config['db_type']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}csl_user#{@sid_suffix}"
      @table_role         = "#{@table_space}csl_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}csl_user_role_rela#{@sid_suffix}"
      @table_menu         = "#{@table_space}csl_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}csl_role_menu_rela#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_super_role_permissions

      import_ledgers(XyZhifu::Account)
    end

    def destroy_exist_datas
      accounts = XyZhifu::Account.where(quarter_id: @quarter_id)
      XyZhifu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      XyZhifu::Role.where(quarter_id: @quarter_id).delete_all
      XyZhifu::Data1Permission.where(quarter_id: @quarter_id).delete_all
      XyZhifu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select USER_ID, USER_CODE, USER_NAME, STATUS from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << XyZhifu::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '1'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select ROLE_ID, ROLE_ID, ROLE_NAME, ROLE_TYPE from #{@table_role} where STATUS=1
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << XyZhifu::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            role_type:  get_role_type(r[3])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLE_ID, USER_ID from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          XyZhifu::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select MENU_CODE, MENU_CODE, MENU_NAME, PARENT_CODE from #{@table_menu} where STATUS = 1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          parent_id_value = r[3]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, r[2], parent_id_value, 2, 3))

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << XyZhifu::Data1Permission.create(json)
        end
      end
    end

    # 普通管理员根据关联关系导入权限
    def import_data1_role_permissions_sql
      <<-EOF
        select m.ROLE_ID, m.MENU_CODE from #{@table_role_menu} m, #{@table_role} r where m.role_id = r.role_id and r.ROLE_TYPE != '0'
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          XyZhifu::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    # 超级管理员导入全部权限
    def import_data1_super_role_permissions_sql
      <<-EOF
        select ROLE_ID, ROLE_NAME from #{@table_role} where STATUS=1 and ROLE_TYPE='0'
      EOF
    end

    def import_data1_super_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_super_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          next unless role

          XyZhifu::Data1AccountsRolesPermission.bulk_insert(:quarter_id, :role_id, :data1_permission_id) do |obj|
            obj.set_size = 1000
            @data1_permissions.each do |x|
              obj.add [@quarter_id, role.id, x.id]
            end
          end
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      limit = "#{@db_type.to_s == 'oracle' ? 'and rownum = 1' : 'limit 1'}"
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' #{limit}"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 角色类型
    def get_role_type(code)
      case code.to_s
      when '0' then '超级角色'
      when '1' then '普通角色'
      end
    end
  end
end
