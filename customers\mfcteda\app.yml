# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'mfcteda'
  customer: '泰达宏利'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  workday:
    enable: true
    # 泰达直接配在 crontab 中了，这里改为 false
    mode: 'mfcteda'
    config:
      tnsname: bidb
  quarter_format: previous_quarter
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    skip_after_import_rake: false
    after_importer_rake: 'after_import:manual_import_remind_to_admin'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_mfcteda_development
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
      host: 127.0.0.1
  agent:
    oracle:
      # 数据库配置 要改
      # 数据中心系统数据库
      bidb:
        # in tns name
        db_name: 'EDIC12C'
        db_user: 'privaudit'
        db_pass: 'priaudit2020!!'

  importers:

    # 分 TA 系统
    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_sa_'
      sid_suffix: ''
      encode: gbk

    # 直销系统
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_ds_'
      sid_suffix: ''
      sub_system: "CENTER"
      encode: gbk

    # 直销系统
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_ds_'
      sid_suffix: ''
      sub_system: "COUNTER"
      encode: gbk

    # 直销系统
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_ds_'
      sid_suffix: ''
      sub_system: "REMOTE"
      encode: gbk

    # 登记过户 4.0
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_ta_'
      sid_suffix: ''
      encode: gbk

    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_trade_'
      sid_suffix: ''
      days_of_data: 365
      temporary: true
      # 导入员工号
      operator_no_importer: true
      # 交易类型
      trade_type_importer: true
      # 站点权限
      station_importer: true
      encode: gbk
      display_status: true

    # 信披系统
    - name: fund_disclosure_yss_oracle_mfcteda
      bs_id: 32
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_'
      sid_suffix: ''

    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_fa_'
      # 这个是由于数据中心表名配置错了
      post_table_name: t_s_user_post_dat
      # 这里是泰达在发现估值表账套表名称不准，需要到 t_p_ab_port 中获取账套名称
      sync_booksets_table: true
      sid_suffix: ''
      encode: gbk

    - name: yss_qingsuan
      bs_id: 22
      db_type: oracle
      tnsname: bidb
      table_space: 'ods.qxjh_qs_'
      sid_suffix: ''
      import_bookset: false
      encode: gbk

    # 恒生反洗钱系统
    - name: mfcteda_fxq
      bs_id: 311
      db_type: oracle
      tnsname: bidb
      table_space: ods.qxjh_aml_
      sid_suffix: ''
    # 客服系统
    - name: mfcteda_kefu
      bs_id: 310
      db_type: oracle
      tnsname: bidb
      table_space: ods.qxjh_ccp_
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:manual_import_remind_to_admin'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_mfcteda_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


