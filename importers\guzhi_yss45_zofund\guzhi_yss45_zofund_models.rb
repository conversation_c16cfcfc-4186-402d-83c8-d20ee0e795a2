class Guzhi45::Account < ActiveRecord::Base
  has_many :bookset_relations
  has_many :roles,    -> { distinct }, through: :bookset_relations
  has_many :booksets, -> { distinct }, through: :bookset_relations
end

class Guzhi45::Bookset < ActiveRecord::Base; end

class Guzhi45::BooksetRelation < ActiveRecord::Base
  belongs_to :account
  belongs_to :role
  belongs_to :bookset
end

class Guzhi45::Role < ActiveRecord::Base; end