# frozen_string_literal: true

module AasOracleImporter
  # 信披系统导入，mysql 的，没有基金代码对照
  class FundDisclosureImporter < ImporterBase
    include ConvertTools
    def config
      @bs_id        = importer_config['bs_id']
      @email_suffix = importer_config['email_suffix']
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_ledgers(FundDisclosure::Account)
      # 下列方法在 import_ledgers 之后进行
      update_accounts_status
    end

    def destroy_exist_datas
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select
          user_code,
          user_name,
          dict_value,
          fund_code,
          pro_name,
          take_post_date,
          leave_post_date
        from view_product
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.query(import_accounts_sql).each(as: :array) do |r|
          user_code, user_name, role_name, fund_code, fund_name, take_post_date, leave_post_date = r
          next unless user_name

          is_blank_role   = role_name.nil? || role_name.empty?
          @logger.warn "信披系统「view_product」表角色名称「dict_value」为空, user_code: #{user_code}, fund_code: #{fund_code}" if is_blank_role
          account         = find_or_create_account(user_code, user_name)
          role            = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, code: role_name, name: role_name) unless is_blank_role
          take_post_date  = Date.parse(take_post_date) if take_post_date
          leave_post_date = Date.parse(leave_post_date) if leave_post_date

          # 根据客户需求，目前导入所有历史权限
          # next unless in_active_date?(take_post_date, leave_post_date)

          # 放在判定 时间的下面，只记录有效基金
          fund = find_or_create_fund(fund_code, fund_name)

          next unless role && fund

          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  take_post_date,
            leave_post_date: leave_post_date,
            quarter_id:      @quarter_id
          )
        end
      end
    end

    # 信披系统中的用户为从公告中人工导入，并不实际存在。
    # 其账号状态应与 HR 系统账号保持一致
    def update_accounts_status
      ActiveRecord::Base.transaction do
        FundDisclosure::Account.where(quarter_id: @quarter_id).includes(:user).each do |account|
          account.update(status: account.user.inservice) if account.user_id
        end
      end
    end

    def find_or_create_account(code, name)
      code ||= name

      user = FundDisclosure::Account.find_or_create_by(quarter_id: @quarter_id, code: code)
      user.update(name: name, status: false)
      user
    end

    def find_or_create_fund(code, name)
      fund_code      = convert_fund_code(code)
      fund           = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: fund_code)
      fund.name      = name
      fund.o32_fund_codes = add_o32_fund_codes(fund.o32_fund_codes, fund_code)
      fund.save
      fund
    end
  end
end



