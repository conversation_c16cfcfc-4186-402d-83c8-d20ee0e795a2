module XyFaren<PERSON>su<PERSON>
  def self.table_name_prefix
    'xy_farenqingsuan_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class XyFarenqingsuan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles, -> { distinct }

  has_many :data1_accounts_roles_permissions

  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end

class XyFarenqingsuan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts

  has_many :data1_accounts_roles_permissions

  validates :code, presence: true
  validates :name, presence: true
end

class XyFarenqingsuan::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end

class XyFarenqingsuan::Data1Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :level1_name, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class XyFarenqingsuan::Data1AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'xy_farenqingsuan_data1_arps'
  belongs_to :data1_permission
  belongs_to :role, optional: true

  validates :data1_permission_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class XyFarenqingsuan::Department < ActiveRecord::Base
  has_ancestry cache_depth: true

  validates :code, presence: true
  validates :name, presence: true
end