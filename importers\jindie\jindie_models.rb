module Jindie
  def self.table_name_prefix
    'jindie_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class Jindie::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class Jindie::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission
  validates :permission_id, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class Jindie::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end


class Jindie::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  #validates :permission_type, presence: true 
  validates :quarter_id, presence: true 

  serialize :data_json, JsonWithSymbolizeNames
end

