# frozen_string_literal: true

module AasOracleImporter
  # SqlServer 客户端
  class SqlServerClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.execute(sql).each(as: :array).each do |row|
          yield row
        end
      else
        @database.execute(sql).each(as: :array)
      end
    end

    # 输出hash数组
    def query(sql)
      @database.execute(sql)
    end

    private

    def initialize_driver
      load_driver_gem
      @database = TinyTds::Client.new(sql_server_client_params)
    end

    def load_driver_gem
      require 'tiny_tds'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'tiny_tds\'.')
      exit(-127)
    end

    # 转换统一的参数格式为 SqlServer 特定格式
    def sql_server_client_params
      sql_server_client_config.update({password: ConvertTools::Cryptology.decrypt_if_env(sql_server_client_config[:password])})
    end

    def sql_server_client_config
      client_params            = {
        username:    database_info['db_user'],
        password:    database_info['db_pass'],
        host:        database_info['db_host'],
        tds_version: database_info['tds_v']
      }

      client_params[:database] = database_info['db_name'] if database_info['db_name']
      client_params
    end
  end
end
