require 'httparty'
module AasOracleImporter
  class ZyfundHrImporter < ImporterBase
    include HTTParty

    def config
      @bs_id              = importer_config['bs_id']
      @user               = importer_config['user']
      @password           = importer_config['password']
      @department_root_id = importer_config['department_root_id']
      @token              = nil
      self.class.base_uri importer_config['base_uri']
    end

    def import_to_do
      login
      create_or_update_departments
      ActiveRecord::Base.transaction { create_or_update_users }
      logout
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    def login
      body     = { skUserId: @user }
      response = self.class.post('/loginController/simulatorLogin', { headers: headers, body: body.to_json })
      result   = JSON.parse response.body
      pp result['code'] == '0000'
      pp result['message']
      @token = result['data']['token']
    end

    def logout
      response = self.class.post('/loginController/logout', { headers: headers })
      result   = JSON.parse response.body
      pp result['code']
      pp result['message']
    end

    def headers
      { 'Content-Type' => 'application/json', 'netNo' => 'PC', 'token' => @token }
    end

    UserStruct = Struct.new(:user_id, :code, :name, :status, :department_code, :department_name) do
      # TODO: 目前获取这个信息为空，需要和用户确认
      def inservice
        status.blank?
      end

      def present?
        code.present? && name.present?
      end
    end

    DepartmentStruct = Struct.new(:code, :name, :status, :parent_code) do
      # 客户说所有部门都是正常的
      # def inservice
      #   status.blank?
      # end
    end

    def department_structs
      response    = self.class.post('/orgController/queryByType?type=00140000000001', { headers: headers })
      result      = JSON.parse response.body
      departments = []

      result['data'].each do |dep|
        d = DepartmentStruct.new(dep['skOrgNo'], dep['cOrgName'], dep['cOrgStatus'], dep['cOrgParentNo'])
        next if d.code == @department_root_id

        departments << d
      end
      departments
    end

    def user_structs
      response = self.class.post('/userController/findAllUser', { headers: headers })
      result   = JSON.parse response.body
      users    = []

      result['data'].each do |user|
        a        = UserStruct.new(user['skUserId'], user['loginName'], user['userName'], user['status'])
        org_list = user['sysOrgList']
        if org_list&.size&.positive?
          org               = org_list.first
          a.department_code = org['skOrgNo']
          a.department_name = org['orgName']
        end
        users << a if a.present?
      end
      users
    end

    def find_or_init_dep_by(struct)
      department           = Department.find_or_initialize_by(code: struct.code)
      department.name      = struct.name
      department.inservice = true
      department
    end

    # 中银基金包括禁用所有部门信息都能取到，因此不用处理旧部门
    def create_or_update_departments
      dep_structs = department_structs

      level1_deps = create_deps_in_level(dep_structs, 1, [@department_root_id], [])
      level2_deps = create_deps_in_level(dep_structs, 2, level1_deps.map(&:code), level1_deps)
      level3_deps = create_deps_in_level(dep_structs, 3, level2_deps.map(&:code), level2_deps)
      level4_deps = create_deps_in_level(dep_structs, 4, level3_deps.map(&:code), level3_deps)
      level5_deps = create_deps_in_level(dep_structs, 5, level4_deps.map(&:code), level4_deps)

      @departments = level1_deps + level2_deps + level3_deps + level4_deps + level5_deps
    end

    def create_deps_in_level(structs, level, parent_codes, parents)
      return [] if parent_codes.empty?

      departments = []
      structs.select { |x| parent_codes.include? x.parent_code }.each do |struct|
        d           = find_or_init_dep_by(struct)
        parent      = parents.find { |p| struct.parent_code == p&.code }
        d.level     = level
        d.parent_id = parent&.id
        d.save
        departments << d
      end
      departments
    end

    def create_or_update_users
      user_structs.each do |u|
        user               = User.find_or_initialize_by(code: u.code)
        user.name          = u.name
        user.inservice     = u.inservice
        department         = @departments.find { |d| d.code == u.department_code }
        user.department_id = department&.id
        user.save
      end
    end
  end
end



