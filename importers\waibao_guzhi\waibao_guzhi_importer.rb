module AasOracleImporter
  class WaibaoGuzhiImporter < ImporterBase
    def config
      @bs_id               = importer_config['bs_id']
      @table_space         = importer_config['table_space']
      @sid_suffix          = importer_config['sid_suffix']
      @bookset_group_codes = importer_config['bookset_group_codes']
      # 恒生的客户编号，在 tsys_module 表中有个 vc_zykh 的字段（专用客户）代表菜单仅在该客户内生效，其内容为客户编号用逗号分隔
      # yaml 可能识别为数字，需要转换成 string 进行比较
      @hs_customer_code = importer_config['hs_customer_code'].to_s
      # 恒生的增值模块编号，在 tsys_module 表中有个 vc_zzmk 的字段（增值模块）代表菜单仅在指定模块中生效，其内容为模块编号用逗号分隔
      @add_on_modules_disable = importer_config['add_on_modules_disable']
      @add_on_modules_table   = importer_config['add_on_modules_table'] || 'txtcs'
      @import_fund_code       = importer_config['import_fund_code']
      @add_on_modules_codes   = []
      initialize_tables
    end

    def initialize_tables
      @table_user            = "#{@table_space}tsys_user#{@sid_suffix}"
      @table_usergroup       = "#{@table_space}tsys_usergroup#{@sid_suffix}"
      @table_usergroup_map   = "#{@table_space}tsys_usergroup_map#{@sid_suffix}"
      @table_bookset         = "#{@table_space}tsysinfo#{@sid_suffix}"
      @table_subsystem       = "#{@table_space}tsys_subsystem#{@sid_suffix}"
      @table_sys_module      = "#{@table_space}tsys_module#{@sid_suffix}"
      @table_usergroup_right = "#{@table_space}tsys_usergroup_right#{@sid_suffix}"
      @table_fund_info       = "#{@table_space}tfundinfo#{@sid_suffix}"
      @table_add_on_modules  = "#{@table_space}#{@add_on_modules_table}#{@sid_suffix}"
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :add_on_modules_sql if @add_on_modules_disable
      ignore_list
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      # import_add_on_modules_code 需要在 import_menus 前执行
      import_add_on_modules_code
      import_menus
      import_booksets

      import_ledgers(WaibaoGuzhi::Account)
    end

    def destroy_exist_datas
      WaibaoGuzhi::Account.where(quarter_id: @quarter_id).destroy_all
      WaibaoGuzhi::Bookset.where(quarter_id: @quarter_id).destroy_all
      WaibaoGuzhi::Role.where(quarter_id: @quarter_id).destroy_all
      WaibaoGuzhi::Menu.where(quarter_id: @quarter_id).destroy_all
      WaibaoGuzhi::Permission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select vc_code, vc_name, vc_dept,l_void from #{@table_user}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = WaibaoGuzhi::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              org_name:   r[2],
              status:     r[3].to_i.zero?
            )

            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       'WaibaoGuzhi::Account',
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[3]&.to_s
              )
            end
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select vc_code,vc_name from #{@table_usergroup} where l_void = 0
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          WaibaoGuzhi::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          distinct u.vc_code, a.vc_code
        from
          #{@table_usergroup} a,
          #{@table_usergroup_map} b,
          #{@table_user} u,
          #{@table_bookset} f
        where
          a.l_id = b.l_gid
          and b.l_uid = u.l_id
          and b.l_ztbh = f.l_id
          and f.l_void = 0
      SQL
    end

    # 这里用于部分用户除账套外有额外的账套分组导入使用
    def import_accounts_roles_in_bookset_groups_sql
      str = <<-SQL
        select
          distinct u.vc_code, a.vc_code
        from
          #{@table_usergroup} a,
          #{@table_usergroup_map} b,
          #{@table_user} u
        where
          a.l_id = b.l_gid
          and b.l_uid = u.l_id
      SQL
      str += " and b.l_ztbh in (#{@bookset_group_codes&.join(',')})" if @bookset_group_codes.present?
      str
    end

    def import_accounts_roles_in_bookset_groups
      @database.exec(import_accounts_roles_in_bookset_groups_sql) do |r|
        account = WaibaoGuzhi::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
        role    = WaibaoGuzhi::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

        if account && role
          account.roles << role unless account.roles.include?(role)
        else
          @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
          @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
        end
      end
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = WaibaoGuzhi::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = WaibaoGuzhi::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            WaibaoGuzhi::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
          end
        end

        import_accounts_roles_in_bookset_groups if @bookset_group_codes&.size&.positive?
      end
    end

    def import_menus_sql
      <<-SQL
        SELECT
          c.role_code "角色代码",
          c.vc_name "角色名称",
          a.vc_cdmc "菜单",
          a.VC_NAME "模块名称",
          decode( l_bzw_qxjc, 2, '账套级', 1, '公共+账套级', '公共级' ) "权限类型",
          decode( c.VC_CODE, NULL, 0, 1 ) AS "浏览",
          a.VC_FUNC1 "编辑",
          a.VC_FUNC2 "删除",
          a.VC_FUNC3 "复核",
          a.VC_FUNC4 "取消复核",
          a.VC_FUNC5 "参数设置",
          a.VC_FUNC6 "盖章",
          a.VC_FUNC7 "取消盖章",
          a.VC_FUNC8 "打印带盖章",
          c.L_FUNCID,
          a.VC_ZZMK,
          a.VC_ZYKH
        FROM
          (
          SELECT
        /*+no_merge(m)*/
            m.*,
            dense_rank ( ) over ( ORDER BY m.l_bzw_qxjc, m.l_sys_no, m.l_first_no ) l_rec_no,
            row_number ( ) over ( partition BY m.l_bzw_qxjc, m.l_sys_no, m.l_first_no ORDER BY m.vc_parent_no, m.VC_CODE ) l_index_no,
            m.l_bzw_qxjc || m.l_sys_no || m.l_first_no AS parentID
          FROM
            (
            SELECT
              t.vc_sys,
              t.vc_code,
              t.VC_NAME,
              t.VC_PARENT,
              t.L_VOID,
              t.l_no,
              t.VC_FUNC1,
              t.VC_FUNC2,
              t.VC_FUNC3,
              t.VC_FUNC4,
              t.VC_FUNC5,
              t.VC_FUNC6,
              t.VC_FUNC7,
              t.VC_FUNC8,
              t.vc_bzw,
              t.VC_ZZMK,
              t.VC_ZYKH,
        /*connect_by_root t.vc_name as vc_cdmc,connect_by_root t.l_no as l_first_no,*/
              REPLACE ( ( sys_connect_by_path ( decode( LEVEL, 1, to_char( t.vc_name ), '' ), '|' ) ), '|', '' ) AS vc_cdmc,
              to_number( trim( sys_connect_by_path ( decode( LEVEL, 1, to_char( t.l_no ), '' ), ' ' ) ) ) AS l_first_no,
              ( SELECT p.l_no FROM #{@table_subsystem} p WHERE p.vc_sys = t.vc_sys ) AS l_sys_no,
              sys_connect_by_path ( lpad( t.l_no, LEVEL * 3, '0' ), '|' ) AS vc_parent_no,
              decode( substr( t.vc_bzw, 3, 2 ), '11', 1, '10', 2, '01', 0, 0 ) AS l_bzw_qxjc,
              decode( substr( t.vc_bzw, 1, 1 ), '1', 1, 0 ) AS l_bzw_tjzt
            FROM
              #{@table_sys_module} t
            WHERE
              t.l_void = 0
              AND t.vc_name <> '-'
              AND NOT EXISTS ( SELECT 1 FROM #{@table_sys_module} WHERE vc_parent = t.vc_code ) START WITH t.vc_parent IS NULL CONNECT BY t.vc_parent = PRIOR t.vc_code
            ) m
          ) a
          INNER JOIN (
          SELECT
            r2.VC_CODE,
            r2.VC_SYS,
            r2.L_FUNCID,
            g.vc_name,
            g.vc_code role_code
          FROM
            #{@table_usergroup_right} r2,
            #{@table_usergroup} g
          WHERE
            r2.l_gid = g.l_id
          ) c ON a.vc_code = c.vc_code
          AND a.VC_SYS = c.VC_SYS
        ORDER BY
          c.role_code,
          l_bzw_qxjc,
          a.l_sys_no,
          a.vc_parent_no,
          a.VC_CODE
      SQL
    end

    def import_menus
      last_menu_name = nil

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          role_code, _role_name, menu_name, module_name, menu_type = r[0..4]
          read_flag, p1, p2, p3, p4, p5, p6, p7, p8                = r[5..13]
          func_id, add_on_module, customer_code                    = r[14..16]

          role           = WaibaoGuzhi::Role.where(quarter_id: @quarter_id).find_by(code: role_code)
          last_menu_name = menu_name if menu_name

          menu =
            if create_menu?(add_on_module, customer_code)
              WaibaoGuzhi::Menu
                .create_with(quarter_id: @quarter_id)
                .find_or_create_by(
                  menu_name:   last_menu_name,
                  module_name: module_name,
                  menu_type:   menu_type
                )
            end

          unless menu
            @logger.info "#{self.class}.#{__method__}: found invalid menu #{module_name} vc_zzmk #{add_on_module} vc_zykh #{customer_code}"
            next
          end

          unless role
            @logger.warn "#{self.class}.#{__method__}: not found role #{role_code}"
            next
          end

          perm_names = [p1, p2, p3, p4, p5, p6, p7, p8]

          perms = read_flag.to_i == 1 ? ['浏览'] : []

          if func_id
            perms += perms_convert(perm_names, func_id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found valid func_id in #{r.join}"
          end

          WaibaoGuzhi::Permission
            .create_with(quarter_id: @quarter_id)
            .find_or_create_by(
              role_id:          role.id,
              menu_id:          menu.id,
              permission_items: perms.join('、')
            )
        end
      end
    end

    # 将权限与角色实际拥有的权限进行位与运算
    # 该功能的附加权限字段从func1 至 func8 为例如 ['编辑', '删除', '计算', nil, '生成历史', '审核', '锁定', '解锁'] ，从右至左，存在该权限（非空），转换成二进制为 '11110111'
    # 角色的 func_id 为 229 ，转换成二进制为 '11100101', 两者进行位与运算，结果为，'11100101'。
    # 所以角色实际权限结果对应权限位置为 1 的权限： ['编辑', '计算', '审核', '锁定', '解锁']
    def perms_convert(perm_names, func_id)
      func_codes = func_id.to_i.to_s(2).rjust(8, '0').chars.reverse
      perm_names.map.with_index do |x, i|
        x.present? && func_codes[i].to_i == 1 ? x : nil
      end.compact
    end

    # 是否创建这个菜单，有两个可能，菜单隶属于某个增值模块或是某个客户的定制开发
    # 普通菜单，导入
    # 专用客户菜单，匹配的导入，不匹配的拒绝
    # 增值模块菜单，匹配的导入，不匹配的拒绝
    def create_menu?(add_on_module_string, customer_code)
      common_menu?(add_on_module_string, customer_code) ||
        menu_in_add_on_modules?(add_on_module_string) ||
        menu_in_customer_list?(customer_code)
    end

    def common_menu?(add_on_module_string, customer_code)
      add_on_module_string.blank? && customer_code.blank?
    end

    # 发现恒生的估值系统会有一些专用菜单，仅在部分客户生效
    # 恒生的客户编号，在 tsys_module 表中有个 vc_zykh 的字段（专用客户？）代表菜单仅在该客户内生效，其内容为客户编号用逗号分隔
    # 如果没有填写客户编号时，应当只取 vc_zykh 为空的菜单, 返回 false
    # 如果填写了客户编号，取 vc_zykh 为空的菜单和包含客户编号的菜单
    def menu_in_customer_list?(customer_code)
      return false if customer_code.blank?
      return false if @hs_customer_code.blank?

      customer_code.split(/,\s*/).include?(@hs_customer_code)
    end

    # 发现恒生的估值系统会有一些专用菜单，仅在部分增值模块中生效，获取增值模块 ID 的方法在 add_on_modules_sql 中
    # 恒生的客户编号，在 tsys_module 表中有个 vc_zzmk 的字段, 代表菜单仅在某些模块内生效，其内容为客户编号用逗号分隔
    # 应当只取 vc_zzmk 为空的菜单或启用了增值模块的菜单
    def menu_in_add_on_modules?(add_on_module_string)
      return false if add_on_module_string.blank?
      # 如果用户禁用，默认他有所有增值模块的权限
      return true if @add_on_modules_disable

      record_codes = add_on_module_string.split(/,\s*/)
      !(record_codes & @add_on_modules_codes).empty?
    end

    def add_on_modules_sql
      "select vc_csz from #{@table_add_on_modules} t where t.vc_csdm = 'ZZMKLB'"
    end

    def import_add_on_modules_code
      return if @add_on_modules_disable

      ActiveRecord::Base.transaction do
        @database.exec(add_on_modules_sql) do |r|
          codes = r.first.to_s.split(/,\s*/)
          @add_on_modules_codes += codes unless codes.empty?
        end
      end
      @add_on_modules_codes.uniq!
      @logger.info "#{self.class}.#{__method__}: found add_on_modules: #{@add_on_modules_codes.join(', ')}"
      @add_on_modules_codes
    end

    def import_booksets_sql
      if @import_fund_code
        <<-SQL
          SELECT
            u.vc_code "用户代码",
            u.vc_name "用户姓名",
            p.l_ztbh || '_' || t.vc_name "账套",
            g.vc_name "分组" ,
            f.vc_code
          FROM
            #{@table_usergroup_map} p,
            #{@table_usergroup} g,
            #{@table_user} u,
            #{@table_bookset} t,
            #{@table_fund_info} f
          WHERE
            p.l_gid = g.l_id
            AND p.l_uid = u.l_id
            AND p.l_ztbh = t.l_id
            AND t.l_void = 0
            AND f.l_fundid = t.l_id
          ORDER BY
            u.vc_code,
            g.l_id,
            p.l_ztbh
        SQL
      else
        <<-SQL
          SELECT
            u.vc_code "用户代码",
            u.vc_name "用户姓名",
            p.l_ztbh || '_' || t.vc_name "账套",
            g.vc_name "分组"
          FROM
            #{@table_usergroup_map} p,
            #{@table_usergroup} g,
            #{@table_user} u,
            #{@table_bookset} t
          WHERE
            p.l_gid = g.l_id
            AND p.l_uid = u.l_id
            AND p.l_ztbh = t.l_id
            AND t.l_void = 0
          ORDER BY
            u.vc_code,
            g.l_id,
            p.l_ztbh
        SQL
      end
    end

    # 数据量大时会很慢，所以要格式化数据后批量处理
    def import_booksets
      data = []
      ActiveRecord::Base.transaction do
        @database.exec(import_booksets_sql) do |r|
          data << r
        end
      end
      uniq_codes = data.map { |r| r[0] }.uniq
      # 格式化数据，账户编码对应bookset的名称
      json = {}
      uniq_codes.each do |code|
        code_data = data.select { |x| x[0] == code }
        booksets = code_data.map { |x| { name: x[2], fund_code: x[4] } }.uniq
        json[code] = booksets
      end

      # 批量插入WaibaoGuzhi::Bookset
      uniq_booksets = data.map { |r| { name: r[2], fund_code: r[4] } }.uniq
      time = Time.now
      WaibaoGuzhi::Bookset.bulk_insert(:quarter_id, :name, :fund_code, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        uniq_booksets.each do |x|
          obj.add [@quarter_id, x[:name], x[:fund_code], time, time]
        end
      end

      data = []
      # 批量插入WaibaoGuzhi::AccountsBooksets
      json.each do |code, json_booksets|
        @logger.info "#{self.class}.#{__method__} #{code}"
        account = WaibaoGuzhi::Account.find_by(quarter_id: @quarter_id, code: code)
        if account
          json_booksets.each do |json_bookset|
            booksets = WaibaoGuzhi::Bookset.where(
              quarter_id: @quarter_id,
              name:       json_bookset[:name],
              fund_code:  json_bookset[:fund_code]
            )
            booksets.each do |bookset|
              data << { account_id: account.id, bookset_id: bookset.id }
            end
          end
        else
          @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}"
        end
      end

      WaibaoGuzhi::AccountsBooksets.bulk_insert(:account_id, :bookset_id) do |obj|
        obj.set_size = 1000
        data.uniq.each do |x|
          obj.add [x[:account_id], x[:bookset_id]]
        end
      end
    end
  end
end
