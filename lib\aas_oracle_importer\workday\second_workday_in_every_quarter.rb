# frozen_string_literal: true

require 'date'
require_relative 'workday_mode_base'

module AasOracleImporter
  module Workday
    # 每个季度第二个工作日
    class SecondWorkdayInEveryQuarter < WorkdayModeBase
      def initialize(date, config)
        super
      end

      def workday?
        return false unless first_month_in_a_quarter.include? date.month
        return false if during_holidays?(date)
        return false if in_weekend?(date)

        second_workday?
      end

      private

      def first_month_in_a_quarter
        [1, 4, 7, 10]
      end

      def during_holidays?(the_date)
        return true if the_date.month == 1 && new_years_days.include?(the_date.day)
        return true if the_date.month == 10 && national_days.include?(the_date.day)

        false
      end

      # 元旦假期
      def new_years_days
        (1..3).to_a
      end

      # 国庆假期
      def national_days
        return (1..8).to_a if date.year == 2020

        (1..7).to_a
      end

      def in_weekend?(the_date)
        [0, 6].include? the_date.wday
      end

      def second_workday?
        workday_count = 0
        the_date      = date.dup
        while the_date.month == date.month
          workday_count += 1 unless during_holidays?(the_date) || in_weekend?(the_date)
          the_date      -= 1
        end

        workday_count == 2
      end
    end
  end
end
