module AasOracleImporter

  class GuohuSaEtfImporter < ImporterBase

    def config
      @bs_id                   = importer_config['bs_id']
      @table_space             = importer_config['table_space'].to_s
      @sid_suffix              = importer_config['sid_suffix'].to_s
      @table_accounts          = @table_space + 'hsi_user' + @sid_suffix
      @table_roles             = @table_space + 'hsi_group' + @sid_suffix
      @table_accounts_roles    = @table_space + 'hsi_usergroup' + @sid_suffix
      @table_menus             = @table_space + 'hsi_menu' + @sid_suffix
      @table_menus_roles       = @table_space + 'hsi_groupright' + @sid_suffix
      @table_other_permission  = @table_space + 'hsi_right' + @sid_suffix
      @table_sub_system        = @table_space + 'hsi_system' + @sid_suffix
      @table_log               = @table_space + 'hsi_oplog' + @sid_suffix
      @table_password_security = @table_space + 'tsysparameter' + @sid_suffix
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_other_permissions
      import_accounts_roles

      import_menus_group(GuohuSaEtf::Role, GuohuSaEtf::Menu)
      import_menus_group(GuohuSaEtf::Role, GuohuSaEtf::OtherPermission)
      import_menus_group(GuohuSaEtf::Account, GuohuSaEtf::Menu)
      import_menus_group(GuohuSaEtf::Account, GuohuSaEtf::OtherPermission)

      import_system_user_permissions
      import_logs
      import_password_securities
      import_last_login_at_data

      import_ledgers(GuohuSaEtf::Account)
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_accounts

      GuohuSaEtf::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_usercode, c_username, c_status from #{@table_accounts}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            GuohuSaEtf::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i == 0
            )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      GuohuSaEtf::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select l_groupid, c_groupname from #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaEtf::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
    end

    def import_menus

      GuohuSaEtf::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_menucode, c_menuname from #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaEtf::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_other_permissions

      GuohuSaEtf::OtherPermission.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_rightcode, c_rightname from #{@table_other_permission}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaEtf::OtherPermission.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menus_group(group, menu)
      group_class   = GuohuSaEtf::Role
      account_class = GuohuSaEtf::Account
      menu_class    = GuohuSaEtf::Menu
      otherp_class  = GuohuSaEtf::OtherPermission

      join_class =
        if group == group_class && menu == menu_class
          GuohuSaEtfMenusRoles
        elsif group == account_class && menu == menu_class
          GuohuSaEtfAccountsMenus
        elsif group == group_class && menu == otherp_class
          GuohuSaEtfOtherPermissionsRoles
        elsif group == account_class && menu == otherp_class
          GuohuSaEtfAccountsOtherPermissions
        end

      case group.to_s
      when group_class.to_s
        group_name   = "g.c_groupname,"
        group_from   = "#{@table_roles} g,"
        group_flag   = 0
        group_id     = "g.l_groupid "
        send_method  = :find_by_name
        role_join_id = :role_id
      when account_class.to_s
        group_name   = "u.c_usercode,"
        group_from   = "#{@table_accounts} u,"
        group_flag   = 1
        group_id     = "u.l_userid"
        send_method  = :find_by_code
        role_join_id = :account_id
      end

      case menu.to_s
      when menu_class.to_s
        menu_name    = "m.c_menuname"
        menu_code    = "m.c_menucode"
        menu_from    = "#{@table_menus} m"
        menu_flag    = 1
        menu_join_id = :menu_id
      when otherp_class.to_s
        menu_name    = "m.c_rightname"
        menu_code    = "m.c_rightcode"
        menu_from    = "#{@table_other_permission} m"
        menu_flag    = 0
        menu_join_id = :other_permission_id
      end

      sql = <<-EOF
        select
          #{group_name}
          gm.c_rightcode,
          #{menu_name}
        from
          #{@table_menus_roles} gm,
          #{group_from}
          #{menu_from}
        where
          gm.c_flag           = #{group_flag}
          and gm.c_rightclass = #{menu_flag}
          and gm.l_groupid    = #{group_id}
          and gm.c_rightcode  = #{menu_code}
         order by
          gm.c_rightcode
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          group_record = group.where(quarter_id: @quarter_id).send(send_method, r[0])
          menu_record  = menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu_record and group_record
            join_class.create(menu_join_id => menu_record.id, role_join_id => group_record.id)
          else
            @logger.warn "#{self.class}: not found #{group} #{r[0]}" unless group_record
            @logger.warn "#{self.class}: not found #{menu} #{r[1]}" unless menu_record
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select
         g.c_groupname,
         u.c_usercode
        from #{@table_accounts_roles} ug,
             #{@table_accounts} u,
             #{@table_roles} g
        where ug.l_userid = u.l_userid
          and ug.l_groupid = g.l_groupid
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role    = GuohuSaEtf::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = GuohuSaEtf::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account and role
            GuohuSaEtfAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_system_user_permissions
      system_account = GuohuSaEtf::Account.find_by(quarter_id: @quarter_id, code: 'system')
      return unless system_account

      ActiveRecord::Base.transaction do
        GuohuSaEtf::Menu.where(quarter_id: @quarter_id).each do |menu|
          GuohuSaEtfAccountsMenus.create(
            account_id: system_account.id,
            menu_id:    menu.id
          )
        end

        GuohuSaEtf::OtherPermission.where(quarter_id: @quarter_id).each do |perm|
          GuohuSaEtfAccountsOtherPermissions.create(
            account_id:          system_account.id,
            other_permission_id: perm.id
          )
        end
      end
    end

    # 导入日志sql语句
    def customer_audit_sql
      start_at  = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      where_sql = case @database.database_type
      when 'oracle'
        "where D_OPTIME > TO_DATE('#{start_at}', 'yyyy-mm-dd hh24:mi:ss')"
      else
        "where D_OPTIME > '#{start_at}'"
      end
      <<-EOF
        select L_OPID, C_USERCODE, C_OSUSERNAME, D_OPTIME, C_OPCLASS, C_OPCONTENT
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        data << {
          'source_id'          => r[0],
          'account_code'       => r[1],
          'account_name'       => r[2],
          'operation_at'       => r[3],
          'operation_category' => r[4],
          'operation'          => r[5],
          'bs_id'              => @bs_id,
          'ip_address'         => nil
        }
      end
    end

    def password_security_sql
      "select C_ITEM, C_VALUE, C_DESCRIBE from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        json['bs_id']                = @bs_id
        json['password_length']      = data.find { |x| x[0] == 'PasswordLengthMin' }&.[](1)
        json['is_uppercase']         = nil
        json['is_lowercase']         = nil
        json['is_number']            = data.find { |x| x[0] == 'PasswordMix' }&.[](1)&.to_s == '1'
        json['is_character']         = nil
        json['login_failure_number'] = data.find { |x| x[0] == 'PasswordError' }&.[](1)
        json['password_valid_time']  = data.find { |x| x[0] == 'PasswordExpireDays' }&.[](1)
      end
    end

    def last_login_at_sql
      if @import_last_login_at.present?
        where_sql = case @database.database_type
                    when 'oracle'
                      "where d_optime > sysdate-#{@import_last_login_at}"
                    else
                      start_at = (Time.now - @import_last_login_at.days).strftime('%Y-%m-%d %H:%M:%S')
                      "where d_optime > '#{start_at}'"
                    end
      else
        where_sql = nil
      end
      <<-EOF
        select c_usercode, max(d_optime)
        from #{@table_log}
        #{where_sql}
        group by c_usercode
      EOF
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        data << {
          'code'          => x[0],
          'last_login_at' => x[1]
        }
      end
    end
  end
end



