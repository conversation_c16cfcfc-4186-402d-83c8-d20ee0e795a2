# frozen_string_literal: true

module AasOracleImporter
  # 系统导入状态
  class SystemImportStatus
    attr_reader :quarter, :the_system

    def initialize(quarter, the_system)
      @quarter    = quarter
      @the_system = the_system
      @logger = AasOracleImporter.initialize_logger
      initialize_status_record
    end

    def processing!
      @import_status.update(status: 'processing')
    end

    def success!
      @import_status.update(status: 'success', end_at: Time.now)
      AasOracleImporter.audit_log_system_import(quarter, the_system, :success)
    end

    def failed!
      @import_status.update(status: 'failed', end_at: Time.now)
      AasOracleImporter.audit_log_system_import(quarter, the_system, :failed)
    end

    def skipped!
      @import_status.update(status: 'skipped')
      AasOracleImporter.audit_log_system_import(quarter, the_system, :skipped)
    end

    def import_status
      @import_status
    end

    private

    def initialize_status_record
      # 没有系统默认为员工同步
      @import_status = if the_system.id
                         QuarterBusinessSystemImportStatus.find_or_initialize_by(
                           business_system_id: the_system.id,
                           quarter_id:         quarter.id
                         )
                       else
                         QuarterUserImportStatus.find_or_initialize_by(quarter_id: quarter.id)
                       end
      @import_status.start_at = Time.now
      @import_status.update(status: 'pending')

      @import_status
    end
  end
end
