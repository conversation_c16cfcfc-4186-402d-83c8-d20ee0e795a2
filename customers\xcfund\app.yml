# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'xcfund'
  customer: '湘财基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'

  agent:
    oracle:
      # 投资交易 O32 系统数据库
      tradedb:
        db_name: 'fmdb'
        db_user: 'fmreader'
        db_pass: 'fmreader'
        db_host: '**********'

      tadb:
        # in tns name
        db_name: 'tadb'
        db_user: 'tareader'
        db_pass: 'tareader'
        db_host: '**********'

      # 估值系统数据库
      gzdb:
        db_name: 'fadb'
        db_user: 'fareader'
        db_pass: 'fareader'
        db_host: '**********'

      # 恒生信披系统数据库
      xpdb:
        db_name: 'fadb'
        db_user: 'fareader'
        db_pass: 'fareader'
        db_host: '**********'
      # 清算数据库
      qsdb:
        db_name: 'tadb'
        db_user: 'ccreader'
        db_pass: 'ccreader'
        db_host: '**********'
      # 直销数据库
      zxdb:
        db_name: 'tadb'
        db_user: 'dsreader'
        db_pass: 'dsreader'
        db_host: '**********'

  importers:
    # MARK: 按照顺序依次导入
    # HR 系统

    #估值系统
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: gzdb
      table_space: ''
      sid_suffix: ''
      bookset_group_codes:
        - 0
      add_on_modules_disable: true

    # 登记过户 4.0
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: tadb
      table_space: ''
      sid_suffix: ''

    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: ''
      sid_suffix: ''
      days_of_data: 365
      temporary: true

    # 恒生信披系统
    - name: fund_disclosure_hs_oracle
      bs_id: 32
      db_type: oracle
      tnsname: xpdb
      table_space: ''
      sid_suffix: ''

    # 恒生清算系统
    - name: hs_qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: qsdb
      table_space: ''
      sid_suffix: ''

    # 直销系统
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
      sub_system: "CENTER"

    # 直销系统
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
      sub_system: "COUNTER"

    # 直销系统
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zxdb
      table_space: ''
      sid_suffix: ''
      sub_system: "REMOTE"

development:
  <<: *defaults
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_xcfund_development
      username: root
      password: 123456
      host: 127.0.0.1

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    # 要改
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_production
      username: aas
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: aas-db-01
