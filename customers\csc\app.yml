# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'csc'
  customer: '中信建投'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/opt/qdhh/tdhl_permission_check'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_csc_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # tnsname
      sdata:
        # in tns name
        db_name: 'sdata'
        db_user: 'sdata'
        db_pass: 'Aa111111'

  importers:
    # guohu_components/zofund_guohu_ta: true
    # 登记过户 4.0
    - name: mot
      bs_id: 202
      db_type: oracle
      tnsname: sdata
      table_accounts: "L_MEMBER"
      table_roles: "L_ROLE"
      table_positions: "LMSP_RESOURCE"
      table_accounts_positions: "LMSP_AUTHORITY"
      table_accounts_roles: "L_MEMBER_ROLE_REL"
      table_positions_roles: "LMSP_AUTHORITY"

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_csc_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


