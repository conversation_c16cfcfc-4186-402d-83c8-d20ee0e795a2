class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class User < ActiveRecord::Base
  belongs_to :department, optional: true

  validates :name, presence: true
  validates :code, presence: true

end

class Department < ActiveRecord::Base
  has_many :children, class_name: 'Department', foreign_key: 'parent_id'
  belongs_to :parent, class_name: 'Department', optional: true
  has_many :users

  validates :name, presence: true
  validates :code, presence: true
end

class TpyzqOaNotify < ActiveRecord::Base
  belongs_to :user
  serialize :notify_user_ids, JsonWithSymbolizeNames
  serialize :content, JsonWithSymbolizeNames
  
end

