# frozen_string_literal: true

module AasOracleImporter
  class HspbImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @temporary   = importer_config['temporary']
      super
    end

    def import_to_do
      destroy_old_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_sub_systems
      import_menus
      import_menu_additions
      import_accounts_menus
      import_menus_roles
      import_funds
      import_accounts_funds

      # 导入临时权限
      import_temp_menu_rights
      import_temp_fund_rights
      import_temp_fund_unit_rights
      import_temp_fund_combi_rights

      trade_type_importer  if importer_config['trade_type_importer']
      station_importer     if importer_config['station_importer']
      import_temp_all_caches
      import_ledgers(Hspb::Account)
    end

    def destroy_old_datas
      Hspb::Account.where(quarter_id: @quarter_id).destroy_all
      Hspb::Role.where(quarter_id: @quarter_id).destroy_all
      Hspb::Menu.where(quarter_id: @quarter_id).destroy_all
      Hspb::System.where(quarter_id: @quarter_id).destroy_all
      Hspb::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Hspb::MenuPermission.where(quarter_id: @quarter_id).destroy_all
      Hspb::MenuAddition.where(quarter_id: @quarter_id).destroy_all
      Hspb::MenuAdditionPermission.where(quarter_id: @quarter_id).destroy_all
      Hspb::Fund.where(quarter_id: @quarter_id).destroy_all
      Hspb::FundUnit.where(quarter_id: @quarter_id).destroy_all
      Hspb::FundCombination.where(quarter_id: @quarter_id).destroy_all
      Hspb::FundPermission.where(quarter_id: @quarter_id).destroy_all
      Hspb::FundUnitPermission.where(quarter_id: @quarter_id).destroy_all
      Hspb::FundCombinationPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_station_sql
      <<-SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,VC_STATION_NO
        from
          #{@table_space}topstation#{@sid_suffix}
      SQL
    end

    def station_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_station_sql) do |r|
          Hspb::TopStation.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            station_code: r[2]
          )
        end
      end
    end

    def import_trade_type_sql
      <<-SQL
        select
          L_SERIAL_ID,VC_TRADE_NO,VC_TRADE_NAME,L_PARENT_ID
        from
          #{@table_space}ttradetypes#{@sid_suffix}
      SQL
    end

    def trade_type_importer
      ActiveRecord::Base.transaction do
        @database.exec(import_trade_type_sql) do |r|
          Hspb::TradeType.create(
            quarter_id:  @quarter_id,
            l_serial_id: r[0],
            code:        r[1],
            name:        r[2],
            parent_id:   r[3]
          )
        end

        @database.exec(import_top_trade_type_sql) do |r|
          Hspb::TopTradeType.create(
            quarter_id:   @quarter_id,
            role_code:    r[0],
            account_code: r[1],
            l_serial_id:  r[2],
            vc_right:     r[3],
            c_status:     r[4]
          )
        end
      end
    end

    def import_top_trade_type_sql
      <<-SQL
        select
          L_ROLE_ID,L_OPERATOR_NO,L_SERIAL_ID,VC_RIGHT,C_STATUS
        from
          #{@table_space}toptradetype#{@sid_suffix}
      SQL
    end

    def import_accounts_sql
      vc_operator_no = importer_config['operator_no_importer'] ? ', vc_operator_no' : ''
      <<-SQL
        select   l_operator_no,
                vc_operator_name,
                 c_operator_status,
                 l_register_date,
                 l_cancel_date #{vc_operator_no}
        from #{@table_space}toperator#{@sid_suffix}
        where l_org_id = 0
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Hspb::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i == 1
            )
            if importer_config['register_date']
              account.register_date = r[3]
              account.cancel_date   = r[4]
              account.save
            end

            if importer_config['operator_no_importer']
              account.operator_no = r[5]
              account.save
            end

            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       'Hspb::Account',
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[2]&.to_s
              )
            end
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_role_id, vc_role_name, vc_remarks
        from #{@table_space}trole#{@sid_suffix}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          Hspb::Role.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          vc_menu_no, vc_menu_name, c_subsystem_no
        from
          #{@table_space}tmenuitem#{@sid_suffix}
        where
          vc_menu_name <> '-'
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          sub_system = Hspb::System.find_by(quarter_id: @quarter_id, code: r[2])
          next unless sub_system

          Hspb::Menu.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1],
            system_id:  sub_system.id
          )
        end
      end
    end

    def import_sub_systems_sql
      <<-SQL
        select
          c_lemma_item, vc_item_name
        from
          #{@table_space}tdictionary#{@sid_suffix}
        where
          l_dictionary_no = '10002'
        and
          c_lemma_item <> '!'
      SQL
    end

    def import_sub_systems
      ActiveRecord::Base.transaction do
        @database.exec(import_sub_systems_sql) do |r|
          Hspb::System.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       r[1]
          )
        end
      end
    end

    def import_menu_additions_sql
      <<-SQL
        select
          vc_menu_no,
          c_rights_id,
          vc_remarks
        from
          #{@table_space}tmenurights#{@sid_suffix}
      SQL
    end

    def import_menu_additions
      ActiveRecord::Base.transaction do
        @database.exec(import_menu_additions_sql) do |r|
          menu = Hspb::Menu.find_by(quarter_id: @quarter_id, code: r[0])
          next unless menu

          Hspb::MenuAddition.create(
            quarter_id: @quarter_id,
            sub_code:   r[1],
            name:       r[2],
            menu_id:    menu.id
          )
        end
      end
    end

    def import_menus_roles_sql
      "select l_role_id, vc_menu_no, vc_menu_rights from #{@table_space}topmenurights#{@sid_suffix} where l_operator_no = -1"
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = Hspb::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Hspb::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && role
            HspbMenusRoles.create(menu_id: menu.id, role_id: role.id)

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps                         =
              Hspb::MenuAddition.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              HspbMenuAdditionsRoles.create(
                role_id:          role.id,
                menu_addition_id: ap.id
              )
            end
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_menus_sql
      <<-SQL
        select
          l_operator_no,
          vc_menu_no,
          vc_menu_rights
        from
         #{@table_space}topmenurights#{@sid_suffix}
        where
          l_role_id = -1
      SQL
    end

    def import_accounts_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_menus_sql) do |r|
          account = Hspb::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu    = Hspb::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu && account
            Hspb::MenuPermission.create(
              menu_id:    menu.id,
              account_id: account.id,
              quarter_id: @quarter_id
            )

            next unless r[2]

            additional_permission_codes = r[2].chars
            aps                         =
              Hspb::MenuAddition.where(
                quarter_id: @quarter_id,
                menu_id:    menu.id,
                sub_code:   additional_permission_codes
              )

            aps.each do |ap|
              Hspb::MenuAdditionPermission.create(
                account_id:       account.id,
                menu_addition_id: ap.id,
                quarter_id:       @quarter_id
              )
            end
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select l_operator_no, l_role_id
        from #{@table_space}toprolerights#{@sid_suffix}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Hspb::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Hspb::Role.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account && role
            HspbAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_funds_sql
      <<-SQL
        select l_fund_id, vc_fund_code, vc_fund_name, vc_fund_caption, c_fund_status
        from #{@table_space}tfundinfo#{@sid_suffix}
      SQL
    end

    def import_fund_asset_sql
      <<-SQL
        select l_fund_id, l_asset_id, vc_asset_name
        from #{@table_space}tasset#{@sid_suffix}
      SQL
    end

    def import_fund_combi_sql
      <<-SQL
        select l_asset_id, l_combi_id, vc_combi_name
        from #{@table_space}tcombi#{@sid_suffix}
      SQL
    end

    def import_funds
      ActiveRecord::Base.transaction do
        @database.exec(import_funds_sql) do |r|
          Hspb::Fund.create(
            quarter_id:   @quarter_id,
            code:         r[0],
            fund_code:    r[1],
            name:         r[2],
            fund_caption: r[3],
            inservice:    (r[4].to_i == 2)
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_fund_asset_sql) do |r|
          fund = Hspb::Fund.find_by(quarter_id: @quarter_id, code: r[0])
          next unless fund

          Hspb::FundUnit.create(
            quarter_id: @quarter_id,
            fund_id:    fund.id,
            code:       r[1],
            name:       r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_fund_combi_sql) do |r|
          unit = Hspb::FundUnit.find_by(quarter_id: @quarter_id, code: r[0])
          next unless unit

          Hspb::FundCombination.create(
            quarter_id:   @quarter_id,
            fund_unit_id: unit.id,
            code:         r[1],
            name:         r[2]
          )
        end
      end
    end

    # MARK: 在投资交易中，要咨询客户是否有通过角色进行授权基金的情况，根据表结构，在 O32 系统中其是支持的。
    # 但是多数客户都是直接授权给人。
    def import_accounts_funds_sql
      <<-SQL
        select
          l_operator_no,
          l_fund_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_fund_id <> -1
        and l_asset_id = -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_uniq_sql
      <<-SQL
        select
          l_operator_no,
          l_asset_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_asset_id <> -1
        and l_basecombi_id = -1
      SQL
    end

    def import_accounts_combi_sql
      <<-SQL
        select
          l_operator_no,
          l_basecombi_id,
          vc_rights
        from #{@table_space}topfundright#{@sid_suffix}
        where
          l_role_id = -1
        and l_basecombi_id <> -1
      SQL
    end

    def import_accounts_funds
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_funds_sql) do |r|
          account = Hspb::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          fund    = Hspb::Fund.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && fund

          Hspb::FundPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id:    fund.id,
            permission: r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_uniq_sql) do |r|
          account = Hspb::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          unit    = Hspb::FundUnit.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && unit

          Hspb::FundUnitPermission.create(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            fund_unit_id: unit.id,
            permission:   r[2]
          )
        end
      end

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_combi_sql) do |r|
          account = Hspb::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          combi   = Hspb::FundCombination.find_by(quarter_id: @quarter_id, code: r[1])
          next unless account && combi

          Hspb::FundCombinationPermission.create(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            fund_combination_id: combi.id,
            permission:          r[2]
          )
        end
      end
    end

    def import_temp_menu_rights_sql
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '1'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
        ORDER BY
          t.vc_right
      SQL
    end

    def import_temp_menu_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(import_temp_menu_rights_sql) do |r|
          start_date, end_date, account_code, grant_code, menu_code, addi_codes = r
          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)

          next if today > end_date

          account       = Hspb::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Hspb::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          menu          = Hspb::Menu.find_by(quarter_id: @quarter_id, code: menu_code)

          next unless account && menu

          perm = Hspb::MenuPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            menu_id:    menu.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Hspb::MenuPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              menu_id:    menu.id
            )
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            update_temp_right(perm, start_date, end_date, grant_account)
          end

          next unless addi_codes

          additional_permission_codes = addi_codes.chars
          aps                         =
            Hspb::MenuAddition.where(
              quarter_id: @quarter_id,
              menu_id:    menu.id,
              sub_code:   additional_permission_codes
            )

          aps.each do |ap|
            # map: MenuAdditionPermission 的缩写
            map_record = Hspb::MenuAdditionPermission.find_or_initialize_by(
              quarter_id:       @quarter_id,
              account_id:       account.id,
              menu_addition_id: ap.id
            )

            # 如果已经存在临时授权了，可能是授权重复
            if map_record.is_temp
              new_perm = Hspb::MenuAdditionPermission.new(
                quarter_id:       @quarter_id,
                account_id:       account.id,
                menu_addition_id: ap.id
              )
              update_temp_right(new_perm, start_date, end_date, grant_account)
            else
              update_temp_right(map_record, start_date, end_date, grant_account)
            end
          end
        end
      end
    end

    def import_temp_fund_rights_sql
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id = -1
          and t.l_basecombi_id = -1
        ORDER BY
          t.vc_right
      SQL
    end

    def import_temp_fund_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(import_temp_fund_rights_sql) do |r|
          start_date, end_date, account_code, grant_code, fund_code, perm_codes, _asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Hspb::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Hspb::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          fund          = Hspb::Fund.find_by(quarter_id: @quarter_id, code: fund_code)

          next unless account && fund

          perm = Hspb::FundPermission.find_or_initialize_by(
            quarter_id: @quarter_id,
            account_id: account.id,
            fund_id:    fund.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Hspb::FundPermission.new(
              quarter_id: @quarter_id,
              account_id: account.id,
              fund_id:    fund.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_unit_rights_sql
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id = -1
        ORDER BY
          t.vc_right
      SQL
    end

    def import_temp_fund_unit_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(import_temp_fund_unit_rights_sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, asset_code, _combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Hspb::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Hspb::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          unit          = Hspb::FundUnit.find_by(quarter_id: @quarter_id, code: asset_code)

          next unless account && unit

          perm = Hspb::FundUnitPermission.find_or_initialize_by(
            quarter_id:   @quarter_id,
            account_id:   account.id,
            fund_unit_id: unit.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Hspb::FundUnitPermission.new(
              quarter_id:   @quarter_id,
              account_id:   account.id,
              fund_unit_id: unit.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_fund_combi_rights_sql
      <<-SQL
        SELECT
          t.L_BEGIN_DATE,
          t.L_END_DATE,
          t.L_OPERATOR_NO,
          t.L_GRANT_OPERATOR,
          t.VC_RIGHT,
          t.VC_RIGHTS_ID,
          t.l_asset_id,
          t.l_basecombi_id
        FROM
          #{@table_space}toptempright#{@sid_suffix} t
        WHERE
          t.c_right_type = '0'
          AND EXISTS ( SELECT t.l_operator_no FROM #{@table_space}toperator#{@sid_suffix} o WHERE t.l_operator_no = o.l_operator_no )
          AND t.c_status IN ( '2' )
          AND t.vc_right <> '-1'
          and t.l_asset_id <> -1
          and t.l_basecombi_id <> -1
        ORDER BY
          t.vc_right
      SQL
    end

    def import_temp_fund_combi_rights
      today = Date.today

      ActiveRecord::Base.transaction do
        @database.exec(import_temp_fund_combi_rights_sql) do |r|
          start_date, end_date, account_code, grant_code, _fund_code, perm_codes, _asset_code, combi_code = r

          # 日期不存在的跳过（泰达遇见过此类脏数据）
          next unless start_date && end_date

          # 过期的跳过
          start_date = Date.parse(start_date.to_s)
          end_date   = Date.parse(end_date.to_s)
          next if today > end_date

          account       = Hspb::Account.find_by(quarter_id: @quarter_id, code: account_code)
          grant_account = Hspb::Account.find_by(quarter_id: @quarter_id, code: grant_code)
          combi         = Hspb::FundCombination.find_by(quarter_id: @quarter_id, code: combi_code)

          next unless account && combi

          perm = Hspb::FundCombinationPermission.find_or_initialize_by(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            fund_combination_id: combi.id
          )
          # 如果已经存在临时授权了，可能是授权重复
          if perm.is_temp
            new_perm = Hspb::FundCombinationPermission.new(
              quarter_id:          @quarter_id,
              account_id:          account.id,
              fund_combination_id: combi.id
            )
            new_perm.permission = perm_codes
            update_temp_right(new_perm, start_date, end_date, grant_account)
          else
            perm.permission = perm_codes
            update_temp_right(perm, start_date, end_date, grant_account)
          end
        end
      end
    end

    def import_temp_all_caches
      return true unless @temporary

      @destroy_datas = get_destroy_datas if importer_config['temp_delete_record']

      days_of_data = (Time.now - importer_config['days_of_data'].to_i.days).strftime('%Y%m%d')
      sql          = <<-SQL
        select
          L_OPERATOR_NO,
          C_RIGHT_TYPE,
          C_RIGHT_OPERATOR,
          VC_RIGHT,
          L_ASSET_ID,
          L_BASECOMBI_ID,
          VC_RIGHTS_ID,
          L_BEGIN_DATE,
          L_END_DATE,
          L_GRANT_OPERATOR,
          L_INPUT_OPERATOR,
          L_GRANT_DATE,
          L_GRANT_TIME,
          C_STATUS
        from
          #{@table_space}toptempright#{@sid_suffix}
        where
          L_BEGIN_DATE >= #{days_of_data}
      SQL
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          # 当授权时间不存在时，跳出后面操作
          next unless r[7] && r[8]

          g_time = r[12].to_s
          g_time = '0' + g_time while g_time.size < 6

          if importer_config['temp_delete_record'] && r[13].to_i == 5
            temporary = Hspb::Temporary.find_or_create_by(
              account_code:            r[0],
              authorizer_account_code: r[9],
              operator_account_code:   r[10],
              operation_type:          r[2],
              permission_type:         r[1].to_i,
              temp_start_date:         Date.parse(r[7].to_s),
              temp_end_date:           Date.parse(r[8].to_s),
              authorize_time:          Time.parse(r[11].to_s + g_time),
              permission_code:         r[3],
              unit_code:               r[4],
              combination_code:        r[5],
              addition_code:           r[6],
              authorize_status:        r[13]
            )
            destroy_data = find_destroy_data(temporary)
            temporary.update(destroy_time: destroy_data[:L_TIME]) if destroy_data
          else
            temporary = Hspb::Temporary.find_or_create_by(
              account_code:            r[0],
              authorizer_account_code: r[9],
              operator_account_code:   r[10],
              operation_type:          r[2],
              permission_type:         r[1].to_i,
              temp_start_date:         Date.parse(r[7].to_s),
              temp_end_date:           Date.parse(r[8].to_s),
              authorize_time:          Time.parse(r[11].to_s + g_time),
              permission_code:         r[3],
              unit_code:               r[4],
              combination_code:        r[5],
              addition_code:           r[6]
            )
            temporary.update(
              authorize_status: r[13]
            )
          end
        end
      end
    end

    def find_destroy_data(temporary)
      if temporary.permission_type == 1
        t_data = [
          temporary.account_code,
          '2',
          temporary.permission_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.delete('-'),
          (temporary.temp_end_date.to_s.delete('-').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..6] == t_data }
      elsif temporary.permission_type == 0
        t_data = [
          temporary.account_code,
          '1',
          temporary.permission_code,
          temporary.unit_code,
          temporary.combination_code,
          temporary.addition_code,
          temporary.temp_start_date.to_s.delete('-'),
          (temporary.temp_end_date.to_s.delete('-').to_i - 1).to_s,
          temporary.authorizer_account_code
        ]
        @destroy_datas.find { |x| x[:VC_REMARK][0..8] == t_data }
      end
    end

    def get_destroy_datas
      sql = <<-SQL
        select
          L_SERIAL_NO, L_OPERATOR_NO, L_DATE, L_TIME, VC_REMARKS
        from
          #{@table_space}thissystemlog#{@sid_suffix}
        where
          VC_OPCONTENT = '删除临时授权,[复核通过]'
        ORDER BY L_SERIAL_NO ASC
      SQL
      output_datas = []
      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[4].to_s.include?('[续前一条]')
            output_datas.last[:VC_REMARKS] = output_datas.last[:VC_REMARKS] + r[4].gsub('[续前一条]', '')
          else
            g_time = r[3].to_s
            g_time = '0' + g_time while g_time.size < 6
            output_datas << {
              L_SERIAL_NO:   r[0],
              L_OPERATOR_NO: r[1],
              L_TIME:        Time.parse(r[2].to_s + g_time),
              VC_REMARKS:    r[4]
            }
          end
        end
      end

      destroy_datas_list = []
      output_datas.each do |output_data|
        output_data[:VC_REMARKS].split('][').each do |remark|
          destroy_datas_list << {
            L_SERIAL_NO:   output_data[:L_SERIAL_NO],
            L_OPERATOR_NO: output_data[:L_OPERATOR_NO],
            L_TIME:        output_data[:L_TIME],
            VC_REMARK:     remark.split(',').map { |y| y.split('：')[1].to_s }
          }
        end
      end

      destroy_datas_list
    end

    def update_temp_right(perm, start_date, end_date, grant_account)
      perm.is_temp                 = true
      perm.temp_start_date         = start_date
      perm.temp_end_date           = end_date
      perm.authorizer_account_id   = grant_account.id
      perm.authorizer_account_name = grant_account.name
      perm.save
    end

    def class_exists?(class_name)
      klass = Module.const_get(class_name)
      klass.first
      klass.is_a?(Class)
    rescue NameError
      false
    end
  end
end
