# frozen_string_literal: true

module AasOracleImporter
  # 财务系统导入的类
  class GreatlifeCaiwuImporter < ImporterBase
    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @table_accounts = importer_config['table_accounts']
      @accounts    = []
      @roles       = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}cw_fnd_user#{@sid_suffix}"
      # @table_role         = "#{@table_space}#{@sid_suffix}"
      # @table_account_role = "#{@table_space}#{@sid_suffix}"
      @table_menu         = "#{@table_space}cw_fnd_responsibility_vl#{@sid_suffix}"
      @table_account_menu = "#{@table_space}cw_fnd_user_resp_groups_all#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_data1_permissions
      import_data1_account_permissions
      import_ledgers(GreatlifeCaiwu::Account)
    end

    def destroy_exist_datas
      accounts = GreatlifeCaiwu::Account.where(quarter_id: @quarter_id)
      GreatlifeCaiwu::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      GreatlifeCaiwu::Role.where(quarter_id: @quarter_id).delete_all
      GreatlifeCaiwu::Data1Permission.where(quarter_id: @quarter_id).delete_all
      GreatlifeCaiwu::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    private

    def import_accounts
      GreatlifeCaiwu::Account.where(quarter_id: @quarter_id).destroy_all

      sql = "select user_name, description, end_date, user_id from #{@table_accounts}"

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |row|
          import_a_account(row)
        end
      end
    end

    def import_a_account(row)
      code, name, fired_date, user_id = row

      unless code && name
        @logger.warn "#{self.class}: not found account code '#{code}' or name '#{name}' in #{row.join}"
        return
      end

      # 离职日期为空的账号为启用状态
      # 或判断离职时间是否晚于当前时间
      status = fired_date.nil? ? true : fired_date.to_time > Time.now

      account = GreatlifeCaiwu::Account.create(
        quarter_id: @quarter_id,
        code:       code,
        name:       name.strip,
        status:     status,
        source_id:  user_id
      )
      @accounts << account
    end

    def import_data1_permissions_sql
      <<-EOF
        select RESPONSIBILITY_ID, RESPONSIBILITY_NAME, END_DATE from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_data1_permissions_sql) do |r|
          code, name, end_date = r[0], r[1], r[2]
          next unless end_date.nil? || end_date.to_date > Date.current

          json = {
            quarter_id:  @quarter_id,
            source_id:   code,
            code:        code,
            level1_name: name
          }
          @data1_permissions << GreatlifeCaiwu::Data1Permission.create(json)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select user_id, RESPONSIBILITY_ID, END_DATE from #{@table_account_menu}
      EOF
    end

    def import_data1_account_permissions
      @database.exec(import_data1_account_permissions_sql) do |r|
        end_date = r[2]
        next unless end_date.nil? || end_date.to_date > Date.current

        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        # r[1] 读取出来的数据是 0.50772e5，需要to_i
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1]&.to_i&.to_s }
        next unless account && permission

        GreatlifeCaiwu::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

  end
end
