module AasOracleImporter
  class HsTouyanImporter < ImporterBase
    def config
      @bs_id       = 323
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables; end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_data1_account_permissions

      import_ledgers(HsTouyan::Account)
    end

    def destroy_exist_datas
      accounts = HsTouyan::Account.where(quarter_id: @quarter_id)
      HsTouyan::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HsTouyan::Role.where(quarter_id: @quarter_id).delete_all

      HsTouyan::Data1Permission.where(quarter_id: @quarter_id).delete_all

      HsTouyan::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all

      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select user_id, user_id, user_name, user_status from bizframe.tsys_user
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '0', 'enum_value' => '正常', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << HsTouyan::Account.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2],

            status:     r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_code, role_code, role_name from bizframe.tsys_role
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << HsTouyan::Role.create(
            quarter_id: @quarter_id,

            source_id:  r[0],

            code:       r[1],

            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_code, user_code from bizframe.tsys_role_user
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          HsTouyan::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    # kind_code
    # 场外:       3379
    # 信评:       CRS5
    # 投研:       3353
    # 投决:       FAIS
    # 工作流:     workflow
    # 任务调度:   scheduler_ui
    # 操纵员中心: BIZFRAME

    def import_data1_permissions_sql
      <<-EOF
        select
          concat(m.menu_code, b.sub_trans_code),
          concat(m.menu_code, b.sub_trans_code),
          m.kind_code,
          m.menu_name,
          b.sub_trans_name
        from
          bizframe.tsys_menu m
        left join
          bizframe.tsys_subtrans b
        on b.trans_code = m.menu_code
      EOF
    end
    # where m.trans_code like '3328%'
    # and m.trans_code != '3328'

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'level3_name', 'enum_value' => '场外', 'value' => '3379', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '信评', 'value' => 'CRS5', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '投研', 'value' => '3353', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '投决', 'value' => 'FAIS', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '工作流', 'value' => 'workflow', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '任务调度', 'value' => 'scheduler_ui', 'label' => '权限标记' },
                 { 'name' => 'level3_name', 'enum_value' => '操纵员中心', 'value' => 'BIZFRAME', 'label' => '权限标记' }]

        level1_name_index = 3
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   r[0],

            code:        r[1],

            level3_name: get_enum(enums, 'level3_name', r[2]),

            level1_name: level1_name,

            level2_name: r[4]

          }
          @data1_permissions << HsTouyan::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select
          role_code,
          concat(trans_code, sub_trans_code)
        from
          bizframe.tsys_role_right
        where right_flag = '1'
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          HsTouyan::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select
          user_id,
          concat(trans_code, sub_trans_code)
        from
          bizframe.tsys_user_right
        where right_flag = '1'
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        HsTouyan::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id)
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
