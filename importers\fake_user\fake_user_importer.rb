# frozen_string_literal: true

module AasOracleImporter
  # 模拟员工导入状态，测试 QuarterUserImportStatus
  # 根据 expect_status 选择导入成功或失败
  # success 为成功，其余字符为失败
  class FakeUserImporter < ImporterBase
    def config
      @bs_id         = importer_config['bs_id']
      @expect_status = importer_config['expect_status']
    end

    def import_to_do
      return if @expect_status == 'success'

      raise "FakeUser import failed."
    end
  end
end
