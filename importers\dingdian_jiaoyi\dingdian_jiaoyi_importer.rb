module AasOracleImporter
  class DingdianJiaoyiImporter < ImporterBase
    def config
      @bs_id       = 315
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account       = "#{@table_space}TGYXX#{@sid_suffix}"
      @table_role          = "#{@table_space}TJSXX#{@sid_suffix}"
      @table_account_role  = "#{@table_space}TYHJSDY#{@sid_suffix}"
      @table_menu          = "#{@table_space}TMENU#{@sid_suffix}"
      @table_role_menu     = "#{@table_space}TCDQX_JS#{@sid_suffix}"
      @table_account_menu  = "#{@table_space}TCDQX#{@sid_suffix}"
      @table_menu2         = "#{@table_space}TJGGL#{@sid_suffix}"
      @table_account_menu2 = "#{@table_space}TGYJBQX#{@sid_suffix}"
      @table_org           = "#{@table_space}TJGGL#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions

      import_ledgers(DingdianJiaoyi::Account)
    end

    def destroy_exist_datas
      DingdianJiaoyi::Department.where(quarter_id: @quarter_id).delete_all
      accounts = DingdianJiaoyi::Account.where(quarter_id: @quarter_id)
      DingdianJiaoyi::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      DingdianJiaoyi::Role.where(quarter_id: @quarter_id).delete_all
      DingdianJiaoyi::Data1Permission.where(quarter_id: @quarter_id).delete_all
      DingdianJiaoyi::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      DingdianJiaoyi::Data2Permission.where(quarter_id: @quarter_id).delete_all
      DingdianJiaoyi::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select jgdm, jgdm, jgmc, sjjgdm from #{@table_org}"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        DingdianJiaoyi::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end

      # 建联关联关系
      DingdianJiaoyi::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = DingdianJiaoyi::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      DingdianJiaoyi::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_accounts_sql
      <<-EOF
        select LOGINID, GYDM, XM, ZT, JGDM from #{@table_account}
      EOF
    end

    def import_accounts
      @departments = DingdianJiaoyi::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          @accounts << DingdianJiaoyi::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '0',
            department_id: department&.id
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select JSDM, JSDM, JSMC, JSLB from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << DingdianJiaoyi::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            role_type:  role_type_text(r[3])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select JSDM, LOGINID from #{@table_account_role} where SHBZ IN (1,2,3)
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          DingdianJiaoyi::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select XTDM, XTDM, CDMC, CDYWLB from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << DingdianJiaoyi::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select JSDM, XTDM, QXLB from #{@table_role_menu} where SHBZ in (1,2,3)
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          DingdianJiaoyi::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(r[2])
          )
        end
      end
    end

    # YXRQ 是有效日期，0 为始终有效，其他值如********为有效截止日期
    def import_data1_account_permissions_sql
      <<-EOF
        select LOGINID, XTDM, QXLB, YXRQ from #{@table_account_menu} where SHBZ in (1,2,3)
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        yxrq       = r[3].to_s
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        # 校验有效性
        next unless yxrq == '0' || yxrq.empty? || (yxrq.length == 8 && yxrq.to_date >= Date.current)

        DingdianJiaoyi::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: additional_permission_text(r[2])
        )
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select JGDM, JGDM, JGMC, SJJGDM from #{@table_menu2}
      EOF
    end

    def import_data2_permissions
      parent_id_index = 3
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data2_permissions_sql, name, parent_id_value, 2, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << DingdianJiaoyi::Data2Permission.create(json)
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select GYDM, CZQX_YYB from #{@table_account_menu2} where SHBZ IN (1,2,3)
      EOF
    end

    def import_data2_account_permissions
      data = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account = @accounts.find { |x| x.code.to_s == r[0].to_s }
        next unless account

        permission_codes = r[1].to_s.split(',')
        permissions = @data2_permissions.select { |x| permission_codes.include? x.code.to_s }
        permissions.each do |permission|
          next unless permission

          data << [account.id, permission.id]
        end
      end

      DingdianJiaoyi::Data2AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data2_permission_id) do |obj|
        obj.set_size = 1000
        data.each do |o|
          obj.add [@quarter_id, o[0], o[1]]
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]

        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def role_type_text(name)
      case name.to_s
      when '1' then '总部角色'
      when '2' then '区域管理中心角色'
      when '3' then '营业部及服务部角色'
      else
        name
      end
    end

    def additional_permission_text(name)
      case name.to_s
      when '1' then '可以操作'
      when '2' then '可以授权'
      when '3' then '可以审核'
      when '4' then '可以审核'
      else
        name
      end
    end
  end
end
