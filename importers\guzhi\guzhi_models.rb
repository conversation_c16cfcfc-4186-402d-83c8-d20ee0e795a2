module G<PERSON>hi
  def self.table_name_prefix
    'guzhi_'
  end
end

class Guzhi::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :booksets
end

class Guzhi::Bookset < ActiveRecord::Base
  has_and_belongs_to_many :accounts
end

class Guzhi::Menu < ActiveRecord::Base
end

class Guzhi::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_many :permissions
end

class Guzhi::Permission < ActiveRecord::Base
  belongs_to :role
  belongs_to :menu
end

class GuzhiAccountsRoles < ActiveRecord::Base; end
class GuzhiAccountsBooksets < ActiveRecord::Base; end
