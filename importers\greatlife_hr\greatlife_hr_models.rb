class InsuranceHrAccount < ActiveRecord::Base; end
class InsuranceHrDepartment < ActiveRecord::Base; end

class User < ActiveRecord::Base
  belongs_to :department, optional: true
end
class Department < ActiveRecord::Base
  has_many :users
  has_many :children, class_name: "Department", foreign_key: "parent_id"
  belongs_to :parent, class_name: "Department", optional: true
end

module GreatlifeHr
  def self.table_name_prefix
    'greatlife_hr_'
  end
end

class GreatlifeHr::Account < ActiveRecord::Base; end

