module AasOracleImporter
  class DingdianCifImporter < ImporterBase
    def config
      @bs_id       = 317
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}tuser#{@sid_suffix}"
      @table_role         = "#{@table_space}LBROLE#{@sid_suffix}"
      @table_account_role = "#{@table_space}LBMEMBER#{@sid_suffix}"
      @table_menu         = "#{@table_space}LBFUNDEFINITION#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}lbfunpermission#{@sid_suffix}"
      @table_menu2        = "#{@table_space}lborganization#{@sid_suffix}"
      @table_role_menu2   = "#{@table_space}lbDataScopeAuth#{@sid_suffix}"
      @table_org          = "#{@table_space}lborganization#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      # import_data1_department_permissions

      import_data2_permissions
      import_data2_role_permissions
      import_data2_account_permissions
      # import_data2_department_permissions

      import_ledgers(DingdianCif::Account)
    end

    def destroy_exist_datas
      DingdianCif::Department.where(quarter_id: @quarter_id).delete_all
      accounts = DingdianCif::Account.where(quarter_id: @quarter_id)
      DingdianCif::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      DingdianCif::Role.where(quarter_id: @quarter_id).delete_all
      DingdianCif::Data1Permission.where(quarter_id: @quarter_id).delete_all
      DingdianCif::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      DingdianCif::Data2Permission.where(quarter_id: @quarter_id).delete_all
      DingdianCif::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select ID, ORGCODE, NAME, FID from #{@table_org} where (zt is null or zt = 0)"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        DingdianCif::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end
      # 建联关联关系
      DingdianCif::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = DingdianCif::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      DingdianCif::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    # ZT 账号状态，0为正常，STATUS 是否允许登录，1为允许登录，2为不允许登录
    # 正常账号状态判断逻辑为 允许登录并且状态为正常
    def import_accounts_sql
      <<-EOF
        select ID, USERID, NAME, ZT, ORGID, STATUS from #{@table_account}
      EOF
    end

    def import_accounts
      @departments = DingdianCif::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          status = r[3]&.to_s == '0' && r[5] == 1
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          @accounts << DingdianCif::Account.create(
            quarter_id:        @quarter_id,
            source_id:         r[0],
            code:              r[1],
            name:              r[2],
            status:            status,
            organization_code: r[4],
            department_id:     department&.id
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        SELECT ID, ROLECODE, NAME FROM #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << DingdianCif::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLEID, USERID from #{@table_account_role} where AUDITFLAG=1
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          DingdianCif::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select ID, NAME, DESCRIBE, FID from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        parent_id_index = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, 2, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << DingdianCif::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      date = Date.current.to_s
      <<-EOF
        select MEMBERID, FUNNAME from #{@table_role_menu} where TYPE = 1 and AUDITFLAG=1 and (ENDDATE <= '#{date}' OR ENDDATE IS NULL OR ENDDATE = NULL)
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
          next unless permission && role

          DingdianCif::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      date = Date.current.to_s
      <<-EOF
        select MEMBERID, FUNNAME from #{@table_role_menu} where TYPE = 0 and AUDITFLAG=1 and (ENDDATE <= '#{date}' OR ENDDATE IS NULL OR ENDDATE = NULL)
      EOF
    end

    def import_data1_department_permissions_sql
      date = Date.current.to_s
      <<-EOF
        select MEMBERID, FUNNAME from #{@table_role_menu} where TYPE = 2 and AUDITFLAG=1 and (ENDDATE <= '#{date}' OR ENDDATE IS NULL OR ENDDATE = NULL)
      EOF
    end

    def import_data1_account_permissions
      data = []
      # 导入账户权限关系
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
        next unless account && permission

        data << [account.id, permission.id]
      end

      # 导入部门对应的账户的权限关系，这里要去重
      select_db_datas(import_data1_department_permissions_sql).each do |r|
        accounts   = @accounts.select { |x| x.organization_code.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.code.to_s == r[1].to_s }
        next unless permission

        accounts.each do |account|
          data << [account.id, permission.id]
        end
      end
      DingdianCif::Data1AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data1_permission_id) do |obj|
        obj.set_size = 1000
        data.uniq.each do |o|
          obj.add [@quarter_id, o[0], o[1]]
        end
      end
    end

    # 注意sql条件语句需要加括号，当获取上一级数据的时候，条件要和括号的语句并列
    def import_data2_permissions_sql
      <<-EOF
        select ID, ORGCODE, NAME, FID from #{@table_menu2} where (zt is null or zt = 0)
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        parent_id_index = 3
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data2_permissions_sql, name, parent_id_value, 2, parent_id_index)
          level1_name = replace_blank_name(full_name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << DingdianCif::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select MEMBERID, SCOPEEXP from #{@table_role_menu2} where TYPE = 1 and AUDITFLAG=1
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          DingdianCif::Data2AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data2_permission_id: permission.id
          )
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select MEMBERID, SCOPEEXP from #{@table_role_menu2} where TYPE = 0 and AUDITFLAG=1
      EOF
    end

    def import_data2_department_permissions_sql
      <<-EOF
        select MEMBERID, SCOPEEXP from #{@table_role_menu2} where TYPE = 2 and AUDITFLAG=1
      EOF
    end

    def import_data2_account_permissions
      data = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        data << [account.id, permission.id]
      end

      select_db_datas(import_data2_department_permissions_sql).each do |r|
        accounts   = @accounts.select { |x| x.organization_code.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless permission

        accounts.each do |account|
          data << [account.id, permission.id]
        end
      end

      DingdianCif::Data2AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data2_permission_id) do |obj|
        obj.set_size = 1000
        data.each do |o|
          obj.add [@quarter_id, o[0], o[1]]
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    # 菜单名称中会有根菜单这样的数据
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        return name if parent_name == '根菜单'

        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def scope_type_text(name)
      case name.to_s
      when '0' then '对象'
      when '1' then '数据字典'
      else
        name
      end
    end
  end
end
