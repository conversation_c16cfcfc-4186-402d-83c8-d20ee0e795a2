module <PERSON><PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'guohu_zd_'
  end
end

class GuohuZd::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuZd::OtherPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuZd::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class GuohuZd::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :other_permissions
end

class GuohuZdAccountsRoles < ActiveRecord::Base; end
class GuohuZdMenusRoles < ActiveRecord::Base; end
class GuohuZdAccountsMenus < ActiveRecord::Base; end
class GuohuZdAccountsOtherPermissions < ActiveRecord::Base; end
class GuohuZdOtherPermissionsRoles  < ActiveRecord::Base; end
