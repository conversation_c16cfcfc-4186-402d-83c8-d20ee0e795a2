module AasOracleImporter
  class DingdianLiangrongImporter < ImporterBase
    def config
      @bs_id       = 256
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}tmc_gyxx#{@sid_suffix}"
      @table_role         = "#{@table_space}tmc_jsxx#{@sid_suffix}"
      @table_account_role = "#{@table_space}tmc_yhjsdy#{@sid_suffix}"
      @table_menu         = "#{@table_space}tmc_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}tmc_jscdqx#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_ledgers(DingdianLiangrong::Account)
    end

    def destroy_exist_datas
      accounts = DingdianLiangrong::Account.where(quarter_id: @quarter_id)
      DingdianLiangrong::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      DingdianLiangrong::Role.where(quarter_id: @quarter_id).delete_all
      DingdianLiangrong::Data1Permission.where(quarter_id: @quarter_id).delete_all
      DingdianLiangrong::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select GYDM, GYDM, XM, ZT from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          account = DingdianLiangrong::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'DingdianLiangrong::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select JSDM, JSDM, JSMC from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << DingdianLiangrong::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select JSDM, GYDM from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          DingdianLiangrong::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      # WZDM 字段用于判断祖先菜单
      <<-EOF
        select CDDM, CDDM, CDMC, CDSX, WZDM from #{@table_menu}
      EOF
    end

    def import_data1_permissions
      # 获取所有菜单，用于查找当前菜单的祖先菜单
      full_menus = select_db_datas(import_data1_permissions_sql)
      full_menus = format_full_menus(full_menus)
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = full_name(r, full_menus)
          # name = r[2]
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << DingdianLiangrong::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select JSDM, CDDM, CDSX from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          DingdianLiangrong::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id,
            permission_scope:    menu_attr_text(r[2])
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    def full_name(menu, full_menus)
      menus = tree_menus(menu, full_menus)
      menus.reverse.map { |o| o[2] }.join(' -> ')
    end

    # 找到菜单的同层级的第一个菜单
    # 通过第一个菜单才能找上一级
    def previous_menu(menu, full_menus)
      code          = menu[0]
      previous_menu = full_menus.find { |o| o[4]&.[](1).to_s.strip == code.strip }
      return menu if previous_menu.nil?

      previous_menu(previous_menu, full_menus)
    end

    # 找到上级菜单
    def parent_menu(menu, full_menus)
      code = menu[0]
      full_menus.find { |o| o[4]&.[](2).to_s.strip == code.strip }
    end

    # 树形菜单
    def tree_menus(menu, full_menus, menus = [])
      return menus if menu.blank?

      menus << menu
      first_menu  = previous_menu(menu, full_menus)
      parent_menu = parent_menu(first_menu, full_menus)
      tree_menus(parent_menu, full_menus, menus)
    end

    # 格式化full_menus，用于运算树形菜单
    def format_full_menus(full_menus)
      full_menus.map do |menu|
        tree_code = menu[4].to_s
        menu[4]   = [tree_code.first, tree_code[1, 6], tree_code.from(7)]
        menu
      end
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 菜单属性
    def menu_attr_text(attr)
      case attr
      when 0 then '查询权限'
      when 1 then '维护权限'
      end
    end
  end
end
