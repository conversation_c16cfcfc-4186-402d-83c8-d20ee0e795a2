module AasOracleImporter
  class JzBaobiaoImporter < ImporterBase
    def config
      @bs_id       = 285
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @db_type     = importer_config['db_type']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      @additional_permissions = []
      @data2_permissions_data1 = []
      @data2_permissions_data2 = []
      @role_groups = []
      @account_orgs = []
      @new_task_data = []

      initialize_tables
    end

    def initialize_tables
      @table_user          = "#{@table_space}aus_authone#{@sid_suffix}"
      @table_account       = "#{@table_space}cif_cust#{@sid_suffix}"
      @table_role          = "#{@table_space}acl_role#{@sid_suffix}"
      @table_account_role  = "#{@table_space}cif_cust_role#{@sid_suffix}"
      @table_menu          = "#{@table_space}acl_menu#{@sid_suffix}"
      @table_role_menu     = "#{@table_space}acl_role_menu#{@sid_suffix}"
      @table_account_menu  = "#{@table_space}acl_role_menu#{@sid_suffix}"
      # @table_account_menu2 = "#{@table_space}cif_cust_menu#{@sid_suffix}"
      @table_role_groups   = "#{@table_space}acl_role_group#{@sid_suffix}"
      @table_org           = "#{@table_space}acl_department#{@sid_suffix}"
      @table_account_org   = "#{@table_space}cif_cust_department#{@sid_suffix}"
      @table_department1   = "#{@table_space}ACL_DEPARTMENT#{@sid_suffix}"
      @table_department2   = "#{@table_space}TASK_CUSTOM_DEPARTMENT#{@sid_suffix}"
      @table_task_relation   = "#{@table_space}T_TASK_RELATION_INFO_INST#{@sid_suffix}" # 任务部门参与人表,部门信息
      @table_task_node   = "#{@table_space}TASK_NODE_DEPARTMENT_INFO_INST#{@sid_suffix}" # 任务部门表,部门节点信息
      @table_task_info   = "#{@table_space}T_TASK_INFO_INSTANCE#{@sid_suffix}" # 任务实例,记录任务名称和报告期
      @table_act_re   = "#{@table_space}ACT_RE_PROCDEF#{@sid_suffix}" # 流程定义表,记录流程图信息
      @table_bpm_process   = "#{@table_space}BPM_PROCESS_NODE#{@sid_suffix}" # 流程节点表
      @table_task_dept_role = "#{@table_space}task_dept_role_user_group#{@sid_suffix}"
      @table_department_role_group = "#{@table_space}acl_department_role_group#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_account_orgs
      import_accounts
      import_role_groups
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_newest_tasks # 获取最新的任务
      import_data2_permissions
      import_data2_additional_permissions
      import_data2_account_permissions
      # import_other_data1_account_permissions

      import_ledgers(JzBaobiao::Account)
    end

    def destroy_exist_datas
      JzBaobiao::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JzBaobiao::Account.where(quarter_id: @quarter_id)
      JzBaobiao::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzBaobiao::Role.where(quarter_id: @quarter_id).delete_all
      JzBaobiao::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzBaobiao::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JzBaobiao::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JzBaobiao::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_orgs_sql
      "select department_id, department_id, department_name, parent_department_id from #{@table_org} where status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzBaobiao::Department.create(
          quarter_id: @quarter_id,
          source_id:  r[0],
          code:       r[1],
          name:       r[2],
          status:     true
        )
      end
      # 建联关联关系
      JzBaobiao::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzBaobiao::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzBaobiao::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end
    end

    def import_account_orgs_sql
      "select customer_no, department_id from #{@table_account_org}"
    end

    # 导入账号和机构关联关系
    def import_account_orgs
      select_db_datas(import_account_orgs_sql).each do |r|
        @account_orgs << [r[0], r[1]]
      end
    end

    def import_accounts_sql
      "select a.customer_no, u.LOGIN_NAME, a.customer_name, a.cust_status from #{@table_user} u, #{@table_account} a where u.customer_no = a.customer_no"
    end

    # 0是正常、1是锁定，这里锁定就是禁用
    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          source_department_id = @account_orgs.find { |x| x[0]&.to_s == r[0]&.to_s }&.[](1)
          department = source_department_id.present? ? JzBaobiao::Department.find_by(source_id: source_department_id) : nil
          account = JzBaobiao::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        %w[0].include?(r[3]&.to_s),
            department_id: department&.id
          )

          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzBaobiao::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_role_groups_sql
      "select group_id, group_name from #{@table_role_groups}"
    end

    def import_role_groups
      ActiveRecord::Base.transaction do
        select_db_datas(import_role_groups_sql).each do |r|
          @role_groups << [r[0], r[1]]
        end
      end
    end

    def import_roles_sql
      "select role_id, role_id, role_name, group_id from #{@table_role}"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          role_type = get_role_type(r[3])
          @roles << JzBaobiao::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            role_type:  role_type
          )
        end
      end
    end

    def import_accounts_roles_sql
      "select role_id, customer_no from #{@table_account_role}"
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          JzBaobiao::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      "select menu_id, menu_id, menu_name, endtype, parent_menu_id from #{@table_menu} where isonline = '1'"
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          parent_id_value = r[4]
          name = full_name(import_data1_permissions_sql, r[2], parent_id_value, 2, 4)
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: permission_type_text(r[3])
          }
          @data1_permissions << JzBaobiao::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      "select role_id, menu_id from #{@table_role_menu} where menu_perm_type = '0'"
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzBaobiao::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      "select role_id, menu_id from #{@table_account_menu} where menu_perm_type = '1'"
    end

    # menu_perm_type为1时，role_id对应cif_cust的customer_no
    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzBaobiao::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

    # def import_other_data1_account_permissions_sql
    #   "select customer_no, menu_id from #{@table_account_menu2}"
    # end

    # def import_other_data1_account_permissions
    #   select_db_datas(import_other_data1_account_permissions_sql).each do |r|
    #     account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
    #     permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
    #     next unless account && permission

    #     JzBaobiao::Data1AccountsRolesPermission.find_or_create_by(
    #       quarter_id:          @quarter_id,
    #       account_id:          account.id,
    #       data1_permission_id: permission.id
    #     )
    #   end
    # end
    def import_newest_tasks_sql
      "select t.TASK_ID, max(PERIOD) from #{@table_task_info} t group by t.TASK_ID"
    end

    def import_newest_tasks
      select_db_datas(import_newest_tasks_sql).each do |r|
        @new_task_data << [r[0], r[1]]
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select t.DEPARTMENT_ID, t.DEPARTMENT_NAME from #{@table_department1} t
        union
        select t2.CUSTOM_DEPARTMENT_ID, t2.CUSTOM_DEPARTMENT_NAME from #{@table_department2} t2
      EOF
      # where t2.TASK_ID='' and t2.CUSTOM_DEPARTMENT_ID =''
    end

    def import_data2_permissions
      # 去重保存部门为菜单
      select_db_datas(import_data2_permissions_sql).each do |r|
        code, name = r[0], r[1]
        json = {
          quarter_id:  @quarter_id,
          code:        code,
          level1_name: name
        }
        @data2_permissions << JzBaobiao::Data2Permission
                                .create_with(source_id: code)
                                .find_or_create_by(json)
      end

      # 记录部门ID、任务ID、部门名称的关系
      sql1 = "select t.DEPARTMENT_ID, t.DEPARTMENT_NAME from #{@table_department1} t"
      sql2 = "select t2.TASK_ID, t2.CUSTOM_DEPARTMENT_ID, t2.CUSTOM_DEPARTMENT_NAME from #{@table_department2} t2"
      select_db_datas(sql1).each do |r|
        @data2_permissions_data1 << r
      end

      select_db_datas(sql2).each do |r|
        @data2_permissions_data2 << r
      end

    end

    def import_data2_additional_permissions_sql
      <<-EOF
        select r.TASK_ID, r.PERIOD, r.DEPARTMENT_ID, r.STEP_ID, nvl(n.TASK_DEF_NAME_,decode(r.STEP_TYPE,'0','审核','1','填报','2','审核+填报')) from #{@table_task_node} d
        left join #{@table_task_relation} r on d.TASK_ID=r.TASK_ID and d.PERIOD=r.PERIOD and d.DEPARTMENT_ID=r.DEPARTMENT_ID
        left join #{@table_act_re} f on d.PROC_DEF=f.ID_
        left join #{@table_bpm_process} n on n.PROC_DEF_ID_=f.DEPLOYMENT_ID_ and r.STEP_ID=n.TASK_DEF_KEY_ and n.TASK_DEF_TYPE_='userTask'
      EOF
        # where r.TASK_ID='' and r.PERIOD='' and r.DEPARTMENT_ID='' and r.STEP_ID=''
    end

    def import_data2_additional_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_additional_permissions_sql).each do |r|
          @additional_permissions << [r[0], r[1], r[2], r[3], r[4]]
        end
      end
    end

    # 通过最新任务的task_id和period生成sql条件语句
    def relation_sql_str
      @new_task_data.map { |x| "t.task_id = #{x[0]} and t.period = #{x[1]}" }.join(' or ')
    end

    def import_data2_account_permissions_sql
      where_sql = relation_sql_str.present? ? "where (#{relation_sql_str})" : ''
      <<-EOF
        select t1.relate_no, t.task_id, t.period, t1.DEPARTMENT_ID, t1.step_id
        from #{@table_task_info} t
        inner join #{@table_task_relation} t1 on t.task_id = t1.task_id and t.period = t1.period and t1.relate_type='1'
        #{where_sql}
      EOF
      # where t1.relate_no =#{customerno}
    end

    def import_data2_account_permissions_sql2
      where_sql = relation_sql_str.present? ? "(#{relation_sql_str})" : ''
      <<-EOF
        SELECT t2.customer_no "customer_no",
               t.task_id "taskId",
               t.period "period",
               t1.DEPARTMENT_ID "departmentId",
               t1.step_id "stepId"
        FROM #{@table_task_info} t
                 INNER JOIN #{@table_task_relation} t1
                            ON t.task_id = t1.task_id
                                and t.period = t1.period
                                and t1.relate_type = '0'
                 INNER JOIN #{@table_account_role} t2
                            ON t1.relate_no = t2.role_id
                 INNER JOIN #{@table_task_node} ad
                            ON ad.task_id = t1.task_id
                                and ad.department_id = t1.department_id
                                and ad.period = t1.period
                 LEFT JOIN #{@table_account_org} ccd
                           ON ccd.company_id = t1.department_id
                               and t2.customer_no = ccd.customer_no
        where #{where_sql}
          and ad.custom_flag = '0' and t1.STEP_COMPANY_TYPE != '0'
          and ccd.department_id is not null
          and not exists (select 1 from #{@table_task_dept_role} ag
            inner join #{@table_department_role_group} ar on ag.group_id = ar.group_id
            where ar.department_id = t1.DEPARTMENT_ID and t1.task_id = ag.task_id and t1.RELATE_NO = ar.role_id)

      EOF
        # where  ad.custom_flag = '0' and t2.customer_no = 5093 and t1.STEP_COMPANY_TYPE != '0'
    end

    def import_data2_account_permissions
      data = []
      permission_data = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        permission_data << r
      end

      select_db_datas(import_data2_account_permissions_sql2).each do |r|
        permission_data << r
      end
      permission_data.uniq!

      account_codes = permission_data.map { |x| x[0] }.uniq
      account_codes.each do |account_code|
        account = @accounts.find { |x| x.source_id.to_s == account_code.to_s }
        account_permission_data = permission_data.select { |x| x[0] == account_code }
        account_permission_data.each do |y|
          task_id, period, department_id, step_id = y[1], y[2], y[3], y[4]
          name1 = @data2_permissions_data1.find { |x| x[0] == department_id }&.[](1)
          name2 = @data2_permissions_data2.find { |x| x[0] == task_id && x[1] == department_id }&.[](2)
          permission_names = [name1, name2].compact.uniq
          permission_name = permission_names.first
          permission = @data2_permissions.find { |x| x.level1_name == permission_name }
          next unless account && permission

          additional_permissions = @additional_permissions.select do |x|
            x[0] == task_id && x[1] == period && x[2] == department_id && x[3] == step_id
          end
          additional_permission_name = additional_permissions.map { |x| x[4] }.compact.uniq.join('、')
          data << [account.id, permission.id, additional_permission_name]
        end
      end

      JzBaobiao::Data2AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data2_permission_id, :permission_scope) do |obj|
        obj.set_size = 1000
        data.uniq.each do |x|
          obj.add [@quarter_id, x[0], x[1], x[2]]
        end
      end
    end

    # 权限类型
    def permission_type_text(code)
      case code.to_s
      when '0' then '服务平台'
      when '1' then '管理平台'
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      limit = "#{@db_type.to_s == 'oracle' ? 'and rownum = 1' : 'limit 1'}"
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' #{limit}"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 角色分组
    def get_role_type(group_id)
      @role_groups.find { |x| x[0].to_s == group_id.to_s }&.[](1)
    end
  end
end
