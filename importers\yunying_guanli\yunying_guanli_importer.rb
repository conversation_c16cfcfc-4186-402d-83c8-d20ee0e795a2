module AasOracleImporter

  class YunyingGuanliImporter < ImporterBase

    def config
      @bs_id = 10007
      @accounts = []
      @roles = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @import_report  = importer_config['import_report']
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_reports if @import_report
      # import_data1_account_permissions

      import_ledgers(YunyingGuanli::Account)
    end

    
    def import_accounts_sql
      <<-EOF
        select fUserID,fUserCode,fUserName,fStatus from #{@table_space}zo_m_user#{@sid_suffix}
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << YunyingGuanli::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[1]),
            name: get_enum(enums, "name", r[2]),
            status: r[3].to_s == '0',
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select fGroupID,fGroupName from #{@table_space}zo_m_group#{@sid_suffix}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << YunyingGuanli::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[0]), name: get_enum(enums, "name", r[1]))
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select fGroupID,fUserID from #{@table_space}zo_m_usergroup#{@sid_suffix}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            YunyingGuanli::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select fMenuCode,fMenuName,fParentCode,fSystemName from #{@table_space}zo_m_menu#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, r[1], r[2], 1, 2))
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: level1_name,
            level3_name: r[3]
          }
          @data1_permissions << YunyingGuanli::Data1Permission.create(json)
        end
      end
    end
    
    def import_data1_role_permissions_sql
      <<-EOF
        select fGroupID,fRightCode from #{@table_space}zo_m_groupright#{@sid_suffix}
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            YunyingGuanli::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end

    def import_reports
      reports = reports_json
      @disabled_reports = FundEmailsCheck::Report.where(business_system_id: @bs_id).pluck(:source_id)
      reports.each do |report|
        import_reports_line(report)
      end
      FundEmailsCheck::Report.where(business_system_id: @bs_id, source_id: @disabled_reports).each{|x| x.update(status: false)}
    end

    def import_reports_line(data)
      report = FundEmailsCheck::Report.find_or_initialize_by(source_id: data[:key], report_name: data[:title], business_system_id: @bs_id)
      report.receiver_email = set_email(data[:mail1],data[:mail2])
      report.status = true
      report.save
      @disabled_reports.delete(data[:key].to_s)
      if report.created_at.to_date == Date.today
        fund = get_fund(data[:key])
        report.funds << fund if fund && !report.funds.include?(fund)
        report.fund_count = report.funds.size
        report.save
      end
    end

    def set_email(mail1,mail2)
      mail1.to_s.gsub("\n","").gsub(" ", "").split(";") | mail2.to_s.gsub("\n","").gsub(" ", "").split(";")
    end

    def get_fund(key)
      codes = key.split("_")
      if codes.size > 1
        codes.each do |code|
          fund = FundEmailsCheck::Fund.find_by(code: code)
          return fund if fund
        end
      end

      codes = key.split("-")
      if codes.size > 1
        codes.each do |code|
          fund = FundEmailsCheck::Fund.find_by(code: code)
          return fund if fund
        end
      end

      codes = key.split("(")
      codes.each do |code|
        fund = FundEmailsCheck::Fund.find_by(code: code.split(")")[0])
        return fund if fund
      end

      return nil
    end

    def reports_json
      require 'rexml/document'
      datas = []
      importer_config['import_report_xmls'].each do |file_path|
        xmlfile = File.new(file_path)
        xmldoc = REXML::Document.new(xmlfile)

        xmldoc.elements.each("LCFUNDS/Group/TextMessage") do |e|
          json = {}
          json[:key] = e.attributes['key']
          json[:title] = e.attributes['title']
          json[:mail1] = e.elements['MAIL1'].text
          json[:mail2] = e.elements['MAIL2'].text
          datas << json
        end

      end
      datas
    end

    # def import_data1_account_permissions_sql
    #   <<-EOF
    #     select * from zo_m_groupright
    #   EOF
    # end

    # def import_data1_account_permissions
    #   enums = []
    #   select_db_datas(import_data1_account_permissions_sql).each do |r|
    #     account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
    #     permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
    #     if account && permission
    #       YunyingGuanli::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id)
    #     end
    #   end
    # end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end



