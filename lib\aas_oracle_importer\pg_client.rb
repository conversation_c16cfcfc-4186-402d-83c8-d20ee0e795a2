# frozen_string_literal: true

module AasOracleImporter
  # pg 客户端
  class PgClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      data = query(sql).map(&:values)
      if block_given?
        data.each do |row|
          yield row
        end
      else
        data
      end
    end

    def query(sql)
      data = []
      result = @database.query(sql)
      result.each { |x| data << x }
      data
    end

    private

    def initialize_driver
      load_driver_gem
      @database = PG.connect pg_client_params
    rescue PG::Error::ConnectionError => e
      raise PG::Error::ConnectionError, message_prefix + e.message
    end

    def pg_client_params
      pg_client_config.update({ password: ConvertTools::Cryptology.decrypt_if_env(pg_client_config[:password]) })
    end

    def pg_client_config
      {
        host:     database_info['db_host'],
        port:     database_info['db_port'] || 5432,
        dbname:   database_info['db_name'],
        user:     database_info['db_user'],
        password: database_info['db_pass']
      }
    end

    def load_driver_gem
      require 'pg'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'pg\'.')
      exit(-127)
    end
  end
end
