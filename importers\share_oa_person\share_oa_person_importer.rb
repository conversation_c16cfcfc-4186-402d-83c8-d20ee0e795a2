# frozen_string_literal: true

module AasOracleImporter
  # 国投共享目录导入
  class ShareOaPersonImporter < ImporterBase
    def config
      @bs_id                = importer_config['bs_id']
      @ad_group_id          = importer_config['ad_group_id']
      @file_path            = importer_config['file_path']
      @config_path          = importer_config['config_path']
      @ad_name              = importer_config['ad_name']
      @remark_array         = share_remark
      @share_pass           = share_pass
      @the_system           = BusinessSystem.find(@bs_id)
      @system_name          = @the_system.name
    end

    def import_to_do
      import_accounts
      import_roles
      import_permissions
      import_account_roles
      import_ledgers(ShareOaPerson::Account)
    end

    def import_accounts
      AdGroupOa::Account.where(quarter_id: @quarter_id).each do |ad_account|
        share_account = ShareOaPerson::Account.create(
          code: ad_account.code,
          name: ad_account.name,
          status: ad_account.status,
          data_json: {DC: @ad_name},
          quarter_id: @quarter_id
        )
      end
    end

    def import_roles
      AdGroupOa::Role.where(quarter_id: @quarter_id).each do |role|
        dc_code = role.data_json[:sAMAccountType] == "*********" ? 'BUILTIN' : @ad_name
        ShareOaPerson::Role.find_or_create_by(
          code: role.name,
          quarter_id: @quarter_id,
          name: role.name,
          data_json: {DC: dc_code}
        )
      end
    end

    def import_permissions
      date_now = (Time.now - 86400).strftime("%Y%m%d")
      file_name = `cd #{@file_path} && ls`.split("\n").select{|x| x.include?("#{date_now}.txt")}.first
      if file_name
        file = File.open("#{@file_path}/#{file_name}", 'r:bom|utf-8')
        output_datas = file.readlines.drop(0).join.force_encoding('gbk').encode('utf-8', replace: nil).split("\r\n")
        output_datas.each do |line|
          import_account_line(line)
        end
      end
    end

    def import_account_roles
      AdGroupOa::Account.where(quarter_id: @quarter_id).each do |ad_account|
        share_account = ShareOaPerson::Account.find_by(quarter_id: @quarter_id, code: ad_account.code)
        if share_account
          ad_account.roles.each do |ad_role|
            share_role = ShareOaPerson::Role.find_by(quarter_id: @quarter_id, code: ad_role.name)
            share_account.roles << share_role if share_role && !share_account.roles.include?(share_role)
            ad_role.data_json[:memberOf].each do |memberOf|
              ad_memberOf = AdGroupOa::Role.find_by(quarter_id: @quarter_id, code: memberOf)
              if ad_memberOf
                share_role = ShareOaPerson::Role.find_by(quarter_id: @quarter_id, code: ad_memberOf.name)
                share_account.roles << share_role if share_role && !share_account.roles.include?(share_role)
              end
            end
          end
        end
      end
    end

    private

    def share_remark
      share_remark_config[:share_remark]
    end

    def share_pass
      share_remark_config[:share_pass]
    end

    def share_remark_config
      YAML.load_file(share_remark_file)
    end

    def share_remark_file
      import = Pathname.new(@config_path)
      import.mkdir unless import.exist?
      base_url = import.join('share_directory')
      base_url.mkdir unless base_url.exist?
      base_url.join('share_remark.yml')
    end

    def import_account_line(line)
      begin
        line = line.split(":\\")[1].split(';')
        ml = line[0].split('\\')
        pr = line[1]
        account_role_code = pr.split('\\')[1].to_s.split(":")[0]
        dc_code = pr.split('\\')[0]
        file_permission = pr.split('\\')[1].split(":")[1]
        return false if ml.size > 4
      rescue
        return false
      end
      if true || check_pass(ml)
        permission = set_permission(line[0])

        if share_account = set_account(account_role_code, dc_code)
          ShareOaPerson::AccountsRolesPermission.create(
              quarter_id: @quarter_id,
              permission_id: permission.id,
              additional_permission: file_permission,
              account_id: share_account.id
            )
        elsif share_role = set_role(account_role_code, dc_code)
          ShareOaPerson::AccountsRolesPermission.create(
              quarter_id: @quarter_id,
              permission_id: permission.id,
              additional_permission: file_permission,
              role_id: share_role.id
            )
        end
      end
    end

    def set_account(account_code, dc_code)
      ShareOaPerson::Account.find_by(quarter_id: @quarter_id, code: account_code)
    end

    def set_role(role_code, dc_code)
      role = ShareOaPerson::Role.find_by(quarter_id: @quarter_id, name: role_code)
      unless role
        if role_code.to_s != ""
          role = ShareOaPerson::Role.find_or_create_by(
            code: role_code,
            quarter_id: @quarter_id,
            name: role_code,
            data_json: {DC: dc_code}
          )
        else
          role = ShareOaPerson::Role.find_or_create_by(
            code: dc_code,
            quarter_id: @quarter_id,
            name: dc_code,
            data_json: {DC: nil}
          )
        end
      end
      role
    end

    def set_permission(code)
      permission = ShareOaPerson::Permission.find_or_create_by(
          quarter_id: @quarter_id,
          code: code
        )

      if permission.name == nil
        ml = code.split('\\')
        s_config = @remark_array.find{|x| x[6].to_s.split(",").include?(@system_name) && x[0] == ml[0] && x[1] == ml[1] && x[2] == ml[2] && x[3] == ml[3]}
        s_config = @remark_array.find{|x| x[6].to_s.split(",").include?(@system_name) && x[0] == ml[0] && x[1] == ml[1] && x[2] == ml[2] && x[3] == ''} if !s_config
        s_config = @remark_array.find{|x| x[6].to_s.split(",").include?(@system_name) && x[0] == ml[0] && x[1] == ml[1] && x[2] == '' && x[3] == ''} if !s_config
        s_config = @remark_array.find{|x| x[6].to_s.split(",").include?(@system_name) && x[0] == ml[0] && x[1] == '' && x[2] == '' && x[3] == ''} if !s_config
        remark = s_config ? s_config[4] : ''
        charge = s_config ? s_config[5] : ''
        permission.update(name: permission.code, data_json: {remark: remark, charge: charge}) 
      end

      permission
    end

    def check_pass(ml)
      status = true
      
      pass_config = nil
      status = false if pass_config = @share_pass.find{|x| x[5].to_s.split(",").include?(@system_name) && [x[0].to_s,x[1].to_s,x[2].to_s,x[3].to_s] == [ml[0].to_s,ml[1].to_s,ml[2].to_s,ml[3].to_s]}
      status = false if status && pass_config = @share_pass.find{|x| x[5].to_s.split(",").include?(@system_name) && x[3].to_s == "" && [x[0].to_s,x[1].to_s,x[2].to_s] == [ml[0].to_s,ml[1].to_s,ml[2].to_s]}
      status = false if status && pass_config = @share_pass.find{|x| x[5].to_s.split(",").include?(@system_name) && x[2].to_s == "" && [x[0].to_s,x[1].to_s] == [ml[0].to_s,ml[1].to_s]}
      status = false if status && pass_config = @share_pass.find{|x| x[5].to_s.split(",").include?(@system_name) && x[1].to_s == "" && [x[0].to_s] == [ml[0].to_s]}
      if pass_config
        pass_folder = pass_config[0..3]
        pass_folder.delete('')
      end
      if pass_config && pass_config[4] != '是' && pass_folder == ml[0..3]
        status = true
      end
      status
    end

  end
end
