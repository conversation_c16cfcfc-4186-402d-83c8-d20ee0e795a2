require 'yaml'
require 'logger'
require 'active_support/time'
require_relative '../aas_oracle_importer' unless ENV['AAS_PACKAGING']
require_relative '../aas_oracle_importer/database_client'
require_relative '../aas_oracle_importer/csv_client'
require_relative '../aas_oracle_importer/oracle_client'
require_relative '../aas_oracle_importer/mysql_client'
require_relative '../aas_oracle_importer/oceanbase_client'
require_relative '../aas_oracle_importer/kingbase_client'
require_relative '../aas_oracle_importer/sql_server_client'
require_relative '../aas_oracle_importer/ldap_client'
require_relative '../aas_oracle_importer/sql_check'
require_relative '../aas_oracle_importer/txt_client'
require_relative '../aas_oracle_importer/fake_client'
require_relative '../aas_oracle_importer/http_client'
require_relative '../aas_oracle_importer/system_import_status'
require_relative '../convert_tools'

include AasOracleImporter

# 民生证券 数据中心 同步状态请求模块
module MszqDatacenterAsyncTask

  # 数据中心请求
  class DataCenterRequest

    def initialize
      @database               ||= DatabaseClient.new_database(Config.db_type, Config.tnsname)

      @database.source_encode = @source_encode if @source_encode
      @path                   = @database.csv_path if @database.respond_to? :csv_path
    end

    def import_status
      if Time.now >= Time.parse(Config.time_out)
        return_failed_status
      end

      success_status = @database.exec(systems_import_status_sql).select{|status| status[1].to_s == '1'}

      if success_status.blank?
        sleep 5 * 60

        import_status
      else
        return_success_status
      end
    end

    private

    def quarter_name
      Date.today.strftime("%Y-%m-%d")
    end

    def systems_import_status_sql
      <<-SQL
        select date_format(data_date,'%Y-%m-%d'), status from #{Config.table_name} where data_date = \'#{quarter_name}\'
      SQL
    end

    def return_success_status
      logger.info { 'all systems import success' }
      RedisProvider.change_redis_status('success')
      exit 0
    end

    def return_failed_status
      logger.fatal { 'There are systems that have not been imported or Timeout' }
      RedisProvider.change_redis_status('failed')
      exit 2
    end

    def logger
      @logger ||= MszqDatacenterAsyncTask.logger
    end
  end

  # 民生证券数据中心配置
  module Config
    module_function

    def config
      @config ||= parse_config_file
    end

    def table_info
      @table_info ||= config&.[]("import_status")
    end

    def db_type
      @db_type ||= table_info&.[]("db_type")
    end

    def tnsname
      @tnsname ||= table_info&.[]("tnsname")
    end

    def table_name
      @table_name ||= table_info&.[]("name")
    end

    def time_out
      @time_out ||= config&.[]("time_out")
    end

    def log_level
      @log_level ||= config&.[]("log_level")
    end

    def log_file
      @log_file ||= (config&.[]("log_file") || STDOUT)
    end

    def config_file
      File.join(__dir__, '../../config/mszq_data_center.yml')
    end

    def parse_config_file
      YAML.load_file config_file
    end
  end

  # 导入状态存入redis,方便发送监控信息读取
  module RedisProvider

    module_function

    def provider
      @provider ||= provider_initialize
    end

    def provider_initialize
      server_path = ::AasOracleImporter.config['server']['path']
      load "#{server_path}/config/initializers/redis.rb"

      ::AasProvider
    end

    def redis_key
      'aas:customer:mszq:dc_import_status'
    end

    def change_redis_status(status)
      provider.redis.set(redis_key, status)
    end
  end

  module_function

  def logger
    logger                 = Logger.new(Config.log_file)
    logger.level           = Config.log_level
    logger.progname        = 'MszqDatacenterAsyncTask'
    logger.datetime_format = '%Y-%m-%dT%H:%M:%S.%L%z '
    logger
  end
end
