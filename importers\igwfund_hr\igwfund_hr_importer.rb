# 长盛专用

module AasOracleImporter
  class IgwfundHrImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      initialize_tables
    end

    def initialize_tables
      @table_resource   = "#{@table_space}hrmresource#{@sid_suffix}"
      @table_department = "#{@table_space}HrmDepartment#{@sid_suffix}"
    end

    def import_to_do
      import_users
    end

    private

    def import_users_sql
      <<-SQL
        select t1.loginid,t1.status,t1.departmentid,t1.lastname,t2.departmentmark from #{@table_resource} t1 inner join #{@table_department} t2 on t1.departmentid=t2.id where status=1
      SQL
    end

    def import_users
      user_codes = []
      @database.exec(import_users_sql) do |r|
        user_codes << r[0]
      end
      User.all.each do |user|
        next if user_codes.include? user.code

        user.update(inservice: false)
      end
      result.each do |row|
        user = User.find_or_create_by(code: row[0])
        user.name = row[3]
        user.inservice = true
        department = Department.find_or_create_by(code: row[2])
        department.name = row[4]
        department.inservice = true
        department.save
        user.department_id = department.id
        user.save
      end
    end
  end
end
