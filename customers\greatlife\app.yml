# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'greatlife'
  customer: '长城人寿'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_greatlife_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    dm:
      oadb:
        conn_str: DRIVER=DM8;SERVER=**********;TCP_PORT=5236;UID=jhread;PWD=***********;ENCODING=utf8;
    oracle:
      bidb:
        db_host: ***********
        db_name: bi
        db_user: biselect
        db_pass: biselect
      assetdb:
        db_host: ***********
        db_name: racdb
        db_user: imsopr
        db_pass: imsopr#123
    mysql:
      oadb:
        db_host: ***********
        db_name: oa2016
        db_user: oaread
        db_pass: oaread#715
      jiradb:
        db_host: ***********
        db_name: jira445
        db_user: shiyaxiong
        db_pass: shiyaxiong#751
      greatlife_ywzy_db:
        db_host: *********
        db_name: bmps-cloud-sys
        db_user: qxjhapp
        db_pass: vqiIEm2#qY3xq
  importers:
    - name: greatlife_hr
      bs_id: 106
      db_type: oracle
      tnsname: bidb
    - name: bi
      bs_id: 103
      db_type: oracle
      tnsname: bidb
      table_accounts: BIPL.LDUSER
      table_roles: BIPL.LDMENUGRP
      table_menus: BIPL.LDMENU
      table_accounts_roles: BIPL.LDUSERTOMENUGRP
      table_menus_roles: BIPL.LDMENUGRPTOMENU
    - name: feikong
      bs_id: 101
      db_type: oracle
      tnsname: bidb
      table_accounts: bistag1.hec_sys_user
      table_roles: bistag1.hec_sys_role
      table_roles_vl: bistag1.hec_sys_role_vl
      table_accounts_roles: bistag1.hec_sys_user_role_groups
      table_menus: bistag1.hec_sys_function_vl
      table_menus_roles: bistag1.hec_sys_role_function
      table_descriptions: bistag1.hec_fnd_descriptions
      table_companies: bistag1.hec_fnd_companies_vl
      table_employees: bistag1.hec_exp_employees
    - name: hexin
      bs_id: 102
      db_type: oracle
      tnsname: bidb
      table_accounts: bistag1.LISSYS_LDUSER
      table_roles: bistag1.LISSYS_LDMENUGRP
      table_menus: bistag1.LISSYS_LDMENU
      table_accounts_roles: bistag1.LISSYS_LDUSERTOMENUGRP
      table_menus_roles: bistag1.LISSYS_LDMENUGRPTOMENU
    - name: risk
      bs_id: 104
      db_type: oracle
      tnsname: bidb
      table_accounts: bistag1.RISK_USERINFO2
      table_roles: bistag1.RISK_ROLEINFO
      table_accounts_roles: bistag1.RISK_USERROLE
      table_menus: bistag1.RISK_MENUINFO
      table_menus_roles: bistag1.RISK_ROLEMENU
    - name: zijin
      bs_id: 105
      db_type: oracle
      tnsname: bidb
      table_accounts: bistag1.ZJ_SYS_USER
      table_agency: bistag1.ZJ_SYS_AGENCY
      table_roles: bistag1.ZJ_SYS_DUTY
      table_accounts_roles: bistag1.ZJ_SYS_R_USER_DUTY_AGENCY
      table_menus: bistag1.ZJ_SYS_R_DUTY_AGENCY
    - name: greatlife_oa
      bs_id: 107
      db_type: dm
      tnsname: oadb
      table_space: 'ekp.'
    - name: greatlife_asset
      bs_id: 108
      db_type: oracle
      tnsname: assetdb
    - name: greatlife_caiwu
      bs_id: 109
      db_type: oracle
      tnsname: bidb
      table_accounts: bistag1.cw_fnd_user
      table_space: 'bistag1.'
      sid_suffix: ''
    - name: jira
      bs_id: 110
      db_type: mysql
      tnsname: jiradb
      email_suffix: greatlife.cn
    - name: greatlife_ywzy
      bs_id: 500
      db_type: mysql
      tnsname: greatlife_ywzy_db

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: odbc
      conn_str: DRIVER=DM8;SERVER=**********;TCP_PORT=5238;UID=QXJH;PWD=***********;ENCODING=utf8;
