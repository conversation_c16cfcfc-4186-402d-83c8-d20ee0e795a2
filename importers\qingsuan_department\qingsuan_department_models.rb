# frozen_string_literal: true

module <PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'qingsuan_'
  end
end

class Qingsuan::Account < ActiveRecord::Base; end
class Qingsuan::Menu    < ActiveRecord::Base; end
class Qingsuan::Role    < ActiveRecord::Base; end
class Qingsuan::Department < ActiveRecord::Base; end
class QingsuanAccountsRoles < ActiveRecord::Base; end
class QingsuanMenusRoles    < ActiveRecord::Base; end
