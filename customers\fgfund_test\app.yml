# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'fgfund'
  customer: '富国基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  agent:
    ldap:
      ad_db:
        host: ************
        port: 389
        base: dc=fullgoalos,dc=com,dc=cn
        user: ithelpdesk
        password: abcd=2019
    mysql:
      # 投资交易 O32 系统数据库
      data_center:
        db_host: <%= ENV['DATACENTER_DATABASE_HOST'] %>
        db_port: <%= ENV['DATACENTER_DATABASE_PORT'] %>
        db_name: <%= ENV['DATACENTER_DATABASE_NAME'] %>
        db_user: <%= ENV['DATACENTER_DATABASE_USER'] %>
        db_pass: <%= ENV['DATACENTER_DATABASE_PASSWORD'] %>

  importers:
    - name: fgfund_hr
      bs_id: 1
      db_type: mysql
      tnsname: data_center
      company_map:
        - full_name: 富国基金管理有限公司
          abbr: 总公司
        - full_name: 富国资产管理（上海）有限公司
          abbr: 富国资产
        - full_name: 富国资产管理（香港）有限公司
          abbr: 香港子公司
        - full_name: 富国基金管理有限公司成都分公司
          abbr: 成都分公司
        - full_name: 富国基金管理有限公司广州分公司
          abbr: 广州分公司
        - full_name: 富国基金管理有限公司北京分公司
          abbr: 北京分公司

    - name: km_system
      bs_id: 65
      db_type: mysql
      tnsname: data_center
      table_space: 'km_'
      sid_suffix: ''

    - name: ad
      bs_id: 230
      db_type: ldap
      tnsname: ad_db
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs: 
      - dn
      - name
      - cn
      - samaccountname
      - title
      - useraccountcontrol
      - memberof
      - objectSid
      - primaryGroupID
      - description
      - sAMAccountType
      group_filter: '(&(objectclass = group))'
      group_attrs: 
      - dn
      - name
      - memberOf
      - objectSid
      - primaryGroupID
      - sAMAccountType
      #touyan_sync: true

    - name: dingdian_crm_mysql
      bs_id: 37
      db_type: mysql
      tnsname: data_center
      name_must_in_users: true
      table_space: 'CRM_'
      sid_suffix: ''

    - name: fund_disclosure_fgfund
      bs_id: 32
      db_type: mysql
      tnsname: data_center
      table_space: 'fd_'
      sid_suffix: ''
      inservice_fund_statuses:
        - 运作开放
        - 运作封闭
        - 清算中
      disabled_fund_statuses:
        - 终止
    - name: guzhi_yss45
      bs_id: 24
      db_type: mysql
      tnsname: data_center
      table_space: 'gz_'
      sid_suffix: ''
      import_fund_type: true
      sync_booksets_table: true
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1

    - name: guohu_ta
      bs_id: 30
      db_type: mysql
      tnsname: data_center
      table_space: 'ta_'
      sid_suffix: ''
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    - name: guohu_sa_etf
      bs_id: 40
      db_type: mysql
      tnsname: data_center
      table_space: 'etfta_'
      sid_suffix: ''
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    - name: guohu_sa_lof
      bs_id: 60
      db_type: mysql
      tnsname: data_center
      table_space: 'lofta_'
      sid_suffix: ''
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    - name: guohu_sa_ylb
      bs_id: 61
      db_type: mysql
      tnsname: data_center
      table_space: 'ylbta_'
      sid_suffix: ''
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    # 直销系统
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: mysql
      tnsname: data_center
      table_space: 'zx_'
      sid_suffix: ''
      sub_system: "CENTER"
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    # 直销系统
    - name: zhixiao_guitai
      bs_id: 28
      db_type: mysql
      tnsname: data_center
      table_space: 'zx_'
      sid_suffix: ''
      sub_system: "COUNTER"
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    # 直销系统
    - name: yuancheng_guitai
      bs_id: 29
      db_type: mysql
      tnsname: data_center
      table_space: 'zx_'
      sid_suffix: ''
      sub_system: "REMOTE"
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: mysql
      tnsname: data_center
      table_space: 'o32_'
      sid_suffix: ''
      days_of_data: 30
      temporary: true
      time_control: true
      temp_delete_record: true
      history_temp: true
      station_user_importer: true
      c_menu_type: [1]
      import_log:
        enable: true
        start_at: 5
      import_password_security:
        enable: true
      import_last_login_at:
        enable: true
        start_at: 1
development:
  <<: *defaults
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_fgfund_development
      username: root
      password: 123456
      host: 127.0.0.1

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    # 要改
    database:
      adapter: mysql2
      encoding: utf8
      database: <%= ENV['AAS_DATABASE_NAME'] %>
      host: <%= ENV['AAS_DATABASE_HOST'] %>
      port: <%= ENV['AAS_DATABASE_PORT'] %>
      username: <%= ENV['AAS_DATABASE_USER'] %>
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>


