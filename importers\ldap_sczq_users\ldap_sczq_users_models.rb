class User < ActiveRecord::Base
  belongs_to :department, optional: true
end

class Department < ActiveRecord::Base
  has_many :users
  has_many :children, class_name: "Department", foreign_key: "parent_id"
  belongs_to :parent, class_name: "Department", optional: true
end

class LdapDepartment
  attr_reader :name, :dn, :description
  attr_accessor :top_ou, :hidden_parent_name

  def initialize(entry)
    @dn          = entry.dn
    @name        = entry.name&.first
    @description = entry[:description]&.first
  end

  def parent_name
    # OU=策略六组,OU=投资研究部,OU=组织架构,DC=lombardachina,DC=com
    @dn.gsub(/(OU=|DC=)/, '').split(',')[1]
  end

  def display_name
    # @description || @name
    return name if hidden_parent_name

    "#{parent_name} / #{name}"
  end

  def code
    @dn
  end

  def top?
    parent_name == top_ou
  end

  def root?
    name == top_ou
  end
end

class LdapPerson
  attr_reader :name, :login, :email, :ipphone, :mobile, :code, :department_name, :job_name
  attr_accessor :ou_sequence

  def initialize(entry)
    @dn      = entry.dn
    @name    = entry.name&.first&.gsub(/\s+/, '')
    @login   = entry.samaccountname&.first&.downcase
    @email   = entry[:mail]&.first&.downcase
    @ipphone = entry[:ipphone]&.first
    @mobile  = entry[:mobile]&.first
    @code    = @login
    @department_name = entry[:department]&.first
    @job_name = entry[:title]&.first
    @useraccountcontrol = entry.useraccountcontrol.first
  end

  def department
    default_sequence = 1
    real_department  = departments[default_sequence]

    if index = ou_sequence[real_department]
      departments[index.to_i]
    else
      departments[(ou_sequence['default'].to_i || default_sequence)]
    end
  end

  def department_code
    default_sequence = 1
    dn_list = @dn.split(',')
    real_department  = dn_list[default_sequence]
    department = real_department.gsub(/(CN=|OU=|DC=)/, '')

    if index = ou_sequence[department]
      dn_list.from(index.to_i).join(',')
    else
      dn_list.from(ou_sequence['default'].to_i || default_sequence).join(',')
    end
  end

  def departments
    @dn.gsub(/(CN=|OU=|DC=)/, '').split(',')
  end

  def robot?
    @email.nil?
  end

  def inservice
    status_sequences = @useraccountcontrol.to_i.to_s(2)
    status_sequences[-2] != '1'
  end
end
