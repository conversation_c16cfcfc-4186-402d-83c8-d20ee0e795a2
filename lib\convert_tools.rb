# frozen_string_literal: true

# 一些转换工具
module ConvertTools
  # 将数据库中的 json 转化为 Hash 的类，Key 转换为 symbol
  class JsonWithSymbolizeNames
    def self.load(string)
      return unless string

      JSON.parse(string, symbolize_names: true)
    end

    def self.dump(object)
      object.to_json
    end
  end

  module_function

  # 转换基金代码，原 8 位基金代码转换为 6 位
  def convert_fund_code(code)
    code = code.to_s
    return code unless code.length == 8

    chars = code.chars
    # 8 位且 0、5 两位为 0 做转换
    if chars[0] == '0' && code[4] == '0'
      chars[0] = nil
      chars[4] = nil
      chars.join
    else
      code
    end
  end

  def in_active_date?(start_date, end_date)
    return true unless end_date

    today = Date.today
    today >= start_date && today <= end_date
  end

  # 增加基金代码
  def add_o32_fund_codes(source, code_string)
    source ||= []
    raise 'not support add_o32_fund_codes type' unless source.is_a? Array

    source << code_string
    source.uniq
  end

  # 加密解密
  class Cryptology
    def self.crypt
      ActiveSupport::MessageEncryptor.new(ENV["SECRET_KEY_BASE"][0..31])
    end

    def self.encrypt(string)
      crypt.encrypt_and_sign(string)
    end

    def self.decrypt(string)
      crypt.decrypt_and_verify(string)
    end

    # 未加密 => 加密；已加密 => 返回自己
    def self.encrypt_if_need(string)
      begin
        crypt.decrypt_and_verify(string)
      rescue
        crypt.encrypt_and_sign(string)
      else
        string
      end
    end

    # 未加密 => 返回自己；已加密 => 解密
    def self.decrypt_if_need(string)
      begin
        value = crypt.decrypt_and_verify(string)
      rescue
        string
      else
        value
      end
    end

    # 判断环境变量并解密
    def self.decrypt_if_env(string)
      begin
        ENV['AAS_ENCRYPTED'] == 'true' ? decrypt(string) : string
      rescue
        raise $! #表示异常信息
      end
    end

    # 判断环境变量并加密
    def self.encrypt_if_env(string)
      ENV['AAS_ENCRYPTED'] == 'true' ? encrypt(string) : string
    end

    # 判断文本是否是加密状态
    def self.encrypt?(string)
      begin
        decrypt(string)
      rescue
        false
      else
        true
      end
    end
  end
end
