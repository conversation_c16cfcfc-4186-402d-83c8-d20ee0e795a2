module <PERSON><PERSON><PERSON>
  def self.table_name_prefix
    'zhengtou_'
  end
end

class Zhengtou::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class Zhengtou::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class Zhengtou::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class Zhengtou::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class Zhengtou::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class Zhengtou::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class Zhengtou::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class Zhengtou::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class Zhengtou::System < ActiveRecord::Base
  has_many :menus
end

class Zhengtou::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Zhengtou::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class Zhengtou::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class Zhengtou::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class Zhengtou::Temporary < ActiveRecord::Base; end

class ZhengtouAccountsRoles < ActiveRecord::Base; end
class ZhengtouMenusRoles < ActiveRecord::Base; end
class ZhengtouMenuAdditionsRoles < ActiveRecord::Base; end

class Zhengtou::TimeControl < ActiveRecord::Base; end

class Zhengtou::TopTradeType < ActiveRecord::Base; end
class Zhengtou::TopStation < ActiveRecord::Base; end
class Zhengtou::TradeType < ActiveRecord::Base; end