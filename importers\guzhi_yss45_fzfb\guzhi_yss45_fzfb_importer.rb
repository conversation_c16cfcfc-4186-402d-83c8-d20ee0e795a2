require 'yaml'
module AasOracleImporter
  class GuzhiYss45FzfbImporter < ImporterBase
    def config
      @bs_id           = importer_config['bs_id']
      @table_space     = importer_config['table_space']
      @sid_suffix      = importer_config['sid_suffix']
      @sync_booksets_table = importer_config['sync_booksets_table']
      @import_fund_type = importer_config['import_fund_type']
      @role_check      = importer_config['role_check']
      @display_status = importer_config['display_status']
      @post_table_name = importer_config['post_table_name'] || 't_s_user_post_data'
      @table_log       = @table_space+'t_s_oper_log'+@sid_suffix
      @table_password_security = @table_space + 't_s_safe_system' + @sid_suffix
      @accounts = []
      @roles = []
      @menus = []
      super
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_roles
      # import_accounts_roles
      import_menus
      import_role_menus
      import_additional_permissions
      #import_account_menus
      #import_account_additional_permissions
      import_booksets
      import_booksets_relation
      import_logs
      import_password_securities
      import_last_login_at_data

      import_ledgers(Guzhi::Account)
    end

    def destroy_exist_data
      Guzhi::Account.where(quarter_id: @quarter_id).destroy_all
      Guzhi::Bookset.where(quarter_id: @quarter_id).destroy_all
      Guzhi::Role.where(quarter_id: @quarter_id).destroy_all
      Guzhi::BooksetRelation.where(quarter_id: @quarter_id).destroy_all
      Guzhi::Menu.where(quarter_id: @quarter_id).destroy_all
      Guzhi::AdditionalPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_accounts_sql
      <<-SQL
        select C_USER_CODE, C_USER_NAME, C_DV_STATE from #{@table_space}t_s_user#{@sid_suffix} t
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Guzhi::Account.find_or_create_by(code: r[0], quarter_id: @quarter_id)
            account.update(
              name:       r[1],
              status:     %w[ENAB].include?(r[2].to_s)
            )
            if @display_status
              QuarterAccountInfo.create(
                account_id: account.id,
                account_type: 'Guzhi::Account',
                business_system_id: @bs_id,
                quarter_id: @quarter_id,
                display_status: r[2].to_s
              )
            end
            @accounts << account
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      role_check_sql = ''
      if @role_check
        role_check_sql = 'where t.n_check_state = 1'
      end
      <<-SQL
        select C_POST_CODE, C_POST_NAME from #{@table_space}t_s_post#{@sid_suffix} t #{role_check_sql}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          if r[0] && r[1]
            @roles << Guzhi::Role.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1]
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_role_menus_sql
      <<-SQL
        select C_POST_CODE, C_FUN_CODE, C_OPER_VALUE from #{@table_space}t_s_post_rights#{@sid_suffix} t
      SQL
    end

    def import_role_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_role_menus_sql) do |r|
          role = @roles.find { |item| item.code == r[0] }
          menu = @menus.find { |m| m.code == r[1] }
          next if role.nil? || menu.nil?

          Guzhi::AccountRoleMenu.find_or_create_by(
            role_id:    role.id,
            menu_id:    menu.id
          )
        end
      end
    end

    def import_account_menus_sql
      <<-SQL
        select C_USER_CODE, C_FUN_CODE, C_OPER_VALUE from #{@table_space}t_s_user_post_edit#{@sid_suffix} t
      SQL
    end
    def import_account_menus
      ActiveRecord::Base.transaction do
        @database.exec(import_account_menus_sql) do |r|
          account = @accounts.find { |item| item.code == r[0] }
          menu = @menus.find { |m| m.code == r[1] }
          next if account.nil? || menu.nil?

          Guzhi::AccountRoleMenu.find_or_create_by(
            account_id: account.id,
            menu_id:    menu.id
          )
        end
      end
    end

    def import_additional_permissions_sql
      <<-SQL
        select C_OPER_CODE, C_OPER_VALUE, C_DV_OPER_TYPE from #{@table_space}t_s_oper_value#{@sid_suffix} ov
      SQL
    end

    def import_additional_permissions
      additional_permissions = []
      @database.exec(import_additional_permissions_sql) do |r|
        additional_permissions << r
      end

      @database.exec(import_role_menus_sql) do |r|
        oper_value = r[2]
        next if oper_value.nil?

        role = @roles.find { |item| item.code == r[0] }
        menu = @menus.find { |m| m.code == r[1] }
        next if role.nil? || menu.nil?

        oper_values = convert_oper_value(oper_value)
        oper_values.each do |value|
          permission = additional_permissions.find { |item| item[1].to_s == value.to_s }

          next if permission.nil?

          additional_permission = Guzhi::AdditionalPermission.create(
            quarter_id: @quarter_id,
            menu_id: menu.id,
            code: permission[0],
            name: permission[2]
          )
          if additional_permission
            Guzhi::AccountRoleAdditionalPermission.find_or_create_by(
              role_id:                   role.id,
              additional_permission_id:  additional_permission.id
            )
          end
        end
      end
    end

    def import_account_additional_permissions
      additional_permissions = []
      @database.exec(import_additional_permissions_sql) do |r|
        additional_permissions << r
      end

      @database.exec(import_account_menus_sql) do |r|
        oper_value = r[2]
        next if oper_value.nil?

        account = @accounts.find { |item| item.code == r[0] }
        menu = @menus.find { |m| m.code == r[1] }
        next if account.nil? || menu.nil?

        #取出来的是一个负数，转成正数了再转换
        oper_values = convert_oper_value(oper_value)
        oper_values.each do |value|
          permission = additional_permissions.find { |item| item[1].to_s == value.to_s }

          next if permission.nil?

          additional_permission = Guzhi::AdditionalPermission.create(
            quarter_id: @quarter_id,
            menu_id: menu.id,
            code: permission[0],
            name: permission[2]
          )
          if additional_permission
            Guzhi::AccountRoleAdditionalPermission.find_or_create_by(
              account_id:                account.id,
              additional_permission_id:  additional_permission.id
            )
          end
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select C_FUN_CODE, C_FUN_NAME, C_FUN_CODE_P, C_APP_CODE from #{@table_space}t_s_fun#{@sid_suffix} t where t.C_DV_STATE = 'ENAB'
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        data = []
        @database.exec(import_menus_sql) do |r|
          data << r
          if r[0]# && r[3].present?
            @menus << Guzhi::Menu.create(
              quarter_id: @quarter_id,
              code:       r[0],
              menu_name:  r[1]
            )
          end
        end

        Guzhi::Menu.where(quarter_id: @quarter_id).each do |menu|
          menu_data = data.find { |row| row[0]&.to_s == menu.code }
          next if menu_data.nil? || menu_data[2].nil?

          parent_menu = Guzhi::Menu.find_by(quarter_id: @quarter_id, code: menu_data[2])
          next if parent_menu.nil?

          menu.parent = parent_menu
          menu.save
        end
      end
    end

    def import_booksets_sql
      if @sync_booksets_table
        if @import_fund_type
          <<-SQL
            select
              a.C_PORT_CODE,
              a.C_PORT_NAME,
              b.C_ASSET_CODE, # 产品代码
              b.C_PORT_TYPE # 资产类型
            from
              #{@table_space}t_p_ab_port#{@sid_suffix} a,
              #{@table_space}vb_port_baseinfo#{@sid_suffix} b
            where
             a.C_PORT_CODE = b.C_PORT_CODE
          SQL
        else
          <<-SQL
            select C_PORT_CODE, C_PORT_NAME
            from #{@table_space}t_p_ab_port#{@sid_suffix}
          SQL
        end
      else
        <<-SQL
          select upd.C_DATA_CODE, vpb.C_PORT_NAME
          from #{@table_space}#{@post_table_name}#{@sid_suffix} upd
          join #{@table_space}vb_port_baseinfo#{@sid_suffix} vpb on upd.C_DATA_CODE = vpb.C_PORT_CODE
          where c_data_type = '1' and vpb.C_DV_PROD_STATE in ('PS3', 'PS4', 'PS5')
        SQL
      end
    end

    def import_booksets
      ActiveRecord::Base.transaction do
        config_path = File.join(__dir__, '../../config/fund_type_config.yml')

        fund_types = YAML.load_file config_path
        enums = fund_types&.[]('fund_types')

        @database.exec(import_booksets_sql) do |r|
          bookset = Guzhi::Bookset.find_or_create_by(
            quarter_id: @quarter_id,
            code:       r[0]
          )

          bookset.name      = r[1]
          bookset.fund_type = get_enum(enums, 'fund_type', r[3])
          bookset.fund_code = r[2]
          bookset.save
        end
      end
    end

    # TODO: 这块最后面的sql需要join一个表， 然后取data_name使用的是join的表
    def import_booksets_relation_sql
      <<-SQL
        select upd.C_USER_CODE, upd.C_DATA_CODE, vpb.c_port_name, upd.C_POST_CODE
        from #{@table_space}#{@post_table_name}#{@sid_suffix} upd
        join #{@table_space}vb_port_baseinfo#{@sid_suffix} vpb on upd.C_DATA_CODE = vpb.C_PORT_CODE
        where c_data_type = '1' and vpb.C_DV_PROD_STATE in ('PS3', 'PS4', 'PS5')
      SQL
    end

    def import_booksets_relation
      booksets = Guzhi::Bookset.where(quarter_id: @quarter_id).to_a
      ActiveRecord::Base.transaction do
        @database.exec(import_booksets_relation_sql) do |r|
          if r[0] && r[1]

            bookset = booksets.find{|x| x.code == r[1].to_s}
            # unless bookset
            #   bookset = Guzhi::Bookset.create(
            #     quarter_id: @quarter_id,
            #     code:       r[1],
            #     name:       r[2]
            #   )
            # end
            account = @accounts.find{|x| x.code == r[0].to_s}
            role = @roles.find{|x| x.code == r[3].to_s}

            # 建立账号与角色的关联关系
            if account && role
              unless account.roles.ids.include?(role.id)
                account.roles << role
              end
            end

            if account && role && bookset
              cbr               = Guzhi::BooksetRelation.new
              cbr.quarter_id    = @quarter_id
              cbr.account = account
              cbr.role    = role
              cbr.bookset = bookset
              cbr.save
            end
          end
        end
      end
    end

    # 导入日志sql语句
    # c_log_time是VARCHAR2类型
    def customer_audit_sql
      start_at = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      where_sql = "where c_content like '登%' and c_log_time > '#{start_at}'"

      <<-EOF
        select id_s_oper_log, c_user_code, c_log_time, c_oper_type, c_content, c_ip
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        data << {
          'source_id' => r[0],
          'account_code' => r[1],
          'account_name' => nil,
          'operation_at' => r[2],
          'operation_category' => r[3],
          'operation' => r[4],
          'bs_id' => @bs_id,
          'ip_address' => r[5]
        }
      end
    end

    def password_security_sql
      "select C_SAFE_SYS_CODE, C_VALUE, C_SAFE_SYS_NAME from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        # 0 表示不冻结
        login_failure_number = data.find { |x| x[0].to_s == 'A0' }&.[](1)&.to_i
        # 0 表示一直有效
        password_valid_time = data.find { |x| x[0].to_s == 'A6' }&.[](1)&.to_i
        json['bs_id'] = @bs_id
        json['password_length'] = data.find { |x| x[0].to_s == 'A1' }&.[](1)
        json['is_uppercase'] = data.find { |x| x[0].to_s == 'A2' }&.[](1)
        json['is_lowercase'] = data.find { |x| x[0].to_s == 'A3' }&.[](1)
        json['is_number'] = data.find { |x| x[0].to_s == 'A4' }&.[](1)
        json['is_character'] = data.find { |x| x[0].to_s == 'A5' }&.[](1)
        json['login_failure_number'] = login_failure_number
        json['password_valid_time'] = password_valid_time
      end
    end

    def last_login_at_sql
      if @import_last_login_at.present?
        where_sql = case @database.database_type
                    when 'oracle'
                      "where c_content like '登%' and c_log_time > sysdate-#{@import_last_login_at}"
                    else
                      start_at = (Time.now - @import_last_login_at.days).strftime('%Y-%m-%d %H:%M:%S')
                      "where c_content like '登%' and c_log_time > '#{start_at}'"
                    end
      else
        where_sql = nil
      end
      <<-EOF
        select c_user_code, max(c_log_time)
        from #{@table_log}
        #{where_sql}
        group by c_user_code
      EOF
    end

    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        data << {
          'code' => x[0],
          'last_login_at' => x[1]
        }
      end
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/m).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}'"
      select_db_datas(new_sql).each_with_index do |r, index|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
        break if index >= 0
      end
      name
    end
    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def convert_oper_value(oper_value)
      oper_value = oper_value.to_i.abs
      result = []
      binary_representation = oper_value.to_s(2)
      binary_representation.reverse.each_char.with_index do |char, index|
        if char == '1'
          result << (2 ** index)
        end
      end
      result
    end

    def convert_with_negative_value(oper_value, bits="288")
      oper_value = oper_value.to_i

      # 负整数转二进制
    end
  end
end
