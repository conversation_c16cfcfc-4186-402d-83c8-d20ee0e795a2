module AasOracleImporter
  class YizhuangProImporter < ImporterBase
    def config
      @bs_id           = importer_config['bs_id']
      @user_data       = []
      @privilege_data  = []
      @check_errors    = []
      @check_warnings  = []
    end

    def business_system
      BusinessSystem.find(10110)
    end

    def import_to_do
      json = check
      unless json[:success]
        @logger.info { "#{business_system.name} check file 错误：#{json[:errors].join(',')}" }
        return
      end

      destroy_all_datas
      store_user_data
      store_privilege_data

      import_ledgers(YizhuangPro::Account)
    end

    def check
      if check_name?
        if check_type?
          return { success: true, warnings: @check_warnings } if check_content?
        end
      end
      { success: false, warnings: @check_warnings, errors: @check_errors }
    end

    # 导入的excel没有存到时间点，所以时间点传入nil
    def import_path
      "/opt/aas-app/data/import/#{business_system.prefix}"
    end

    def import_files
      # 删除临时文件
      Dir.glob("#{import_path}/*.{xls,xlsx,txt,csv}").delete_if { |x| File.basename(x).match(/^~\$.*/) }
    end

    def check_name?
      errors = 0
      check_files = import_files.dup
      should_exists_files = [

        'user_data.xlsx',

        'Privilege_data.xlsx'

      ]

      should_exists_files.each do |file_name|
        unless check_files.select { |x| File.basename(x, '.xlsx') =~ /#{File.basename(file_name, '.xlsx')}/ }
          @check_errors << "#{business_system.name}错误: 未找到文件 #{file_name}"
          errors += 1
        end
        check_files.delete_if { |x| File.basename(x, '.xlsx') =~ /#{File.basename(file_name, '.xlsx')}/ }
      end

      unnecessary_files = check_files
      @check_warnings += unnecessary_files.map { |x| "#{business_system.name}: 已上传的文件 #{File.basename(x)}，不需要导入系统" }
      @necessary_files = import_files - unnecessary_files

      errors.zero?
    end

    def check_type?
      errors = 0

      @necessary_files.select { |x| File.extname(x) == '.xlsx' }.each do |file|
        unless check_file_type(file) == 'xlsx'
          @check_errors << "#{business_system.name}错误：#{File.basename file} 不是标准格式的 xlsx 文件"
          errors += 1
        end
      end

      errors.zero?
    end

    def check_content?
      errors = 0
      file1s = import_files.select { |x| File.basename(x) =~ /user_data/ }

      file1s.each do |file1|
        book1 = RubyXL::Parser.parse(file1)
        sheet1 = book1.worksheets[0]
        unless sheet1[0].cells.compact.map(&:value).compact == %w[用户名* 密码* 角色* 用户状态* Email* 真实姓名 电话 工号 职务 部门 用户组]
          @check_errors << "#{business_system.name}错误：user_data.xlsx 表结构与预期不符。"
          errors += 1
        end
      end

      file2s = import_files.select { |x| File.basename(x) =~ /Privilege_data/ }

      file2s.each do |file2|
        book2 = RubyXL::Parser.parse(file2)
        sheet2 = book2.worksheets[0]
        unless sheet2[0].cells.compact.map(&:value).compact == %w[用户名称 用户组 设备名 设备IP 设备组 应用名称 设备账号 端口 真实姓名 SSH]
          @check_errors << "#{business_system.name}错误：Privilege_data.xlsx 表结构与预期不符。"
          errors += 1
        end
      end

      errors.zero?
    end

    def destroy_all_datas
      YizhuangPro::Account.where(quarter_id: @quarter.id).destroy_all
      YizhuangPro::Role.where(quarter_id: @quarter.id).destroy_all
      YizhuangPro::Data1Permission.where(quarter_id: @quarter.id).destroy_all
      YizhuangPro::Data1AccountsRolesPermission.where(quarter_id: @quarter.id).destroy_all
    end

    def store_user_data
      files = import_files.select { |x| File.basename(x) =~ /user_data/ }
      return if files.empty?

      files.each do |file|
        @logger.info { "YizhuangProImport: Parse file #{file}" }
        book = RubyXL::Parser.parse(file)
        sheet = book.worksheets[0]
        sheet.each_with_index do |row, index|
          next if index.zero? || row.nil?

          @user_data << {
            user_code:  row[0]&.value,
            role_name:  row[2]&.value,
            status:     row[3]&.value,
            email:      row[4]&.value,
            user_name:  row[5]&.value,
            job_name:   row[8]&.value,
            department: row[9]&.value
          }
        end
      end

      import_accounts
      import_roles
      import_accounts_roles
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '活动', 'enum_value' => '1', 'label' => '账号状态' },
                 { 'name' => 'status', 'value' => '未激活', 'enum_value' => '1', 'label' => '账号状态' }]

        YizhuangPro::Account.bulk_insert(:source_id, :code, :status, :name, :job_number, :user_group, :quarter_id, :user_id, :data_json, :objid, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          @user_data.each do |x|
            return if x[:user_code].empty? || x[:user_name].empty?

            obj.add [x[:user_code], x[:user_code], get_enum(enums, 'status', x[:status])&.to_s == '1', x[:user_name], x[:job_number], x[:user_group], @quarter.id, x[:user_id], x[:data_json], x[:objid], Time.now, Time.now]
          end
        end
      end
    end

    def import_roles
      ActiveRecord::Base.transaction do
        YizhuangPro::Role.bulk_insert(:source_id, :code, :name, :quarter_id, :data_json, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          @user_data.each do |x|
            obj.add [x[:role_name], x[:role_name], x[:role_name], @quarter.id, x[:data_json], Time.now, Time.now]
          end
        end
      end
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        YizhuangPro::AccountsRole.bulk_insert(:role_id, :account_id, :role_scope) do |obj|
          obj.set_size = 1000
          @user_data.each do |x|
            role = YizhuangPro::Role.find_by(quarter_id: @quarter.id, source_id: x[:role_name])
            account = YizhuangPro::Account.find_by(quarter_id: @quarter.id, source_id: x[:user_code])

            next unless account && role

            obj.add [role.id, account.id, nil]
          end
        end
      end
    end

    def store_privilege_data
      files = import_files.select { |x| File.basename(x) =~ /Privilege_data/ }
      return if files.empty?

      files.each do |file|
        @logger.info { "YizhuangProImport: Parse file #{file}" }

        book = RubyXL::Parser.parse(file)
        sheet = book.worksheets[0]
        sheet.each_with_index do |row, index|
          next if index.zero? || row.nil?

          @privilege_data << {
            source_id:       Digest::MD5.hexdigest(row.cells.to_s),
            user_code:       row[0]&.value,
            user_group:      row[1]&.value,
            device_name:     row[2]&.value,
            device_ip:       row[3]&.value,
            device_group:    row[4]&.value,
            connection_mode: row[5]&.value,
            device_account:  row[6]&.value,
            port:            row[7]&.value
          }
        end
      end

      import_data1_permissions
      import_accounts_data1_permissions
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        YizhuangPro::Data1Permission.bulk_insert(:source_id, :code, :device_name, :device_ip, :device_group, :connection_mode, :device_account, :port, :quarter_id, :data_json, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          @privilege_data.each do |x|
            obj.add [x[:source_id], x[:code], x[:device_name], x[:device_ip], x[:device_group], x[:connection_mode], x[:device_account], x[:port], @quarter.id, x[:data_json], Time.now, Time.now]
          end
        end
      end
    end

    def import_accounts_data1_permissions
      ActiveRecord::Base.transaction do
        YizhuangPro::Data1AccountsRolesPermission.bulk_insert(:data1_permission_id, :additional_permission, :permission_scope, :quarter_id, :role_id, :account_id, :data_json, :permission_status) do |obj|
          obj.set_size = 1000
          @privilege_data.each do |x|
            data1_permission = YizhuangPro::Data1Permission.find_by(quarter_id: @quarter.id, source_id: x[:source_id])
            account = YizhuangPro::Account.find_by(quarter_id: @quarter.id, source_id: x[:user_code])

            next unless data1_permission && account

            obj.add [data1_permission.id, nil, nil, @quarter.id, nil, account.id, nil, nil]
          end
        end
      end
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(file, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      book = RubyXL::Parser.parse(file)
      sheet = book.worksheets[0]
      sheet.each_with_index do |row, index|
        next if index.zero? || row.nil?

        parent_name = row[name_index]&.value
        parent_id_value = row[parent_id_index]&.value
        if parent_name.present? && row[parent_id_index]&.value != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(file, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.to_s.gsub('&nbsp;', ' ')
    end
  end
end
