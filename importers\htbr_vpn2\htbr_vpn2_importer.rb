require 'httparty'

module AasOracleImporter
  class HtbrVpn2Importer < ImporterBase
    include HTTP<PERSON>y

    def config
      @bs_id              = importer_config['bs_id']
      @access_key         = importer_config['access_key']
      @base_url           = importer_config['base_uri']
      @accounts           = []
      @roles              = []
      @data1_permissions  = []
      self.class.base_uri importer_config['base_uri']
    end

    def vpn_config
      YAML.load_file File.join(__dir__, '../../config/htbr_vpn2_config.yml')
    end

    def api_config
      vpn_config[@key.to_s]
    end

    def import_to_do
      destroy_exist_datas
      ActiveRecord::Base.transaction { import_accounts }
      update_accounts
      ActiveRecord::Base.transaction { import_roles }
      ActiveRecord::Base.transaction { import_accounts_roles }
      ActiveRecord::Base.transaction { import_data1_permissions }
      ActiveRecord::Base.transaction { import_data1_role_permissions }
      # ActiveRecord::Base.transaction { import_data1_account_permissions }
      import_ledgers(HtbrVpn2::Account)
    end

    def destroy_exist_datas
      accounts = HtbrVpn2::Account.where(quarter_id: @quarter_id)
      HtbrVpn2::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HtbrVpn2::Role.where(quarter_id: @quarter_id).delete_all
      HtbrVpn2::Data1Permission.where(quarter_id: @quarter_id).delete_all
      HtbrVpn2::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.find(@bs_id)
    end

    private

    def import_accounts
      @key = 'account_list'
      all_info.each do |account|
        # next unless account['parent'].to_s == '2'

        @accounts << HtbrVpn2::Account.create(
          quarter_id: @quarter_id,
          source_id:  account['id'],
          code:       account['id'],
          name:       account['name'],
          status:     account['is_enable'].to_s == '1'
        )
      end
    end

    def update_accounts
      @key = 'update_accounts'
      @accounts.each do |account|
        next unless account.status

        account_info = single_info(account.name)
        next if account_info['expire'] == '0'
        type_id = PublicAccountType.find_by(key: "temporary").id
        data_json = {
          name:      account.name,
          system_id: @bs_id,
          code:      account.code,
          remark:    '自动识别',
          type_id:   type_id
        }
        public_account = ::PublicAccount.find_or_initialize_by(data_json)
        public_account.valid_date = Time.at(account_info['expire'].to_i)
        public_account.save
      end
    end

    def import_roles
      @key = 'role_list'
      all_info.each do |role|
        next if role['enable'].to_i.zero?

        @roles << HtbrVpn2::Role.create(
          quarter_id: @quarter_id,
          source_id:  role['_id'],
          code:       role['_id'],
          name:       role['name']
        )
      end
    end

    def import_accounts_roles
      @key = 'account_list'
      all_info.each do |data|
        # role    = @roles.select { |x| x.source_id.to_s == data['roleid'] }
        roles   = @roles.select { |x| data['roleid'].split(',').to_a.include? x.source_id.to_s }
        account = @accounts.find { |x| x.source_id.to_s == data['id'].to_s }

        roles.each do |role|
          HtbrVpn2::AccountsRole.create(account_id: account.id, role_id: role.id) if account
        end
      end
    end

    def import_data1_permissions
      @key = 'data1_permissions_list'
      all_info.each do |data|
        next if data['enableSrc'].to_i.zero?

        @data1_permissions << HtbrVpn2::Data1Permission.create(
          quarter_id:  @quarter_id,
          source_id:   data['id'],
          code:        data['id'],
          level1_name: data['name'],
          level2_name: data['rc_grp_name'],
          level2_id:   data['rc_grp_id'],
          rctype:      api_config&.[]('enum')&.[]('rctype')&.[](data['rctype'].to_s),
          service:     data['service'],
          main_host:   data['main_host'],
          addr_str:    data['addr_str']
        )
      end
    end

    def import_data1_role_permissions
      @key = 'import_data1_role_permissions'

      @roles.each do |role|
        role_info = single_info(role.name)
        # data1_permissions = @data1_permissions.select { |x| x.source_id.to_s == role_info['rcIdsStr'].to_s }
        data1_permissions = HtbrVpn2::Data1Permission.where(quarter_id: @quarter_id, source_id: role_info['rcIdsStr']&.split(','))
        data1_permissions.each do |permission|
          HtbrVpn2::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    def all_info
      @argument = 0
      infos = []
      loop do
        datas = send_request['result']['data']
        infos += datas
        @argument += api_config['pagesize']
        break if datas.to_a.size < api_config['pagesize']
      end
      infos
    end

    def single_info(single)
      @argument = single
      send_request['result']
    end

    def send_request
      response = self.class.post(request_url, {
                                   body:    request_body,
                                   headers: headers
                                 })
      result = JSON.parse response.body

      unless result['code'].to_i.zero?
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def request_url
      "#{@base_url}#{url_action}"
    end

    def url_action
      "controler=#{api_config['controler']}&action=#{api_config['action']}"
    end

    # 发起请求用的
    def request_body
      @request_timestamp  = request_timestamp
      body                = api_config['body']
      body.push({ 'key' => 'timestamp', 'value' => @request_timestamp }, { 'key' => 'sinfor_apitoken', 'value' => api_token })

      result = {}
      body.each do |hash|
        result.merge!({ hash['key'].to_s => hash['value'] })
      end
      result[api_config['argument']] = @argument
      result
    end

    def request_timestamp
      Time.now.to_f.to_i.to_s
    end

    def api_token
      OpenSSL::Digest::SHA256.hexdigest(params_string)
    end

    def params_string
      "#{request_action}#{params_body}#{@request_timestamp}#{@access_key}"
    end

    def request_action
      "action=#{api_config['action']}&controler=#{api_config['controler']}"
    end

    # 生成 api_token 用的
    def params_body
      body = api_config['body']
      argument = body.find { |hash| hash['key'] == api_config['argument'] }
      argument['value'] = @argument if argument
      body.push({ 'key' => 'timestamp', 'value' => @request_timestamp })
      body = body.sort_by { |x| x['key'] }
      body.map { |a| "&#{a['key']}=#{a['value']}" }.join('')
    end

    def headers
      { 'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8' }
    end
  end
end
