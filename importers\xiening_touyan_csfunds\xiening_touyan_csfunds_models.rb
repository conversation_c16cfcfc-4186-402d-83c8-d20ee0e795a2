module To<PERSON><PERSON>
  def self.table_name_prefix
    'touyan_'
  end
end

class Touyan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class Touyan::AccountsRolesPermission < ActiveRecord::Base
	belongs_to :quarter
  belongs_to :permission
  validates :permission_id, presence: true
  validates :quarter_id, presence: true
end

class Touyan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end


class Touyan::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :permission_type, presence: true 
  validates :quarter_id, presence: true 
end


