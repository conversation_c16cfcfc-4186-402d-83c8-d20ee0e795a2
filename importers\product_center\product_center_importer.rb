# frozen_string_literal: true

module AasOracleImporter
  class ProductCenterImporter < ImporterBase
    include ConvertTools

    def config
      @bs_id                  = importer_config['bs_id']
      @table_space            = importer_config['table_space']
      @sid_suffix             = importer_config['sid_suffix']

      @accounts = []
      @roles    = []
      @funds    = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}prod_fund_manager#{@sid_suffix}"
      @table_role         = "#{@table_space}prod_fund_manager#{@sid_suffix}"
      @table_account_role = "#{@table_space}prod_fund_manager#{@sid_suffix}"
      @table_product      = "#{@table_space}prod_product#{@sid_suffix}"
      @table_arps         = "#{@table_space}prod_r_product_manager#{@sid_suffix}"
      @attribute          = "#{@table_space}attribute_value#{@sid_suffix}"
      # staff 是另一个库的表，如果有修改，直接改代码，改config不生效
      @table_account      = "#{@table_space}staff#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      # 导入台账关联字段
      udpate_account
      import_roles
      # import_accounts_roles
      import_funds
      import_fund_position

      import_ledgers(ProductCenter::Account)
    end

    def destroy_exist_data
      accounts = ProductCenter::Account.where(quarter_id: @quarter_id)
      ProductCenter::FundPosition.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      ProductCenter::Role.where(quarter_id: @quarter_id).delete_all
      ProductCenter::Fund.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select
          id,
          id,
          name,
          status,
          user_id,
          type
        from
          #{@table_account}
        where
          user_id is not null
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'status', 'value' => '01', 'enum_value' => '正常', 'label' => '账号状态' }]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << ProductCenter::Account.create(
            quarter_id:   @quarter_id,
            source_id:    get_enum(enums, 'source_id', r[0]),
            code:         get_enum(enums, 'code', r[1]),
            name:         get_enum(enums, 'name', r[2]),
            status:       r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s,
            ledger_rele:  r[4].to_s,
            account_type: r[5]
          )
        end
      end
    end

    def update_accounts_sql
      <<-EOF
        select user_id, employee_number, status from #{@table_account}
      EOF
    end

    def update_accounts
      ActiveRecord::Base.transaction do
        output_datas = []
        @update_pd_db.exec(update_accounts_sql) do |r|
          output_datas << r.to_a
        end

        output_datas.each do |r|
          acts = @accounts.where(ledger_rele: r[0])

          next unless acts.present?

          status = r[2].to_s == '0'
          acts.each do |account|
            account.ledger_rele = r[1]
            account.status = status
            account.save
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select
          distinct
          type,
          type,
          type
        from
          #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = [{ 'name' => 'name', 'value' => '1', 'enum_value' => '基金经理', 'label' => '角色名称' },
                 { 'name' => 'name', 'value' => '2', 'enum_value' => '母公司投资经理', 'label' => '角色名称' },
                 { 'name' => 'name', 'value' => '3', 'enum_value' => '子公司投资经理', 'label' => '角色名称' }]
        select_db_datas(import_roles_sql).each do |r|
          @roles << ProductCenter::Role.create(
            quarter_id: @quarter_id,
            source_id:  get_enum(enums, 'source_id', r[0]),
            code:       get_enum(enums, 'code', r[1]),
            name:       get_enum(enums, 'name', r[2])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select
          type,
          id
        from
          #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          ProductCenter::FundPosition.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_funds_sql
      <<-EOF
        select
          id,
          code,
          name,
          status,
          fund_type,
          stage,
          template_id,
          code,
          progress
        from
          #{@table_product}
        where
          code is not null
        and template_id != 'special'
        union
        select
          p.id,
          p.code,
          p.name,
          p.status,
          p.fund_type,
          p.stage,
          p.template_id,
          t.value,
          p.progress
        from
          #{table_product} p,
        inner join
          #{attribute} t
        on
          p.id = t.table_key
        where
          t.code like 'inner_code'
        and
          p.template_id = 'special'
      EOF
    end

    def import_funds
      enums = []
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        config_path = File.join(__dir__, '../../configs/funds_config.yml')
        enums = YAML.load_file config_path
        fund_category = enums&.[]('fund_category')
        # stage         = enums&.[]('stage')
        fund_type     = enums&.[]('fund_type')

        select_db_datas(import_funds_sql).each do |r|
          name = r[level1_name_index]
          level1_name = replace_blank_name(name)

          # status: { disabled: 0, inservice: 1, unknown: 2 }
          # stage 转换为 status
          # 1 需求期 2
          # 2 筹备期 2
          # 3 发行期 1
          # 4 存续期 1
          # 5 清盘期 1
          # 6 储备库 2

          fund_status =
            case r[5].to_s
            when '5',
              if r[6].to_s == 'special' && r[8].to_s.in?(%w[2 3])
                2
              elsif r[6].to_s == 'childSpecial' && r[8].to_s.in?(%w[2 3])
                2
              elsif r[6].to_s == 'publicFundAC' && r[8].to_s.in?(%w[6 7])
                2
              else
                1
              end
            when '3', '4 '
              1
            else
              2
            end

          json = {
            quarter_id:       @quarter_id,
            source_id:        r[0],
            code:             r[1],
            name:             level1_name,
            status:           fund_status,
            fund_category:    get_enum(fund_category, 'fund_category', r[4]),
            fund_type:        get_enum(fund_type, 'fund_type', r[6]),
            o32_fund_codes:   [r[7]],
            guzhi_fund_codes: [r[7]]
          }
          @funds << ProductCenter::Fund.create(json)
        end
      end
    end

    def import_fund_position_sql
      <<-EOF
        select
          fund_manager_id,
          product_id,
          status,
          start_date,
          end_date,
          fund_code
        from
          #{@table_arps}
      EOF
    end

    def import_fund_position
      enums = []
      select_db_datas(import_fund_position_sql).each do |r|
        account  = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        fund     = @funds.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && fund

        role = @roles.find { |x| x.source_id.to_s == account.account_type.to_s }

        take_post_date  = Date.parse(r[3]) if r[3].present?
        leave_post_date = Date.parse(r[4]) if r[4].present?

        ProductCenter::FundPosition.create(
          quarter_id:      @quarter_id,
          fund_id:         fund.id,
          account_id:      account.id,
          role_id:         role.id,
          status:          r[2],
          take_post_date:  take_post_date,
          leave_post_date: leave_post_date
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
