module AasOracleImporter

  class KmSystemImporter < ImporterBase

    def config
      @bs_id       = 65
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      
      
      @data3_permissions = []
      @data3_accounts_roles_permissions = []
      
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data2_permissions
      import_data2_role_permissions
      import_data3_account_permissions

      import_ledgers(KmSystem::Account)
    end

    def destroy_exist_datas
      accounts = KmSystem::Account.where(quarter_id: @quarter_id)
      KmSystem::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      KmSystem::Role.where(quarter_id: @quarter_id).delete_all
      KmSystem::Data1Permission.where(quarter_id: @quarter_id).delete_all
      KmSystem::Data2Permission.where(quarter_id: @quarter_id).delete_all
      KmSystem::Data3Permission.where(quarter_id: @quarter_id).delete_all
    end

    
    def import_accounts_sql
      <<-EOF
        select t.operatorname,t.operatorid,t.userid from #{@table_space}eosoperator t
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << KmSystem::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[1] ,
            code:  r[2],
            name:  r[0],
            status: true
          )
        end
      end
    end

    
    def import_roles_sql
      <<-EOF
        select DISTINCT
          n.vc_name, m.VC_NAME, m.n_busiroleid               
        from 
          #{@table_space}af_positionrole m
        left join
          #{@table_space}af_position n 
        on
          m.n_positionid = n.n_positionid
      EOF
    end
    

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << KmSystem::Role.create(quarter_id: @quarter_id,code: r[2], name: r[1], source_id: r[2], data_json: {role_type: r[0]})
          #KmSystem::AccountsRole.create(account_id: account.id, role_id: role.id  )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select DISTINCT
          n_posiroleid, n_operatorid, '普通权限' as roletype
        from 
          #{@table_space}af_posi_role2staff
        union
        select DISTINCT
          N_BUSIROLEID, N_OPERATORID, '虚拟岗位' as roletype
        from 
          #{@table_space}AF_POSI_VIRTUALROLE2STAFF
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          if account && role
            KmSystem::AccountsRole.create(account_id: account.id, role_id: role.id, role_scope: r[2])
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select 
          t1.n_recid, t2.n_funcid, t1.vc_funcname, t1.n_parentid, t2.vc_status
        from
          #{@table_space}af_prfunclist t1 
        left join #{@table_space}af_function t2
          on t1.vc_busifunccode = t2.vc_code
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          @data1_permissions << KmSystem::Data1Permission.create(
            quarter_id: @quarter_id, 
            source_id: r[0],
            code: r[0],
            level1_name: r[2],
            level2_name: r[3],
            level3_name: r[1],
            data_json: {vc_status: r[4]}
          )
        end
      end
    end

    
    def import_data1_role_permissions_sql
      <<-EOF
        select 
          t.n_batchid, t.n_funcid, t.vc_busiroleids
        from
          #{@table_space}af_grantrightbatch t
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role_ids = r[2].to_s.split(",")
          role_ids.each do |role_id|
            role = @roles.find{|x| x.source_id.to_s == role_id}
            permissions = @data1_permissions.select{|x| x.level3_name.to_s == r[1].to_s && x.data_json[:vc_status].present? && x.data_json[:vc_status] != '停用'}
            permissions.each do |permission|
              parent_permission = @data1_permissions.find{|x| x.source_id.to_s == permission.level2_name}
              if role && parent_permission && !role.name.include?('系统测试员工')
                KmSystem::Data1AccountsRolesPermission.create(
                  quarter_id: @quarter_id, 
                  role_id: role.id, 
                  data1_permission_id: permission.id,
                  data_json: {
                    filters: filter_user_json[r[0].to_s].to_a
                  }
                )
              end
            end
          end
        end
      end
    end
    
    def import_data2_permissions_sql
      <<-EOF
        select 
          t2.vc_code, t.vc_code, t.vc_funcname, t2.vc_funcname 
        from
          #{@table_space}af_function t             
        left join #{@table_space}af_function t2                
          on t2.vc_wfdefname = t.vc_code
        where
          t2.vc_functype = '活动' && t.vc_status != '停用' && t2.vc_status != '停用'
      EOF
    end

    
    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          @data2_permissions << KmSystem::Data2Permission.create(
            quarter_id: @quarter_id, 
            source_id: r[0], 
            code: r[0],
            level1_name: r[2],
            level2_name: r[3],
            level3_name: r[1]
          )
        end
      end
      
    end
    
    def import_data2_role_permissions_sql
      <<-EOF
        select 
          t.n_batchid, t.n_funcid, t.vc_busiroleids, t.vc_value
        from
          #{@table_space}af_grantrightbatch t
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role_ids = r[2].to_s.split(",")
          role_ids.each do |role_id|
            next if role_id == '2014213'
            role = @roles.find{|x| x.source_id.to_s == role_id}
            permission = @data2_permissions.find{|x| x.source_id.to_s == r[1].to_s}
            if role && permission && !role.name.include?('系统测试员工')
              KmSystem::Data2AccountsRolesPermission.create(
                quarter_id: @quarter_id, 
                role_id: role.id, 
                data2_permission_id: permission.id,
                additional_permission: '',
                data_json: {
                  filters: filter_user_json[r[0].to_s].to_a,
                  add_permission: r[3].to_s.gsub("<![CDATA[","").gsub("]]>","")
                }
              )
            end
          end
        end
      end
    end
    
    def import_data3_account_permissions_sql
      <<-EOF
        select b.vc_enkey,b.vc_cnname, t.n_operatorid
 from #{@table_space}oa_staffposition_staff t,#{@table_space}oa_staffposition b           
where t.vc_staffposition_enkey=b.vc_enkey
      EOF
    end

    def import_data3_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_account_permissions_sql).each do |r|
          permission = KmSystem::Data3Permission.find_or_create_by(
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1]
          )
          account = @accounts.find{|x| x.source_id.to_s == r[2].to_s}
          if account
            KmSystem::Data3AccountsRolesPermission.create(
              quarter_id: @quarter_id, 
              account_id: account.id, 
              data3_permission_id: permission.id
            )
          end
        end
      end
    end

    def filter_user_json
      @filter_user_json ||= set_filter_user_json
    end

    def set_filter_user_json
      sql = <<-EOF
        select n_grantbatchid, n_operatorid, c_filterflag from #{@table_space}af_grantrightfilter
      EOF
      output_json = {}
      select_db_datas(sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
        if account
          output_json[r[0].to_s] ||= []
          output_json[r[0].to_s] << {account_code: account.source_id, account_name: account.name, filter_flag: r[2]}
        end
      end
      output_json
    end
    

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
