module AasOracleImporter
  # GreatlifeOaImporter
  class GreatlifeOaImporter < ImporterBase
    def config
      @bs_id = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @departments = []
      @accounts    = []
      @roles       = []
      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}sys_org_person#{@sid_suffix}"
      @table_element      = "#{@table_space}sys_org_element#{@sid_suffix}"
      @table_role         = "#{@table_space}sys_org_role_conf#{@sid_suffix}"
      @table_account_role = "#{@table_space}sys_org_role_line#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      # import_accounts_and_roles
      import_departments
      import_accounts
      import_roles
      import_accounts_roles
      import_ledgers(GreatlifeOa::Account)
    end

    private

    def destroy_exist_datas
      GreatlifeOa::Account.where(quarter_id: @quarter_id).destroy_all
      GreatlifeOa::Role.where(quarter_id: @quarter_id).destroy_all
    end

    def import_departments_sql
      "select fd_id, fd_name from #{@table_element}"
    end

    def import_departments
      @database.exec(import_departments_sql) do |r|
        @departments << [r[0], r[1]]
      end
    end

    # fd_parentid对应部门
    def import_accounts_sql
      "select a.fd_id, b.fd_name, a.fd_email, b.fd_is_available, b.fd_parentid from #{@table_account} a, #{@table_element} b where a.fd_id=b.fd_id"
    end

    def import_accounts
      @database.exec(import_accounts_sql) do |r|
        company = @departments.find { |x| x[0].to_s == r[4].to_s }&.[](1)
        status = r[3].blank? || r[3] == 1
        account = GreatlifeOa::Account.create(
          code:       r[0],
          name:       r[1],
          email:      r[2],
          company:    company,
          quarter_id: @quarter_id,
          status:     status
        )
        @accounts << account
      end
    end

    def import_roles_sql
      "select fd_id, fd_name from #{@table_role} where fd_order !='999' or fd_order is null"
    end

    def import_roles
      @database.exec(import_roles_sql) do |r|
        @roles << GreatlifeOa::Role.find_or_create_by(
          code:       r[0],
          name:       r[1],
          quarter_id: @quarter_id
        )
      end
    end

    def import_accounts_roles_sql
      "select fd_member_id, fd_role_line_conf_id from #{@table_account_role}"
    end

    def import_accounts_roles
      @database.exec(import_accounts_roles_sql) do |r|
        account = @accounts.find { |x| x.code.to_s == r[0].to_s }
        role    = @roles.find { |x| x.code.to_s == r[1].to_s }
        next unless account && role

        GreatlifeOa::AccountsRole.create(account_id: account.id, role_id: role.id)
      end
    end

    # sys_org_element 中的 fd_is_available 为用户是否有效 （1：有效 0：失效）
    def import_accounts_and_roles_sql
      <<-SQL
        SELECT
          a.fd_id as 用户ID,
          b.fd_name as 用户姓名,
          a.fd_email as 用户邮箱,
          c.fd_name as 用户部门,
          e.fd_id as 角色线ID,
          e.fd_name as 角色线名称,
          b.fd_is_available
        FROM
          #{@table_account} a ,
          #{@table_element} b ,
          #{@table_element} c ,
          #{@table_account_role} d,
          #{@table_role} e
        where
          a.fd_id=b.fd_id
          and b.fd_parentid=c.fd_id
          and d.fd_member_id=c.fd_id
          and d.fd_role_line_conf_id=e.fd_id
          and (e.fd_order !='999' or e.fd_order is null)
      SQL
    end

    def import_accounts_and_roles
      GreatlifeOa::Account.where(quarter_id: @quarter_id).destroy_all
      GreatlifeOa::Role.where(quarter_id: @quarter_id).destroy_all

      @database.exec(import_accounts_and_roles_sql) do |r|
        import_line(r)
      end
    end

    def import_line(row)
      account_code, account_name, account_email, account_company, role_code, role_name, account_status = row
      status = account_status.blank? || account_status == 1
      account = find_or_create_account(account_code, account_name, account_email, account_company, status)
      role    = find_or_create_role(role_code, role_name)
      add_link_with_account_and_role(account, role)
    end

    def find_or_create_account(code, name, email, company, status)
      account =
        GreatlifeOa::Account.find_by(
          code:       code,
          quarter_id: @quarter_id
        )
      if account
        account.update(
          name:    name,
          email:   email,
          company: company,
          status:  status
        )
      else
        account =
          GreatlifeOa::Account.create(
            code:       code,
            name:       name,
            email:      email,
            company:    company,
            quarter_id: @quarter_id,
            status:     status
          )
      end
      account
    end

    def find_or_create_role(code, name)
      role =
        GreatlifeOa::Role.find_or_create_by(
          code:       code,
          name:       name,
          quarter_id: @quarter_id
        )
      role
    end

    def add_link_with_account_and_role(account, role)
      account.roles << role unless account.roles.exists? role.id
    end
  end
end
