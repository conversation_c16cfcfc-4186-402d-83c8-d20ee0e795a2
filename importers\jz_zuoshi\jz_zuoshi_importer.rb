module AasOracleImporter

  class JzZuoshiImporter < ImporterBase

    def config
      @bs_id       = importer_config["bs_id"]
      @table_space = importer_config["table_space"]
      @sid_suffix  = importer_config["sid_suffix"]
      @accounts = []
      @roles = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      @data3_permissions = []
      @data3_accounts_roles_permissions = []
      @data4_permissions = []
      @data4_accounts_roles_permissions = []
      @data5_permissions = []
      @data5_accounts_roles_permissions = []
      
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data3_permissions
      import_data3_account_permissions

      import_data4_permissions
      import_data4_account_permissions

      import_data5_permissions
      import_data5_account_permissions

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_account_permissions
      import_data2_role_permissions

      import_ledgers(JzZuoshi::Account)

    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas("SELECT A.USER_CODE, A.USER_NAME,A.CORP_ORG,A.INT_ORG,B.OP_STATUS FROM USERS A,OPERATOR B WHERE A.USER_CODE=B.OP_CODE").each do |r|
          @accounts << JzZuoshi::Account.create(quarter_id: @quarter_id, code: r[0], name: r[1], int_org: r[3], corp_org: r[2], status: r[4].to_i.to_s == "0")
        end
      end
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas("select POST_CODE, POST_NAME from #{@table_space}POST").each do |r|
          @roles << JzZuoshi::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, POST_CODE from #{@table_space}USER_POST").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          role = @roles.find{|x| x.code == r[1].to_s}
          if account && role
            account.roles << role
          end
        end
      end
    end
    
    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select RTRES_ID, RTRES_NAME from #{@table_space}SYS_RTRES").each do |r|
          @data1_permissions << JzZuoshi::Data1Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1])
        end
      end
    end
    
    def import_data1_role_permissions
      org_scope_json = {
        "0" => "本机构",
        "1" => "下属机构",
        "2" => "本机构及下属机构"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.POST_CODE, a.RTRES_ID, a.ORG_CODE, a.ORG_SCOPE from #{@table_space}POST_RTRES a").each do |r|
          role = @roles.find{|x| x.code == r[0].to_s}
          permission = @data1_permissions.find{|x| x.code == r[1].to_s}
          org = @data3_permissions.find{|x| x.code == r[2].to_s}
          org_name = org ? org.name : ''
          org_name = '总部' if r[2].to_s == '0'
          org_name = '未指定机构' if r[2].to_s == '-1'
          next unless permission && role
          JzZuoshi::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id, additional_permission: org_name , permission_scope: org_scope_json[r[3].to_s] )
        end
      end
    end

    def import_data1_account_permissions
      org_scope_json = {
        "0" => "本机构",
        "1" => "下属机构",
        "2" => "本机构及下属机构"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.USER_CODE, a.RTRES_ID, a.ORG_CODE, a.ORG_SCOPE from #{@table_space}USER_RTRES a").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data1_permissions.find{|x| x.code == r[1].to_s}
          org = @data3_permissions.find{|x| x.code == r[2].to_s}
          org_name = org ? org.name : ''
          org_name = '总部' if r[2].to_s == '0'
          org_name = '未指定机构' if r[2].to_s == '-1'
          next unless permission && account
          JzZuoshi::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id, additional_permission: org_name , permission_scope: org_scope_json[r[3].to_s] )
        end
      end
    end

    def import_data2_permissions
      menu_type_json = {
        "0" => "父级菜单",
        "1" => "功能菜单",
        "2" => "菜单分割线"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.MENU_CODE,a.MENU_NAME,a.MENU_TYPE from #{@table_space}SYS_MENU a").each do |r|
          @data2_permissions << JzZuoshi::Data2Permission.create(quarter_id: @quarter_id, code: r[0].to_i.to_s, name: r[1], level1_name: r[1], data_json: {menu_type: menu_type_json[r[2].to_s]})
        end
      end
    end

    def import_data2_account_permissions
      add_json = {
        "0" => "执行权限",
        "1" => "授权权限",
        "2" => "执行和授权"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.USER_CODE, a.MENU_CODE, a.RIGHT_TYPE from #{@table_space}USER_MENU a").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data2_permissions.find{|x| x.code == r[1].to_i.to_s}
          next unless permission && account

          JzZuoshi::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data2_permission_id: permission.id, additional_permission: add_json[r[2].to_s])
        end
      end
    end

    def import_data2_role_permissions
      add_json = {
        "0" => "执行权限",
        "1" => "授权权限",
        "2" => "执行和授权"
      }
      ActiveRecord::Base.transaction do
        select_db_datas("select a.POST_CODE, a.MENU_CODE, a.RIGHT_TYPE from #{@table_space}POST_MENU a").each do |r|
          role = @roles.find { |x| x.code == r[0].to_s }
          permission = @data2_permissions.find { |x| x.code == r[1].to_i.to_s }
          next unless permission && role

          JzZuoshi::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data2_permission_id: permission.id, additional_permission: add_json[r[2].to_s])
        end
      end
    end

    def import_data3_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select ORG_CODE, ORG_NAME, PARENT_ORG from #{@table_space}ORG").each do |r|
          @data3_permissions << JzZuoshi::Data3Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1], level2_name: r[2])
        end
      end
    end

    def import_data3_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, RTOBJ from #{@table_space}USER_RTOBJ where RTOBJ_TYPE = '0'").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data3_permissions.find{|x| x.code == r[1].to_s}
          next unless permission && account
          JzZuoshi::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id)
        end
      end
    end

    def import_data4_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select ASSET_ID, ASSET_NAME from #{@table_space}ASSET_UNIT").each do |r|
          @data4_permissions << JzZuoshi::Data4Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1])
        end
      end
    end

    def import_data4_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, RTOBJ from #{@table_space}USER_RTOBJ where RTOBJ_TYPE = '4'").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data4_permissions.find{|x| x.code == r[1].to_s}
          next unless permission && account
          JzZuoshi::Data4AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data4_permission_id: permission.id)
        end
      end
    end

    def import_data5_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select STK_POOL_ID, STK_POOL_NAME from #{@table_space}STK_POOL").each do |r|
          @data5_permissions << JzZuoshi::Data5Permission.create(quarter_id: @quarter_id, code: r[0], name: r[1], level1_name: r[1])
        end
      end
    end

    def import_data5_account_permissions
      ActiveRecord::Base.transaction do
        select_db_datas("select USER_CODE, RTOBJ from #{@table_space}USER_RTOBJ where RTOBJ_TYPE = '5'").each do |r|
          account = @accounts.find{|x| x.code == r[0].to_s}
          permission = @data5_permissions.find{|x| x.code == r[1].to_s}
          next unless permission && account
          JzZuoshi::Data5AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data5_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r
      end
      output_datas
      
    end

  end
end



