module AasOracleImporter
  # AD 域数据导入
  class AdGroupYebImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @account_filter = importer_config['account_filter']
      @account_attrs  = importer_config['account_attrs']
      @group_filter   = importer_config['group_filter']
      @group_attrs    = importer_config['group_attrs']
      @set_gtry_account_type = importer_config['set_gtry_account_type']
      initialize_classes
    end

    def import_to_do
      destroy_old_data
      import_groups
      import_accounts
      set_all_account_type if @set_gtry_account_type
      import_ledgers(@account_class)
    end

    private

    def initialize_classes
      @account_class = AdGroupYeb::Account
      @role_class    = AdGroupYeb::Role
    end

    def destroy_old_data
      @account_class.where(quarter_id: @quarter_id).destroy_all
      @role_class.where(quarter_id: @quarter_id).destroy_all
    end

    def import_groups
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @group_filter, attributes: @group_attrs) do |entry|
          import_a_group(entry)
        end
      end
    end

    def import_accounts
      @roles = @role_class.where(quarter_id: @quarter_id).to_a
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @account_filter, attributes: @account_attrs) do |entry|
          import_an_account(entry)
        end
      end
    end

    def import_a_group(entry)
      return unless entry.name.first
      @role_class.create(
        quarter_id: @quarter_id,
        code:       entry.dn,
        gid:        get_sid_string(entry[:objectSid]&.first.to_s).split("-").last,
        name:       entry.name.first,
        data_json:  {memberOf: entry[:memberOf], sAMAccountType: entry[:sAMAccountType].first.to_s}
      )
      
    end

    def import_an_account(entry)
      gid   = get_sid_string(entry[:objectSid]&.first.to_s).split("-").last
      account_code   = entry.samaccountname&.first&.downcase
      account_name   = entry.name&.first&.gsub(/\s+/, '')
      account_comment   = entry[:description]&.first&.gsub(/\s+/, '')

      return unless account_code && account_name

      account = @account_class.create(
        quarter_id:   @quarter_id,
        code:         account_code,
        name:         account_name,
        gid:          gid,
        status:       account_status(entry),
        comment:      account_comment,
        account_type: 1
      )

      role = AdGroupYeb::Role.where(gid: entry[:primaryGroupID]&.first).last
      account.roles << role if role

      member_of = entry[:memberof]
      return unless member_of&.size&.positive?
      link_account_and_roles(account, member_of)
    end

    def account_status(entry)
      status_sequences = entry.useraccountcontrol.first.to_i.to_s(2)
      status_sequences[-2] != '1'
    end

    def link_account_and_roles(account, member_of)
      member_of.each do |role_code|
        role = @roles.find { |x| x.code == role_code }
        account.roles << role if role
      end
    end

    def set_all_account_type
      accounts = AdGroupYeb::Account.where(quarter_id: @quarter_id)
      user_code_list = ['yuanye'] # 特殊用户账号
      accounts.each do |account|
        groups = account.roles.pluck(:name)
        # account.status = !groups.include?('离职员工组')
        # account_type 1 用户账号，2 应用账号，3 系统账号 4 管理账号
        #if (groups - ['离职员工组','国投瑞银全体员工','实习生','外包人员']).size != groups.size
          #account.account_type = 1
        if account.code.include?('.') || user_code_list.include?(account.code) || account.comment.to_s.downcase.include?('citrix')
          account.account_type = 1
        elsif account.comment.to_s[0..8].include?('管理账号-') #account.code.to_s.downcase.include?('admin')
          account.account_type = 4
        #elsif groups.include?('应用系统管理及备份组')
        #  account.account_type = 2
        elsif account.comment.to_s[0..8].include?('系统账号-')
          account.account_type = 3
        else
          account.account_type = 3
        end
        account.save
      end
    end

    def get_sid_string(data)
      sid = data.to_s.unpack('b x nN V*')
      sid[1, 2] = Array[nil, b48_to_fixnum(sid[1], sid[2])]
      'S-' + sid.compact.join('-')
    end

    def b48_to_fixnum(i16, i32)
      i32 + (i16 * 2**32)
    end
  end
end



