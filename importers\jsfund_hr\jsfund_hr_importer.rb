module AasOracleImporter

  class JsfundHrImporter < ImporterBase

    def config
      @table_name = importer_config['table_name']
      @user_normal_status_text = importer_config['user_normal_status_text']
    end

    # HR 系统没有 bs_id 因此重写 import 方法

    def import_to_do
      # before_import
      update_records
    end

    private

    def before_import
      #clear_departments_and_users
    end

    def update_records
      sql = <<-EOF
        select EMPLID, OPRID, NAME, HR_STATUS, EMAIL_ADDR, PHONE, DEPTDESC, BUDESC from #{@table_name}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          code, ad_name, name, status, email, phone, department_name, company_name = r

          department = find_or_create_department(department_name)
          company    = find_or_create_company(company_name)
          link_company_department(company, department)

          user = User.find_by(code: code)
          if user
            user.update(
              name: name,
              ad_name: ad_name,
              inservice: status == @user_normal_status_text,
              email: email,
              phone: phone,
              department_id: department&.id,
              company_id: company&.id
            )
          else
            User.create(
              code: code,
              name: name,
              ad_name: ad_name,
              inservice: status == @user_normal_status_text,
              email: email,
              phone: phone,
              department_id: department&.id,
              company_id: company&.id
            )
          end
        end
      end

    end

    def find_or_create_department(name)
      return nil if name.nil? or name == ''

      d = Department.find_by(name: name)
      if d
        d.update(inservice: true, d_type: 2, level: 2)
      else
        d = Department.create(name: name, inservice: true, d_type: 2, level: 2)
      end
      d
    end

    def find_or_create_company(name)
      return nil if name.nil? or name == ''

      d = Department.find_by(name: name)
      if d
        d.update(inservice: true, d_type: 1, level: 1)
      else
        d = Department.create(name: name, inservice: true, d_type: 1, level: 1)
      end
      d
    end

    def link_company_department(company, department)
      department.update(parent_id: company.id)
    end
  end
end



