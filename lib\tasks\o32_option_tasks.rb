class O32OptionRakes
  def initialize(quarter_id = nil)
    @quarter_id = quarter_id
    @importer = AasOracleImporter.config['importers'].find { |x| x['bs_id'] == 31 }
    @logger = AasOracleImporter.initialize_logger
    @server_config = AasOracleImporter.config['server']
  end

  def import
    return if @importer.nil?

    database = DatabaseClient.new_database(@importer['db_type'], @importer['tnsname'])
    o32_option_sql = "select L_SERIAL_NO, VC_DESCRIBE, VC_VALUE, VC_SHOW_TITLE, VC_SHOW_SUBTITLE, VC_VALUE_BOUND, VC_FULL_DESCRIBE, C_MODIFY from #{@importer['table_space']}tsysparameter"
    # o32_option_sql = 'select code, name, value, category, children_category from o32_options'
    @quarter = @quarter_id.present? ? O32OptionQuarter.find(@quarter_id) : O32OptionQuarter.create(name: Time.now.to_s(:db), status: 0)
    @logger.info "开始导入o32参数 quarter_id「#{@quarter.id}」"
    data = []
    begin
      database.exec(o32_option_sql) do |r|
        data << r
      end
      O32Option.bulk_insert(:item_id, :item_type, :code, :name, :value, :category, :children_category, :desc, :full_desc, :c_modify, :created_at, :updated_at) do |obj|
        obj.set_size = 1000
        data.uniq.each do |x|
          obj.add [@quarter.id, 'O32OptionQuarter', x[0], x[1]&.strip, x[2], x[3], x[4], x[5], x[6], x[7], Time.now, Time.now]
        end
      end

      # 更新配置信息更新参数的owner和操作人
      o32_option_configs = O32OptionConfig.all
      o32_options = O32Option.where(item_id: @quarter.id, item_type: 'O32OptionQuarter', code: o32_option_configs.pluck(:code))
      o32_options.find_each do |x|
        o32_option_config = o32_option_configs.find { |config| config.code == x.code }
        next if o32_option_config.blank?

        x.update_columns(
          department_ids:   o32_option_config.department_ids,
          department_names: o32_option_config.department_names,
          department_id:    o32_option_config.department_id,
          department_name:  o32_option_config.department&.name
        )
      end
      @quarter.update(status: 1)
      after_importer_rake
    rescue => e
      @quarter.update(status: 2)
      @logger.error { e.message }
      @logger.debug { e.backtrace.join("\n") }
      AasOracleImporter.audit_log_o32_option_import(@quarter, e.backtrace.join("\n"), :failed)
    end
  end

  # 导入完成后，运行o32参数时间点和O32基线差异任务
  def after_importer_rake
    return unless @server_config['after_importer_rake']

    task = "bundle exec rails 'o32_option_difference:generate[#{@quarter.id}]' && bundle exec rails 'o32_option_difference:generate_baseline[#{@quarter.id}]'"
    @logger.info "开始执行对比时间点和参数基线任务 #{task}"
    Dir.chdir @server_config['path']
    system "BUNDLE_GEMFILE=#{@server_config['path']}/Gemfile && #{task}"
    @logger.info "结束执行对比时间点和参数基线任务 #{task}"
  end
end