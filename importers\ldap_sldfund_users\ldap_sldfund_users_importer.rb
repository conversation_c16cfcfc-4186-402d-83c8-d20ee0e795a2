# frozen_string_literal: true

module AasOracleImporter
  # AD 域数据导入
  class LdapSldfundUsersImporter < ImporterBase
    def config
      @bs_id                      = importer_config['bs_id']
      @account_filter             = importer_config['account_filter']
      @account_attrs              = importer_config['account_attrs']
      @group_filter               = importer_config['group_filter']
      @group_attrs                = importer_config['group_attrs']
      @top_ou                     = importer_config['top_ou']
      @ignore_account_name        = importer_config['ignore_accountname']
      @ignore_ou                  = importer_config['ignore_ou']
      @ignore_ou_but_import_users = importer_config['ignore_ou_but_import_users'] || []
      # @type [Hash]
      @user_ou_sequence           = importer_config['user_ou_sequence']
      initialize_classes
    end

    def import_to_do
      if @top_ou.count.zero?
        start_import(nil)
      else
        @top_ou.each do |root_ou|
          start_import(root_ou)
        end
      end
    end

    def start_import(root_ou)
      import_groups(root_ou)
      import_accounts(root_ou)
      flag_default_ou
    end

    def the_system
      BusinessSystem.new(name: 'AD')
    end

    private

    def initialize_classes
      @default_ou_flag = true
    end

    def flag_default_ou
      @default_ou_flag = false if @default_ou_flag
    end

    def import_groups(top_ou)
      ActiveRecord::Base.transaction do
        entries = @database.search(base: query_base(top_ou), filter: @group_filter, attributes: @group_attrs)
        entries.each do |entry|
          import_a_group(entry, top_ou)
        end
      end
    end

    def import_accounts(top_ou)
      ActiveRecord::Base.transaction do
        entries = @database.search(base: query_base(top_ou), filter: @account_filter, attributes: @account_attrs)
        entries.each do |entry|
          import_an_account(entry)
        end
      end
    end

    def query_base(top_ou)
      top_ou.present? ? "ou=#{top_ou}, #{@database.base}" : @database.base
    end

    def ignore_import_department?(name)
      @ignore_ou.include?(name) ||
        @ignore_ou_but_import_users.include?(name)
    end

    def ignore_import_account?(person_struct)
      @ignore_account_name.include?(person_struct.login) || @ignore_ou.include?(person_struct.department)
    end

    def import_a_group(entry, _top_ou)
      # department_struct                    = LdapDepartment.new(entry)
      # department_struct.top_ou             = top_ou
      # department_struct.hidden_parent_name = @default_ou_flag

      # return unless department_struct.name

      name = entry.name&.first
      code = entry&.dn

      return unless code&.start_with?('OU')
      return if name.blank?
      return if ignore_import_department?(name)

      names = code.scan(/(?<=OU=)[^,]*/) - @ignore_ou
      level = names.length
      parent = Department.find_by(name: names[1], level: level - 1) if names.length > 1
      department           = Department.find_or_initialize_by(code: code)
      department.name      = name
      department.inservice = true
      department.level     = level
      department.parent_id = parent&.id
      department.save
    end

    def import_an_account(entry)
      person_struct             = LdapPerson.new(entry)
      person_struct.ou_sequence = @user_ou_sequence

      return if ignore_import_account?(person_struct)
      return unless person_struct.code && person_struct.name

      user               = User.find_or_initialize_by(code: person_struct.code)
      user.email         = person_struct.email
      user.name          = person_struct.name
      user.inservice     = person_struct.inservice
      user.department_id = Department.find_by(code: person_struct.department_code)&.id
      # 邮箱如果没有设置就手动拼一个
      user.save
    end
  end
end
