# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'gffund'
  customer: '广发基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'

  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_gffund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # 投资交易 O32 系统数据库
      tradedb:
        db_name: 'trade'
        db_user: 'tradetest3'
        db_pass: 'tradetest3'
        db_host: '************'

  importers:

    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: ''
      sid_suffix: ''
      days_of_data: 30
      temporary: true

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    # 要改
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_gffund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


