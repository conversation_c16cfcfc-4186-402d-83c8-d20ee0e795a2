module ZofundGuohuTa
  def self.table_name_prefix
    'zofund_guohu_ta_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class ZofundGuohuTa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles, -> { distinct }

  has_many :data1_accounts_roles_permissions

  serialize :data_json, JsonWithSymbolizeNames
  validates :code, presence: true
  validates :name, presence: true
end

class ZofundGuohuTa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts

  has_many :data1_accounts_roles_permissions

  validates :code, presence: true
  validates :name, presence: true
end

class ZofundGuohuTa::AccountsRole < ActiveRecord::Base
  validates :account_id, presence: true
  validates :role_id, presence: true
end

class ZofundGuohuTa::Data1Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :level1_name, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class ZofundGuohuTa::Data1AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'zofund_guohu_ta_data1_arps'

  belongs_to :data1_permission
  belongs_to :role, optional: true

  validates :data1_permission_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end
