# frozen_string_literal: true

module AasOracleImporter
  # 信披系统导入，mysql 的，没有基金代码对照
  class FundDisclosureImporter < ImporterBase
    include ConvertTools
    def config
      @bs_id        = importer_config['bs_id']
      @base_url = importer_config['base_url'] #glajj.
      @token = importer_config['token'] #''
      @reqSys = importer_config['reqSys']
      @pass_company = importer_config['pass_company']
      @departments = []
      @accounts = []
      @fd_funds = []
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_funds
      import_accounts_funds
      import_ledgers(FundDisclosure::Account)
    end

    def destroy_exist_datas
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts
      datas = send_request('/comsvr/sqlservice/getFundManagerInfos')['data']
      datas.each do |data|
        account = FundDisclosure::Account.find_or_initialize_by(quarter_id: @quarter_id, code: data['managerCode'])
        account.name = data['managerName']
        account.status = true
        account.save
        @accounts << account unless @accounts.include?(account)
      end
    end

    def import_funds
      datas = send_request('/comsvr/sqlservice/getFundInfos')['data']
      datas.each do |data|
        fd_fund = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: data['fundCode'])
        fd_fund.name = data['fundName']
        fd_fund.fund_type = data['businType']
        fd_fund.status = fund_status_array[data['fundStatus'].to_i]
        fd_fund.save
        @fd_funds << fd_fund unless @fd_funds.include?(fd_fund)
      end
    end

    def import_accounts_funds
      datas = send_request('/comsvr/sqlservice/getFundManagerInfos')['data']
      datas.each do |data|
        account = @accounts.find{|x| x.code == data['managerCode']}
        fund = @fd_funds.find{|x| x.code == data['fundCode']}
        role_name = role_name_json[data['managerType'].to_s]
        role = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, code: role_name, name: role_name)
        if account && fund
          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  data['startDate'],
            leave_post_date: data['leaveDate'],
            quarter_id:      @quarter_id
          )
        end
      end
    end

    def role_name_json
      @role_name_json ||= {
        '0' => '经理', 
        '1' => '助理'
      }
    end

    def fund_status_array
      @fund_status_array ||= ['unknown','inservice','disabled']
    end

    def fund_type_json
      @fund_type_json ||= {
        GM: '公募',
        YDY: '一对一',
        YDD: '一对多',
        NJ: '年金',
        SB: '社保',
        YLJ: '养老金',
        ZYNJ: '职业年金',
        YL: '基本养老',
        HRJJ_HK: '香港互认基金'
      }
    end

    def send_request(uri)
      require 'httparty'
      body = {
        "reqSys" => @reqSys,
        "page" => 1,
        "size" => 100000
      }

      response = HTTParty.post("#{@base_url}#{uri}", 
                                  headers: headers,
                                  body: body.to_json
                                )
      result = JSON.parse response.body
      if result['retCode'].to_i != 0 || result['data'].size == 0
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers
      { 
        'Esb-Token' => @token,
        'Content-Type' => 'application/json' 
      }
    end
  end
end



