# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'hftfund'
  customer: '海富通基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_hftfund_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      tradedb:
        db_host: ************
        db_name: hfttrade
        db_user: dc_query
        db_pass: dc_query
      tadb:
        db_host: ***********
        db_name: hftta
        db_user: dc_query
        db_pass: dc_query
      sadb:
        db_host: ***********
        db_name: hftta
        db_user: dc_query
        db_pass: dc_query
      guzhidb:
        db_host: ************
        db_name: fav45
        db_user: dc_query
        db_pass: DC_QUEYR
      touyan_db:
        db_host: ***********
        db_name: touyandb
        db_user: dc_query
        db_pass: DC_QUEYR
      yss_qingsuan_db:
        db_host: ************
        db_name: hftfa
        db_user: dc_query
        db_pass: dc_query
      igwfund_ta_db:
        db_host: ************
        db_name: sstapro
        db_user: dc_query
        db_pass: dc_query
    mysql:
      hft_touyan_db:
        db_host: *************
        db_port: 3306
        db_name: jdbc
        db_user: rolecheckuser
        db_pass: Role$checkuser888
    ldap:
      dc:
        host: *************
        port: 389
        base: dc=hftfund, dc=com
        user: hftservicedesk
        password: 987755Wellcom
  importers:
    - name: ldap_users
      bs_id: 1
      db_type: ldap
      tnsname: dc
      # 正则匹配名字的字段
      regular_field: 'OU'
      # 是否判断座机号，没座机号的不导入
      telephonenumber: false
      # 是否员工部门，适用于员工的部门信息不明确的，根据部门下的员工修改员工的部门id
      update_user_dept: false
      account_filter: "(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))"
      account_attrs:
      - dn
      - name
      - cn
      - samaccountname
      - title
      - useraccountcontrol
      - memberof
      - objectSid
      - primaryGroupID
      - description
      group_filter: "(&(objectclass = organizationalUnit)(objectcategory=CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=hftfund,DC=com))"
      group_attrs:
      - dn
      - name
      - memberOf
      - objectSid
      - primaryGroupID
      top_ou:
      - 海富通基金管理有限公司
      - 上海富诚海富通资产管理有限公司
      - 离职人员
      ignore_accountname: []
      ignore_ou:
      - 计算机
      ignore_ou_but_import_users:
      - 用户
      user_ou_sequence:
        default: 1
        用户: 2
        离职人员: 1
    - name: hft_touyan
      bs_id: 339
      db_type: mysql
      tnsname: hft_touyan_db
      table_space: 'nbpx.'
      sid_suffix: ''
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'hstrade.'
      sid_suffix: ''
      temporary: true
      c_menu_type: [1,6]
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: tadb
      table_space: hftta4.
      sid_suffix: ''
    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: sadb
      table_space: hftta.
      sid_suffix: ''
    - name: guzhi_yss45
      bs_id: 24
      db_type: oracle
      tnsname: guzhidb
      table_space: hftgzv45.
      sid_suffix: ''
    # - name: xiening_touyan_hft
    #   bs_id: 38
    #   db_type: oracle
    #   tnsname: touyan_db
    #   table_space: hftjj.
    #   sid_suffix: ''
    - name: yss_qingsuan
      bs_id: 22
      db_type: oracle
      tnsname: yss_qingsuan_db
      table_space: hftqsv4.
      sid_suffix: ''
    - name: jinzheng_ta
      bs_id: 32
      db_type: oracle
      tnsname: igwfund_ta_db
      table_accounts: kdta_com.kd_userid
      table_roles: kdta_com.kd_role
      table_accounts_roles: kdta_com.kd_rolemember
      table_menus: kdta_com.kd_sysmenu
      table_menus_roles: kdta_com.kd_menuright_role
      table_accounts_menus: kdta_com.kd_menuright_user
    - name: zhixiao_50
      bs_id: 53
      db_type: oracle
      tnsname: yss_qingsuan_db
      table_space: hftqsv4.
      sid_suffix: ''
    - name: guohu_sa_etf
      bs_id: 40
      db_type: oracle
      tnsname: etftadb
      table_space: 'hftetfta.'
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas-app/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_hftfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1



