module AasOracleImporter
  class SidiCmsImporter < ImporterBase
    def config
      @bs_id       = 319
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}T_USER#{@sid_suffix}"
      @table_role         = "#{@table_space}T_ROLE#{@sid_suffix}"
      @table_account_role = "#{@table_space}T_ROLE_USER#{@sid_suffix}"
      @table_menu         = "#{@table_space}T_MANAGE_CATALOG#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}T_ROLE_RIGHT#{@sid_suffix}"
      @table_menu2        = "#{@table_space}T_B_BRANCH#{@sid_suffix}"
      @table_role_menu2   = "#{@table_space}T_ROLE_DATA_RIGHTS#{@sid_suffix}"
      @table_org          = "#{@table_space}T_B_BRANCH#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data2_permissions
      import_data2_role_permissions
      import_data2_account_permissions
      import_data2_user_permissions

      import_ledgers(SidiCms::Account)
    end

    def destroy_exist_datas
      SidiCms::Department.where(quarter_id: @quarter_id).delete_all
      accounts = SidiCms::Account.where(quarter_id: @quarter_id)
      SidiCms::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      SidiCms::Role.where(quarter_id: @quarter_id).delete_all
      SidiCms::Data1Permission.where(quarter_id: @quarter_id).delete_all
      SidiCms::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      SidiCms::Data2Permission.where(quarter_id: @quarter_id).delete_all
      SidiCms::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select BRANCHNO, BRANCHNO, BRANCHNAME from #{@table_org}"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        SidiCms::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end
    end

    def import_accounts_sql
      <<-EOF
        select USER_ID, UID2, NAME, STATE, BRANCHNO from #{@table_account}
      EOF
    end

    def import_accounts
      @departments = SidiCms::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          @accounts << SidiCms::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3]&.to_s == '1',
            department_id: department&.id
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select ROLE_ID, ROLENO, NAME from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << SidiCms::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLE_ID, USER_ID from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          SidiCms::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select CATALOG_ID, CATALOG_NO, NAME, PARENT_ID from #{@table_menu} where STATE=1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        parent_id_index = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, 2, parent_id_index)
          level1_name = replace_blank_name(full_name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << SidiCms::Data1Permission.create(json)
        end
      end
    end

    # CATALOG_NO_LIST是权限代码,CLOB类型，示例：3011|3012|3014|3015
    def import_data1_role_permissions_sql
      <<-EOF
        select ROLE_ID, CATALOG_NO_LIST from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        data = []
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          permission_code_text = r[1].is_a?(OCI8::CLOB) ? r[1].read : r[1]
          next if permission_code_text.nil? || permission_code_text.empty?

          permission_codes = permission_code_text.split('|').compact.map(&:strip)
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission_codes.each do |code|
            permission = @data1_permissions.find { |x| x.source_id.to_s == code }
            next unless permission && role

            data << [role.id, permission.id]
          end
        end

        SidiCms::Data1AccountsRolesPermission.bulk_insert(:quarter_id, :role_id, :data1_permission_id) do |obj|
          obj.set_size = 1000
          data.each do |x|
            obj.add [@quarter_id, x[0], x[1]]
          end
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
        select BRANCHNO, BRANCHNO, BRANCHNAME from #{@table_menu2}
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << SidiCms::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select FROM_USER_CODE, RIGHTS_CONTENT from #{@table_role_menu2} where FROM_USER_TYPE = 1
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        data = []
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          next if r[1].nil? || r[1].to_s.empty?

          branch_numbers = r[1].split(',').map(&:strip).uniq
          role           = @roles.find { |x| x.code.to_s == r[0].to_s }
          branch_numbers.each do |number|
            permission = @data2_permissions.find { |x| x.source_id.to_s == number }
            next unless permission && role

            data << [role.id, permission.id]
          end
        end

        SidiCms::Data2AccountsRolesPermission.bulk_insert(:quarter_id, :role_id, :data2_permission_id) do |obj|
          obj.set_size = 1000
          data.each do |x|
            obj.add [@quarter_id, x[0], x[1]]
          end
        end
      end
    end

    def import_data2_account_permissions_sql
      <<-EOF
        select FROM_USER_CODE, RIGHTS_CONTENT from #{@table_role_menu2} where FROM_USER_TYPE = 0
      EOF
    end

    def import_data2_account_permissions
      data = []
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        next if r[1].nil? || r[1].to_s.empty?

        branch_numbers = r[1].split(',').map(&:strip).uniq
        account        = @accounts.find { |x| x.code.to_s == r[0].to_s }
        branch_numbers.each do |number|
          permission = @data2_permissions.find { |x| x.source_id.to_s == number }
          next unless permission && account

          data << [account.id, permission.id]
        end
      end

      SidiCms::Data2AccountsRolesPermission.bulk_insert(:quarter_id, :account_id, :data2_permission_id) do |obj|
        obj.set_size = 1000
        data.each do |x|
          obj.add [@quarter_id, x[0], x[1]]
        end
      end
    end

    # 根据账户表BRANCHNO字段导入营业部编号
    def import_data2_user_permissions_sql
      <<-EOF
        select UID2, BRANCHNO from #{@table_account}
      EOF
    end

    def import_data2_user_permissions
      select_db_datas(import_data2_user_permissions_sql).each do |r|
        next if r[1].nil? || r[1].to_s.empty?

        branch_numbers = r[1].split(',').map(&:strip).uniq
        account        = @accounts.find { |x| x.code.to_s == r[0].to_s }
        branch_numbers.each do |number|
          permission = @data2_permissions.find { |x| x.source_id.to_s == number }
          next unless permission && account

          SidiCms::Data2AccountsRolesPermission.find_or_create_by(
            quarter_id:          @quarter_id,
            account_id:          account.id,
            data2_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
