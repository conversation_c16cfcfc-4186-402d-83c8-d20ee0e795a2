require 'httparty'
require 'byebug'
module AasOracleImporter
  class HtbrBastionImporter < ImporterBase
    include HTT<PERSON><PERSON><PERSON>

    def config
      @bs_id              = importer_config['bs_id']
      @access_key         = importer_config['access_key']
      @secret_key         = importer_config['secret_key']
      @base_url           = importer_config['base_uri']
      @accounts           = []
      @roles              = []
      @data1_permissions  = []
      self.class.base_uri importer_config['base_uri']
    end

    def bastion_config
      YAML.load_file File.join(__dir__, '../../config/htbr_bastion_config.yml')
    end

    def api_config
      bastion_config[@key.to_s]
    end

    def import_to_do
      destroy_exist_datas
      ActiveRecord::Base.transaction { import_accounts }
      update_accounts
      ActiveRecord::Base.transaction { import_roles }
      ActiveRecord::Base.transaction { import_accounts_roles }
      ActiveRecord::Base.transaction { import_data1_permissions }
      ActiveRecord::Base.transaction { import_data1_account_permissions }
      import_ledgers(HtbrBastion::Account)
    end

    def destroy_exist_datas
      accounts = HtbrBastion::Account.where(quarter_id: @quarter_id)
      HtbrBastion::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HtbrBastion::Role.where(quarter_id: @quarter_id).delete_all
      HtbrBastion::Data1Permission.where(quarter_id: @quarter_id).delete_all
      HtbrBastion::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.find(@bs_id)
    end

    private

    # id [int] 是	用户的id
    # name [string] 是	用户姓名
    # loginName [string] 是	登录名
    # status [vi5FZ_UserStatusEnum] 是	用户状态
    # role [string] 是	用户角色名
    # department [string] 是	用户所在部门名称
    # isAdmin [xPRjn_CommonStatus] 是	是否超级管理员

    def import_accounts
      @key = 'account_list'
      all_info.each do |account|
        @accounts << HtbrBastion::Account.create(
          quarter_id:      @quarter_id,
          source_id:       account['loginName'],
          code:            account['loginName'],
          name:            account['name'].strip,
          department_name: account['department'].strip,
          status:          account['status'].to_s == '1',
          objid:           account['isAdmin'].to_s == '1', # 是否是超级管理员
          id_number:       account['id'].to_s
        )
      end
    end

    def update_accounts
      @key = 'update_accounts'
      @accounts.each do |account|
        next unless account.status

        account_info = single_info(account.id_number.to_i)
        next unless account_info['base']&.[]('loginName') == account.code

        end_time   = account_info['conf']&.[]('time')&.[]('endTime').to_s
        start_time = account_info['conf']&.[]('time')&.[]('startTime').to_s

        next if end_time.empty?
        next if start_time.empty?
        next if Date.today < Date.parse(start_time)

        # 三种情况：
        # 1. 今天 早于 开始时间             不创建
        # 2. 今天 在 开始时间和结束时间之间 创建临时账号
        # 3. 今天 晚于结束时间              创建临时账号
        type_id = PublicAccountType.find_by(key: "temporary").id
        data_json = {
          name:      account.name,
          system_id: @bs_id,
          code:      account.code,
          remark:    '自动识别',
          type_id:   type_id
        }
        public_account = ::PublicAccount.find_or_initialize_by(data_json)
        public_account.valid_date = Date.parse(end_time)
        public_account.save
      end
    end

    def import_roles
      @key = 'role_list'
      all_info.each do |role|
        @roles << HtbrBastion::Role.create(
          quarter_id: @quarter_id,
          source_id:  role['id'],
          code:       role['id'],
          name:       role['name'].strip
        )
      end
    end

    def import_accounts_roles
      @key = 'account_list'
      all_info.each do |data|
        role    = @roles.find { |x| x.name.to_s == data['role'].to_s.strip }
        account = @accounts.find { |x| x.source_id.to_s == data['loginName'].to_s }

        HtbrBastion::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
      end
    end

    def import_data1_permissions
      @key = 'data1_permissions_list'
      all_info.each do |data|
        next unless data['status'].to_s == '已启用'

        source_id = "#{data['accountName']}_#{data['opsStrategyId']}_#{data['resourceName']}_#{data['protocol']}_#{data['loginName']}_#{data['ip']}_#{data['port']}"

        @data1_permissions << HtbrBastion::Data1Permission.create(
          quarter_id:   @quarter_id,
          source_id:    source_id,
          code:         data['opsStrategyId'],
          level1_name:  data['opsStrategyName'],
          rc_name:      data['resourceName'],
          protocol:     data['protocol'],
          ip:           data['ip'],
          port:         data['port'],
          login_name:   data['loginName'],
          account_name: data['accountName']
        )
      end
    end

    def import_data1_account_permissions
      @key = 'data1_permissions_list'
      all_info.each do |data|
        account    = @accounts.find { |x| x.name.to_s == data['userName'].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == "#{data['accountName']}_#{data['opsStrategyId']}_#{data['resourceName']}_#{data['protocol']}_#{data['loginName']}_#{data['ip']}_#{data['port']}" }

        if account && permission
          HtbrBastion::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id)
        end
      end
    end

    def all_info
      @argument = 1
      infos = []
      loop do
        datas = send_request['data']['rows']
        total = datas.count.to_i
        infos += datas
        @argument += 1
        break if total < api_config['pagesize']
      end
      infos
    end

    def single_info(single)
      @argument = single
      send_request['data']
    end

    def send_request
      @timestamp = h_timestamp
      @nonce     = h_nonce
      response   = self.class.post("#{@base_url}#{api_config['url']}", {
                                     body:    message_body,
                                     headers: headers,
                                     verify:  false
                                   })
      result = JSON.parse response.body

      unless result['code'].to_i.zero?
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers
      {
        'Accept':           'application/json',
        'Authorization':    h_authorization,
        'Content-Type':     'application/json',
        'nonce':            @nonce,
        'SignatureVersion': h_signature_version,
        'Timestamp':        @timestamp
      }
    end

    def h_authorization
      string_to_sign = message_body + '&' + @access_key + '&' + h_signature_version + '&' + @timestamp + '&' + @nonce
      sha1_sign      = OpenSSL::HMAC.digest('sha1', @secret_key.encode('UTF-8'), string_to_sign.encode('UTF-8'))
      signature      = Base64.strict_encode64(sha1_sign)
      "#{@access_key}:#{signature}"
    end

    def h_nonce
      rand(36**32).to_s 36
    end

    def h_signature_version
      '1.0'
    end

    def h_timestamp
      (Time.now.to_f * 1000).to_i.to_s
    end

    def message_body
      body   = api_config['body']
      result = {}
      body.each do |hash|
        result.merge!({ hash['key'].to_s => hash['value'] })
      end
      result[api_config['argument'].to_s] = @argument

      JSON.dump(result)
    end
  end
end
