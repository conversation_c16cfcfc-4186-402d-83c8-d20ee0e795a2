# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'dfham'
  customer: '东证资管'
  ipaddr: 'localhost'
  log_level: 'DEBUG'
  quarter_format: "%Y年%m月%d日"
  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_dfham_development
      username: root
      password: 123456
      host: 127.0.0.1

  agent:
    oracle:
      # tnsname
      o32db:
        db_name: 'HSO32'
        db_user: 'trade32'
        db_pass: 'trade320728'
      guzhidb:
        db_name: 'guzhi'
        db_user: 'dfzgv45'
        db_pass: 'y2iaciej$123451'
      qingsuandb:
        db_name: 'qingsuan'
        db_user: 'ES_SYSTEM'
        db_pass: 'ES_SYSTEM'

  importers:
    # MARK: 按照顺序依次导入
    # 德萌阳光oa
    #- name: lanling_oa
    #  bs_id: 1
    #  db_type: sqlserver
    #  tnsname: lanling_oa
    # 清算系统
    - name: qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: qingsuandb
      table_space: ''
      sid_suffix: ''

    - name: guzhi_yss45
      bs_id: 30
      db_type: oracle
      tnsname: guzhidb
      table_space: ''
      sid_suffix: ''

    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o32db
      table_space: ''
      sid_suffix: ''

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_dfham_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1



