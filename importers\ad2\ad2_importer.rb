module AasOracleImporter
  # AD 域数据导入
  class Ad2Importer < ImporterBase
    def config
      @bs_id          = importer_config['bs_id']
      @account_filter = importer_config['account_filter']
      @account_attrs  = importer_config['account_attrs']
      @group_filter   = importer_config['group_filter']
      @group_attrs    = importer_config['group_attrs']
      initialize_classes
    end

    def import_to_do
      destroy_old_data
      import_groups
      import_accounts
      import_ledgers(@account_class)
    end

    private

    def initialize_classes
      @account_class = Ad2::Account
      @role_class    = Ad2::Role
    end

    def destroy_old_data
      @account_class.where(quarter_id: @quarter_id).destroy_all
      @role_class.where(quarter_id: @quarter_id).destroy_all
    end

    def import_groups
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @group_filter, attributes: @group_attrs) do |entry|
          import_a_group(entry)
        end
      end
    end

    def import_accounts
      @roles = @role_class.where(quarter_id: @quarter_id).to_a
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @account_filter, attributes: @account_attrs) do |entry|
          import_an_account(entry)
        end
      end
    end

    def import_a_group(entry)
      return unless entry.name.first

      @role_class.create(
        quarter_id: @quarter_id,
        code:       entry.dn,
        gid:        get_sid_string(entry[:objectSid]&.first.to_s).split('-').last,
        name:       entry.name.first,
        data_json:  { memberOf: entry[:memberOf], sAMAccountType: entry[:sAMAccountType].first.to_s }
      )
    end

    def import_an_account(entry)
      gid = get_sid_string(entry[:objectSid]&.first.to_s).split('-').last
      account_code = entry.samaccountname&.first&.downcase
      account_name = entry.name&.first&.gsub(/\s+/, '')
      account_comment = entry[:description]&.first&.gsub(/\s+/, '')

      return unless account_code && account_name

      account = @account_class.create(
        quarter_id:   @quarter_id,
        code:         account_code,
        name:         account_name,
        gid:          gid,
        status:       account_status(entry),
        comment:      account_comment,
        account_type: 1
      )

      role = Ad2::Role.where(gid: entry[:primaryGroupID]&.first).last
      account.roles << role if role

      member_of = entry[:memberof]
      return unless member_of&.size&.positive?

      link_account_and_roles(account, member_of)
    end

    def convert_account_expires(account_expires)
      # 将100纳秒转换为秒，并从1601年1月1日开始计算
      timestamp = (account_expires / 10_000_000) - 11_644_473_600
      
      # 将时间戳转换为Ruby的DateTime对象
      datetime = Time.strptime(timestamp.to_s, '%s')
      
      # 返回转换后的日期时间对象
      return datetime
    end

    # 有的域控账户禁用后，没有useraccountcontrol字段
    def account_status(entry)
      return false unless entry.respond_to? :useraccountcontrol

      # '9223372036854775807','0' ad域默认为永久配置
      if entry[:accountExpires].size > 0 && !['9223372036854775807','0'].include?(entry[:accountExpires]&.first.to_s)
        return false if convert_account_expires(entry[:accountExpires]&.first.to_i) < Time.now
      end
      status_sequences = entry.useraccountcontrol.first.to_i.to_s(2)
      status_sequences[-2] != '1'
    end

    def link_account_and_roles(account, member_of)
      member_of.each do |role_code|
        role = @roles.find { |x| x.code == role_code }
        account.roles << role if role
      end
    end

    def get_sid_string(data)
      sid = data.to_s.unpack('b x nN V*')
      sid[1, 2] = Array[nil, b48_to_fixnum(sid[1], sid[2])]
      'S-' + sid.compact.join('-')
    end

    def b48_to_fixnum(i16, i32)
      i32 + (i16 * 2**32)
    end
  end
end
