module AasOracleImporter

  class HftTouyanImporter < ImporterBase

    def config
      @bs_id       = 339
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      
      
      import_data1_permissions
      
      import_data1_role_permissions
      
      
      
      import_ledgers(HftTouyan::Account)
    end

    def destroy_exist_datas
      accounts = HftTouyan::Account.where(quarter_id: @quarter_id)
      HftTouyan::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HftTouyan::Role.where(quarter_id: @quarter_id).delete_all
      
      
      HftTouyan::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      HftTouyan::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
      
      
    end

    
    def import_accounts_sql
      <<-EOF
        SELECT distinct u.user_id, u.username, u.chinese_name, u.stat
FROM #{@table_space}sys_user u
         JOIN #{@table_space}sys_user_role ur ON u.user_id = ur.user_id
         JOIN #{@table_space}sys_role r ON ur.role_id = r.role_id
         JOIN #{@table_space}sys_role_menu rm ON r.role_id = rm.role_id
         JOIN #{@table_space}sys_menu m ON rm.menu_id = m.menu_id
WHERE u.del_flag = 0
  AND u.tenant_id = 1
  AND u.username is not null
  AND u.oa_dept_id IS NOT NULL
  AND u.state = 1
  AND u.enable_flag = 1
  AND u.ad_account_flag = 1
  AND r.del_flag = 0
  AND r.tenant_id = 1
  AND m.del_flag = 0
  AND m.tenant_id = 1
  AND m.system_type = 'ERMS'
      EOF
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        
        enums = [{"name"=>"status", "value"=>"1", "enum_value"=>"正常", "label"=>"账号状态"}]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << HftTouyan::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[1]),
            name: get_enum(enums, "name", r[2]),
            status: r[3]&.to_s == '1'
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select 
          role_id,
          role_id,
          role_name 
        from 
          #{@table_space}sys_role
        where
          del_flag = 0
          and tenant_id = 1
          and role_id in (
            select 
              role_id
            from 
              #{@table_space}sys_role_menu
            where menu_id in (
              select 
                menu_id
              from 
                #{@table_space}sys_menu
              where del_flag = 0
                and tenant_id = 1
                and system_type = 'ERMS'
            )
          )
      EOF
    end
    

    def import_roles
      
      ActiveRecord::Base.transaction do
        
        
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << HftTouyan::Role.create(
              quarter_id: @quarter_id,
              source_id: r[0],
              code: r[1],
              name: r[2]
          )
        end
      end
      
    end

    
    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id
        from #{@table_space}sys_user_role
      EOF
    end
    

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        
        
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          next unless account && role

          HftTouyan::AccountsRole.create(account_id: account.id, role_id: role.id  )
        end
      end
      
    end

    
    def import_data1_permissions_sql
      <<-EOF
        select 
          menu_id,
          menu_id,
          name,      
          type,
          parent_id
        from #{@table_space}sys_menu
        where del_flag = 0
          and tenant_id = 1
          and system_type = 'ERMS'
      EOF
    end

    def import_data1_permissions
      
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 4
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          
          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index))
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[1],
            level1_name: level1_name,
            level2_name: r[3],
          }
          @data1_permissions << HftTouyan::Data1Permission.create(json)
        end
      end
      
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select 
          role_id,
          menu_id
        from 
          #{@table_space}sys_role_menu
      EOF
    end

    def import_data1_role_permissions
      
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          next unless permission && role

          HftTouyan::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = "menu_id"
      new_sql = sql + " #{ sql.downcase.include?('where') ? 'and' : 'where' } #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
