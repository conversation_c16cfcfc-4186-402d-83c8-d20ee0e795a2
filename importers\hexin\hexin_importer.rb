module AasOracleImporter

  class HexinImporter < ImporterBase

    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_roles          = importer_config['table_roles']
      @table_menus          = importer_config['table_menus']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus_roles    = importer_config['table_menus_roles']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_menus_roles
      import_accounts_roles
      import_ledgers(Hexin::Account)
    end

    def import_accounts

      Hexin::Account.where(quarter_id: @quarter_id).destroy_all

      sql = "select usercode, username, comcode, userstate from #{@table_accounts}"

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            Hexin::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1].strip,
              company_code: r[2]&.strip,
              status: r[3].to_i == 0
              )
          else
             @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      Hexin::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select menugrpcode, menugrpname
        from #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Hexin::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus

      Hexin::Menu.where(quarter_id: @quarter_id).destroy_all
      sql = <<-EOF
        select distinct (d.nodecode),
           Case
             When a.nodename is null and b.nodename is null and c.nodename is null then
              d.nodename
             When a.nodename is null and b.nodename is null then
              c.nodename || '->' || d.nodename
             when a.nodename is null then
              b.nodename || '->' || c.nodename || '->' || d.nodename
             else
              a.nodename || '->' || b.nodename || '->' || c.nodename || '->' || d.nodename
           end,
           d.nodeorder
           from #{@table_menus} a, #{@table_menus} b, #{@table_menus} c, #{@table_menus} d
          where a.nodecode(+) = b.parentnodecode
            and b.nodecode(+) = c.parentnodecode
            and c.nodecode(+) = d.parentnodecode
            and d.childflag = '0'
          order by d.nodeorder
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Hexin::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus_roles
      sql = <<-EOF
        select menugrpcode,nodecode
        from #{@table_menus_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = Hexin::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Hexin::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            Hexin::MenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
             @logger.warn "#{self.class}: not found role #{r[0]}" unless role
             @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select usercode,menugrpcode
        from #{@table_accounts_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = Hexin::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Hexin::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            Hexin::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
             @logger.warn "#{self.class}: not found account #{r[0]}" unless account
             @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



