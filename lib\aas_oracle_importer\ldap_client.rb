# frozen_string_literal: true

module AasOracleImporter
  # CSV 客户端
  class LdapClient < DatabaseClient
    class LdapLoginFailed < StandardError; end

    def initialize(database_type, tnsname)
      super
    end

    def base
      @database.base
    end

    def search(args, &block)
      @database.search(args, &block)
    end

    private

    def initialize_driver
      load_driver_gem
      password = ConvertTools::Cryptology.decrypt_if_env(database_info['password'])
      port = database_info['port']
      is_tls = database_info['is_tls'] || false
      if is_tls
        @database = Net::LDAP.new(encryption: :simple_tls, auth: { method: :simple, username: ldap_username, password: password })
      else
        @database = Net::LDAP.new
      end
      @database.host = database_info['host']
      @database.port = port || 389
      @database.base = database_info['base'].downcase
      @database.auth(ldap_username, password) if !is_tls
      auth_login
    end

    def load_driver_gem
      require 'net/ldap'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'net-ldap\'.')
      exit(-127)
    end

    def ldap_username
      return database_info['user'] if database_info['not_change_user']

      database_info['user'] + '@' + @database.base.split(/,* *dc=/).delete_if { |x| x == '' }.join('.')
    end

    def auth_login
      return if @database.bind

      raise_ldap_internal_error @database.get_operation_result
    end

    def raise_ldap_internal_error(ldap_response)
      ldap_error_codes = {
        1793 => 'windows 域错误消息：用户账户已到期',
        1326 => 'windows 域错误消息：未知的用户名或错误密码',
        1327 => 'windows 域错误消息：用户账户限制',
        1328 => 'windows 域错误消息：违反账户登录时间限制',
        1329 => 'windows 域错误消息：不允许用户登录到此计算机',
        1330 => 'windows 域错误消息：账户密码已过期',
        1331 => 'windows 域错误消息：当前账户已禁用',
        1239 => 'windows 域错误消息：试图在这个账户未被授权的时间内登录',
        1396 => 'windows 域错误消息：登录失败: 该目标账户名称不正确',
        1907 => 'windows 域错误消息：在第一次登录之前，必须更改用户密码',
        1909 => 'windows 域错误消息：引用的账户当前已锁定，且可能无法登录',
        2239 => 'windows 域错误消息：此用户账户已过期',
        2242 => 'windows 域错误消息：此用户的密码已经过期',
        4506 => 'windows 域错误消息：登录的用户数量上限'
      }

      error_code = ldap_response.error_message.match(/data (.*)\,/)[1]
      message = ldap_error_codes[error_code.hex]

      message ||= "未知的 windows 域错误代码 #{error_code}"

      @logger.error { "LDAP login failed - code: #{ldap_response.code}, error_code: #{error_code}, message: #{message}" }

      raise LdapLoginFailed, message
    end
  end
end
