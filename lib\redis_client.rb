class RedisClient
  def self.redis
    @@redis ||= redis_initialize.redis
  end

  def self.redis_initialize
    require_relative 'redis_initializers/redis.rb'

    ::AasProvider
  end

  # 获取set指定元素，并且删除，如果存在返回true，不存在为false
  def self.pop_specific_element(set_key, element)
    result = redis.multi do |multi|
      multi.sismember(set_key, element)  # 检查元素是否存在
      multi.srem(set_key, element)       # 移除元素
    end

    result[0]  # 如果元素存在，返回它；否则返回 false
  end
end