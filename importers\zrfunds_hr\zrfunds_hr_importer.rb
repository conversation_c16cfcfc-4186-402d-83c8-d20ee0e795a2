# frozen_string_literal: true

module AasOracleImporter
  # AD 域数据导入
  class ZrfundsHrImporter < ImporterBase
    def config
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      initialize_tables
    end

    def initialize_tables
      @table_user = "#{@table_space}it_employee_info#{@sid_suffix}"
    end

    def import_to_do
      @departments = []
      @jobs = []
      @accounts_map = {}
      import_departemnts
      import_jobs
      import_accounts
      import_job_users

      # 如果一个部门的员工不存在, 则将该部门设置为inservice: false
      update_deparment_inservice
    end

    def import_departemnts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[2]
            department           = Department.find_or_initialize_by(name: r[2])
            department.code      = r[2]
            department.inservice = true
            department.save
            @departments << department
          end
        end
      end
    end

    def import_jobs
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[3].present?
            department         = @departments.find { |d| d.name == r[2] }
            job                = Job.find_or_initialize_by(name: r[3])
            job.code           = r[3]
            job.inservice      = true
            job.ancestry_depth = 0
            job.department_id  = department.id
            job.save
            @jobs << job
          end
        end
      end
    end

    def import_accounts_sql
      <<-SQL
        select
          EMP_ID,
          EMP_NAME,
          DEPT_NAME,
          POST_NAME,
          ENTRY_DATE,
          LEAVE_DATE,
          END_DATE,
          EMP_STATUS
        from
          #{@table_user}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          disable_date = date_format(r[5])
          join_date = date_format(r[4])
          end_date = date_format(r[6])

          if r[0] && r[1]
            department = @departments.find { |d| d.name == r[2] }
            user_hash = {
              name: r[1],
              position: r[3],
              inservice: r[7].to_s == '在职', # 从表中取出的是中文, 在职|离职
              disable_date: disable_date,
              join_date: join_date,
              department_name: r[2],
              department_id: department.id,
              end_date: end_date
            }

            if @accounts_map[r[0]]
              @accounts_map[r[0]] = user_hash if end_date > @accounts_map[r[0]][:end_date]
            else
              @accounts_map[r[0]] = user_hash
            end
          end
        end
      end

      @accounts_map.each do |code, user_hash|
        user = User.find_or_initialize_by(code: code)
        user.update(user_hash.except(:end_date))
      end
    end

    def import_job_users
      JobUser.delete_all
      users = User.all.select(:id, :position)
      Job.all.each do |job|
        user_list = users.select { |u| u.position == job.name }
        user_list.each do |user|
          JobUser.find_or_create_by(user_id: user.id, job_id: job.id)
        end
      end
    end

    def update_deparment_inservice
      Department.all.each do |department|
        if department.users.any?
          department.update(inservice: true)
        else
          department.update(inservice: false)
        end
      end
    end

    def date_format(date)
      return '' unless date

      date.to_date
    end
  end
end
