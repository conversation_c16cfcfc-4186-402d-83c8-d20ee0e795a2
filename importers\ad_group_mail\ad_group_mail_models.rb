module AdGroupMail
  def self.table_name_prefix
    'ad_group_mail_'
  end
end

class AdGroupMail::Role < ActiveRecord::Base
end

class AdGroupMail::Account < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true
end

class LdapPerson
  attr_reader :name, :login, :email, :code
  attr_accessor :ou_sequence

  def initialize(entry)
    @dn      = entry.dn
    @name    = entry.name&.first&.gsub(/\s+/, '')
    @login   = entry.samaccountname&.first&.downcase
    @email   = entry[:mail]&.first&.downcase
    @code    = @login
  end
end
