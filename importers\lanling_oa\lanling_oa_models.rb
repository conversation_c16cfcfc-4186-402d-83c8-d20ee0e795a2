module LanlingOa
  def self.table_name_prefix
    'lanling_oa_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class User < ActiveRecord::Base; end

class Department < ActiveRecord::Base
  has_many :children, class_name: 'Department', foreign_key: 'parent_id'
  belongs_to :parent, class_name: 'Department', optional: true
  has_many :users

  validates :name, presence: true
  validates :code, presence: true
end

class LanlingOa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class LanlingOa::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission

  validates :permission_id, presence: true
  validates :quarter_id, presence: true
end

class LanlingOa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts

  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true
end

class LanlingOa::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :permission_type, presence: true 
  validates :quarter_id, presence: true 
end
