module AasOracleImporter

  class DingdianCrmImporter < ImporterBase

    def config
      @bs_id                            = importer_config['bs_id']
      @name_must_in_users               = importer_config['name_must_in_users']
      @table_space                      = importer_config['table_space']
      @sid_suffix                       = importer_config['sid_suffix']
      @accounts                         = []
      @roles                            = []
      @data1_permissions                = []
      @data1_accounts_roles_permissions = []

      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_ledgers(DingdianCrm::Account)
    end

    def import_accounts_sql
      <<-EOF
        SELECT
          T1.ID,
          T1.USERID,
          T1.NAME,
          T1.ZT,
          T2.NAME ORGNAME,
          T1.ORGID 
        FROM
          #{@table_space}TUSER T1
          LEFT JOIN #{@table_space}LBORGANIZATION T2 ON ( T1.ORGID = T2.ID )
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do

        enums = [{ "name" => "status", "value" => "0", "enum_value" => "1", "label" => "账号状态" }]
        select_db_datas(import_accounts_sql).each do |r|
          account_name = r[2]
          next unless account_can_import?(account_name)

          @accounts << DingdianCrm::Account.create(
            quarter_id:        @quarter_id,
            source_id:         get_enum(enums, "source_id", r[0]),
            code:              get_enum(enums, "code", r[1]),
            name:              get_enum(enums, "name", r[2]),
            status:            r[3].to_i == 0,
            department_name:   get_enum(enums, "department_name", r[4]),
            organization_code: get_enum(enums, "organization_code", r[5]),
          )
        end
      end
    end

    def account_can_import?(account_name)
      return true unless @name_must_in_users
      # 富国基金 CRM 账号仅导入包含在员工列表里的账号信息
      return true if user_names.any? { |user_name| account_name&.include? user_name }

      @logger.info { "account name #{account_name} not in user list , ignore import. " }
      false
    end

    def user_names
      @user_names ||= User.all.pluck(:name)
    end

    def import_roles_sql
      <<-EOF
        SELECT ID, ROLECODE, NAME FROM #{@table_space}LBROLE
      EOF
    end

    def import_roles

      ActiveRecord::Base.transaction do

        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << DingdianCrm::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[1]), name: get_enum(enums, "name", r[2]))
        end
      end

    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLEID, USERID from #{@table_space}LBMEMBER
      EOF
    end

    def import_accounts_roles

      ActiveRecord::Base.transaction do

        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          if account && role
            DingdianCrm::AccountsRole.create(account_id: account.id, role_id: role.id)
          end
        end
      end

    end

    def import_data1_permissions_sql
      <<-EOF
        select ID,NAME,DESCRIBE,FID from #{@table_space}LBFUNDEFINITION
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])

          parent_id_value = r[parent_id_index]
          level1_name     = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index))

          json = {
            quarter_id:  @quarter_id,

            source_id:   get_enum(enums, "source_id", r[0]),

            code:        get_enum(enums, "code", r[1]),

            level1_name: level1_name,

          }
          @data1_permissions << DingdianCrm::Data1Permission.create(json)
        end
      end

    end

    def import_data1_role_permissions_sql
      <<-EOF
        SELECT A.MEMBERID, B.ID 
          FROM #{@table_space}LBFUNPERMISSION A, #{@table_space}LBFUNDEFINITION B
         WHERE B.NAME = A.FUNNAME
           AND A.TYPE = 1
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          if permission && role
            DingdianCrm::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end

    def select_db_datas(sql)

      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |k, v| v } : r)
      end
      output_datas

    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql       = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql   = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' "
      select_db_datas(new_sql).each do |r|
        parent_name     = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end



