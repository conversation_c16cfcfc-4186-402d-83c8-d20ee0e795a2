# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'xbldfund'
  customer: '西部利得基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'

  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_xbldfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # 数据库配置 要改
      # ta 系统数据库
      hrdb:
        db_name: 'hr01'
        db_user: 'queryUser'
        db_pass: 'queryUser'
        db_host: '************'

      # 投资交易 O32 系统数据库
      tradedb:
        db_name: 'trade'
        db_user: 'tradetest3'
        db_pass: 'tradetest3'
        db_host: '************'

      tydb:
        # in tns name
        db_name: 'test01'
        db_user: 'ta2'
        db_pass: 'ta2'
        db_host: '**************'

  importers:
    # MARK: 按照顺序依次导入
    # HR 系统
    - name: fanwei_oa_users
      # 接口对接，没有其他属性, 下面这个为了通过 initialize
      db_type: oracle
      tnsname: hrdb

    - name: xiening_touyan71
      bs_id: 38
      db_type: oracle
      tnsname: tydb
      table_space: ''
      sid_suffix: ''

    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: ''
      sid_suffix: ''
      days_of_data: 365
      temporary: true

development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    # 要改
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_xbldfund_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1


