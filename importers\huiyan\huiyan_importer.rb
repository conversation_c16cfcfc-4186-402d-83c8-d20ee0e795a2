module AasOracleImporter

  class HuiyanImporter < ImporterBase

    def config
      @bs_id = 10008
      @accounts = []
      @roles = []
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @import_report  = importer_config['import_report']
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_data1_permissions
      import_data1_account_permissions
      import_reports if @import_report
      import_ledgers(Huiyan::Account)
    end

    
    def import_accounts_sql
      <<-EOF
        select USERNAME,FULLNAME,status,email,groupid,id from #{@table_space}EDR_USER#{@sid_suffix}
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          unless @accounts.find{|x| x.code.to_s == r[0].to_s}
            account = Huiyan::Account.create(
              quarter_id: @quarter_id,
              source_id: r[0],
              code: r[0],
              name: r[1],
              email: r[3],
              organization_code: r[4],
              status: r[2].to_s == '0',
              objid: r[5]
            )
            @accounts << account 
          end
        end
      end
      
    end


    def import_data1_permissions_sql
      <<-EOF
        select ptfcode,ptfname,ptfpath from #{@table_space}view_edr_user_auth#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: r[1],
            level2_name: r[2]
          }
          @data1_permissions << Huiyan::Data1Permission.create(json) unless @data1_permissions.find{|x| x.code == r[0]}
        end
      end
    end
    
    def import_data1_account_permissions_sql
      <<-EOF
        select username, ptfcode from #{@table_space}view_edr_user_auth#{@sid_suffix}
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
        if account && permission
          Huiyan::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data1_permission_id: permission.id)
        end
      end
    end

    def import_reports_sql
      <<-EOF
        select id, name from #{@table_space}edr_email_config#{@sid_suffix} where enabled = 1
      EOF
    end

    def import_reports
      @disabled_reports = FundEmailsCheck::Report.where(business_system_id: @bs_id).pluck(:source_id)
      select_db_datas(import_reports_sql).each do |r|
        import_reports_line(r)
      end
      FundEmailsCheck::Report.where(business_system_id: @bs_id, source_id: @disabled_reports).each{|x| x.update(status: false)}
    end

    def import_reports_line(r)
      report = FundEmailsCheck::Report.find_or_initialize_by(business_system_id: @bs_id, source_id: r[0], report_name: r[1])
      report.receiver_email = set_email(r[0])
      report.status = true
      report.save
      #set_fund(report, r[1])
      @disabled_reports.delete(r[0].to_s)
    end

    def set_email(report_id)
      mails = []
      mail_sql = "select subjectid, subjecttype from #{@table_space}edr_email_config_receiver#{@sid_suffix} where configid = #{report_id}"
      select_db_datas(mail_sql).each do |r|
        mails |= import_reports_mail_line(r)
      end
      mails
    end

    def import_reports_mail_line(r)
      if r[1].to_s == '1'
        return @accounts.select{|x| x.organization_code.to_s == r[0].to_s}.map{|x| x.email}
      else
        account = @accounts.find{|x| x.objid.to_s == r[0].to_s}
        return account ? [account.email] : []
      end
    end

    def set_fund(report, fund_codes)
      fund_codes.each do |fund_code|
        fund = FundEmailsCheck::Fund.find_by(code: fund_code)
        report.funds << fund if fund && !report.funds.include?(fund)
      end
      report.fund_count = report.funds.size
      report.save
    end

    def select_db_datas(sql)
      
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
      
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end



