module Feikong
  def self.table_name_prefix
    'feikong_'
  end
end

class Feikong::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Feikong::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Feikong::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class Feikong::Company <  ActiveRecord::Base; end
class FeikongMenusRoles < ActiveRecord::Base; end
class FeikongAccountsRoles < ActiveRecord::Base; end
