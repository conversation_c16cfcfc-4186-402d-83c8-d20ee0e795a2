module AasOracleImporter
  class YyfundHrImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @position_users = [] # 拥有岗位的用户
    end

    def import_to_do
      import_hr
      create_or_update_departments
      create_or_update_users
      link_manager
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.new(name: 'HR 系统')
    end

    private

    HrData =
      Struct.new(:company, :department,
                 :user_code, :user_name, :email, :cellphone,
                 :position, :status_string, :manager_code) do

        def department_code
          "#{company}/#{department}"
        end

        def department_name
          if company == '永赢资产'
            "#{company} / #{department}"
          else
            department
          end
        end

        def inservice
          status_string == '在职'
        end

        def present?
          user_code.present? && user_name.present?
        end
      end

    def import_hr_sql
      # <<-SQL
      #   select o.C_EMPLOYEE_CODE 工号,
      #     o.C_EMPLOYEE_NAME 姓名,
      #     o.C_COMPANY_NAME  公司,
      #     o.C_DEPT_NAME     所在部门,
      #     o.C_POSITION_NAME 岗位,
      #     hrsys.getcodename(o.C_EMPLOYEE_STATUS, 'CODE_StaffStatusType') 在职状态,
      #     se.c_code as 上级领导工号,
      #     se.c_name as 上级领导姓名
      #   from hrsys.tb_v_sta_emp_cur_org o
      #   left join hrsys.TB_sta_emp se on(se.c_oid = hrsys.reportto(o.C_EMPLOYEE_ID))
      # SQL

      <<-SQL
        select * from exch.hr_tb_v_sta_emp_superior
      SQL
    end

    def import_hr
      @data = []
      # db 的 id 类型都是数字的，需要转成 string
      @database.exec(import_hr_sql) do |row|
        @data << create_user_struct(row)
      end
    end

    def create_user_struct(row)
      code, name, company, department, position, status_string, manager_code, _manager_name = row

      user               = HrData.new
      user.user_code     = code
      user.user_name     = name
      user.company       = company
      user.department    = department
      user.position      = position
      user.status_string = status_string
      user.manager_code  = manager_code
      # 最后返回结构体
      user
    end

    def create_or_update_departments
      old_departments = Department.where.not(code: %w[public disabled]).to_a
      old_codes       = old_departments.map(&:code)
      new_codes       = @data.map(&:department_code).uniq

      # 新增的 inservice 为 true
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          department_struct = @data.find { |x| x.department_code == code }
          Department.create(
            code:      department_struct.department_code,
            name:      department_struct.department_name,
            inservice: true
          )
        end
      end
      # 没有人的部门注销
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          department = old_departments.find { |x| x.code == code }
          department.update(inservice: false)
        end
      end

      # 现有的可能名称有变动
      # 存在的，有人部门，inservice 肯定为 true
      ActiveRecord::Base.transaction do
        (old_codes & new_codes).each do |code|
          department        = old_departments.find { |x| x.code == code }
          department_struct = @data.find { |x| x.department_code == code }

          department.update(name: department_struct.department_name, inservice: true)
        end
      end
    end

    def create_or_update_users
      departments = Department.all.reload.to_a
      old_users   = User.all.to_a
      old_codes   = old_users.map(&:code)
      new_codes   = @data.map(&:user_code)

      # 新增
      ActiveRecord::Base.transaction do
        (new_codes - old_codes).each do |code|
          user_struct = @data.find { |x| x.user_code == code }
          department  = departments.find { |x| x.code == user_struct.department_code }

          next unless user_struct.present?

          user = User.create(
            code:          user_struct.user_code,
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end

      # 已有的更新
      ActiveRecord::Base.transaction do
        old_users.each do |user|
          user_struct = @data.find { |x| x.user_code == user.code }
          next unless user_struct&.present?

          department = departments.find { |x| x.code == user_struct.department_code }

          user.update(
            name:          user_struct.user_name,
            position:      user_struct.position,
            inservice:     user_struct.inservice,
            department_id: department&.id
          )
          @position_users << user if user.position?
        end
      end

      # 已删除的需要禁用掉
      # 在永赢系统中，发现有员工编号变化的情况，此情况中，原来的账号在同步过来的数据表中是已删除的状态
      ActiveRecord::Base.transaction do
        (old_codes - new_codes).each do |code|
          user = old_users.find { |x| x.code == code }
          @logger.warn "#{self.class}: found deleted hr user code: #{user.code}, name: #{user.name}"
          user.update(inservice: false)
        end
      end
    end

    def link_manager
      all_users = User.all.reload.to_a

      ActiveRecord::Base.transaction do
        @data.each do |struct|
          user    = all_users.find { |x| x.code == struct.user_code }
          manager = all_users.find { |x| x.code == struct.manager_code }
          user.update(manager_id: manager&.id)
        end
      end
    end

    # 没有具体的岗位字段，只能获取岗位后，通过程序去重了
    def import_jobs_sql
      import_hr_sql
    end

    # 导入岗位
    def import_jobs
      names = []
      @database.exec(import_jobs_sql) do |r|
        names << r[4]
      end
      names = names.uniq.compact
      names.each do |name|
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



