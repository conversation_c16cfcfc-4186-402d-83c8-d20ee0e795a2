module AasOracleImporter
  # AD 域数据导入
  class AdGroupTwoImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @account_filter = importer_config['account_filter']
      @account_attrs  = importer_config['account_attrs']
      @group_filter   = importer_config['group_filter']
      @group_attrs    = importer_config['group_attrs']
      initialize_classes
    end

    def import_to_do
      destroy_old_data
      import_groups
      import_accounts
      import_ledgers(@account_class)
    end

    private

    def initialize_classes
      @account_class = AdGroupTwo::Account
      @role_class    = AdGroupTwo::Role
    end

    def destroy_old_data
      @account_class.where(quarter_id: @quarter_id).destroy_all
      @role_class.where(quarter_id: @quarter_id).destroy_all
    end

    def import_groups
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @group_filter, attributes: @group_attrs) do |entry|
          import_a_group(entry)
        end
      end
    end

    def import_accounts
      @roles = @role_class.where(quarter_id: @quarter_id).to_a
      ActiveRecord::Base.transaction do
        @database.search(base: @database.base, filter: @account_filter, attributes: @account_attrs) do |entry|
          import_an_account(entry)
        end
      end
    end

    def import_a_group(entry)
      return unless entry.name.first

      @role_class.create(
        quarter_id: @quarter_id,
        code:       entry.dn,
        name:       entry.name.first,
        gid:        get_sid_string(entry[:objectSid]&.first.to_s).split("-").last,
        data_json:  {memberOf: entry[:memberOf]}
      )
    end

    def import_an_account(entry)
      account_code   = entry.samaccountname&.first&.downcase
      account_name   = entry.name&.first&.gsub(/\s+/, '')
      account_status = account_status(entry)

      return unless account_code && account_name

      account = @account_class.create(
        quarter_id:   @quarter_id,
        code:         account_code,
        name:         account_name,
        status:       account_status,
        gid:          get_sid_string(entry[:objectSid]&.first.to_s).split("-").last,
        account_type: 1
      )

      member_of = entry[:memberof]
      return unless member_of&.size&.positive?

      link_account_and_roles(account, member_of)
    end

    def account_status(entry)
      status_sequences = entry.useraccountcontrol.first.to_i.to_s(2)

      status_sequences[-2] != '1'
    end

    def link_account_and_roles(account, member_of)
      member_of.each do |role_code|
        role = @roles.find { |x| x.code == role_code }
        account.roles << role if role
      end
    end
  end
end



