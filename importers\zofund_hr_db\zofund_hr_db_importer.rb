# frozen_string_literal: true

module AasOracleImporter
  # 中欧 HR 系统导入
  class ZofundHrDbImporter < ImporterBase
    def config
      @bs_id              = importer_config['bs_id']
      @table_space        = importer_config['table_space']
      @departments_table  = importer_config['departments_table']
      @users_table        = importer_config['users_table']
      @managers_table     = importer_config['managers_table']
      @all_users_table    = importer_config['all_users_table']
      @position_users     = [] # 拥有岗位的用户
    end

    def import_to_do
      import_departments
      import_users
      update_user_positions
      update_user_columns
      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    def import_jobs_sql
      <<-EOF
        select distinct title from #{@users_table}
      EOF
    end

    # 导入岗位
    def import_jobs
      @database.exec(import_jobs_sql) do |r|
        name = r[0]
        next if name.nil? || name.empty?

        Job.find_or_create_by(name: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end

    def import_departments_sql
      <<-EOF
        select department,department_id,company_id from #{@table_space}#{@departments_table}
      EOF
    end

    def import_departments

      Department.update_all(inservice: false)
      select_db_datas(import_departments_sql).each do |row|
        import_one_department(row)
      end
    end

    def import_one_department(row)
      department = Department.find_by(code: row[1])
      #return unless row[2].to_i == 1

      if department
        department.update(name: row[0], inservice: true)
      else
        Department.create(code: row[1], name: row[0], inservice: true)
      end
    end

    def import_all_users_sql
      <<-EOF
        select name, username,company_id, status, department_id, manager, offical_leave_date from #{@table_space}#{@all_users_table}
      EOF
    end

    def import_users
      @exist_users = User.all.to_a
      select_db_datas(import_all_users_sql).each do |row|
        import_one_user(row)
      end

      # 存在的都删除掉之后 exist_users 里剩余的就是离职的人了
      @exist_users.each { |u| u.update(inservice: false) }
    end

    def import_one_user(row)
      #return unless row[2].to_i == 1

      user = @exist_users.find { |x| x.code == row[1] }
      disable_date_str = row[6]
      disable_date = disable_date_str.present? ? Date.parse(disable_date_str) : nil

      if user
        user.update(name: row[0], inservice: row[3].match?(/Active/), disable_date: disable_date)
        @exist_users.delete_if { |x| x.code == user.code }
      else
        user = User.create(code: row[1], name: row[0], inservice: row[3].match?(/Active/), disable_date: disable_date)
      end
      create_zofund_hr(user)
    end

    def create_zofund_hr(user)
      account = ZofundHr::Account.create(
        code:       user.code,
        name:       user.name,
        status:     user.inservice,
        quarter_id: @quarter_id,
        user_id:    user.id
      )
      import_ledger_to_account(account, user)
    end

    def import_users_sql
      <<-EOF
        select username, title from #{@table_space}#{@users_table}
      EOF
    end

    # 职务取sf_employeedata_component1
    def update_user_positions
      @users       = User.all.to_a
      @departments = Department.all.to_a

      select_db_datas(import_users_sql).each do |row|
        update_one_user_position(row)
      end
    end

    def update_one_user_position(row)
      #return unless row[2].to_i == 1

      user = @users.find { |x| x.code == row[0] }
      unless user
        @logger.error "db: not found user when update user position for #{row[0]}"
        return
      end

      user.update(position: row[1])

      @position_users << user if user.position?
    end

    def update_user_columns
      @users = User.all.to_a

      select_db_datas(import_all_users_sql).each do |row|
        update_one_user_columns(row)
      end
    end

    def update_one_user_columns(row)
      user = @users.find { |x| x.code == row[1] }
      manager = @users.find { |u| u.code == row[5] }
      unless user
        @logger.error "db: not found user when update user columns for #{row[0]}"
        return
      end

      options = { department_id: @departments.find { |x| x.code == row[4] }&.id }
      options[:manager_id] = manager.id if manager
      user.update(options)
    end

    # hr 的比较特殊，台账可以自动关联
    def import_ledger_to_account(account, user)
      # TODO: 这里默认就是 code
      ledger =
        Ledger.find_or_create_by(
          business_system_id: @bs_id,
          account_code_field: 'code',
          account_code_value: account.code
        )
      unless ledger.user_id
        ledger.update(
          user_id:         account.user_id,
          user_code_field: 'code',
          user_code_value: user.code
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
    end
  end
end
