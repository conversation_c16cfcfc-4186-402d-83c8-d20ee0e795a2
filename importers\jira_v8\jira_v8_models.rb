module JiraV8
  def self.table_name_prefix
    'jira_v8_'
  end
end

class JiraV8::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :project_permissions
  has_and_belongs_to_many :data2_permissions
end

class JiraV8::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :project_permissions
  has_and_belongs_to_many :data2_permissions
end

class JiraV8::ProjectPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class JiraV8::Data2Permission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end
