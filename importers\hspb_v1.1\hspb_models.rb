# frozen_string_literal: true

module Hspb
  def self.table_name_prefix
    'hspb_'
  end
end

class Hspb::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class Hspb::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class Hspb::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class Hspb::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class Hspb::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class Hspb::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class Hspb::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class Hspb::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class Hspb::System < ActiveRecord::Base
  has_many :menus
end

class Hspb::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Hspb::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class Hspb::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class Hspb::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class Hspb::Temporary < ActiveRecord::Base; end

class HspbAccountsRoles < ActiveRecord::Base; end
class HspbMenusRoles < ActiveRecord::Base; end
class HspbMenuAdditionsRoles < ActiveRecord::Base; end

class Hspb::TimeControl < ActiveRecord::Base; end

class Hspb::TopTradeType < ActiveRecord::Base; end
class Hspb::TopStation < ActiveRecord::Base; end
class Hspb::TradeType < ActiveRecord::Base; end
