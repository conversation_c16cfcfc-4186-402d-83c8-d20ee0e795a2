module AasOracleImporter
  # 直销系统的导入模块
  class ZhixiaoV11Importer < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @sub_system  = importer_config['sub_system']
      @db_type     = importer_config['db_type']
      initialize_tables
      initialize_classes
      super
    end

    def initialize_tables
      @table_accounts         = "#{@table_space}hsi_user#{@sid_suffix}"
      @table_roles            = "#{@table_space}hsi_group#{@sid_suffix}"
      @table_accounts_roles   = "#{@table_space}hsi_usergroup#{@sid_suffix}"
      @table_menus            = "#{@table_space}hsi_menu#{@sid_suffix}"
      @table_menus_roles      = "#{@table_space}hsi_groupright#{@sid_suffix}"
      @table_other_permission = "#{@table_space}hsi_right#{@sid_suffix}"
      @table_log              = "#{@table_space}hsi_oplog#{@sid_suffix}"
      @table_password_security = "#{@table_space}tsysparameter#{@sid_suffix}"
    end

    def initialize_classes
      @account_class          = nil
      @role_class             = nil
      @menu_class             = nil
      @other_permission_class = nil

      @account_role_associate_class   = nil
      @account_menu_associate_class   = nil
      @role_menu_associate_class      = nil
      @role_other_permission_class    = nil
      @account_other_permission_class = nil
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_other_permissions
      import_accounts_roles

      import_menus_group(@role_class, @menu_class)
      import_menus_group(@role_class, @other_permission_class)

      # import_system_user_permissions
      import_logs
      import_password_securities
      import_last_login_at_data

      import_ledgers(@account_class)
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :customer_audit_sql unless @enable_import_log
      ignore_list << :password_security_sql unless @enable_import_password_security
      ignore_list << :last_login_at_sql unless @enable_import_last_login_at
      ignore_list
    end

    def import_accounts_sql
      <<-SQL
        select c_usercode, c_username, c_status from #{@table_accounts}
      SQL
    end

    def import_accounts
      @account_class.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = @account_class.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i.zero?
            )

            if @display_status
              QuarterAccountInfo.create(
                account_id:         account.id,
                account_type:       @account_class.to_s,
                business_system_id: @bs_id,
                quarter_id:         @quarter_id,
                display_status:     r[2]&.to_s
              )
            end
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select l_groupid, c_groupname from #{@table_roles}
      SQL
    end

    def import_roles
      @role_class.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          @role_class.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
    end

    def import_menus_sql
      "select c_menucode, c_menuname, c_parentcode from #{@table_menus} where c_sysname = '#{@sub_system}'"
    end

    def import_menus
      @menu_class.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_menus_sql) do |r|
          parent_id_value = r[2]
          name = full_name(import_menus_sql, r[1], parent_id_value, 1, 2)

          @menu_class.create(
            quarter_id: @quarter_id,
            code:       r[0],
            name:       name
          )
        end
      end
    end

    def import_other_permissions_sql
      <<-SQL
        select c_rightcode, c_rightname from #{@table_other_permission}
      SQL
    end

    def import_other_permissions
      @other_permission_class.where(quarter_id: @quarter_id).destroy_all

      ActiveRecord::Base.transaction do
        @database.exec(import_other_permissions_sql) do |r|
          @other_permission_class.create(quarter_id: @quarter_id, code: r[0], name: r[1])
        end
      end
    end

    def check_import_menus_group_sql
      <<-SQL
        select * from  #{@table_menus_roles} #{@db_type.to_s == 'oracle' ? 'where rownum = 1' : 'limit 1'}
      SQL
    end

    def import_menus_group(group, menu)
      join_class =
        if group == @role_class && menu == @menu_class
          @role_menu_associate_class
        elsif group == @role_class && menu == @other_permission_class
          @role_other_permission_class
        end

      group_name   = 'g.c_groupname,'
      group_from   = "#{@table_roles} g,"
      group_flag   = 0
      group_id     = 'g.l_groupid '
      send_method  = :find_by_name
      role_join_id = :role_id

      case menu.to_s
      when @menu_class.to_s
        menu_name         = 'm.c_menuname'
        menu_code         = 'm.c_menucode'
        menu_from         = " #{@table_menus} m"
        menu_flag         = 1
        menu_join_id      = :menu_id
        where_menu_system = "and m.c_sysname = '#{@sub_system}'"
        where_menu_right_system = "and gm.c_sysname = '#{@sub_system}'"
      when @other_permission_class.to_s
        menu_name         = 'm.c_rightname'
        menu_code         = 'm.c_rightcode'
        menu_from         = "#{@table_other_permission} m"
        menu_flag         = 0
        menu_join_id      = :other_permission_id
        where_menu_system = ''
        where_menu_right_system = ''
      end

      sql = <<-SQL
        select
          #{group_name}
          gm.c_rightcode,
          #{menu_name}
        from
          #{@table_menus_roles} gm,
          #{group_from}
          #{menu_from}
        where
          gm.c_flag           = #{group_flag}
          and gm.c_rightclass = #{menu_flag}
          and gm.l_groupid    = #{group_id}
          and gm.c_rightcode  = #{menu_code}
          #{where_menu_system}
          #{where_menu_right_system}
         order by
          gm.c_rightcode
      SQL

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          group_record = group.where(quarter_id: @quarter_id).send(send_method, r[0])
          menu_record  = menu.where(quarter_id: @quarter_id).find_by(code: r[1])

          if menu_record && group_record
            join_class.create(menu_join_id => menu_record.id, role_join_id => group_record.id)
          else
            @logger.warn "#{self.class}: not found #{group} #{r[0]}" unless group_record
            @logger.warn "#{self.class}: not found #{menu} #{r[1]}" unless menu_record
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
         g.c_groupname,
         u.c_usercode
        from #{@table_accounts_roles} ug,
             #{@table_accounts} u,
             #{@table_roles} g
        where ug.l_userid = u.l_userid
          and ug.l_groupid = g.l_groupid
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          role    = @role_class.where(quarter_id: @quarter_id).find_by(name: r[0])
          account = @account_class.where(quarter_id: @quarter_id).find_by(code: r[1])

          if account && role
            @account_role_associate_class.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_system_user_permissions
      system_account = @account_class.find_by(quarter_id: @quarter_id, code: 'system')
      return unless system_account

      ActiveRecord::Base.transaction do
        @menu_class.where(quarter_id: @quarter_id).each do |menu|
          @account_menu_associate_class.create(account_id: system_account.id, menu_id: menu.id)
        end
      end

      ActiveRecord::Base.transaction do
        @other_permission_class.where(quarter_id: @quarter_id).each do |perm|
          @account_other_permission_class.create(account_id: system_account.id, other_permission_id: perm.id)
        end
      end
    end

    # 导入日志sql语句
    def customer_audit_sql
      start_at = get_start_at(@bs_id).strftime('%Y-%m-%d %H:%M:%S')
      where_sql = case @database.database_type
                  when 'oracle'
                    "where D_OPTIME > TO_DATE('#{start_at}', 'yyyy-mm-dd hh24:mi:ss')"
                  else
                    "where D_OPTIME > '#{start_at}'"
                  end
      <<-EOF
        select L_OPID, C_USERCODE, C_OSUSERNAME, D_OPTIME, C_OPCLASS, C_OPCONTENT
        from #{@table_log}
        #{where_sql}
      EOF
    end

    def import_logs
      import_customer_audit_log(@table_log, customer_audit_sql) do |data, r|
        data << {
          'source_id' => r[0],
          'account_code' => r[1],
          'account_name' => r[2],
          'operation_at' => r[3],
          'operation_category' => r[4],
          'operation' => r[5],
          'bs_id' => @bs_id,
          'ip_address' => nil
        }
      end
    end

    def password_security_sql
      "select C_ITEM, C_VALUE, C_DESCRIBE from #{@table_password_security}"
    end

    def import_password_securities
      import_customer_password_security(password_security_sql) do |data, json|
        json['bs_id'] = @bs_id
        json['password_length'] = data.find { |x| x[0] == 'PasswordLengthMin' }&.[](1)
        json['is_uppercase'] = nil
        json['is_lowercase'] = nil
        json['is_number'] = data.find { |x| x[0] == 'PasswordMix' }&.[](1)&.to_s == '1'
        json['is_character'] = nil
        json['login_failure_number'] = data.find { |x| x[0] == 'PasswordError' }&.[](1)
        json['password_valid_time'] = data.find { |x| x[0] == 'PasswordExpireDays' }&.[](1)
      end
    end

    def last_login_at_sql
      if @import_last_login_at.present?
        where_sql = case @database.database_type
                    when 'oracle'
                      "where d_optime > sysdate-#{@import_last_login_at}"
                    else
                      start_at = (Time.now - @import_last_login_at.days).strftime('%Y-%m-%d %H:%M:%S')
                      "where d_optime > '#{start_at}'"
                    end
      else
        where_sql = nil
      end
      <<-EOF
        select c_usercode, max(d_optime)
        from #{@table_log}
        #{where_sql}
        group by c_usercode
      EOF
    end

    def import_last_login_at_data
      import_last_login_at(@table_log, last_login_at_sql) do |data, x|
        data << {
          'code' => x[0],
          'last_login_at' => x[1]
        }
      end
    end

    protected

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      limit = (@db_type.to_s == 'oracle' ? 'and rownum = 1' : 'limit 1').to_s
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' #{limit}"
      @database.exec(new_sql) do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end
  end
end
