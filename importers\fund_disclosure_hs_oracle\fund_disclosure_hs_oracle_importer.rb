# frozen_string_literal: true

module AasOracleImporter
  # 恒生信披系统
  class FundDisclosureHsOracleImporter < ImporterBase
    include ConvertTools
    CodeTableRecord = Struct.new(:fd_code, :o32_code)

    def config
      @bs_id                 = importer_config['bs_id']
      @table_space           = importer_config['table_space']
      @sid_suffix            = importer_config['sid_suffix']
      @fund_code_table_space = importer_config['fund_code_table_space']

      initialize_tables
    end

    def initialize_tables
      @table_user = "#{@table_space}txbrl_jjjlxx#{@sid_suffix}"
      @table_fund = "#{@table_space}tfundinfo#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_ledgers(FundDisclosure::Account)
      # 下列方法在 import_ledgers 之后进行
      update_accounts_status
    end

    def destroy_exist_data
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select u.vc_jjjlxm,
          f.vc_code,
          f.vc_name,
          u.d_jjjlrzrq,
          u.d_jjjllrrq
        from #{@table_user} u,
             #{@table_fund} f
        where u.vc_code = f.vc_code
      SQL
    end

    def import_accounts
      # 永赢基金这里只有公募基金，所以角色只有基金经理
      role = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, name: '基金经理')

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          user_name, fund_code, fund_name, take_post_date, leave_post_date = r
          user_code = user_name
          # 账号状态默认设为 false ，后面会根据台账的关联再做修改
          user_status = false
          next unless user_name

          account         = find_or_create_account(user_code, user_name, user_status)

          # 这里 take_post_date 会返回 time格式
          take_post_date  = take_post_date.to_date  if take_post_date
          leave_post_date = leave_post_date.to_date if leave_post_date

          # 根据客户需求，目前导入所有历史权限
          # next unless in_active_date?(take_post_date, leave_post_date)

          # 放在判定 时间的下面，只记录有效基金
          fund = find_or_create_fund(fund_code, fund_name)

          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  take_post_date,
            leave_post_date: leave_post_date,
            quarter_id:      @quarter_id
          )
        end
      end
    end

    # 信披系统中的用户为从公告中人工导入，并不实际存在。
    # 其账号状态应与 HR 系统账号保持一致
    def update_accounts_status
      ActiveRecord::Base.transaction do
        FundDisclosure::Account.where(quarter_id: @quarter_id).includes(:user).each do |account|
          account.update(status: account.user.inservice) if account.user_id
        end
      end
    end

    def find_or_create_account(code, name, status)
      user        = FundDisclosure::Account.find_or_initialize_by(quarter_id: @quarter_id, code: code)
      user.name   = name
      user.status = status
      user.save
      user
    end

    def find_or_create_fund(code, name)
      fund_code = convert_fund_code(code)
      fund      = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: fund_code)
      fund.name = name
      fund.o32_fund_codes = add_o32_fund_codes(fund.o32_fund_codes, fund_code)
      fund.save
      fund
    end
  end
end
