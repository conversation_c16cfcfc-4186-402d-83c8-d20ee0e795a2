# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: 'yyfund'
  customer: '永赢基金'
  ipaddr: 'localhost'
  log_level: 'DEBUG'

  server:
    path: '/Users/<USER>/Coding/sdata/account_audit_system'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_yyfund_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      # 数据库配置 要改
      # ta 系统数据库
      hrdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # 投资交易 O32 系统数据库
      tradedb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      tadb:
        # in tns name
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'
      # 分 ta 系统数据库
      sadb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # ETF 分 ta 系统数据库
      etftadb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'
      # 直销系统数据库
      zxdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # 清算系统数据库
      qsdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # 估值系统数据库
      gzdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # 信披系统数据库
      xpdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # crm系统数据库
      crmdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'

      # fxq系统数据库
      fxqdb:
        db_name: 'htdcdb'
        db_user: 'sdata_select'
        db_pass: 'sdata_select'
        db_host: '***********'
    mysql:
      etfdb:
        db_host: *************
        db_name: etf
        db_user: sdata_select
        db_pass: sdata_select
        db_port: '33061'
      momdb:
        db_host: *************
        db_name: dbtrade
        db_user: sdata_select
        db_pass: sdata_select
        db_port: '3306'
      pcdb:
        db_host: *************
        db_name: product-center
        db_user: sdata_select
        db_pass: sdata_select
        db_port: '22371'
      upcdb:
        db_host: ************
        db_name: system_service
        db_user: sdata_select
        db_pass: sdata_select
        db_port: '3306'
  importers:
    # MARK: 按照顺序依次导入
    # HR 系统
    - name: yyfund_hr
    # 接口对接，没有其他属性, 下面这个为了通过 initialize
      db_type: oracle
      tnsname: hrdb

    - name: xiening_touyan
      bs_id: 38
      db_type: oracle
      tnsname: gzdb
      table_space: 'exch.sirm_'
      sid_suffix: ''
      display_status: true

    # 反洗钱
    - name: fanxiqian
      db_type: oracle
      tnsname: fxqdb
      table_space: 'exch.aml_'
      sid_suffix: ''


    #估值系统
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: gzdb
      table_space: 'exch.fa_'
      sid_suffix: ''
      hs_customer_code: 71551
      add_on_modules_table: txtcs_paramter
      import_fund_code: true

    # 清算系统
    - name: hs_qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: qsdb
      table_space: 'exch.power_'
      sid_suffix: ''


    # 分 TA 系统
    - name: guohu_sa
      bs_id: 26
      db_type: oracle
      tnsname: sadb
      table_space: 'exch.xtta_'
      sid_suffix: ''

    # ETF 分 TA 系统
    - name: guohu_sa_etf
      bs_id: 40
      db_type: oracle
      tnsname: etftadb
      table_space: 'exch.etfta_'
      sid_suffix: ''

    # 直销系统
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zxdb
      table_space: 'exch.ds_'
      sid_suffix: ''
      sub_system: "CENTER"

    # 直销系统
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zxdb
      table_space: 'exch.ds_'
      sid_suffix: ''
      sub_system: "COUNTER"

    # 直销系统
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zxdb
      table_space: 'exch.ds_'
      sid_suffix: ''
      sub_system: "REMOTE"

    # guohu_components/yyfund_guohu_ta: true
    # 登记过户 4.0
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: tadb
      table_space: 'exch.ta_'
      sid_suffix: ''

    # 投资交易 O32 系统
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: tradedb
      table_space: 'exch.FM_'
      sid_suffix: ''
      days_of_data: 365
      temporary: true

    # # 信披系统
    # - name: fund_disclosure_dj_oracle
    #   bs_id: 32
    #   db_type: oracle
    #   tnsname: xpdb
    #   table_space: 'exch.xb_'
    #   sid_suffix: ''

    - name: crm_system
      bs_id: 37
      db_type: oracle
      tnsname: crmdb
      table_space: 'exch.crm_'
      sid_suffix: ''
      yyfund_ledger: true

      # 产品中心系统
    - name: product_center
      bs_id: 57
      db_type: mysql
      tnsname: pcdb
      sid_suffix: ''
      table_space: ''
      sid_suffix: ''
      expend_dbs:
        update_pd_db:
          db_type: mysql
          tnsname: upcdb
    # - name: update_product_center
    #   bs_id: 57
    #   db_type: mysql
    #   tnsname: upcdb
    #   sid_suffix: ''
    #   table_space:
    #   sid_suffix: ''
    # mom
    - name: mom
      bs_id: 309
      db_type: mysql
      tnsname: momdb
      sid_suffix: ''
      table_space:
      sid_suffix: ''
    # etf
    - name: etf_yy
      bs_id: 310
      db_type: mysql
      tnsname: etfdb
      sid_suffix: ''
      table_space:
      sid_suffix: ''
development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    # 要改
    database:
      adapter: <%= ENV['DB_ADAPTER'] ||= 'mysql2' %>
      database: aas_production
      host: ************
      pool: 25
      username: aas
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>



