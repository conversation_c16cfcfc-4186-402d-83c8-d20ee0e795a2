require 'httparty'
require 'byebug'
module AasOracleImporter

  class HaikangKaoqinImporter < ImporterBase
    include HTTParty
    def config
      @bs_id              = importer_config['bs_id']
      @app_key            = importer_config['app_key']
      @secret_key         = importer_config['secret_key']
      @base_url           = importer_config['base_uri']
      @account_uri        = importer_config['account_uri']
      @accounts           = []
      @roles              = []
      @data1_permissions  = []
      @timestamp = Time.now.to_i.to_s + "000"
      self.class.base_uri importer_config['base_uri']
    end

    def initialize_tables
    end

    def import_test
      uri = '/artemis/api/resource/v2/person/personList'
      body = {
        pageNo: 1,
        pageSize: 1000
      }
      result = send_request(uri,JSON.dump(body))
      pp result.to_s
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_ledgers(HaikangKaoqin::Account)
    end

    def destroy_exist_datas
      accounts = HaikangKaoqin::Account.where(quarter_id: @quarter_id)
      HaikangKaoqin::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HaikangKaoqin::Role.where(quarter_id: @quarter_id).delete_all
      HaikangKaoqin::Data1Permission.where(quarter_id: @quarter_id).delete_all
      HaikangKaoqin::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        uri = '/artemis/api/resource/v2/person/personList'
        body = {
          pageNo: 1,
          pageSize: 1000
        }
        result = send_request(uri, JSON.dump(body))
        result['data']['list'].each do |r|
          @accounts << HaikangKaoqin::Account.create(
            quarter_id: @quarter_id,
            source_id: r['personId'],
            code: r['jobNo'],
            name: r['personName'],
            status: true
          )
        end
      end
      
    end

    def send_request(uri, body)
      response   = self.class.post(@base_url+uri, {
                                     headers: headers(uri),
                                     body: body,
                                     verify:  false
                                   }) 
      result = JSON.parse response.body
      unless result['code'].to_i.zero?
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers(uri)
      {
        'Accept':           '*/*',
        'Content-Type':     'application/json',
        'X-Ca-Key': @app_key.to_s,
        'X-Ca-Signature': h_authorization(uri),
        'X-Ca-Signature-Headers': 'x-ca-key,x-ca-timestamp',
        'X-Ca-Timestamp': @timestamp.to_s,
        'X-Requested-With': 'XMLHttpRequest'
      }
    end

    def h_authorization(uri)
      string_to_sign = "POST\n*/*\napplication/json\nx-ca-key:#{@app_key}\nx-ca-timestamp:#{@timestamp}\n#{uri}"
      sha1_sign      = OpenSSL::HMAC.digest('sha256', @secret_key.encode('UTF-8'), string_to_sign.encode('UTF-8'))
      signature      = Base64.strict_encode64(sha1_sign)
      puts string_to_sign
      puts signature
      signature
    end
  end
end
