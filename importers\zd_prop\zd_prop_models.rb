module ZdProp
  def self.table_name_prefix
    'zd_prop_'
  end
end

class ZdProp::Account < ActiveRecord::Base; end
class ZdProp::Role    < ActiveRecord::Base; end
class ZdProp::Data1Permission < ActiveRecord::Base; end
class ZdProp::Data1AccountsRolesPermission < ActiveRecord::Base
  self.table_name = 'zd_prop_data1_arps'
end
class ZdProp::Department < ActiveRecord::Base
  has_ancestry cache_depth: true

  validates :code, presence: true
  validates :name, presence: true
end