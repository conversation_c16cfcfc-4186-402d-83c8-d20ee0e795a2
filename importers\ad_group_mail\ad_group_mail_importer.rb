module AasOracleImporter
  # AD 域数据导入
  class AdGroupMailImporter < ImporterBase
    def config
      @bs_id          = importer_config['bs_id']
      @account_filter = importer_config['account_filter']
      @account_attrs  = importer_config['account_attrs']
      @top_ou         = importer_config['top_ou']
    end

    def import_to_do
      destroy_all_datas
      import_accounts
      import_ledgers(AdGroupMail::Account)
    end

    def the_system
      BusinessSystem.find(@bs_id)
    end

    private

    def destroy_all_datas
      AdGroupMail::Account.where(quarter_id: @quarter.id).destroy_all

      AdGroupMail::Role.where(quarter_id: @quarter.id).destroy_all
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        entries = @database.search(base: query_base(@top_ou), filter: @account_filter, attributes: @account_attrs)
        entries.each do |entry|
          import_an_account(entry)
        end
      end
    end

    def query_base(top_ou)
      top_ou.present? ? "ou=#{top_ou.first}, #{@database.base}" : @database.base
    end

    def import_an_account(entry)
      return unless value?(entry,
                           'samaccountname') && value?(entry, 'name') && value?(entry, 'msexchwhenmailboxcreated')

      account_struct = LdapPerson.new(entry)

      AdGroupMail::Account.create(
        quarter_id: @quarter.id,

        source_id:  account_struct.code,

        code:       account_struct.code,

        status:     account_struct.email ? true : false,

        name:       account_struct.name,

        email:      account_struct.email
      )
    end

    def value?(entry, key)
      key = key.to_sym
      entry.attribute_names.include?(key) && entry.send(key).first
    end
  end
end
