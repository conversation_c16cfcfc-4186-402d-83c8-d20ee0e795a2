module AasOracleImporter

  class JzJisuJiaoyiImporter < ImporterBase

    def config
      @bs_id       = 59
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      initialize_tables
    end

    def initialize_tables
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      
      
      import_data1_permissions
      
      import_data1_role_permissions
      
      
      
      import_ledgers(JzJisuJiaoyi::Account)
    end

    def destroy_exist_datas
      accounts = JzJisuJiaoyi::Account.where(quarter_id: @quarter_id)
      JzJisuJiaoyi::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzJisuJiaoyi::Role.where(quarter_id: @quarter_id).delete_all
      
      
      JzJisuJiaoyi::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      JzJisuJiaoyi::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
      
      
    end

    
    def import_accounts_sql
      <<-EOF
        SELECT  a.OP_CODE,a.OP_CODE,b.USER_NAME,a.OP_STATUS FROM UUM_OPERATOR a LEFT JOIN UEM_EMP b on a.OP_CODE = b.USER_CODE
      EOF
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        enums = [{"name"=>"status", "value"=>"0", "enum_value"=>"正常", "label"=>"账号状态"}]
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << JzJisuJiaoyi::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[1]),
            name: get_enum(enums, "name", r[2]),
            status: r[3]&.to_s == enums.find { |enum| enum['name'] == 'status' }&.[]('value')&.to_s
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select POST_ID,POST_ID,POST_NAME,MAINTAIN_FLAG from UEM_POST
      EOF
    end
    

    def import_roles
      
      ActiveRecord::Base.transaction do
        
        
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzJisuJiaoyi::Role.create(
            quarter_id: @quarter_id,
            source_id: r[0].to_i.to_s,
            code: r[1].to_i.to_s,
            name: r[2]
          )
        end
      end
      
    end

    
    def import_accounts_roles_sql
      <<-EOF
        select POST_ID, USER_CODE from UEM_EMP_POST
      EOF
    end
    

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find{|x| x.source_id.to_i.to_s == r[0].to_i.to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          next unless account && role

          JzJisuJiaoyi::AccountsRole.create(account_id: account.id, role_id: role.id  )
        end
      end
      
    end

    def import_data1_permissions_sql
      <<-EOF
        select MENU_ID,MENU_ID,MENU_NAME, PAR_MENU from UPM_MENU
      EOF
    end

    def import_data1_permissions
      
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          
          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index))
          
          json = {
            quarter_id: @quarter_id,
            
            source_id: r[0],
            
            code: r[1],
            
            level1_name: level1_name,
            
          }
          @data1_permissions << JzJisuJiaoyi::Data1Permission.create(json)
        end
      end
      
    end
    
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        select OPP_OBJ_CODE,MENU_ID,OPP_AUTH_TYPE from UUM_OBJ_PERM where OPP_OBJ_TYPE = 2
      EOF
    end

    def import_data1_role_permissions
      
      enums = [{"name"=>"additional_permission", "value"=>"1", "enum_value"=>"执行权限", "label"=>"附加权限"}, {"name"=>"additional_permission", "value"=>"2", "enum_value"=>"授权权限", "label"=>"附加权限"}]
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find{|x| x.source_id.to_i.to_s == r[0].to_i.to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          next unless permission && role

          JzJisuJiaoyi::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id, additional_permission: get_enum(enums, "additional_permission", r[2])  )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}'"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
