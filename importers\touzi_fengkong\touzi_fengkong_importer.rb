module AasOracleImporter

  class TouziFengkongImporter < ImporterBase

    def config
      @bs_id = 10006
      @accounts = []
      @roles = []
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @data2_permissions = []
      @data2_accounts_roles_permissions = []
      @data3_permissions = []
      @data3_accounts_roles_permissions = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data2_permissions
      import_data2_role_permissions
      import_data3_permissions
      import_data3_account_permissions
      import_reports
      import_ledgers(TouziFengkong::Account)
    end

    def import_accounts_sql
      <<-EOF
        select EM_USERNO, EM_USERNAME, EM_STATE from #{@table_space}di_edge_xrisk_user#{@sid_suffix}
      EOF
    end
    

    def import_accounts
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << TouziFengkong::Account.create(
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            name: get_enum(enums, "name", r[1]),
            status: r[2] == '正常',
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select R_ID,R_NAME from #{@table_space}di_edge_xrisk_role#{@sid_suffix}
      EOF
    end
    
    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << TouziFengkong::Role.create(
            quarter_id: @quarter_id, 
            source_id: get_enum(enums, "source_id", r[0]), 
            code: get_enum(enums, "code", r[0]), 
            name: get_enum(enums, "name", r[1])
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select R_ID,EM_USERNO from #{@table_space}di_edge_xrisk_user_role#{@sid_suffix}
      EOF
    end
    

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            TouziFengkong::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
    end
    
    def import_data1_permissions_sql
      <<-EOF
        select SM_CODE,SM_NAME,SM_CODE_PARENT,SM_URL from #{@table_space}di_edge_xrisk_MODULE#{@sid_suffix}
      EOF
    end

    def import_data1_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, r[1], r[2], 1, 2))
          json = {
            quarter_id: @quarter_id,
            source_id: r[0],
            code: r[0],
            level1_name: level1_name,
            level2_name: r[2],
            level3_name: r[3]
          }
          @data1_permissions << TouziFengkong::Data1Permission.create(json)
        end
      end
    end
    
    def import_data1_role_permissions_sql
      <<-EOF
        select R_ID,SM_CODE from #{@table_space}di_edge_xrisk_role_module#{@sid_suffix}
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role && !@data1_accounts_roles_permissions.find{|x| x.role_id == role.id && x.data1_permission_id == permission.id }
            @data1_accounts_roles_permissions << TouziFengkong::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data1_permission_id: permission.id)
          end
        end
      end
    end
    
    def import_data2_permissions_sql
      <<-EOF
        select REP_CODE,REP_NAME,GROUP_NAME from #{@table_space}di_edge_xrisk_module_rep#{@sid_suffix}
      EOF
    end

    def import_data2_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1],
            level2_name: r[2]
          }
          @data2_permissions << TouziFengkong::Data2Permission.create(json)
        end
      end
    end
    
    def import_data2_role_permissions_sql
      <<-EOF
        select R_ID,SM_CODE from #{@table_space}di_edge_xrisk_role_module#{@sid_suffix}
      EOF
    end

    def import_data2_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data2_permissions.find{|x| x.source_id.to_s == r[1].to_s}
          if permission && role
            TouziFengkong::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id, data2_permission_id: permission.id)
          end
        end
      end
    end

    def import_data3_permissions_sql
      <<-EOF
        select PORT_CODE,PORT_NAME from #{@table_space}di_edge_xrisk_product#{@sid_suffix}
      EOF
    end

    def import_data3_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data3_permissions_sql).each do |r|
          json = {
            quarter_id: @quarter_id,
            source_id: get_enum(enums, "source_id", r[0]),
            code: get_enum(enums, "code", r[0]),
            level1_name: r[1]
          }
          @data3_permissions << TouziFengkong::Data3Permission.create(json)
        end
      end
    end
    
    def import_data3_account_permissions_sql
      <<-EOF
        select EM_USERNO,PORT_CODE from #{@table_space}di_edge_xrisk_user_data#{@sid_suffix}
      EOF
    end

    def import_data3_account_permissions
      enums = []
      select_db_datas(import_data3_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.code.to_s == r[0].to_s}
        permission = @data3_permissions.find{|x| x.code.to_s == r[1].to_s}
        if account && permission
          TouziFengkong::Data3AccountsRolesPermission.create(quarter_id: @quarter_id, account_id: account.id, data3_permission_id: permission.id)
        end
      end
    end

    def import_reports_sql
      <<-EOF
        select JOB_OBJECT,JOB_NAME,MAIL_USER_LIST from  #{@table_space}di_edge_xrisk_report_mail#{@sid_suffix}
      EOF
    end

    def import_reports
      ActiveRecord::Base.transaction do
        @disabled_reports = FundEmailsCheck::Report.where(business_system_id: @bs_id).pluck(:source_id)
        select_db_datas(import_reports_sql).each do |r|
          import_reports_line(r)
        end
        FundEmailsCheck::Report.where(business_system_id: @bs_id, source_id: @disabled_reports).each{|x| x.update(status: false)}
      end
    end

    def import_reports_line(r)
      source_id = r[0].to_s + r[1].to_s
      report = FundEmailsCheck::Report.find_or_initialize_by(business_system_id: @bs_id, source_id: source_id, report_name: r[1])
      report.receiver_email = set_email(r[2])
      report.status = true
      report.save
      @disabled_reports.delete(source_id)
    end

    def set_email(emails)
      emails.to_s.gsub("\n","").gsub(" ", "").split(";")
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map{|k,v| v} : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?
      name.gsub('&nbsp;', ' ')
    end

  end
end



