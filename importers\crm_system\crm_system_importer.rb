module AasOracleImporter

  class CrmSystemImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @yyfund_ledger = importer_config['yyfund_ledger']
    end

    def import_to_do
      import_accounts
      import_roles
      
      import_ledgers(CrmSystem::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def account_sql
      if @yyfund_ledger
        "SELECT t.C_USERCODE,t.C_USERNAME,t.C_STATUS,t.L_USERID,a.c_brokeraccount FROM #{@table_space}hsi_user t, #{@table_space}tfundbroker a where t.c_usercode=a.c_usercode"
      else
        "SELECT C_USERCODE,C_USERNAME,C_STATUS,L_USERID FROM #{@table_space}hsi_user"
      end
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        
        @database.exec(account_sql) do |r|
          if r[0].to_s != ''
            account = CrmSystem::Account.new(quarter_id: @quarter_id, code: r[0], name: r[1], status: r[2].to_i == 0, objid: r[3])
            account.data_json = {user_code: r[4]} if @yyfund_ledger
            account.save
          end
        end
      end
    end

    def role_sql
      "SELECT L_GROUPID,C_GROUPNAME FROM #{@table_space}hsi_group"
    end

    def account_role_sql
      "SELECT * FROM #{@table_space}hsi_usergroup"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(role_sql) do |r|
          if r[0].to_s != ''
            role = CrmSystem::Role.create(quarter_id: @quarter_id, code: r[0], name: r[1])
          end
        end

        @database.exec(account_role_sql) do |r|
          if r[0].to_s != ''
            account = CrmSystem::Account.find_by(quarter_id: @quarter_id, objid: r[0])
            role = CrmSystem::Role.find_by(quarter_id: @quarter_id, code: r[1])
            if account && role
              account.roles << role
            end
          end
        end
      end
    end

    

  end
end



