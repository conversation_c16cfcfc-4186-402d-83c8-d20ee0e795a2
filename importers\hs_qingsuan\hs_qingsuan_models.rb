module HsQingsuan
  def self.table_name_prefix
    'hs_qingsuan_'
  end
end

class HsQingsuan::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class HsQingsuan::AdditionalPermission < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class HsQingsuan::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
  has_many :additional_permissions
end

class HsQingsuan::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :additional_permissions
end

class HsQingsuanMenusRoles < ActiveRecord::Base; end
class HsQingsuanAccountsRoles < ActiveRecord::Base; end
class HsQingsuanAccountsMenus < ActiveRecord::Base; end
class HsQingsuanAccountsAdditionalPermissions < ActiveRecord::Base; end
class HsQingsuanAdditionalPermissionsRoles  < ActiveRecord::Base; end
