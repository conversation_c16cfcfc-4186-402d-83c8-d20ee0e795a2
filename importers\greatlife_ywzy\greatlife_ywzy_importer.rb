module AasOracleImporter
  class GreatlifeYwzyImporter < ImporterBase
    def config
      @bs_id       = 500
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}sys_user#{@sid_suffix}"
      @table_role         = "#{@table_space}sys_role#{@sid_suffix}"
      @table_account_role = "#{@table_space}sys_user_role#{@sid_suffix}"
      @table_menu         = "#{@table_space}sys_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}sys_role_menu#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions

      import_ledgers(GreatlifeYwzy::Account)
    end

    def destroy_exist_datas
      accounts = GreatlifeYwzy::Account.where(quarter_id: @quarter_id)
      GreatlifeYwzy::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      GreatlifeYwzy::Role.where(quarter_id: @quarter_id).delete_all
      GreatlifeYwzy::Data1Permission.where(quarter_id: @quarter_id).delete_all
      GreatlifeYwzy::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      QuarterAccountInfo.where(quarter_id: @quarter_id, business_system_id: @bs_id).destroy_all
    end

    def import_accounts_sql
      <<-EOF
        select user_id, user_name, nick_name, status, email, comcode from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << GreatlifeYwzy::Account.create(
            quarter_id:        @quarter_id,
            source_id:         r[0],
            code:              r[1],
            name:              r[2],
            status:            r[3]&.to_s == '0',
            email:             r[4],
            organization_code: r[5]&.strip
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select role_id, role_key, role_name from #{@table_role} where status = 0
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << GreatlifeYwzy::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role_id, user_id from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          GreatlifeYwzy::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select menu_id, menu_id code, menu_name, parent_id from #{@table_menu} where status=0
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          parent_id_value = r[3]
          name = full_name(import_data1_permissions_sql, r[2], parent_id_value, 2, 3)
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << GreatlifeYwzy::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select role_id, menu_id from #{@table_role_menu}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          GreatlifeYwzy::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}'"
      select_db_datas(new_sql).each_with_index do |r, index|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
        break if index >= 0
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
