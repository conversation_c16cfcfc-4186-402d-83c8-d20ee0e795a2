module AasOracleImporter

  class XieningFxqImporter < ImporterBase

    def config
      @bs_id = 71
      @accounts = []
      @roles = []
      @account_permissions = []
      @role_permissions = []
      @additional_permissions = []
      
      
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      
      
      # initialize_tables
      # initialize_classes
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      
      import_additional_permissions
      import_additional_role_permissions
      import_additional_account_permissions
      
      import_data1_permissions
      
      import_data1_role_permissions
      
      
      import_data1_account_permissions
      
      
      
      import_ledgers(XieningFxq::Account)
    end

    def destroy_exist_datas
      accounts = XieningFxq::Account.where(quarter_id: @quarter_id)
      XieningFxq::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      XieningFxq::Role.where(quarter_id: @quarter_id).delete_all
      
      
      XieningFxq::Data1Permission.where(quarter_id: @quarter_id).delete_all
      
      XieningFxq::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      
    end

    
    def import_accounts_sql
      <<-EOF
        select userid, userid, empname, case state when '0' then 1 else 0 end as state from Aml_st_product.um_ecuserinfo
      EOF
    end
    

    def import_accounts
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << XieningFxq::Account.create(
            quarter_id: @quarter_id,
            
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            
            
            code: get_enum(enums, "code", r[1]),
            
            
            
            name: get_enum(enums, "name", r[2]),
            
            
            
            status: get_enum(enums, "status", r[3])&.to_s == '1.0',
            
            
          )
        end
      end
      
    end

    
    def import_roles_sql
      <<-EOF
        select orgid, orgid, orgname, parentorgid from Aml_st_product.sprt_orgrole
      EOF
    end
    

    def import_roles
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << XieningFxq::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, "source_id", r[0]), code: get_enum(enums, "code", r[1]), name: get_enum(enums, "name", r[2]), parent_code: get_enum(enums, "parent_code", r[3]))
        end
      end
      
    end

    
    def import_accounts_roles_sql
      <<-EOF
        select parentorgid, userid from Aml_st_product.sprt_userrolerela
      EOF
    end
    

    def import_accounts_roles
      
      ActiveRecord::Base.transaction do
        
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          account = @accounts.find{|x| x.source_id.to_s == r[1].to_s}
          if account && role
            XieningFxq::AccountsRole.create(account_id: account.id, role_id: role.id  )
          end
        end
      end
    end

    
    
    
    
    def import_data1_permissions_sql
      <<-EOF
        select OBJID, OBJID, name, URL, parentid from Aml_st_product.t_menu
      EOF
    end

    def import_data1_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 4
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])
          
          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index, 'menu'))

          json = {
            quarter_id: @quarter_id,
            
            source_id: get_enum(enums, "source_id", r[0]),
            
            code: get_enum(enums, "code", r[1].to_i),
            
            level1_name: level1_name,
            
            level2_name: get_enum(enums, "level2_name", r[3]),
            
          }
          @data1_permissions << XieningFxq::Data1Permission.create(json)
        end
      end
      
    end
    
    
    def import_data1_role_permissions_sql
      <<-EOF
        select parentorgid, orgid from Aml_st_product.sprt_orgrelation where RIGHTDEFINEKEY ='SPRT_SYSMENU'
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @data1_permissions.find{|x| x.source_id.to_i.to_s == r[1].to_s}
          if permission && role
            role_permissions = @role_permissions.select{|x| x[:role_id].to_s == r[0].to_s}.map{|x| x[:permission]}
            additional_permissions = role_permissions.select{|x| x[:menuid].to_s == r[1].to_s}
            additional_name = additional_permissions.map{|x| x[:level1_name]}.uniq.join(",")
            
            XieningFxq::Data1AccountsRolesPermission.create(
              quarter_id: @quarter_id, 
              role_id: role.id, 
              data1_permission_id: permission.id,
              additional_permission: additional_name
            )
          end
        end
      end
    end


    
    def import_data1_account_permissions_sql
      <<-EOF
        select su.userid, so.orgid from Aml_st_product.SPRT_ORGRELATION so inner join Aml_st_product.SPRT_USERROLERELA su on so.parentorgid = su.parentorgid
      where so.rightdefinekey = 'SPRT_SYSMENU'
      EOF
    end

    def import_data1_account_permissions
      enums = []
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @data1_permissions.find{|x| x.source_id.to_s == r[1].to_s}

        if account && permission
          account_permissions = @account_permissions.select{|x| x[:account_id].to_s == r[0].to_s}.map{|x| x[:permission]}
          additional_permissions = account_permissions.select{|x| x[:menuid].to_s == r[1].to_s}
          additional_name = additional_permissions.map{|x| x[:level1_name]}.uniq.join(",")

          XieningFxq::Data1AccountsRolesPermission.create(
            quarter_id: @quarter_id, 
            account_id: account.id, 
            data1_permission_id: permission.id,
            additional_permission: additional_name
          )
        end
      end
    end



    def import_additional_permissions_sql
      <<-EOF
        select tf.objid, tf.objid, tf.name, tf.funckey, tf.menuid from Aml_st_product.t_menu_function tf
      EOF
    end

    def import_additional_permissions
      enums = []
      
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 4
        select_db_datas(import_additional_permissions_sql).each do |r|
          name = get_enum(enums, "level1_name", r[level1_name_index])
          
          parent_id_value = r[parent_id_index]
          level1_name = replace_blank_name(full_name(import_additional_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index, 'function'))
          
          per = 
          {
            source_id: get_enum(enums, "source_id", r[0]),

            objid: get_enum(enums, "source_id", r[0]), # 权限id

            menuid: r[4], # 菜单id
            
            level1_name: level1_name # 权限名称
            
          }

          @additional_permissions << per
        end
      end
    end

    def import_additional_role_permissions_sql
      <<-EOF
        select parentorgid, orgid from Aml_st_product.sprt_orgrelation where RIGHTDEFINEKEY ='FUNCTIONINFO'
      EOF
    end

    def import_additional_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        @role_permissions = []
        select_db_datas(import_additional_role_permissions_sql).each do |r|
          role = @roles.find{|x| x.source_id.to_s == r[0].to_s}
          permission = @additional_permissions.find{|x| x[:source_id].to_i.to_s == r[1].to_s}
          if permission && role
            role_permission = 
            {
              role_id: role.source_id,
              permission: permission
            }
            @role_permissions << role_permission
          end
        end
      end
    end

    def import_additional_account_permissions_sql
      <<-EOF
        select su.userid, so.orgid from Aml_st_product.SPRT_ORGRELATION so inner join Aml_st_product.SPRT_USERROLERELA su on so.parentorgid = su.parentorgid
      where so.rightdefinekey = 'FUNCTIONINFO'
      EOF
    end

    def import_additional_account_permissions
      enums = []
      @account_permissions = []
      select_db_datas(import_additional_account_permissions_sql).each do |r|
        account = @accounts.find{|x| x.source_id.to_s == r[0].to_s}
        permission = @additional_permissions.find{|x| x[:source_id].to_i.to_s == r[1].to_s}
        if account && permission
          account_permission = 
          {
            account_id: account.source_id,
            permission: permission
          }
          @account_permissions << account_permission
        end
      end
    end
    

    def select_db_datas(sql)
      sql = sql.gsub(';', '')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id=nil, name_index, parent_id_index, type)
      return name if parent_id.blank?

      sql = sql.downcase
      if type == 'function'
        id_column = 'tm.objid'
        new_sql = sql + ", Aml_st_product.t_menu tm #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      else
        id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
        new_sql = sql + " #{ sql.downcase.include?(' where ') ? 'and' : 'where' } #{id_column}='#{parent_id}' and rownum = 1"
      end

      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index, type)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

  end
end
