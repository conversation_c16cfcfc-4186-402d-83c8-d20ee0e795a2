# frozen_string_literal: true

module AasOracleImporter
  # Sqlite 客户端
  class SqliteClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.execute(sql).each do |row|
          yield row
        end
      else
        @database.execute(sql)
      end
    end

    # 输出hash数组
    def query(sql)
      results = @database.execute2(sql)
      # 获取表字段数组
      attrs   = results.first
      data    = []
      results.each_with_index do |result, index|
        next if index.zero?

        h = {}
        result.each_with_index do |r, i|
          h[attrs[i]] = r
        end
        data << h
      end
      data
    end

    private

    def initialize_driver
      load_driver_gem
      raise 'sqlite db not found' unless File.exist?(sqlite_db_path)

      @database = SQLite3::Database.new(sqlite_db_path)
    rescue SQLite3::Exception => e
      raise SQLite3::Exception, message_prefix + e.message
    end

    def load_driver_gem
      require 'sqlite3'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'sqlite3\'.')
      exit(-127)
    end

    # Sqlite 特定格式
    def sqlite_db_path
      database_info['db_path']
    end
  end
end
