module AasOracleImporter
  class JzYunyingImporter < ImporterBase
    def config
      @bs_id       = 255
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      @data2_permissions = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}UUM_OPERATOR#{@sid_suffix}"
      @table_user         = "#{@table_space}UEM_EMP#{@sid_suffix}"
      @table_role         = "#{@table_space}UEM_POST#{@sid_suffix}"
      @table_account_role = "#{@table_space}UEM_EMP_POST#{@sid_suffix}"
      @table_menu         = "#{@table_space}UPM_MENU#{@sid_suffix}"
      @table_menu2        = "#{@table_space}UUM_OBJ_PERM#{@sid_suffix}"

      @table_post_permission  = "#{@table_space}POST_PERMISSION#{@sid_suffix}"
      @table_user_permission  = "#{@table_space}UEM_PERMISSION#{@sid_suffix}"
      @table_org              = "#{@table_space}UEM_ORG_INFO#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas

      import_orgs
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_data2_permissions
      import_data2_role_permissions
      import_data2_account_permissions

      import_ledgers(JzYunying::Account)
    end

    def destroy_exist_datas
      JzYunying::Department.where(quarter_id: @quarter_id).delete_all
      accounts = JzYunying::Account.where(quarter_id: @quarter_id)
      JzYunying::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      JzYunying::Role.where(quarter_id: @quarter_id).delete_all
      JzYunying::Data1Permission.where(quarter_id: @quarter_id).delete_all
      JzYunying::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
      JzYunying::Data2Permission.where(quarter_id: @quarter_id).delete_all
      JzYunying::Data2AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_orgs_sql
      "select org_code, org_code, org_name, par_org from #{@table_org} where org_status = 0"
    end

    def import_orgs
      return unless exist_table?(@table_org)

      department_datas = select_db_datas(import_orgs_sql)
      # 创建department(忽略关联关系)
      department_datas.each do |r|
        JzYunying::Department.create(
          quarter_id:        @quarter_id,
          source_id:         r[0],
          code:              r[1],
          name:              r[2],
          status:            true
        )
      end
      # 建联关联关系
      JzYunying::Department.where(quarter_id: @quarter_id).each do |department|
        department_data = department_datas.find { |row| row[0]&.to_s == department.source_id }
        next if department_data.nil? || department_data&.[](3).nil?

        parent_department = JzYunying::Department.find_by(quarter_id: @quarter_id, source_id: department_data[3])
        next if parent_department.nil?

        department.parent = parent_department
        department.save
      end

      # 设置full_name
      JzYunying::Department.where(quarter_id: @quarter_id).each do |department|
        names = department.ancestors.pluck(:name)
        names << department.name
        name = names.join(' / ')
        department.update_column(:full_name, name)
      end

    end

    def import_accounts_sql
      <<-EOF
        select u.USER_CODE, u.USER_CODE, u.USER_NAME, o.OP_STATUS, o.ORG_CODE from #{@table_user} u, #{@table_account} o where u.USER_CODE = o.OP_CODE
      EOF
    end

    def import_accounts
      @departments = JzYunying::Department.where(quarter_id: @quarter_id)
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          department = @departments.find { |x| x.source_id == r[4]&.to_s }
          account = JzYunying::Account.create(
            quarter_id:    @quarter_id,
            source_id:     r[0],
            code:          r[1],
            name:          r[2],
            status:        r[3] == '0',
            department_id: department&.id
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'JzYunying::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select POST_ID, POST_ID, POST_NAME from #{@table_role}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << JzYunying::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select POST_ID, USER_CODE from #{@table_account_role}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          JzYunying::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select MENU_ID, MENU_ID, MENU_NAME, PAR_MENU from #{@table_menu} where MENU_STA=1
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(full_name(name, import_data1_permissions_sql, r[3]))
          # 菜单编码是BigDecimal类型，转为字符串会以.0结尾，所以需要修改
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        decimal_to_str(r[1]),
            level1_name: level1_name
          }
          @data1_permissions << JzYunying::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select OPP_OBJ_CODE, MENU_ID, OPP_AUTH_TYPE from #{@table_menu2} where OPP_OBJ_TYPE=2
      EOF
    end

    def import_data1_role_permissions
      enums = [
        {
          'name' => 'additional_permission',
          'value' => '1',
          'enum_value' => '执行权限',
          'label' => '附加权限'
        },
        {
          'name' => 'additional_permission',
          'value' => '2',
          'enum_value' => '授权权限',
          'label' => '附加权限'
        }
      ]
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          JzYunying::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data1_permission_id:   permission.id,
            additional_permission: get_enum(enums, 'additional_permission', r[2])
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select OPP_OBJ_CODE, MENU_ID, OPP_AUTH_TYPE from #{@table_menu2} where OPP_OBJ_TYPE=1
      EOF
    end

    def import_data1_account_permissions
      enums = [
        {
          'name' => 'additional_permission',
          'value' => '1',
          'enum_value' => '执行权限',
          'label' => '附加权限'
        },
        {
          'name' => 'additional_permission',
          'value' => '2',
          'enum_value' => '授权权限',
          'label' => '附加权限'
        }
      ]
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        JzYunying::Data1AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data1_permission_id:   permission.id,
          additional_permission: get_enum(enums, 'additional_permission', r[2])
        )
      end
    end

    def import_data2_permissions_sql
      <<~EOF
        SELECT
        distinct PERMISSION_CODE,
        PERMISSION_CODE,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN (SELECT ORG_NAME FROM #{@table_space}UEM_ORG_INFO WHERE ORG_TYPE = '0' AND ORG_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '2' THEN (SELECT SYS_NAME FROM #{@table_space}UPM_SYSTEM WHERE SYS_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '3' THEN (SELECT PAR_NAME FROM #{@table_space}UPM_SYSPARAM WHERE PAR_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '4' THEN (SELECT DICT_NAME FROM #{@table_space}UPM_DICTIONARY WHERE DICT_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '5' THEN (SELECT POST_NAME FROM #{@table_space}UEM_POST WHERE POST_ID = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '6' THEN (SELECT DICT_ITEM_NAME FROM #{@table_space}UPM_DICT_ITEMS WHERE DICT_CODE = 'CUST_AGMT_TYPE' AND DICT_ITEM = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '7' THEN (SELECT BUSI_NAME FROM #{@table_space}OPP_BUSI_DEF WHERE BUSI_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '8' THEN (SELECT PARAM_NAME FROM #{@table_space}OPP_BUSI_COMM_PARAM WHERE PARAM_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '9' THEN (SELECT TEAM_NAME FROM #{@table_space}UEM_TEAM_DEF WHERE TEAM_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = 'A' THEN (SELECT MENU_NAME FROM #{@table_space}UPM_MENU WHERE MENU_ID = T2.PERMISSION_CODE)
        ELSE ''
        END) AS PER_CODE_NAME
        FROM #{@table_post_permission} T2
        
        UNION
        
        SELECT 
        distinct PERMISSION_CODE,
        PERMISSION_CODE,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN (SELECT ORG_NAME FROM #{@table_space}UEM_ORG_INFO WHERE ORG_TYPE = '0' AND ORG_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '2' THEN (SELECT SYS_NAME FROM #{@table_space}UPM_SYSTEM WHERE SYS_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '3' THEN (SELECT PAR_NAME FROM #{@table_space}UPM_SYSPARAM WHERE PAR_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '4' THEN (SELECT DICT_NAME FROM #{@table_space}UPM_DICTIONARY WHERE DICT_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '5' THEN (SELECT POST_NAME FROM #{@table_space}UEM_POST WHERE POST_ID = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '6' THEN (SELECT DICT_ITEM_NAME FROM #{@table_space}UPM_DICT_ITEMS WHERE DICT_CODE = 'CUST_AGMT_TYPE' AND DICT_ITEM = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '7' THEN (SELECT BUSI_NAME FROM #{@table_space}OPP_BUSI_DEF WHERE BUSI_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '8' THEN (SELECT PARAM_NAME FROM #{@table_space}OPP_BUSI_COMM_PARAM WHERE PARAM_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = '9' THEN (SELECT TEAM_NAME FROM #{@table_space}UEM_TEAM_DEF WHERE TEAM_CODE = T2.PERMISSION_CODE)
        WHEN PERMISSION_CLS = 'A' THEN (SELECT MENU_NAME FROM #{@table_space}UPM_MENU WHERE MENU_ID = T2.PERMISSION_CODE)
        ELSE ''
        END) AS PER_CODE_NAME
        FROM #{@table_user_permission} T2
      EOF
    end

    def import_data2_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = r[2]
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data2_permissions << JzYunying::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<~EOF
        SELECT
        POST_ID,
        PERMISSION_CODE,
        (CASE
        WHEN PERMISSION_TYPE = '0' THEN '受理权限'
        WHEN PERMISSION_TYPE = '1' THEN '审核权限'
        WHEN PERMISSION_TYPE = '2' THEN '查询权限'
        WHEN PERMISSION_TYPE = '3' THEN '管理权限'
        WHEN PERMISSION_TYPE = '4' THEN '质检权限'
        END) AS PER_TYPE,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN '机构'
        WHEN PERMISSION_CLS = '1' THEN '客户账号'
        WHEN PERMISSION_CLS = '2' THEN '子系统'
        WHEN PERMISSION_CLS = '3' THEN '公共参数'
        WHEN PERMISSION_CLS = '4' THEN '数据字典'
        WHEN PERMISSION_CLS = '5' THEN '岗位'
        WHEN PERMISSION_CLS = '6' THEN '强开协议'
        WHEN PERMISSION_CLS = '7' THEN '影像下载'
        WHEN PERMISSION_CLS = '8' THEN '业务公参'
        WHEN PERMISSION_CLS = '9' THEN '机构群组'
        WHEN PERMISSION_CLS = 'A' THEN '操作权限'
        END) AS PER_CLS,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN '机构代码'
        WHEN PERMISSION_CLS = '2' THEN '子系统编号'
        WHEN PERMISSION_CLS = '3' THEN '公共参数代码'
        WHEN PERMISSION_CLS = '4' THEN '数据字典代码'
        WHEN PERMISSION_CLS = '5' THEN '岗位编号'
        WHEN PERMISSION_CLS = '6' THEN '协议编号'
        WHEN PERMISSION_CLS = '7' THEN '业务代码'
        WHEN PERMISSION_CLS = '8' THEN '参数代码'
        WHEN PERMISSION_CLS = '9' THEN '机构群组编号'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '1' THEN '客户代码'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '2' THEN '资金账号'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '3' THEN '证件号码'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '6' THEN '客户全称'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '1' THEN '新增'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '2' THEN '修改'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '3' THEN '删除'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '4' THEN '导入'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '5' THEN '导出'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '6' THEN '打印'
        ELSE ''
        END) AS PER_CLS_TYPE
        FROM #{@table_post_permission} T2
      EOF
    end

    def import_data2_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          permission_scope = [r[4], r[3]].select(&:present?).join('-')
          JzYunying::Data2AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            role_id:               role.id,
            data2_permission_id:   permission.id,
            additional_permission: r[2],
            permission_scope:      permission_scope
          )
        end
      end
    end

    def import_data2_account_permissions_sql
      <<~EOF
        SELECT
        USER_CODE AS OP_CODE,
        PERMISSION_CODE AS PER_CODE,
        (CASE
        WHEN PERMISSION_TYPE = '0' THEN '受理权限'
        WHEN PERMISSION_TYPE = '1' THEN '审核权限'
        WHEN PERMISSION_TYPE = '2' THEN '查询权限'
        WHEN PERMISSION_TYPE = '3' THEN '管理权限'
        WHEN PERMISSION_TYPE = '4' THEN '质检权限'
        END) AS PER_TYPE,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN '机构'
        WHEN PERMISSION_CLS = '1' THEN '客户账号'
        WHEN PERMISSION_CLS = '2' THEN '子系统'
        WHEN PERMISSION_CLS = '3' THEN '公共参数'
        WHEN PERMISSION_CLS = '4' THEN '数据字典'
        WHEN PERMISSION_CLS = '5' THEN '岗位'
        WHEN PERMISSION_CLS = '6' THEN '强开协议'
        WHEN PERMISSION_CLS = '7' THEN '影像下载'
        WHEN PERMISSION_CLS = '8' THEN '业务公参'
        WHEN PERMISSION_CLS = '9' THEN '机构群组'
        WHEN PERMISSION_CLS = 'A' THEN '操作权限'
        END) AS PER_CLS,
        (CASE
        WHEN PERMISSION_CLS = '0' THEN '机构代码'
        WHEN PERMISSION_CLS = '2' THEN '子系统编号'
        WHEN PERMISSION_CLS = '3' THEN '公共参数代码'
        WHEN PERMISSION_CLS = '4' THEN '数据字典代码'
        WHEN PERMISSION_CLS = '5' THEN '岗位编号'
        WHEN PERMISSION_CLS = '6' THEN '协议编号'
        WHEN PERMISSION_CLS = '7' THEN '业务代码'
        WHEN PERMISSION_CLS = '8' THEN '参数代码'
        WHEN PERMISSION_CLS = '9' THEN '机构群组编号'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '1' THEN '客户代码'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '2' THEN '资金账号'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '3' THEN '证件号码'
        WHEN PERMISSION_CLS = '1' AND PERMISSION_CLS_TYPE = '6' THEN '客户全称'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '1' THEN '新增'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '2' THEN '修改'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '3' THEN '删除'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '4' THEN '导入'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '5' THEN '导出'
        WHEN PERMISSION_CLS = 'A' AND PERMISSION_CLS_TYPE = '6' THEN '打印'
        ELSE ''
        END) AS PER_CLS_TYPE
        FROM #{@table_user_permission} T2
      EOF
    end

    def import_data2_account_permissions
      select_db_datas(import_data2_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        permission_scope = [r[4], r[3]].select(&:present?).join('-')
        JzYunying::Data2AccountsRolesPermission.create(
          quarter_id:            @quarter_id,
          account_id:            account.id,
          data2_permission_id:   permission.id,
          additional_permission: r[2],
          permission_scope:      permission_scope
        )
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.class == Hash ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 返回包含祖先菜单名称的名称
    def full_name(name, sql, parent_id = nil)
      parent_id = decimal_to_str(parent_id)
      return name if parent_id.blank? || parent_id.to_s == '0'

      new_sql = sql + " and MENU_ID='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[2]
        root_parent_id = r[3]
        name = "#{parent_name} -> #{name}" if parent_name.present?
        name = full_name(name, sql, root_parent_id)
      end
      name
    end

    # decimal转换为字符串
    def decimal_to_str(code)
      return code unless code.is_a?(BigDecimal)

      code.to_s.gsub(/\.0$/, '')
    end
  end
end
