module AasOracleImporter

  class GuohuSaOrderImporter < ImporterBase

    def config
      @bs_id                = importer_config['bs_id']
      @table_space          = importer_config['table_space'].to_s
      @sid_suffix           = importer_config['sid_suffix'].to_s
      @table_accounts       = @table_space+'hsi_user'+@sid_suffix
      @table_roles          = @table_space+'hsi_group'+@sid_suffix
      @table_accounts_roles = @table_space+'hsi_usergroup'+@sid_suffix
      @table_menus          = @table_space+'hsi_menu'+@sid_suffix
      @table_menus_roles    = @table_space+'hsi_groupright'+@sid_suffix
      @table_other_permission = @table_space+'hsi_right'+@sid_suffix
      @table_sub_system     = @table_space+'hsi_system'+@sid_suffix

    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_other_permissions
      import_accounts_roles

      import_menus_group(GuohuSaOrder::Role, GuohuSaOrder::Menu)
      import_menus_group(GuohuSaOrder::Role, GuohuSaOrder::OtherPermission)
      import_menus_group(GuohuSaOrder::Account, GuohuSaOrder::Menu)
      import_menus_group(GuohuSaOrder::Account, GuohuSaOrder::OtherPermission)

      import_system_user_permissions

      import_ledgers(GuohuSaOrder::Account)
    end

    def import_accounts

      GuohuSaOrder::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_usercode, c_username, c_status from #{@table_accounts}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            GuohuSaOrder::Account.create(
              quarter_id: @quarter_id,
              code: r[0],
              name: r[1],
              status: r[2].to_i == 0
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      GuohuSaOrder::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select l_groupid, c_groupname from #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaOrder::Role.create( quarter_id: @quarter_id, code: r[0], name: r[1] )
        end
      end
    end

    def import_menus

      GuohuSaOrder::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_menucode, c_menuname from #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaOrder::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_other_permissions

      GuohuSaOrder::OtherPermission.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        select c_rightcode, c_rightname from #{@table_other_permission}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          GuohuSaOrder::OtherPermission.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
          )
        end
      end
    end


    def import_menus_group(group, menu)
      group_class   = GuohuSaOrder::Role
      account_class = GuohuSaOrder::Account
      menu_class    = GuohuSaOrder::Menu
      otherp_class  = GuohuSaOrder::OtherPermission

      join_class =
        if    group == group_class   && menu == menu_class
          GuohuSaOrderMenusRoles
        elsif group == account_class && menu == menu_class
          GuohuSaOrderAccountsMenus
        elsif group == group_class   && menu == otherp_class
          GuohuSaOrderOtherPermissionsRoles
        elsif group == account_class && menu == otherp_class
          GuohuSaOrderAccountsOtherPermissions
        end

      case group.to_s
      when group_class.to_s
        group_name  = "g.c_groupname,"
        group_from  = "#{@table_roles} g,"
        group_flag  = 0
        group_id    = "g.l_groupid "
        send_method = :find_by_name
        role_join_id = :role_id
      when account_class.to_s
        group_name  = "u.c_usercode,"
        group_from  = "#{@table_accounts} u,"
        group_flag  = 1
        group_id    = "u.l_userid"
        send_method = :find_by_code
        role_join_id = :account_id
      end

      case menu.to_s
      when menu_class.to_s
        menu_name = "m.c_menuname"
        menu_code = "m.c_menucode"
        menu_from = "#{@table_menus} m"
        menu_flag = 1
        menu_join_id = :menu_id
      when otherp_class.to_s
        menu_name = "m.c_rightname"
        menu_code = "m.c_rightcode"
        menu_from = "#{@table_other_permission} m"
        menu_flag = 0
        menu_join_id = :other_permission_id
      end

      sql = <<-EOF
        select
          #{group_name}
          gm.c_rightcode,
          #{menu_name}
        from
          #{@table_menus_roles} gm,
          #{group_from}
          #{menu_from}
        where
          gm.c_flag           = #{group_flag}
          and gm.c_rightclass = #{menu_flag}
          and gm.l_groupid    = #{group_id}
          and gm.c_rightcode  = #{menu_code}
         order by
          gm.c_rightcode
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          group_record = group.where(quarter_id: @quarter_id).send(send_method, r[0])
          menu_record  =  menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu_record and group_record
            join_class.create(menu_join_id => menu_record.id, role_join_id => group_record.id)
          else
            @logger.warn "#{self.class}: not found #{group} #{r[0]}" unless group_record
            @logger.warn "#{self.class}: not found #{menu} #{r[1]}" unless menu_record
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        select
         g.c_groupname,
         u.c_usercode
        from #{@table_accounts_roles} ug,
             #{@table_accounts} u,
             #{@table_roles} g
        where ug.l_userid = u.l_userid
          and ug.l_groupid = g.l_groupid
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role    =    GuohuSaOrder::Role.where(quarter_id: @quarter_id).find_by_name(r[0])
          account = GuohuSaOrder::Account.where(quarter_id: @quarter_id).find_by_code(r[1])

          if account and role
            GuohuSaOrderAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_system_user_permissions
      system_account = GuohuSaOrder::Account.find_by(quarter_id: @quarter_id, code: 'system')
      return unless system_account

      ActiveRecord::Base.transaction do
        GuohuSaOrder::Menu.where(quarter_id: @quarter_id).each do |menu|
          GuohuSaOrderAccountsMenus.create(
            account_id: system_account.id,
            menu_id: menu.id
          )
        end
      end

      ActiveRecord::Base.transaction do
        GuohuSaOrder::OtherPermission.where(quarter_id: @quarter_id).each do |perm|
          GuohuSaOrderAccountsOtherPermissions.create(
            account_id: system_account.id,
            other_permission_id: perm.id
          )
        end
      end
    end

  end
end



