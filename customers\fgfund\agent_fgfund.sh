#!/bin/bash
source /etc/profile

IMPORTER_PATH=$(cd "$(dirname "$0")/../" || exit; pwd)
BASE_PATH=$(cd "$IMPORTER_PATH/../" || exit; pwd)
LOGFILE=$BASE_PATH/aas/log/agent-$(date +%Y-%m-%d-%H-%M-%S).log

cd $IMPORTER_PATH
rake fgfund_async >> "$LOGFILE" 2>&1
if test $? -ne 0 ; then
  echo "datacenter sync run failed"
  exit 1
fi

ruby "$IMPORTER_PATH/bin/agent" >> "$LOGFILE" 2>&1

test "$(uname)" == 'Darwin' && exit
test "$RAILS_ENV" != 'production' && exit

aas_owner=$(ls -ldG "$BASE_PATH/aas" | awk '{print $3}')

# docker 中不能添加同名组，因为没有
chown -R "$aas_owner" "$BASE_PATH/data"
chown -R "$aas_owner" "$BASE_PATH/aas/tmp"
