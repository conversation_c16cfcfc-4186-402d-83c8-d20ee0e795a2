---
production:
  customer_id: htbr
  customer: 华泰柏瑞
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  workday:
    enable: false
    mode: trading_day
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_htbr_production
      username: root
      password: "<%= ENV['AAS_DATABASE_PASSWORD'] %>"
      host: localhost
  agent:
    ldap:
      dc:
        host: **********
        port: 389
        base: dc=aig-huatai, dc=internal
        user: fuser
        password: '00000000'
    http:
      bastion:
        base_uri: https://*************
      bastion2:
        base_uri: https://************
      bastion3:
        base_uri: https://************
      vpn:
        base_uri: http://************:1000/cgi-bin/php-cgi/html/delegatemodule/WebApi.php?
      vpn2:
        base_uri: http://************:1000/cgi-bin/php-cgi/html/delegatemodule/WebApi.php?
    oracle:
      o32db:
        db_host: ***********
        db_name: quantdb
        db_user: fmuser
        db_pass: fmuser001
      o322db:
        db_host: ***********
        db_name: quantdb
        db_user: emuser
        db_pass: emuser001
      jydb:
        db_host: ***********
        db_name: quantdb
        db_user: jydb2user
        db_pass: Jydb1199
      oadb:
        db_host: ***********
        db_name: quantdb
        db_user: fanwei
        db_pass: fanwei1199
  importers:
  - name: fake_user
    db_type: ldap
    tnsname: dc
    expect_status: success
  - name: haikang_kaoqin
    bs_id: 341
    db_type: http
    tnsname: bastion
    app_key: ********
    secret_key: QoOYzPFXfHcXYJOXvai3
    base_uri: https://************
    account_uri: '/api/resource/v2/person/personList'
  - name: fanwei_oa
    db_type: oracle
    tnsname: oadb
    table_space: ''
    sid_suffix: ''
  - name: jiaoyi
    bs_id: 31
    db_type: oracle
    tnsname: o32db
    table_space: fmuser.
    sid_suffix: ''
    time_control: false
    temporary: true
    days_of_data: 999
    operator_no_importer: true
    trade_type_importer: true
    station_importer: true
    c_menu_type:
    - 1
  - name: jiaoyi2
    bs_id: 5031
    db_type: oracle
    tnsname: o322db
    table_space: emuser.
    sid_suffix: ''
    time_control: false
    temporary: false
    days_of_data: 999
    operator_no_importer: true
    trade_type_importer: true
    station_importer: true
    c_menu_type:
    - 1
  - name: htbr_bastion
    bs_id: 312
    db_type: http
    tnsname: bastion
    access_key: hnxnluis5zdmuuu03m0ik24u6veomp3v
    secret_key: 9563mrincewr7xy5ofd3n170lxnqtn7v
    base_uri: https://*************
  - name: htbr_bastion2
    bs_id: 5312
    db_type: http
    tnsname: bastion2
    access_key: w3l8aatoyrhf7w8u1dox1me8tc4prxwr
    secret_key: 591ws9a3psiettf11fkma31q5tagpxi7
    base_uri: https://************
  - name: htbr_bastion3
    bs_id: 5314
    db_type: http
    tnsname: bastion3
    access_key: 9raaxmnamv3th0obvqtfl8stjmknr90z
    secret_key: 8ngftqv3lkbsrch5ooz84jrh5ckj5uz7
    base_uri: https://************
  - name: htbr_vpn
    bs_id: 313
    db_type: http
    tnsname: vpn
    access_key: 8567193c841a006973e4241d50eff84c
    base_uri: http://************:1000/cgi-bin/php-cgi/html/delegatemodule/WebApi.php?
  - name: htbr_vpn2
    bs_id: 5313
    db_type: http
    tnsname: vpn2
    access_key: 409a225339be92358ff6c9503e6eda9f
    base_uri: http://************:1000/cgi-bin/php-cgi/html/delegatemodule/WebApi.php?
  - name: ldap_system
    bs_id: 322
    db_type: ldap
    tnsname: dc
    regular_field: CN
    telephonenumber: true
    update_user_dept: true
    account_filter: "(&(objectcategory=person)(objectClass=user)(!(objectclass=computer)))"
    account_attrs:
    - dn
    - name
    - cn
    - samaccountname
    - department
    - title
    - useraccountcontrol
    - memberof
    - objectSid
    - primaryGroupID
    - description
    - mobile
    - telephonenumber
    group_filter: "(&(objectclass=group)(objectcategory=CN=Group,CN=Schema,CN=Configuration,DC=aig-huatai,DC=internal)(samaccounttype=*********)(grouptype=-**********))"
    group_attrs:
    - dn
    - member
    - name
    - samaccountname
    top_ou:
    -
    - Internship
    ignore_accountname: []
    ignore_ou: []
    ignore_ou_but_import_users: []
    user_ou_sequence:
      default: 1
      用户: 2
      离职人员: 1
  - name: juyuan_yanbao
    db_type: oracle
    tnsname: jydb