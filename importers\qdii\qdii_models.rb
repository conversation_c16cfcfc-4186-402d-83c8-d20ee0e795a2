module Qdii
  def self.table_name_prefix
    'qdii_'
  end
end

class Qdii::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_many :fund_permissions
  has_many :fund_unit_permissions
  has_many :fund_combination_permissions
  has_many :funds,             through: :fund_permissions
  has_many :fund_units,        through: :fund_unit_permissions
  has_many :fund_combinations, through: :fund_combination_permissions

  has_many :menu_permissions
  has_many :menu_addition_permissons
  has_many :menus,          through: :menu_permissions
  has_many :menu_additions, through: :menu_addition_permissons
end

class Qdii::Fund < ActiveRecord::Base
  has_many :fund_permissions
  has_many :accounts, through: :fund_permissions
end

class Qdii::FundUnit < ActiveRecord::Base
  has_many :fund_unit_permissions
  has_many :accounts, through: :fund_unit_permissions
end

class Qdii::FundCombination < ActiveRecord::Base
  has_many :fund_combination_permissions
  has_many :accounts, through: :fund_combination_permissions
end

class Qdii::FundPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund
end

class Qdii::FundUnitPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_unit
end

class Qdii::FundCombinationPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :fund_combination
end

class Qdii::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
  has_and_belongs_to_many :menu_additions
end

class Qdii::System < ActiveRecord::Base
  has_many :menus
end

class Qdii::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class Qdii::MenuPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu
end

class Qdii::MenuAddition < ActiveRecord::Base
  has_and_belongs_to_many :roles
  belongs_to :menu
  belongs_to :quarter
end

class Qdii::MenuAdditionPermission < ActiveRecord::Base
  belongs_to :account
  belongs_to :menu_addition
end

class Qdii::Temporary < ActiveRecord::Base; end

class QdiiAccountsRoles < ActiveRecord::Base; end
class QdiiMenusRoles < ActiveRecord::Base; end
class QdiiMenuAdditionsRoles < ActiveRecord::Base; end

class Qdii::TimeControl < ActiveRecord::Base; end

class Qdii::TopTradeType < ActiveRecord::Base; end
class Qdii::TopStation < ActiveRecord::Base; end
class Qdii::TradeType < ActiveRecord::Base; end