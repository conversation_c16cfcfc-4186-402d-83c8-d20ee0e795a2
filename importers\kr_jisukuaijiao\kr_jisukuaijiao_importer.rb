module AasOracleImporter
  class KrJisukuaijiaoImporter < ImporterBase
    def config
      @bs_id       = 262
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      @deny_permission_ids = []

      initialize_tables
    end

    def initialize_tables
      @table_account = "#{@table_space}sys_user#{@sid_suffix}"
      @table_role    = "#{@table_space}sys_user_role#{@sid_suffix}"
      @table_menu    = "#{@table_space}sys_menu#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions

      import_ledgers(KrJisukuaijiao::Account)
    end

    def destroy_exist_datas
      accounts = KrJisukuaijiao::Account.where(quarter_id: @quarter_id)
      KrJisukuaijiao::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      KrJisukuaijiao::Role.where(quarter_id: @quarter_id).delete_all
      KrJisukuaijiao::Data1Permission.where(quarter_id: @quarter_id).delete_all
      KrJisukuaijiao::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select id, account, name, del_flag from #{@table_account}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          account = KrJisukuaijiao::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'KrJisukuaijiao::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id, name from #{@table_role} where del_flag = 0
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << KrJisukuaijiao::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select role, id from #{@table_account}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          # role数据是"3,4,5"这样的数据
          role_ids = r[0].to_s.split(',').map(&:strip)
          role_ids.each do |role_id|
            role    = @roles.find { |x| x.source_id.to_s == role_id.to_s }
            account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
            next unless account && role

            KrJisukuaijiao::AccountsRole.create(account_id: account.id, role_id: role.id)
          end
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, permission, name, module_id from #{@table_menu} where del_flag=0
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = 3
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << KrJisukuaijiao::Data1Permission.create(json)
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select id, allow_permission, deny_permission from #{@table_account}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account              = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        @deny_permission_ids = r[2].to_s.split(',').map(&:strip)
        permission_ids       = r[1].to_s.split(',').map(&:strip)
        permission_ids.each do |permission_id|
          # 禁止权限忽略掉
          next if @deny_permission_ids.include?(permission_id.to_s)

          permission = @data1_permissions.find { |x| x.code.to_s == menu_code(permission_id.to_s) }
          next unless account && permission

          KrJisukuaijiao::Data1AccountsRolesPermission.create(
            quarter_id:            @quarter_id,
            account_id:            account.id,
            data1_permission_id:   permission.id,
            additional_permission: additional_permission_text(permission_id)
          )
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select id, permission from #{@table_role}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role           = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission_ids = r[1].to_s.split(',').map(&:strip)
          permission_ids.each do |permission_id|
            # 禁止权限忽略掉
            next if @deny_permission_ids.include?(permission_id.to_s)

            permission = @data1_permissions.find { |x| x.code.to_s == menu_code(permission_id.to_s) }
            next unless permission && role

            KrJisukuaijiao::Data1AccountsRolesPermission.create(
              quarter_id:            @quarter_id,
              role_id:               role.id,
              data1_permission_id:   permission.id,
              additional_permission: additional_permission_text(permission_id)
            )
          end
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    # 附加权限
    def additional_permission_text(code)
      operation = operations.find { |obj| obj[:code] == code }
      return nil if operation.nil?

      operation[:name]
    end

    # sys_user 和 sys_user_role中的permission对应Operation枚举类，通过Operation枚举类对应Functional枚举类
    def menu_code(code)
      operation = operations.find { |obj| obj[:code] == code }
      return nil if operation.nil?

      operation[:menu_code]
    end

    # sys_user 和 sys_user_role中的permission 和
    def operations
      [
        { code: 'ANC_SYSTEM_MONITOR', name: '系统监控-锚标记', menu_code: 'ANC_SYSTEM_MONITOR' },
        { code: 'OES_MTR_VIEW', name: '查看交易主机监控', menu_code: 'OES_MTR' },
        { code: 'MDS_MTR_VIEW', name: '查看行情主机监控', menu_code: 'MDS_MTR' },
        { code: 'WARN_LOG_VIEW', name: '预警日志', menu_code: 'WARN_LOG' },
        { code: 'LOCAL_MTR_VIEW', name: '本机监控', menu_code: 'LOCAL_MTR' },
        { code: 'RISK_STATE_MTR_VIEW', name: '风控状态监控', menu_code: 'RISK_STATE_MTR' },
        { code: 'RISK_DATA_MTR_VIEW', name: '风控数据监控', menu_code: 'RISK_DATA_MTR' },
        { code: 'RISK_DATA_MTR_EXPORT', name: '风控数据导出', menu_code: 'RISK_DATA_MTR' },
        { code: 'SYS_TASK_MTR_VIEW', name: '系统任务监控', menu_code: 'SYS_TASK_MTR' },
        { code: 'SWITCH_TRADE_DAY', name: '切换交易日', menu_code: 'SYS_TASK_MTR' },
        { code: 'RESET_INIT_DATA_EXEC', name: '重置并初始化数据', menu_code: 'SYS_TASK_MTR' },
        { code: 'RESET_CLIENT_CONTACT_SYNC', name: '重置客户端联系方式同步状态', menu_code: 'SYS_TASK_MTR' },
        { code: 'RESET_CUST_CONTACT_UPDATE', name: '重置客户联系方式更新状态', menu_code: 'SYS_TASK_MTR' },
        { code: 'MON_DATA_EXPORT', name: 'MON管理数据导出', menu_code: 'SYS_TASK_MTR' },
        { code: 'CONF_EXPORT_DONE', name: '配置文件导出', menu_code: 'CONF_EXPORT' },
        { code: 'ANC_TRADE_MONITOR', name: '交易监控-锚标记', menu_code: 'ANC_TRADE_MONITOR' },
        { code: 'TRD_OVERVIEW_VIEW', name: '盯市总览', menu_code: 'TRD_OVERVIEW' },
        { code: 'RISK_OVERVIEW_VIEW', name: '交易监控总览', menu_code: 'RISK_OVERVIEW' },
        { code: 'TRD_CTRL_VIEW', name: '查看客户交易控制', menu_code: 'TRD_CTRL' },
        { code: 'ALLOW_FORBID_TRD', name: '允许禁止交易', menu_code: 'TRD_CTRL' },
        { code: 'CUST_RISK_LEVEL_SET', name: '风险等级设置', menu_code: 'TRD_CTRL' },
        { code: 'INV_STATUS_SET', name: '切换股东账户状态', menu_code: 'TRD_CTRL' },
        { code: 'CASH_STATUS_SET', name: '切换资金账户状态', menu_code: 'TRD_CTRL' },
        { code: 'TRSF_STATUS_SET', name: '切换资金账户出入金状态', menu_code: 'TRD_CTRL' },
        { code: 'CUST_GUARD_STATUS_SET', name: '切换客户警戒状态', menu_code: 'TRD_CTRL' },
        { code: 'INV_RIGHT_VIEW', name: '查看股东权限', menu_code: 'INV_RIGHT' },
        { code: 'INV_RIGHT_SET', name: '股东权限设置', menu_code: 'INV_RIGHT' },
        { code: 'INV_LIMIT_VIEW', name: '查看股东限制', menu_code: 'INV_LIMIT' },
        { code: 'INV_LIMIT_SET', name: '股东限制设置', menu_code: 'INV_LIMIT' },
        { code: 'INV_BLOCK_VIEW', name: '持仓冻结/解冻', menu_code: 'INV_BLOCK' },
        { code: 'INV_BLOCK_SET', name: '持仓冻结/解冻', menu_code: 'INV_BLOCK' },
        { code: 'INV_SUB_QUOTA_VIEW', name: '申购额度调整', menu_code: 'INV_SUB_QUOTA' },
        { code: 'INV_SUB_QUOTA_SET', name: '申购额度调整', menu_code: 'INV_SUB_QUOTA' },
        { code: 'CASH_REVE_VIEW', name: '红冲/蓝补', menu_code: 'CASH_REVE' },
        { code: 'CASH_REVE_SET', name: '红冲/蓝补', menu_code: 'CASH_REVE' },
        { code: 'CASH_BLOCK_VIEW', name: '资金冻结/解冻', menu_code: 'CASH_BLOCK' },
        { code: 'CASH_BLOCK_SET', name: '资金冻结/解冻', menu_code: 'CASH_BLOCK' },
        { code: 'CASH_LIMIT_VIEW', name: '查看资金限制', menu_code: 'CASH_LIMIT' },
        { code: 'CASH_LIMIT_SET', name: '资金限制设置', menu_code: 'CASH_LIMIT' },
        { code: 'CUST_RISK_VIEW', name: '客户交易监控', menu_code: 'CUST_RISK' },
        { code: 'CUST_RISK_HANDLE', name: '风控结果已处理', menu_code: 'CUST_RISK' },
        { code: 'CUST_RISK_CTRL_VIEW', name: '客户风控控制总览', menu_code: 'CUST_RISK_CTRL' },
        { code: 'CUST_RISK_CTRL_ENABLE_TRADE', name: '客户风控交易解禁', menu_code: 'CUST_RISK_CTRL' },
        { code: 'CUST_RISK_CTRL_REMOVE_LIMIT', name: '客户风控限制解禁', menu_code: 'CUST_RISK_CTRL' },
        { code: 'CLIENT_LOG_VIEW', name: '客户操作查询', menu_code: 'CLIENT_LOG' },
        { code: 'SINGLE_CUST_VIEW', name: '单客户查询', menu_code: 'SINGLE_CUST' },
        { code: 'SINGLE_CUST_EXPORT', name: '单客户查询数据导出', menu_code: 'SINGLE_CUST' },
        { code: 'ORDER_VIEW', name: '委托查询', menu_code: 'ORDER' },
        { code: 'ORDER_EXPORT', name: '委托信息导出', menu_code: 'ORDER' },
        { code: 'TRADE_VIEW', name: '成交查询', menu_code: 'TRADE' },
        { code: 'TRADE_EXPORT', name: '成交信息导出', menu_code: 'TRADE' },
        { code: 'TRANSFER_VIEW', name: '出入金查询', menu_code: 'TRANSFER' },
        { code: 'TRANSFER_EXPORT', name: '出入金信息导出', menu_code: 'TRANSFER' },
        { code: 'CASH_ASSET_VIEW', name: '资金资产查询', menu_code: 'CASH_ASSET' },
        { code: 'CASH_ASSET_EXPORT', name: '资金资产信息导出', menu_code: 'CASH_ASSET' },
        { code: 'HOLD_VIEW', name: '持仓查询', menu_code: 'HOLD' },
        { code: 'HOLD_EXPORT', name: '持仓信息导出', menu_code: 'HOLD' },
        { code: 'CLIENT_ADJUST_QUOTA_VIEW', name: '查看最大委托笔数', menu_code: 'CLIENT_ADJUST_QUOTA' },
        { code: 'CLIENT_ADJUST_QUOTA_SET', name: '盘中调整最大委托笔数', menu_code: 'CLIENT_ADJUST_QUOTA' },
        { code: 'ANC_TRADE_MONITOR_STK', name: '交易监控(现货)-锚标记', menu_code: 'ANC_TRADE_MONITOR_STK' },
        { code: 'STK_SECURITY_QRY_VIEW', name: '证券信息查询', menu_code: 'STK_SECURITY_QRY' },
        { code: 'STK_ISSUE_QRY_VIEW', name: '证券发行信息查询', menu_code: 'STK_ISSUE_QRY' },
        { code: 'STK_ETF_QRY_VIEW', name: 'ETF信息查询', menu_code: 'STK_ETF_QRY' },
        { code: 'STK_LOT_WINNING_VIEW', name: '新股中签/配号查询', menu_code: 'STK_LOT_WINNING' },
        { code: 'CUST_ADJUST_REDUCE_QUOTA_VIEW', name: '查看客户最大减持额度', menu_code: 'CUST_ADJUST_REDUCE_QUOTA' },
        { code: 'CUST_ADJUST_REDUCE_QUOTA_SET', name: '盘中调整客户最大减持额度', menu_code: 'CUST_ADJUST_REDUCE_QUOTA' },
        { code: 'ANC_TRADE_MONITOR_OPT', name: '交易监控(期权)-锚标记', menu_code: 'ANC_TRADE_MONITOR_OPT' },
        { code: 'OPT_SECURITY_QRY_VIEW', name: '合约信息查询', menu_code: 'OPT_SECURITY_QRY' },
        { code: 'OPT_CUST_CONTRACT_EXPIRES_VIEW', name: '临近到期合约查询', menu_code: 'OPT_CUST_CONTRACT_EXPIRES' },
        { code: 'OPT_EXERCISE_VIEW', name: '行权信息查询', menu_code: 'OPT_EXERCISE' },
        { code: 'OPT_UNDERLYING_HOLDING_VIEW', name: '标的证券持仓查询', menu_code: 'OPT_UNDERLYING_HOLDING' },
        { code: 'OPT_GAP_VIEW', name: '查看缺口统计查询', menu_code: 'OPT_GAP' },
        { code: 'OPT_REALTIME_BRO_MARGIN_VIEW', name: '查看券商保证金额度', menu_code: 'OPT_REALTIME_BRO_MARGIN' },
        { code: 'OPT_REALTIME_BRO_MARGIN_SET', name: '盘中临时调整券商保证金额度', menu_code: 'OPT_REALTIME_BRO_MARGIN' },
        { code: 'OPT_REALTIME_BRO_POSITION_LIMIT_VIEW', name: '盘中临时调整券商限仓额度', menu_code: 'OPT_REALTIME_BRO_POSITION_LIMIT' },
        { code: 'OPT_REALTIME_BRO_POSITION_LIMIT_SET', name: '盘中临时调整券商限仓额度', menu_code: 'OPT_REALTIME_BRO_POSITION_LIMIT' },
        { code: 'OPT_REALTIME_CUST_PURCHASE_LIMIT_VIEW', name: '查看客户限购额度', menu_code: 'OPT_REALTIME_CUST_PURCHASE_LIMIT' },
        { code: 'OPT_REALTIME_CUST_PURCHASE_LIMIT_SET', name: '盘中临时调整客户限购额度', menu_code: 'OPT_REALTIME_CUST_PURCHASE_LIMIT' },
        { code: 'OPT_REALTIME_CUST_POSITION_LIMIT_VIEW', name: '查看客户限仓额度', menu_code: 'OPT_REALTIME_CUST_POSITION_LIMIT' },
        { code: 'OPT_REALTIME_CUST_POSITION_LIMIT_SET', name: '盘中临时调整客户限仓额度', menu_code: 'OPT_REALTIME_CUST_POSITION_LIMIT' },
        { code: 'OPT_BROKER_EXCHANGE_MARGIN_VIEW', name: '券商交易所保证金查询', menu_code: 'OPT_BROKER_EXCHANGE_MARGIN' },
        { code: 'OPT_BROKER_EXCHANGE_MARGIN_QUERY', name: '发送券商交易所保证金查询指令', menu_code: 'OPT_BROKER_EXCHANGE_MARGIN' },
        { code: 'ANC_TRADE_MONITOR_CRD', name: '交易监控(信用)-锚标记', menu_code: 'ANC_TRADE_MONITOR_CRD' },
        { code: 'CRD_SECURITY_QRY_VIEW', name: '证券信息查询', menu_code: 'CRD_SECURITY_QRY' },
        { code: 'CRD_ISSUE_QRY_VIEW', name: '证券发行信息查询', menu_code: 'CRD_ISSUE_QRY' },
        { code: 'CRD_ETF_QRY_VIEW', name: 'ETF信息查询', menu_code: 'CRD_ETF_QRY' },
        { code: 'CRD_LOT_WINNING_VIEW', name: '新股中签/配号查询', menu_code: 'CRD_LOT_WINNING' },
        { code: 'CRD_COLLATERAL_INFO_VIEW', name: '担保品查询', menu_code: 'CRD_COLLATERAL_INFO' },
        { code: 'CRD_UNDERLYING_SECURITY_VIEW', name: '标的证券查询', menu_code: 'CRD_UNDERLYING_SECURITY' },
        { code: 'CRD_CUST_INTEREST_RATE_VIEW', name: '客户融资融券息费利率查询', menu_code: 'CRD_CUST_INTEREST_RATE' },
        { code: 'CRD_DEBT_VIEW', name: '合约信息查询', menu_code: 'CRD_DEBT' },
        { code: 'CRD_DEBT_EXPORT', name: '合约信息导出', menu_code: 'CRD_DEBT' },
        { code: 'CRD_DEBT_JOURNAL_VIEW', name: '合约流水信息查询', menu_code: 'CRD_DEBT_JOURNAL' },
        { code: 'CRD_DEBT_JOURNAL_EXPORT', name: '合约流水信息导出', menu_code: 'CRD_DEBT_JOURNAL' },
        { code: 'CRD_ASSET_VIEW', name: '资产信息查询', menu_code: 'CRD_ASSET' },
        { code: 'CRD_ASSET_EXPORT', name: '资产信息导出', menu_code: 'CRD_ASSET' },
        { code: 'CRD_CASH_REPAY_VIEW', name: '直接还款信息查询', menu_code: 'CRD_CASH_REPAY' },
        { code: 'CRD_CASH_REPAY_EXPORT', name: '直接还款信息导出', menu_code: 'CRD_CASH_REPAY' },
        { code: 'CRD_CUST_SECURITY_DEBT_STATS_VIEW', name: '客户单证券负债统计查询', menu_code: 'CRD_CUST_SECURITY_DEBT_STATS' },
        { code: 'CRD_CUST_SECURITY_DEBT_STATS_EXPORT', name: '客户单证券负债统计信息导出', menu_code: 'CRD_CUST_SECURITY_DEBT_STATS' },
        { code: 'CRD_BROKER_SECURITY_DEBT_STATS_VIEW', name: '券商单证券负债统计查询', menu_code: 'CRD_BROKER_SECURITY_DEBT_STATS' },
        { code: 'CRD_BROKER_SECURITY_DEBT_STATS_EXPORT', name: '券商单证券负债统计信息导出', menu_code: 'CRD_BROKER_SECURITY_DEBT_STATS' },
        { code: 'CRD_REALTIME_BROKER_CONF_LIMIT_SET', name: '盘中临时调整券商业务参数', menu_code: 'CRD_REALTIME_BROKER_CONF_LIMIT' },
        { code: 'CRD_REALTIME_CUST_QUOTA_LIMIT_SET', name: '客户两融额度调整', menu_code: 'CRD_REALTIME_CUST_QUOTA_LIMIT' },
        { code: 'CRD_POSTPONE_SET', name: '合约展期', menu_code: 'CRD_POSTPONE' },
        { code: 'CRD_REPAY_STOCK_BY_CASH_SET', name: '现金了结融券负债', menu_code: 'CRD_REPAY_STOCK_BY_CASH' },
        { code: 'CRD_REPAY_MARGIN_BY_OUTSIDE', name: '场外了结融资负债', menu_code: 'CRD_REPAY_OUTSIDE' },
        { code: 'CRD_REPAY_STOCK_BY_OUTSIDE', name: '场外了结融券负债', menu_code: 'CRD_REPAY_OUTSIDE' },
        { code: 'CRD_OTHER_BACKED_ASSET_SET', name: '调整其它担保资产', menu_code: 'CRD_ADJUST_OTHER_BACKED_ASSET' },
        { code: 'CRD_EXCESS_STOCK_TRANSFER_SET', name: '余券划转', menu_code: 'CRD_EXCESS_STOCK_TRANSFER' },
        { code: 'CRD_EXCESS_STOCK_TRANSFER_CANCEL', name: '余券划转撤单', menu_code: 'CRD_EXCESS_STOCK_TRANSFER' },
        { code: 'CRD_COMPACT_INFO_VIEW', name: '客户合同信息查看', menu_code: 'CRD_QRY_COMPACT_INFO' },
        { code: 'CRD_COMPACT_INFO_SET', name: '客户合同信息调整', menu_code: 'CRD_QRY_COMPACT_INFO' },
        { code: 'CRD_CUST_POSITION_VIEW', name: '客户头寸信息查询', menu_code: 'CRD_CUST_POSITION' },
        { code: 'CRD_CUST_POSITION_EXPORT', name: '客户头寸信息文件导出', menu_code: 'CRD_CUST_POSITION' },
        { code: 'CRD_CUST_CASH_POSITION_ADJUST', name: '盘中临时调整融资头寸', menu_code: 'CRD_CUST_POSITION' },
        { code: 'CRD_CUST_SECURITY_POSITION_ADJUST', name: '盘中临时调整融券头寸', menu_code: 'CRD_CUST_POSITION' },
        { code: 'CRD_CUST_CONTRACT_EXPIRES_VIEW', name: '客户合约到期监控查询', menu_code: 'CRD_CUST_CONTRACT_EXPIRES' },
        { code: 'CRD_CUST_UNDERLYING_INFO_VIEW', name: '客户担保品及标的信息查询', menu_code: 'CRD_CUST_UNDERLYING_INFO' },
        { code: 'CRD_CUST_COLLATERAL_INFO_SET', name: '调整客户担保证券信息', menu_code: 'CRD_CUST_UNDERLYING_INFO' },
        { code: 'CRD_CUST_UNDERLYING_INFO_SET', name: '调整客户融资融券标的信息', menu_code: 'CRD_CUST_UNDERLYING_INFO' },
        { code: 'CRD_CUST_MAINTENANCE_PARAMS_VIEW', name: '客户个人维保比例参数查询', menu_code: 'CRD_CUST_MAINTENANCE_PARAMS' },
        { code: 'CRD_CUST_MAINTENANCE_PARAMS_SET', name: '调整客户个人维保比例参数', menu_code: 'CRD_CUST_MAINTENANCE_PARAMS' },
        { code: 'CRD_BROKER_STATS_VIEW', name: '券商统计信息查询', menu_code: 'CRD_BROKER_STATS' },
        { code: 'CRD_CUST_NEW_SECURITY_POSITION_SET', name: '客户专项证券头寸新增', menu_code: 'CRD_CUST_NEW_SECURITY_POSITION' },
        { code: 'ANC_EMERGENCY_PROCESS', name: '应急处理-锚标记', menu_code: 'ANC_EMERGENCY_PROCESS' },
        { code: 'QUICK_CANCEL_VIEW', name: '查看快速撤销申报', menu_code: 'QUICK_CANCEL' },
        { code: 'QUICK_CANCEL_REQ', name: '快速撤销申报', menu_code: 'QUICK_CANCEL' },
        { code: 'PAUSE_TRD_VIEW', name: '查看暂停交易', menu_code: 'PAUSE_TRD' },
        { code: 'PAUSE_TRD_REQ', name: '暂停交易', menu_code: 'PAUSE_TRD' },
        { code: 'RESUME_TRD_REQ', name: '恢复交易', menu_code: 'PAUSE_TRD' },
        { code: 'TEMP_SUSP_VIEW', name: '查看盘中证券停复牌', menu_code: 'TEMP_SUSP' },
        { code: 'TEMP_SUSP_REQ', name: '盘中证券停牌', menu_code: 'TEMP_SUSP' },
        { code: 'RESUME_SUSP_REQ', name: '盘中证券复牌', menu_code: 'TEMP_SUSP' },
        { code: 'CUST_SECU_FORBID_VIEW', name: '查看客户证券买卖限制', menu_code: 'CUST_SECU_FORBID' },
        { code: 'CUST_SECU_FORBID_REQ', name: '客户证券买卖禁止', menu_code: 'CUST_SECU_FORBID' },
        { code: 'CUST_SECU_CANCEL_FORBID_REQ', name: '客户证券买卖恢复', menu_code: 'CUST_SECU_FORBID' },
        { code: 'ANC_EMERGENCY_PROCESS_STK', name: '应急处理(现货)-锚标记', menu_code: 'ANC_EMERGENCY_PROCESS_STK' },
        { code: 'ANC_EMERGENCY_PROCESS_OPT', name: '应急处理(期权)-锚标记', menu_code: 'ANC_EMERGENCY_PROCESS_OPT' },
        { code: 'ANC_EMERGENCY_PROCESS_CRD', name: '应急处理(信用)-锚标记', menu_code: 'ANC_EMERGENCY_PROCESS_CRD' },
        { code: 'ANC_BUSINESS_MANAGEMENT', name: '业务管理-锚标记', menu_code: 'ANC_BUSINESS_MANAGEMENT' },
        { code: 'CLIENT_VIEW', name: '查看客户信息', menu_code: 'CLIENT' },
        { code: 'CLIENT_ADD', name: '添加客户', menu_code: 'CLIENT' },
        { code: 'CLIENT_EDIT', name: '修改客户', menu_code: 'CLIENT' },
        { code: 'CLIENT_STATUS_EDIT', name: '修改客户状态', menu_code: 'CLIENT' },
        { code: 'CLIENT_API_STATUS_EDIT', name: '修改API登录状态', menu_code: 'CLIENT' },
        { code: 'CLIENT_CHANGE_PWD', name: '修改密码', menu_code: 'CLIENT' },
        { code: 'CLIENT_RESET_PWD', name: '重置密码', menu_code: 'CLIENT' },
        { code: 'CLIENT_RES_VIEW', name: '查看客户资源', menu_code: 'CLIENT_RES' },
        { code: 'CLIENT_RES_ADD', name: '添加客户资源', menu_code: 'CLIENT_RES' },
        { code: 'CLIENT_RES_EDIT', name: '修改客户资源', menu_code: 'CLIENT_RES' },
        { code: 'CLIENT_RES_DELETE', name: '删除客户资源', menu_code: 'CLIENT_RES' },
        { code: 'IP_WB_LIST_VIEW', name: '查看IP黑白名单', menu_code: 'IP_WB_LIST' },
        { code: 'IP_WB_LIST_ADD', name: '添加IP黑白名单', menu_code: 'IP_WB_LIST' },
        { code: 'IP_WB_LIST_EDIT', name: '修改IP黑白名单', menu_code: 'IP_WB_LIST' },
        { code: 'IP_WB_LIST_DELETE', name: '删除IP黑白名单', menu_code: 'IP_WB_LIST' },
        { code: 'IP_WB_LIST_UPLOAD', name: '导入IP黑白名单', menu_code: 'IP_WB_LIST' },
        { code: 'KEY_ACCT_VIEW', name: '查看重点账户', menu_code: 'KEY_ACCT' },
        { code: 'KEY_ACCT_UPLOAD', name: '导入重点账户', menu_code: 'KEY_ACCT' },
        { code: 'KEY_ACCT_DELETE', name: '删除重点账户', menu_code: 'KEY_ACCT' },
        { code: 'RISK_RULE_VIEW', name: '查看风控规则', menu_code: 'RISK_RULE' },
        { code: 'RISK_RULE_ADD', name: '添加风控规则', menu_code: 'RISK_RULE' },
        { code: 'RISK_RULE_EDIT', name: '修改风控规则', menu_code: 'RISK_RULE' },
        { code: 'RISK_RULE_DELETE', name: '删除风控规则', menu_code: 'RISK_RULE' },
        { code: 'NOTIFY_TEMPLATE_VIEW', name: '查看通知模版', menu_code: 'NOTIFY_TEMPLATE' },
        { code: 'NOTIFY_TEMPLATE_EDIT', name: '修改通知模版', menu_code: 'NOTIFY_TEMPLATE' },
        { code: 'NOTIFY_INFO_VIEW', name: '查看通知信息', menu_code: 'NOTIFY_INFO' },
        { code: 'NOTIFY_INFO_MANUAL_SEND', name: '手动发送通知', menu_code: 'NOTIFY_INFO' },
        { code: 'TRADING_CTRL_INFO_VIEW', name: '查看交易控制消息', menu_code: 'TRADING_CTRL_INFO' },
        { code: 'TRD_LIMIT_VIEW', name: '查看交易限制', menu_code: 'TRD_LIMIT' },
        { code: 'TRD_LIMIT_ADD', name: '添加交易限制', menu_code: 'TRD_LIMIT' },
        { code: 'TRD_LIMIT_EDIT', name: '修改交易限制', menu_code: 'TRD_LIMIT' },
        { code: 'TRD_LIMIT_DELETE', name: '删除交易限制', menu_code: 'TRD_LIMIT' },
        { code: 'TRD_LIMIT_UPLOAD', name: '导入交易限制', menu_code: 'TRD_LIMIT' },
        { code: 'COMMS_VIEW', name: '查看佣金模版', menu_code: 'COMMS' },
        { code: 'COMMS_ADD', name: '添加佣金模版', menu_code: 'COMMS' },
        { code: 'COMMS_EDIT', name: '修改佣金模版', menu_code: 'COMMS' },
        { code: 'COMMS_DELETE', name: '删除佣金模版', menu_code: 'COMMS' },
        { code: 'COMMS_UPLOAD', name: '导入佣金模版', menu_code: 'COMMS' },
        { code: 'COMMS_CHANGE_DEFAULT', name: '切换默认佣金模版', menu_code: 'COMMS' },
        { code: 'CUST_COMMS_VIEW', name: '查看客户佣金关联', menu_code: 'CUST_COMMS' },
        { code: 'CUST_COMMS_EDIT', name: '修改客户佣金关联', menu_code: 'CUST_COMMS' },
        { code: 'CONVERTIBLE_BOND_FILING_INFO_VIEW', name: '查看可转债交易报备信息', menu_code: 'CONVERTIBLE_BOND_FILING_INFO' },
        { code: 'CONVERTIBLE_BOND_FILING_INFO_ADD', name: '添加可转债交易报备信息', menu_code: 'CONVERTIBLE_BOND_FILING_INFO' },
        { code: 'CONVERTIBLE_BOND_FILING_INFO_EDIT', name: '修改可转债交易报备信息', menu_code: 'CONVERTIBLE_BOND_FILING_INFO' },
        { code: 'CONVERTIBLE_BOND_FILING_INFO_DELETE', name: '删除可转债交易报备信息', menu_code: 'CONVERTIBLE_BOND_FILING_INFO' },
        { code: 'USER_CLIENT_AUTH_VIEW', name: '查看用户授权客户', menu_code: 'USER_CLIENT_AUTH' },
        { code: 'USER_CLIENT_AUTH_ADD', name: '添加用户授权客户', menu_code: 'USER_CLIENT_AUTH' },
        { code: 'USER_CLIENT_AUTH_EDIT', name: '修改用户授权客户', menu_code: 'USER_CLIENT_AUTH' },
        { code: 'USER_CLIENT_AUTH_DELETE', name: '删除用户授权客户', menu_code: 'USER_CLIENT_AUTH' },
        { code: 'CUST_DEF_PERMISSION_LIMIT_VIEW', name: '查看客户默认权限限制', menu_code: 'CUST_DEF_PERMISSION_LIMIT' },
        { code: 'CUST_DEF_PERMISSION_LIMIT_ADD', name: '添加客户默认权限限制', menu_code: 'CUST_DEF_PERMISSION_LIMIT' },
        { code: 'CUST_DEF_PERMISSION_LIMIT_EDIT', name: '修改客户默认权限限制', menu_code: 'CUST_DEF_PERMISSION_LIMIT' },
        { code: 'CUST_DEF_PERMISSION_LIMIT_DELETE', name: '删除客户默认权限限制', menu_code: 'CUST_DEF_PERMISSION_LIMIT' },
        { code: 'CUST_DEF_PERMISSION_LIMIT_UPLOAD', name: '导入客户默认权限限制', menu_code: 'CUST_DEF_PERMISSION_LIMIT' },
        { code: 'ANC_BUSINESS_MANAGEMENT_STK', name: '业务管理(现货)-锚标记', menu_code: 'ANC_BUSINESS_MANAGEMENT_STK' },
        { code: 'DESIGNATE_VIEW', name: '查看转指定/托管', menu_code: 'DESIGNATE' },
        { code: 'DESIGNATE_SH', name: '指定登记', menu_code: 'DESIGNATE' },
        { code: 'DESIGNATE_CANCEL_SH', name: '指定撤销', menu_code: 'DESIGNATE' },
        { code: 'DESIGNATE_SZ', name: '托管注册', menu_code: 'DESIGNATE' },
        { code: 'DESIGNATE_CANCEL_SZ', name: '托管撤销', menu_code: 'DESIGNATE' },
        { code: 'CUST_POSITION_LIMIT_VIEW', name: '查看客户股持仓比例限制', menu_code: 'CUST_POSITION_LIMIT' },
        { code: 'CUST_POSITION_LIMIT_ADD', name: '添加客户股持仓比例限制', menu_code: 'CUST_POSITION_LIMIT' },
        { code: 'CUST_POSITION_LIMIT_EDIT', name: '修改客户股持仓比例限制', menu_code: 'CUST_POSITION_LIMIT' },
        { code: 'CUST_POSITION_LIMIT_DELETE', name: '删除客户股持仓比例限制', menu_code: 'CUST_POSITION_LIMIT' },
        { code: 'STK_CONCENTRATE_RATE_RULE_VIEW', name: '查看现货业务集中度控制规则', menu_code: 'STK_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CONCENTRATE_RATE_RULE_ADD', name: '添加现货业务集中度控制规则', menu_code: 'STK_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CONCENTRATE_RATE_RULE_EDIT', name: '修改现货业务集中度控制规则', menu_code: 'STK_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CONCENTRATE_RATE_RULE_DELETE', name: '删除现货业务集中度控制规则', menu_code: 'STK_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CONCENTRATE_SCALE_RULE_VIEW', name: '查看现货业务持仓规模控制规则', menu_code: 'STK_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CONCENTRATE_SCALE_RULE_ADD', name: '添加现货业务持仓规模控制规则', menu_code: 'STK_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CONCENTRATE_SCALE_RULE_EDIT', name: '修改现货业务持仓规模控制规则', menu_code: 'STK_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CONCENTRATE_SCALE_RULE_DELETE', name: '删除现货业务持仓规模控制规则', menu_code: 'STK_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CONCENTRATE_SCALE_RULE_UPLOAD', name: '导入现货业务持仓规模控制规则', menu_code: 'STK_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_RATE_RULE_VIEW', name: '查看客户现货业务集中度控制规则', menu_code: 'STK_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_RATE_RULE_ADD', name: '添加客户现货业务集中度控制规则', menu_code: 'STK_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_RATE_RULE_EDIT', name: '修改客户现货业务集中度控制规则', menu_code: 'STK_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_RATE_RULE_DELETE', name: '删除客户现货业务集中度控制规则', menu_code: 'STK_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_SCALE_RULE_VIEW', name: '查看现货业务客户持仓规模控制规则', menu_code: 'STK_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_SCALE_RULE_ADD', name: '添加现货业务客户持仓规模控制规则', menu_code: 'STK_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_SCALE_RULE_EDIT', name: '修改现货业务客户持仓规模控制规则', menu_code: 'STK_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_SCALE_RULE_DELETE', name: '删除现货业务客户持仓规模控制规则', menu_code: 'STK_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_CUST_CONCENTRATE_SCALE_RULE_UPLOAD', name: '导入现货业务客户持仓规模控制规则', menu_code: 'STK_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'STK_FORCED_CLOSE_POSITION_SET', name: '现货强制平仓', menu_code: 'STK_FORCED_CLOSE_POSITION' },
        { code: 'STK_FORCED_CLOSE_POSITION_CANCEL', name: '现货强制平仓撤单', menu_code: 'STK_FORCED_CLOSE_POSITION' },
        { code: 'STK_FORCED_CLOSE_POSITION_PLAN_ADD', name: '现货强平添加平仓计划', menu_code: 'STK_FORCED_CLOSE_POSITION' },
        { code: 'STK_FORCED_CLOSE_POSITION_PLAN_EDIT', name: '现货强平修改平仓计划', menu_code: 'STK_FORCED_CLOSE_POSITION' },
        { code: 'STK_FORCED_CLOSE_POSITION_PLAN_DELETE', name: '现货强平删除平仓计划', menu_code: 'STK_FORCED_CLOSE_POSITION' },
        { code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT_VIEW', name: '查看风险警示证券单日买数量设置', menu_code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT' },
        { code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT_ADD', name: '添加风险警示证券单日买数量设置', menu_code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT' },
        { code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT_EDIT', name: '修改风险警示证券单日买数量设置', menu_code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT' },
        { code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT_DELETE', name: '删除风险警示证券单日买数量设置', menu_code: 'STK_RISK_WARNING_SECURITY_BUY_QTY_LIMIT' },
        { code: 'ANC_BUSINESS_MANAGEMENT_OPT', name: '业务管理(期权)-锚标记', menu_code: 'ANC_BUSINESS_MANAGEMENT_OPT' },
        { code: 'OPT_POS_LIMIT_VIEW', name: '查看客户限仓额度', menu_code: 'OPT_POS_LIMIT' },
        { code: 'OPT_POS_LIMIT_ADD', name: '添加客户限仓额度', menu_code: 'OPT_POS_LIMIT' },
        { code: 'OPT_POS_LIMIT_EDIT', name: '修改客户限仓额度', menu_code: 'OPT_POS_LIMIT' },
        { code: 'OPT_POS_LIMIT_DELETE', name: '删除客户限仓额度', menu_code: 'OPT_POS_LIMIT' },
        { code: 'OPT_POS_LIMIT_UPLOAD', name: '导入客户限仓额度', menu_code: 'OPT_POS_LIMIT' },
        { code: 'OPT_PUR_LIMIT_VIEW', name: '查看客户限购额度', menu_code: 'OPT_PUR_LIMIT' },
        { code: 'OPT_PUR_LIMIT_ADD', name: '添加客户限购额度', menu_code: 'OPT_PUR_LIMIT' },
        { code: 'OPT_PUR_LIMIT_EDIT', name: '修改客户限购额度', menu_code: 'OPT_PUR_LIMIT' },
        { code: 'OPT_PUR_LIMIT_DELETE', name: '删除客户限购额度', menu_code: 'OPT_PUR_LIMIT' },
        { code: 'OPT_PUR_LIMIT_UPLOAD', name: '导入客户限购额度', menu_code: 'OPT_PUR_LIMIT' },
        { code: 'OPT_MARGIN_FLOAT_RATIO_VIEW', name: '查看客户保证金上浮比例', menu_code: 'OPT_MARGIN_FLOAT_RATIO' },
        { code: 'OPT_MARGIN_FLOAT_RATIO_ADD', name: '添加客户保证金上浮比例', menu_code: 'OPT_MARGIN_FLOAT_RATIO' },
        { code: 'OPT_MARGIN_FLOAT_RATIO_EDIT', name: '修改客户保证金上浮比例', menu_code: 'OPT_MARGIN_FLOAT_RATIO' },
        { code: 'OPT_MARGIN_FLOAT_RATIO_DELETE', name: '删除客户保证金上浮比例', menu_code: 'OPT_MARGIN_FLOAT_RATIO' },
        { code: 'OPT_MARGIN_FLOAT_RATIO_UPLOAD', name: '导入客户保证金上浮比例', menu_code: 'OPT_MARGIN_FLOAT_RATIO' },
        { code: 'OPT_QUALIFICATION_VIEW', name: '查看客户适当性', menu_code: 'OPT_QUALIFICATION' },
        { code: 'OPT_QUALIFICATION_ADD', name: '添加客户适当性', menu_code: 'OPT_QUALIFICATION' },
        { code: 'OPT_QUALIFICATION_EDIT', name: '修改客户适当性', menu_code: 'OPT_QUALIFICATION' },
        { code: 'OPT_QUALIFICATION_DELETE', name: '删除客户适当性', menu_code: 'OPT_QUALIFICATION' },
        { code: 'OPT_QUALIFICATION_UPLOAD', name: '导入客户适当性', menu_code: 'OPT_QUALIFICATION' },
        { code: 'OPTION_ORDER_LIMIT_VIEW', name: '查看客户委托额度', menu_code: 'OPTION_ORDER_LIMIT' },
        { code: 'OPTION_ORDER_LIMIT_ADD', name: '添加客户委托额度', menu_code: 'OPTION_ORDER_LIMIT' },
        { code: 'OPTION_ORDER_LIMIT_EDIT', name: '修改客户委托额度', menu_code: 'OPTION_ORDER_LIMIT' },
        { code: 'OPTION_ORDER_LIMIT_DELETE', name: '删除客户委托额度', menu_code: 'OPTION_ORDER_LIMIT' },
        { code: 'OPT_FORCED_CLOSE_POSITION_SET', name: '期权强制平仓', menu_code: 'OPT_FORCED_CLOSE_POSITION' },
        { code: 'OPT_FORCED_CLOSE_POSITION_CANCEL', name: '期权强制平仓撤单', menu_code: 'OPT_FORCED_CLOSE_POSITION' },
        { code: 'OPT_FILING_LIMIT_VIEW', name: '查看客户报备信息', menu_code: 'OPT_FILING_LIMIT' },
        { code: 'OPT_FILING_LIMIT_ADD', name: '添加客户报备信息', menu_code: 'OPT_FILING_LIMIT' },
        { code: 'OPT_FILING_LIMIT_EDIT', name: '修改客户报备信息', menu_code: 'OPT_FILING_LIMIT' },
        { code: 'OPT_FILING_LIMIT_DELETE', name: '删除客户报备信息', menu_code: 'OPT_FILING_LIMIT' },
        { code: 'OPT_EXERCISE_TRANSFER_VIEW', name: '查看转处置', menu_code: 'OPT_EXERCISE_TRANSFER' },
        { code: 'OPT_EXERCISE_TRANSFER_REQ', name: '转处置', menu_code: 'OPT_EXERCISE_TRANSFER' },
        { code: 'OPT_EXERCISE_TRANSFER_CANCEL', name: '转处置撤销', menu_code: 'OPT_EXERCISE_TRANSFER' },
        { code: 'ANC_BUSINESS_MANAGEMENT_CRD', name: '业务管理(信用)-锚标记', menu_code: 'ANC_BUSINESS_MANAGEMENT_CRD' },
        { code: 'CRD_POSITION_VIEW', name: '查看头寸', menu_code: 'CRD_POSITION' },
        { code: 'CRD_POSITION_ADD', name: '添加头寸', menu_code: 'CRD_POSITION' },
        { code: 'CRD_POSITION_EDIT', name: '修改头寸', menu_code: 'CRD_POSITION' },
        { code: 'CRD_POSITION_DELETE', name: '删除头寸', menu_code: 'CRD_POSITION' },
        { code: 'CRD_CASH_POSITION_VIEW', name: '查看融资头寸', menu_code: 'CRD_CASH_POSITION' },
        { code: 'CRD_CASH_POSITION_ADD', name: '添加融资头寸', menu_code: 'CRD_CASH_POSITION' },
        { code: 'CRD_CASH_POSITION_EDIT', name: '修改融资头寸', menu_code: 'CRD_CASH_POSITION' },
        { code: 'CRD_CASH_POSITION_DELETE', name: '删除融资头寸', menu_code: 'CRD_CASH_POSITION' },
        { code: 'CRD_SECURITY_POSITION_VIEW', name: '查看融券头寸', menu_code: 'CRD_SECURITY_POSITION' },
        { code: 'CRD_SECURITY_POSITION_ADD', name: '添加融券头寸', menu_code: 'CRD_SECURITY_POSITION' },
        { code: 'CRD_SECURITY_POSITION_EDIT', name: '修改融券头寸', menu_code: 'CRD_SECURITY_POSITION' },
        { code: 'CRD_SECURITY_POSITION_DELETE', name: '删除融券头寸', menu_code: 'CRD_SECURITY_POSITION' },
        { code: 'CRD_CONCENTRATE_RATE_RULE_VIEW', name: '查看融资融券业务集中度控制规则', menu_code: 'CRD_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CONCENTRATE_RATE_RULE_ADD', name: '添加融资融券业务集中度控制规则', menu_code: 'CRD_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CONCENTRATE_RATE_RULE_EDIT', name: '修改融资融券业务集中度控制规则', menu_code: 'CRD_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CONCENTRATE_RATE_RULE_DELETE', name: '删除融资融券业务集中度控制规则', menu_code: 'CRD_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CONCENTRATE_SCALE_RULE_VIEW', name: '查看信用业务持仓规模控制规则', menu_code: 'CRD_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CONCENTRATE_SCALE_RULE_ADD', name: '添加信用业务持仓规模控制规则', menu_code: 'CRD_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CONCENTRATE_SCALE_RULE_EDIT', name: '修改信用业务持仓规模控制规则', menu_code: 'CRD_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CONCENTRATE_SCALE_RULE_DELETE', name: '删除信用业务持仓规模控制规则', menu_code: 'CRD_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CONCENTRATE_SCALE_RULE_UPLOAD', name: '导入信用业务持仓规模控制规则', menu_code: 'CRD_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_RATE_RULE_VIEW', name: '查看客户融资融券业务集中度控制规则', menu_code: 'CRD_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_RATE_RULE_ADD', name: '添加客户融资融券业务集中度控制规则', menu_code: 'CRD_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_RATE_RULE_EDIT', name: '修改客户融资融券业务集中度控制规则', menu_code: 'CRD_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_RATE_RULE_DELETE', name: '删除客户融资融券业务集中度控制规则', menu_code: 'CRD_CUST_CONCENTRATE_RATE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_SCALE_RULE_VIEW', name: '查看信用业务客户持仓规模控制规则', menu_code: 'CRD_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_SCALE_RULE_ADD', name: '添加信用业务客户持仓规模控制规则', menu_code: 'CRD_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_SCALE_RULE_EDIT', name: '修改信用业务客户持仓规模控制规则', menu_code: 'CRD_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_SCALE_RULE_DELETE', name: '删除信用业务客户持仓规模控制规则', menu_code: 'CRD_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_CUST_CONCENTRATE_SCALE_RULE_UPLOAD', name: '导入信用业务客户持仓规模控制规则', menu_code: 'CRD_CUST_CONCENTRATE_SCALE_RULE' },
        { code: 'CRD_FORCED_CLOSE_POSITION_SET', name: '信用强制平仓', menu_code: 'CRD_FORCED_CLOSE_POSITION' },
        { code: 'CRD_FORCED_CLOSE_POSITION_CANCEL', name: '信用强制平仓撤单', menu_code: 'CRD_FORCED_CLOSE_POSITION' },
        { code: 'CRD_FORCED_CLOSE_POSITION_LIQUDATION', name: '信用强制平仓指令', menu_code: 'CRD_FORCED_CLOSE_POSITION' },
        { code: 'CRD_FORCED_CLOSE_POSITION_BATCH_CANCEL', name: '信用强平批量撤销申报', menu_code: 'CRD_FORCED_CLOSE_POSITION' },
        { code: 'CRD_INTEREST_TMPL_VIEW', name: '查看息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_INTEREST_TMPL_ADD', name: '添加息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_INTEREST_TMPL_EDIT', name: '修改息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_INTEREST_TMPL_DELETE', name: '删除息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_INTEREST_TMPL_UPLOAD', name: '导入息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_INTEREST_TMPL_CHANGE_DEFAULT', name: '切换默认息费模版', menu_code: 'CRD_INTEREST_TMPL' },
        { code: 'CRD_UNDERLYING_VIEW', name: '查看信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_EDIT', name: '修改信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_UPLOAD', name: '导入信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_IMPORT_COUNTER', name: '同步主柜信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_TO_OES', name: '同步信用标的到OES', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_SYNC', name: '同步信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_UNDERLYING_EXPORT', name: '导出信用标的信息', menu_code: 'CRD_UNDERLYING' },
        { code: 'CRD_COLLATERAL_VIEW', name: '查看信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_EDIT', name: '修改信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_UPLOAD', name: '导入信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_IMPORT_COUNTER', name: '同步主柜信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_TO_OES', name: '同步信用担保品到OES', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_SYNC', name: '同步信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_COLLATERAL_EXPORT', name: '同步信用担保品信息', menu_code: 'CRD_COLLATERAL' },
        { code: 'CRD_CUST_GUARD_VIEW', name: '查看信用客户警戒名单', menu_code: 'CRD_CUST_GUARD' },
        { code: 'CRD_CUST_GUARD_ADD', name: '添加信用客户警戒名单', menu_code: 'CRD_CUST_GUARD' },
        { code: 'CRD_CUST_GUARD_EDIT', name: '修改信用客户警戒名单', menu_code: 'CRD_CUST_GUARD' },
        { code: 'CRD_CUST_GUARD_DELETE', name: '删除信用客户警戒名单', menu_code: 'CRD_CUST_GUARD' },
        { code: 'CRD_CUST_DEBT_REPAY_MODE_VIEW', name: '查看客户融资融券合约归还模式', menu_code: 'CRD_CUST_DEBT_REPAY_MODE' },
        { code: 'CRD_CUST_DEBT_REPAY_MODE_ADD', name: '添加客户融资融券合约归还模式', menu_code: 'CRD_CUST_DEBT_REPAY_MODE' },
        { code: 'CRD_CUST_DEBT_REPAY_MODE_EDIT', name: '修改客户融资融券合约归还模式', menu_code: 'CRD_CUST_DEBT_REPAY_MODE' },
        { code: 'CRD_CUST_DEBT_REPAY_MODE_DELETE', name: '删除客户融资融券合约归还模式', menu_code: 'CRD_CUST_DEBT_REPAY_MODE' },
        { code: 'ANC_CONFIG_MANAGEMENT', name: '参数管理-锚标记', menu_code: 'ANC_CONFIG_MANAGEMENT' },
        { code: 'TRD_DATE_VIEW', name: '查看交易日', menu_code: 'TRD_DATE' },
        { code: 'TRD_DATE_SET', name: '保存交易日', menu_code: 'TRD_DATE' },
        { code: 'TRD_CALE_VIEW', name: '查看交易日历', menu_code: 'TRD_CALE' },
        { code: 'TRD_CALE_SET', name: '保存交易日历', menu_code: 'TRD_CALE' },
        { code: 'TRD_CALE_SHARE', name: '应用交易日历到其他结点', menu_code: 'TRD_CALE' },
        { code: 'TRD_CALE_UPLOAD', name: '导入交易日历', menu_code: 'TRD_CALE' },
        { code: 'FIX_FEE_VIEW', name: '查看标准费率', menu_code: 'FIX_FEE' },
        { code: 'FIX_FEE_ADD', name: '添加标准费率', menu_code: 'FIX_FEE' },
        { code: 'FIX_FEE_EDIT', name: '修改标准费率', menu_code: 'FIX_FEE' },
        { code: 'FIX_FEE_DELETE', name: '删除标准费率', menu_code: 'FIX_FEE' },
        { code: 'FIX_FEE_UPLOAD', name: '导入标准费率', menu_code: 'FIX_FEE' },
        { code: 'BROKER_CLUSTER_CONF_VIEW', name: '查看券商结点参数', menu_code: 'BROKER_CLUSTER_CONF' },
        { code: 'BROKER_CLUSTER_CONF_ADD', name: '添加券商结点参数', menu_code: 'BROKER_CLUSTER_CONF' },
        { code: 'BROKER_CLUSTER_CONF_EDIT', name: '修改券商结点参数', menu_code: 'BROKER_CLUSTER_CONF' },
        { code: 'BROKER_CLUSTER_CONF_DELETE', name: '删除券商结点参数', menu_code: 'BROKER_CLUSTER_CONF' },
        { code: 'BROKER_CLUSTER_CONF_UPLOAD', name: '导入券商结点参数', menu_code: 'BROKER_CLUSTER_CONF' },
        { code: 'SECURITY_VIEW', name: '查看证券信息', menu_code: 'SECURITY' },
        { code: 'SECURITY_ADD', name: '添加证券信息', menu_code: 'SECURITY' },
        { code: 'SECURITY_EDIT', name: '修改证券信息', menu_code: 'SECURITY' },
        { code: 'SECURITY_DELETE', name: '删除证券信息', menu_code: 'SECURITY' },
        { code: 'SECURITY_UPLOAD', name: '导入证券信息', menu_code: 'SECURITY' },
        { code: 'RISK_LEVEL_VIEW', name: '查看证券风险等级', menu_code: 'RISK_LEVEL' },
        { code: 'RISK_LEVEL_ADD', name: '添加证券风险等级信息', menu_code: 'RISK_LEVEL' },
        { code: 'RISK_LEVEL_EDIT', name: '修改证券风险等级信息', menu_code: 'RISK_LEVEL' },
        { code: 'RISK_LEVEL_DELETE', name: '删除证券风险等级信息', menu_code: 'RISK_LEVEL' },
        { code: 'RISK_LEVEL_UPLOAD', name: '导入证券风险等级信息', menu_code: 'RISK_LEVEL' },
        { code: 'ETF_VIEW', name: '查看ETF申赎信息', menu_code: 'ETF' },
        { code: 'ETF_ADD', name: '添加ETF申赎信息', menu_code: 'ETF' },
        { code: 'ETF_EDIT', name: '修改ETF申赎信息', menu_code: 'ETF' },
        { code: 'ETF_DELETE', name: '删除ETF申赎信息', menu_code: 'ETF' },
        { code: 'ETF_UPLOAD', name: '导入ETF申赎信息', menu_code: 'ETF' },
        { code: 'OPT_BRO_POS_LIMIT_VIEW', name: '查看券商限仓额度', menu_code: 'OPT_BRO_POS_LIMIT' },
        { code: 'OPT_BRO_POS_LIMIT_ADD', name: '添加券商限仓额度', menu_code: 'OPT_BRO_POS_LIMIT' },
        { code: 'OPT_BRO_POS_LIMIT_EDIT', name: '修改券商限仓额度', menu_code: 'OPT_BRO_POS_LIMIT' },
        { code: 'OPT_BRO_POS_LIMIT_DELETE', name: '删除券商限仓额度', menu_code: 'OPT_BRO_POS_LIMIT' },
        { code: 'OPT_BRO_POS_LIMIT_UPLOAD', name: '导入券商限仓额度', menu_code: 'OPT_BRO_POS_LIMIT' },
        { code: 'UPGRADE_VIEW', name: '查看升级配置信息', menu_code: 'UPGRADE' },
        { code: 'UPGRADE_ADD', name: '添加升级配置信息', menu_code: 'UPGRADE' },
        { code: 'UPGRADE_EDIT', name: '修改升级配置信息', menu_code: 'UPGRADE' },
        { code: 'UPGRADE_DELETE', name: '删除升级配置信息', menu_code: 'UPGRADE' },
        { code: 'UPGRADE_UPLOAD', name: '导入升级配置信息', menu_code: 'UPGRADE' },
        { code: 'SECURITY_SHARES_VIEW', name: '查看证券产品股本', menu_code: 'SECURITY_SHARES' },
        { code: 'SECURITY_SHARES_ADD', name: '添加证券产品股本', menu_code: 'SECURITY_SHARES' },
        { code: 'SECURITY_SHARES_EDIT', name: '修改证券产品股本', menu_code: 'SECURITY_SHARES' },
        { code: 'SECURITY_SHARES_DELETE', name: '删除证券产品股本', menu_code: 'SECURITY_SHARES' },
        { code: 'SECURITY_SHARES_UPLOAD', name: '导入证券产品股本', menu_code: 'SECURITY_SHARES' },
        { code: 'SSE_CODE_MAP_VIEW', name: '查看上交所证券代码段', menu_code: 'SSE_CODE_MAP' },
        { code: 'SSE_CODE_MAP_ADD', name: '添加上交所证券代码段', menu_code: 'SSE_CODE_MAP' },
        { code: 'SSE_CODE_MAP_EDIT', name: '修改上交所证券代码段', menu_code: 'SSE_CODE_MAP' },
        { code: 'SSE_CODE_MAP_DELETE', name: '删除上交所证券代码段', menu_code: 'SSE_CODE_MAP' },
        { code: 'ANC_CONFIG_MANAGEMENT_STK', name: '参数管理(现货)-锚标记', menu_code: 'ANC_CONFIG_MANAGEMENT_STK' },
        { code: 'STK_SECTOR_VIEW', name: '查看现货证券板块信息', menu_code: 'STK_SECTOR' },
        { code: 'STK_SECTOR_ADD', name: '添加现货证券板块信息', menu_code: 'STK_SECTOR' },
        { code: 'STK_SECTOR_EDIT', name: '修改现货证券板块信息', menu_code: 'STK_SECTOR' },
        { code: 'STK_SECTOR_DELETE', name: '删除现货证券板块信息', menu_code: 'STK_SECTOR' },
        { code: 'STK_SECTOR_DETAIL_VIEW', name: '查看现货证券板块明细信息', menu_code: 'STK_SECTOR_DETAIL' },
        { code: 'STK_SECTOR_DETAIL_ADD', name: '添加现货证券板块明细信息', menu_code: 'STK_SECTOR_DETAIL' },
        { code: 'STK_SECTOR_DETAIL_EDIT', name: '修改现货证券板块明细信息', menu_code: 'STK_SECTOR_DETAIL' },
        { code: 'STK_SECTOR_DETAIL_DELETE', name: '删除现货证券板块明细信息', menu_code: 'STK_SECTOR_DETAIL' },
        { code: 'STK_SECTOR_DETAIL_UPLOAD', name: '导入现货证券板块明细信息', menu_code: 'STK_SECTOR_DETAIL' },
        { code: 'ANC_CONFIG_MANAGEMENT_OPT', name: '参数管理(期权)-锚标记', menu_code: 'ANC_CONFIG_MANAGEMENT_OPT' },
        { code: 'ANC_CONFIG_MANAGEMENT_CRD', name: '参数管理(信用)-锚标记', menu_code: 'ANC_CONFIG_MANAGEMENT_CRD' },
        { code: 'CRD_SECTOR_VIEW', name: '查看信用证券板块信息', menu_code: 'CRD_SECTOR' },
        { code: 'CRD_SECTOR_ADD', name: '添加信用证券板块信息', menu_code: 'CRD_SECTOR' },
        { code: 'CRD_SECTOR_EDIT', name: '修改信用证券板块信息', menu_code: 'CRD_SECTOR' },
        { code: 'CRD_SECTOR_DELETE', name: '删除信用证券板块信息', menu_code: 'CRD_SECTOR' },
        { code: 'CRD_SECTOR_DETAIL_VIEW', name: '查看信用证券板块明细信息', menu_code: 'CRD_SECTOR_DETAIL' },
        { code: 'CRD_SECTOR_DETAIL_ADD', name: '添加信用证券板块明细信息', menu_code: 'CRD_SECTOR_DETAIL' },
        { code: 'CRD_SECTOR_DETAIL_EDIT', name: '修改信用证券板块明细信息', menu_code: 'CRD_SECTOR_DETAIL' },
        { code: 'CRD_SECTOR_DETAIL_DELETE', name: '删除信用证券板块明细信息', menu_code: 'CRD_SECTOR_DETAIL' },
        { code: 'CRD_SECTOR_DETAIL_UPLOAD', name: '导入信用证券板块明细信息', menu_code: 'CRD_SECTOR_DETAIL' },
        { code: 'CRD_BROKER_CLUSTER_CONF_VIEW', name: '查看券商信用结点参数', menu_code: 'CRD_BROKER_CLUSTER_CONF' },
        { code: 'CRD_BROKER_CLUSTER_CONF_ADD', name: '添加券商信用结点参数', menu_code: 'CRD_BROKER_CLUSTER_CONF' },
        { code: 'CRD_BROKER_CLUSTER_CONF_EDIT', name: '修改券商信用结点参数', menu_code: 'CRD_BROKER_CLUSTER_CONF' },
        { code: 'CRD_BROKER_CLUSTER_CONF_DELETE', name: '删除券商信用结点参数', menu_code: 'CRD_BROKER_CLUSTER_CONF' },
        { code: 'CRD_BROKER_CLUSTER_CONF_UPLOAD', name: '导入券商信用结点参数', menu_code: 'CRD_BROKER_CLUSTER_CONF' },
        { code: 'CRD_HIGH_LIQUIDITY_SECURITY_VIEW', name: '查看信用业务高流通性证券信息', menu_code: 'CRD_HIGH_LIQUIDITY_SECURITY' },
        { code: 'CRD_HIGH_LIQUIDITY_SECURITY_ADD', name: '添加信用业务高流通性证券信息', menu_code: 'CRD_HIGH_LIQUIDITY_SECURITY' },
        { code: 'CRD_HIGH_LIQUIDITY_SECURITY_EDIT', name: '修改信用业务高流通性证券信息', menu_code: 'CRD_HIGH_LIQUIDITY_SECURITY' },
        { code: 'CRD_HIGH_LIQUIDITY_SECURITY_DELETE', name: '删除信用业务高流通性证券信息', menu_code: 'CRD_HIGH_LIQUIDITY_SECURITY' },
        { code: 'CRD_HIGH_LIQUIDITY_SECURITY_UPLOAD', name: '导入信用业务高流通性证券信息则', menu_code: 'CRD_HIGH_LIQUIDITY_SECURITY' },
        { code: 'ANC_OPERATIONS_MANAGEMENT', name: '运维管理-锚标记', menu_code: 'ANC_OPERATIONS_MANAGEMENT' },
        { code: 'OPS_CONFIG_VIEW', name: '查看配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_CONFIG_ADD', name: '添加配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_CONFIG_EDIT', name: '编辑配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_CONFIG_DELETE', name: '删除配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_CONFIG_RENAME', name: '重命名配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_CONFIG_UPLOAD', name: '导入配置文件', menu_code: 'OPS_CONFIG' },
        { code: 'OPS_EXPORT_VIEW', name: '运维配置文件导出', menu_code: 'OPS_EXPORT' },
        { code: 'OPS_EXPORT_DONE', name: '运维配置文件导出', menu_code: 'OPS_EXPORT' },
        { code: 'OPS_TASK_CONFIG_VIEW', name: '查看运维任务配置', menu_code: 'OPS_TASK_CONFIG' },
        { code: 'OPS_TASK_CONFIG_ADD', name: '添加运维任务配置', menu_code: 'OPS_TASK_CONFIG' },
        { code: 'OPS_TASK_CONFIG_EDIT', name: '编辑运维任务配置', menu_code: 'OPS_TASK_CONFIG' },
        { code: 'OPS_TASK_CONFIG_DELETE', name: '删除运维任务配置', menu_code: 'OPS_TASK_CONFIG' },
        { code: 'OPS_TASK_CONFIG_EXECUTE', name: '运维任务执行', menu_code: 'OPS_TASK_CONFIG' },
        { code: 'OPS_TASK_EXEC_VIEW', name: '查看运维任务', menu_code: 'OPS_TASK_EXEC' },
        { code: 'OPS_TASK_EXEC_EXECUTE', name: '执行运维任务', menu_code: 'OPS_TASK_EXEC' },
        { code: 'OPS_TASK_EXEC_CANCEL', name: '取消运维任务', menu_code: 'OPS_TASK_EXEC' },
        { code: 'OPS_COMMAND_EXEC_VIEW', name: '查看临时运维指令', menu_code: 'OPS_COMMAND_EXEC' },
        { code: 'OPS_COMMAND_EXEC_EXECUTE', name: '执行临时运维指令', menu_code: 'OPS_COMMAND_EXEC' },
        { code: 'ANC_OPERATIONS_MANAGEMENT_STK', name: '运维管理(现货)-锚标记', menu_code: 'ANC_OPERATIONS_MANAGEMENT_STK' },
        { code: 'ANC_OPERATIONS_MANAGEMENT_OPT', name: '运维管理(期权)-锚标记', menu_code: 'ANC_OPERATIONS_MANAGEMENT_OPT' },
        { code: 'ANC_OPERATIONS_MANAGEMENT_CRD', name: '运维管理(信用)-锚标记', menu_code: 'ANC_OPERATIONS_MANAGEMENT_CRD' },
        { code: 'ANC_SYSTEM_MANAGEMENT', name: '系统管理-锚标记', menu_code: 'ANC_SYSTEM_MANAGEMENT' },
        { code: 'BRANCH_VIEW', name: '查看营业部', menu_code: 'BRANCH' },
        { code: 'BRANCH_ADD', name: '添加营业部', menu_code: 'BRANCH' },
        { code: 'BRANCH_EDIT', name: '修改营业部', menu_code: 'BRANCH' },
        { code: 'BRANCH_DELETE', name: '删除营业部', menu_code: 'BRANCH' },
        { code: 'USER_ROLE_VIEW', name: '查看角色', menu_code: 'USER_ROLE' },
        { code: 'USER_ROLE_ADD', name: '添加角色', menu_code: 'USER_ROLE' },
        { code: 'USER_ROLE_EDIT', name: '修改角色', menu_code: 'USER_ROLE' },
        { code: 'USER_ROLE_DELETE', name: '删除角色', menu_code: 'USER_ROLE' },
        { code: 'USER_VIEW', name: '查看用户', menu_code: 'USER' },
        { code: 'USER_ADD', name: '添加用户', menu_code: 'USER' },
        { code: 'USER_EDIT', name: '修改用户', menu_code: 'USER' },
        { code: 'USER_DELETE', name: '删除用户', menu_code: 'USER' },
        { code: 'USER_CHANGE_PWD', name: '修改密码', menu_code: 'USER' },
        { code: 'USER_RESET_PWD', name: '重置密码', menu_code: 'USER' },
        { code: 'CLUSTER_VIEW', name: '查看结点', menu_code: 'CLUSTER' },
        { code: 'CLUSTER_ADD', name: '添加结点', menu_code: 'CLUSTER' },
        { code: 'CLUSTER_EDIT', name: '修改结点', menu_code: 'CLUSTER' },
        { code: 'CLUSTER_DELETE', name: '删除结点', menu_code: 'CLUSTER' },
        { code: 'SERVER_VIEW', name: '查看主机', menu_code: 'SERVER' },
        { code: 'SERVER_ADD', name: '添加主机', menu_code: 'SERVER' },
        { code: 'SERVER_EDIT', name: '修改主机', menu_code: 'SERVER' },
        { code: 'SERVER_DELETE', name: '删除主机', menu_code: 'SERVER' },
        { code: 'TRD_RES_VIEW', name: '查看交易资源', menu_code: 'TRD_RES' },
        { code: 'TRD_RES_ADD', name: '添加交易资源', menu_code: 'TRD_RES' },
        { code: 'TRD_RES_EDIT', name: '修改交易资源', menu_code: 'TRD_RES' },
        { code: 'TRD_RES_DELETE', name: '删除交易资源', menu_code: 'TRD_RES' },
        { code: 'ERR_CODE_VIEW', name: '查看错误码', menu_code: 'ERROR_CODE' },
        { code: 'ERR_CODE_ADD', name: '添加错误码', menu_code: 'ERROR_CODE' },
        { code: 'ERR_CODE_EDIT', name: '修改错误码', menu_code: 'ERROR_CODE' },
        { code: 'ERR_CODE_DELETE', name: '删除错误码', menu_code: 'ERROR_CODE' },
        { code: 'ERR_CODE_UPLOAD', name: '导入错误码', menu_code: 'ERROR_CODE' },
        { code: 'BROKER_PARAMS_VIEW', name: '查看券商参数', menu_code: 'BROKER_PARAMS' },
        { code: 'BROKER_PARAMS_EDIT', name: '编辑券商参数', menu_code: 'BROKER_PARAMS' },
        { code: 'INV_ACCT_DEFAULT_PERMISSIONS_VIEW', name: '查看投资者默认权限', menu_code: 'INV_ACCT_DEFAULT_PERMISSIONS' },
        { code: 'INV_ACCT_DEFAULT_PERMISSIONS_ADD', name: '添加投资者默认权限', menu_code: 'INV_ACCT_DEFAULT_PERMISSIONS' },
        { code: 'INV_ACCT_DEFAULT_PERMISSIONS_EDIT', name: '编辑投资者默认权限', menu_code: 'INV_ACCT_DEFAULT_PERMISSIONS' },
        { code: 'INV_ACCT_DEFAULT_PERMISSIONS_DELETE', name: '删除投资者默认权限', menu_code: 'INV_ACCT_DEFAULT_PERMISSIONS' },
        { code: 'OPT_LOG_VIEW', name: '操作流水', menu_code: 'OPT_LOG' },
        { code: 'USER_LOGIN', name: '用户登录', menu_code: 'SESSION' },
        { code: 'LOGIN_LOG_VIEW', name: '用户登录日志', menu_code: 'SESSION' },
        { code: 'OTHER', name: '其它', menu_code: 'OTHER' }
      ]
    end
  end
end
