# frozen_string_literal: true

# require 'bundler/gem_tasks'
require_relative 'lib/tasks/customer_tasks'
require_relative 'lib/tasks/fgfund_tasks'
require_relative 'lib/tasks/mszq_tasks'
require_relative 'lib/tasks/o32_option_tasks'
require_relative 'lib/tasks/efunds_tasks'
require_relative 'lib/cli'

# if ENV['RAILS_ENV'] != 'production'
#   require 'rspec/core/rake_task'
#   RSpec::Core::RakeTask.new(:spec)
# end

namespace :customer do
  desc '显示用户列表'
  task :list do
    CustomerRakes.list
  end

  desc '切换用户'
  task :change, [:customer_name] do |_task, args|
    CustomerRakes.change(args.customer_name)
  end

  desc '清理用户环境'
  task :clear do
    CustomerRakes.clear
  end

  desc '显示当前用户环境'
  task :current do
    CustomerRakes.current
  end
end

desc '检查数据库连接与 SQL 表权限'
task :check do
  require_relative 'lib/aas_oracle_importer'

  agent = AasOracleImporter::Agent.new
  agent.check
end

desc '运行 SQL方法获取结果'
task :sql_test, [:name, :sql_name, :lines] do |_task, args|
  require_relative 'lib/aas_oracle_importer'

  lines = (args.lines || 10).to_i
  agent = AasOracleImporter::Agent.new
  agent.sql_test(args.name, args.sql_name, lines)
end

desc '查询 指定系统 SQL 语句'
task :sql_show, [:name] do |_task, args|
  require_relative 'lib/aas_oracle_importer'

  agent = AasOracleImporter::Agent.new
  agent.sql_show(args.name)
end

desc 'ldap test'
task :ldap_test do
  require_relative 'lib/aas_oracle_importer'
  db = AasOracleImporter::LdapClient.new('ldap', 'dc')
  # pp db.base
  # pp db.search(base: db.base, filter: '(&(objectclass = user)(objectclass = person)(!(objectclass = computer)))')
  # pp db.search(base: db.base, filter: '(&(objectclass = group))')
end

desc 'fgfund datacenter async task'
task :fgfund_async do
  queue = FgfundDatacenterAsyncTask::TaskQueue.new
  queue.run_tasks
end

desc 'mszq datacenter async task'
task :mszq_async do
  import_status = MszqDatacenterAsyncTask::DataCenterRequest.new.import_status
end

desc 'fgfund baoleiji test'
task :fgblj_test do
  require_relative 'lib/aas_oracle_importer'
  import_test = AasOracleImporter::QzBaoleijiImporter.new(Quarter.first).import_test
end

desc 'htbr kaoqin test'
task :haikang_kaoqin_test do
  require_relative 'lib/aas_oracle_importer'
  import_test = AasOracleImporter::HaikangKaoqinImporter.new(Quarter.first).import_test
end

namespace :o32_option do
  desc 'o32参数导入数据任务'
  task :import, [:quarter_id] do |_task, args|
    O32OptionRakes.new(args.quarter_id).import
  end
end

desc '执行导入'
task :agent do
  ARGV.replace(ARGV.drop_while { |a| a != '--' }.drop(1))
  AasOracleImporter::CLI.run(ARGV)
end

task default: :spec
