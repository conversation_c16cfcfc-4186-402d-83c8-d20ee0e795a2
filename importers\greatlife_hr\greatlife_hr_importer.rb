module AasOracleImporter

  class GreatlifeHrImporter < ImporterBase

    def config
      @bs_id          = importer_config['bs_id']
      @position_users = [] # 拥有岗位的用户
    end

    # def import
    #   before_import
    #   import_departments
    #   import_users
    #   import_users_departments_links
    # end

    def import_to_do
      @hr_departments = InsuranceHrDepartment.all.to_a
      @hr_accounts    = InsuranceHrAccount.all.to_a
      update_departments
      update_users_and_accounts
      update_users_departments_links

      # 当只更新importer，不更新aas，就会出现Job不存在的情况，所以要判断
      return unless Job.table_exists?

      import_jobs
      import_job_users
    end

    private

    def update_departments
      create_or_update_departments
      update_departments_parent_id
      set_departments_level
    end

    def update_users_and_accounts

      departments = Department.all.to_a
      users = User.all.to_a

      user_codes = users.map(&:code)
      hr_user_codes = @hr_accounts.map(&:usercode)

      # 新增
      ActiveRecord::Base.transaction do
        (hr_user_codes - user_codes).each do |code|
          hr_a = @hr_accounts.find {|x| x.usercode == code }
          department = departments.find {|x| x.code == hr_a.department_code }

          unless department
            @logger.warn "#{self.class}: not found department on code #{hr_a.department_code} when user update"
          end

          user =
            User.create(
              code: hr_a.usercode,
              name: hr_a.name,
              email: hr_a.email,
              cellphone: hr_a.phone,
              position: hr_a.job_descr,
              job: hr_a.job_descr,
              inservice: !hr_a.locked,
              department_id: department&.id
            )
          @position_users << user if user.position?
          account =
            GreatlifeHr::Account.create(
              code: user.code,
              name: user.name,
              status: user.inservice,
              quarter_id: @quarter_id,
              user_id: user.id
            )
          import_ledger_to_account(account, user)
        end
      end

      # 已有
      ActiveRecord::Base.transaction do
        users.each do |a|
          hr_a = @hr_accounts.find {|x| x.usercode == a.code }
          department = departments.find {|x| x.code == hr_a.department_code }

          unless department
            @logger.warn "#{self.class}: not found department on code #{hr_a.department_code} when user update"
          end

          if hr_a
            a.update(
              name: hr_a.name,
              email: hr_a.email,
              cellphone: hr_a.phone,
              position: hr_a.job_descr,
              job: hr_a.job_descr,
              inservice: !hr_a.locked,
              department_id: department&.id
            )
            @position_users << a if a.position?
            GreatlifeHr::Account.create(
              code: a.code,
              name: a.name,
              status: a.inservice,
              quarter_id: @quarter_id,
              user_id: a.id
            )
          end
        end
      end
    end

    # def import_users_departments_links
    #   users = User.where(quarter_id: @quarter.id).to_a
    #   departments = Department.where(quarter_id: @quarter.id).to_a

    #   @hr_departments.each do |hr_d|
    #     department = departments.find {|x| x.code == hr_d.code }
    #     manager =    users.find {|x| x.code == hr_d.manager_id }
    #     charge_leader = users.find {|x| x.code == hr_d.charge_leader_id }


    #     unless manager
    #       @logger.warn "#{self.class}: not found user on manager_code #{hr_d.manager_id}"
    #     end

    #     unless charge_leader
    #       @logger.warn "#{self.class}: not found user on charge_leader_code #{hr_d.charge_leader_id}"
    #     end

    #     department.update(manager_id: manager&.id, charge_leader_id: charge_leader&.id)

    #     department.users.update_all(manager_id: manager&.id)
    #   end

    # end

    def update_users_departments_links
      users = User.all.to_a
      departments = Department.all.to_a

      @hr_departments.each do |hr_d|
        department = departments.find {|x| x.code == hr_d.code }
        manager    = users.find {|x| x.code == hr_d.manager_id }
        charge_leader = users.find {|x| x.code == hr_d.charge_leader_id }

        unless manager
          @logger.warn "#{self.class}: not found user on manager_code #{hr_d.manager_id}"
        end

        unless charge_leader
          @logger.warn "#{self.class}: not found user on charge_leader_code #{hr_d.charge_leader_id}"
        end

        department.update(manager_id: manager&.id, charge_leader_id: charge_leader&.id)

        department.users.update_all(manager_id: manager&.id)

        if department.level == 3
          department.users.update_all(company_id: department.parent&.id)
        end

        if department.level == 1 or department.level == 2
          department.users.update_all(company_id: department.id)
        end
      end
    end

    # def clear_departments_and_users
    #   Department.where(quarter_id: @quarter.id).destroy_all
    #   User.where(quarter_id: @quarter.id).destroy_all
    # end

    # def create_special_departments
    #   Department.create(
    #     code: "disabled",
    #     name: "禁用账号",
    #     quarter_id: @quarter.id,
    #     department_type: 101,
    #     inservice: true
    #   )
    #   Department.create(
    #     code: "public",
    #     name: "公共账号",
    #     quarter_id: @quarter.id,
    #     department_type: 102,
    #     inservice: true
    #   )
    # end

    # def create_departments
    #   @hr_departments.each do |hr_d|
    #     Department.create(
    #       code: hr_d.code,
    #       name: hr_d.name,
    #       quarter_id: @quarter.id,
    #       department_type: hr_d.d_type,
    #       inservice: true
    #     )
    #   end
    # end

    def create_or_update_departments
      departments = Department.all.to_a
      d_codes = departments.map(&:code)
      hr_d_codes = @hr_departments.map(&:code)

      # 新增
      ActiveRecord::Base.transaction do
        (hr_d_codes - d_codes).each do |code|
          hr_d = @hr_departments.find {|x| x.code == code }
          Department.create(
            code: hr_d.code,
            name: hr_d.name,
            d_type: hr_d.d_type,
            inservice: true
          )
        end
      end

      # 现有
      ActiveRecord::Base.transaction do
        departments.each do |d|
          hr_d = @hr_departments.find {|x| x.code == d.code}
          if hr_d
            d.update(
              name: hr_d.name,
              d_type: hr_d.d_type,
              inservice: true
            )
          end
        end
      end

      # 删除
      ActiveRecord::Base.transaction do
        codes = d_codes - hr_d_codes
        Department.where(code: codes).update_all(inservice: false)
      end
    end

    # def link_departments_parent_id
    #   departments = Department.where(quarter_id: @quarter.id).to_a
    #   @hr_departments.each do |hr_d|
    #     d = departments.find {|x| x.code == hr_d.code }
    #     parent_d = departments.find {|x| x.code == hr_d.parent_code }
    #     if parent_d
    #       d.update(parent_id: parent_d.id)
    #     else
    #       @logger.warn "#{self.class}: not found department on parent_code #{hr_d.parent_code}"
    #     end
    #   end
    # end

    def update_departments_parent_id
      departments = Department.all.to_a
      @hr_departments.each do |hr_d|
        d = departments.find {|x| x.code == hr_d.code }
        parent_d = departments.find {|x| x.code == hr_d.parent_code }
        if parent_d
          d.update(parent_id: parent_d.id)
        else
          @logger.warn "#{self.class}: not found department on parent_code #{hr_d.parent_code}"
        end
      end
    end

    def set_departments_level
      departments = Department.all.to_a

      root = departments.find {|x| x.d_type == 0}
      top_levels = root.children
      top_levels.each {|d| d.update(level: 1)}

      second_levels = top_levels.map(&:children).flatten
      second_levels.each {|x| x.update(level: 2)}

      third_levels = second_levels.map(&:children).flatten
      third_levels.each {|x| x.update(level: 3)}

      Department.where(level: nil).each do |d|
        @logger.warn "#{self.class}: can not set level on #{d.code} #{d.name}"
      end
    end

    # hr 的比较特殊，台账可以自动关联
    def import_ledger_to_account(account, user)
      # TODO: 这里默认就是 code
      ledger =
        Ledger.find_or_create_by(
          business_system_id: @bs_id,
          account_code_field: 'code',
          account_code_value: account.code,

        )
      unless ledger.user_id
        ledger.update(user_id: account.user_id,
          user_code_field: 'code',
          user_code_value: user.code
        )
      end
    end

    # 导入岗位
    def import_jobs
      job_names = @hr_accounts.map(&:job_descr).uniq.compact.select(&:present?)
      job_names.each do |name|
        Job.create_with(name: name).find_or_create_by(code: name)
      end
    end

    def import_job_users
      @jobs = Job.all # 用于匹配job，避免n+1
      import_job_user_lines
    end
  end
end



