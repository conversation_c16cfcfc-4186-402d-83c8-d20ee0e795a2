# frozen_string_literal: true

require_relative 'importer'
require_relative 'config'
require_relative 'models'
require_relative 'workday'
require_relative 'quarter_methods'

module AasOracleImporter
  # importer 执行入口类
  class Agent
    include QuarterMethods

    def initialize(options = {})
      @logger = AasOracleImporter.initialize_logger
      @redis = RedisClient.redis
      @quarter_id = options[:quarter_id]
      @importer_names = options[:systems]
      @system_ids = options[:system_ids]&.map(&:to_i) # 通过system_id导入指定系统
      @skip_after_import_rake = options[:skip_after_import_rake] || server_config['skip_after_import_rake'] # 跳过所有rake任务
      @skip_non_system_after_import_rake = options[:skip_non_system_after_import_rake] || server_config['skip_non_system_after_import_rake'] # 跳过非系统rake任务
      @only_import_user = options[:only_import_user] # 导入员工
      @is_force = options[:is_force] # 强制执行
    end

    def run(options = {})
      unless Workday.allow_import?
        @logger.info { 'Workday do not allow this import, exit...' }
        exit
      end
      # quarter放到前面，因为import_quarter的用到依赖@quarter的import_system_client_key
      @quarter = generate_quarter
      import_quarter do
        generate_import_systems
        import_systems
        after_run
      end
    ensure
      release_import_quarter_key
    end

    def run_newest(options = {})
      @quarter = newest_quarter
      import_quarter do
        import_quarter_value('start')
        generate_import_systems
        import_systems
        after_run
      end
    ensure
      release_import_quarter_key
    end

    def list_quarters
      puts 'Quarters:'
      Quarter.all.each do |q|
        puts "\tid: #{q.id}\tname: #{q.name}"
      end
    end

    def run_specify(options = {})
      @quarter = specify_quarter(@quarter_id)
      import_quarter do
        import_quarter_value('start')
        generate_import_systems
        import_systems
        after_run
      end
    ensure
      release_import_quarter_key
    end

    def check
      check_business_systems
    end

    def sql_test(system_name, sql_method_name, lines)
      @quarter  = Quarter.new
      the_class = importer_class_for(system_name)
      the_class.new(@quarter).sql_test(sql_method_name, lines)
    end

    def sql_show(system_name)
      @quarter  = Quarter.new
      the_class = importer_class_for(system_name)
      the_class.new(@quarter, ignore_initialize_db: true).show_sqls
    end

    # 导入时间点key
    # import_system_key也不能使用@quarter.id，因为generate_quarter用到了，此时@quarter还是空的
    def import_system_key
      "import_system:quarter_id:#{@quarter.id}"
    end

    # 非系统跑批的key，用于判断是否有客户端在跑批了
    def after_todo_task_key
      "after_import:todo_non_system:quarter_id:#{@quarter.id}"
    end

    def import_quarter_key
      'import_quarter'
    end

    # 设置标志为执行中，用于标志系统导入执行中
    def import_quarter_value(flag)
      @redis.watch(import_quarter_key) do
        # 如果其他客户端在此期间修改了 'import_system_key'，EXEC 将不会执行
        @redis.multi do |multi|
          ex = 2.hours
          multi.set(import_quarter_key, flag, ex: ex)
          @logger.info { "设置redis锁 #{import_quarter_key}「#{flag}」，过期时间「#{ex}」" }
        end
      end
    end

    # 释放标志
    def release_import_quarter_key
      unless import_system_client_quantity.zero?
        @logger.info { "当前导入系统客户端数量: #{import_system_client_quantity}，不释放redis锁 #{import_quarter_key}" }
        return
      end

      @redis.del(import_quarter_key)
      @logger.info { "当前导入系统客户端数量: #{import_system_client_quantity}，释放redis锁 #{import_quarter_key}" }
    end

    # import_quarter_key是否存在
    def exist_import_quarter_key
      return false if @is_force

      @redis.exists?(import_quarter_key)
    end

    # 导入系统频道名称
    def import_system_client_key
      "import_system_client:#{@quarter.id}"
    end

    # 执行导入系统的任务，执行过程中订阅频道，用来实时判断导入系统的客户端数
    def import_quarter
      @redis.incr(import_system_client_key)
      yield
    ensure
      system_client_quantity = @redis.decr(import_system_client_key)
      @redis.del(import_system_client_key) if system_client_quantity <= 0
    end

    # 返回订阅import_system_channel_key的客户端数量
    def import_system_client_quantity
      @redis.get(import_system_client_key)&.to_i || 0
    end

    private

    def after_run
      if @skip_after_import_rake || @skip_non_system_after_import_rake
        @logger.warn { 'skip after import rake...' }
        return
      end
      after_importer_rake
    end

    def importer_config
      AasOracleImporter.config
    end

    # 创建要导入的系统队列到redis 并且 通过redis key标识
    def generate_import_systems
      importer_names = import_system_names
      import_quarter_value = @redis.get(import_quarter_key)
      return if @redis.scard(import_system_key).positive? # 如果当前时间点已经有了导入系统redis值，则不再创建
      return if import_quarter_value.present? && import_quarter_value != 'start'

      if importer_names.blank?
        @logger.info { '创建导入系统空' }
        return
      end

      @redis.watch(import_system_key) do
        # 如果其他客户端在此期间修改了 'import_system_key'，EXEC 将不会执行
        @redis.multi do |multi|
          multi.sadd(import_system_key, importer_names)
        end
      end
      @logger.info { "*******#{import_system_key}********" }
      @logger.info { "创建导入系统#{importer_names}" }
    end

    # 获取导入的系统
    def import_systems
      # 如果有员工导入，优先导入员工
      # 如果有员工正在导入中，不断轮询，直到员工导入成功或者失败，才进行后面的导入
      loop do
        import_user_names = import_user_configs.map { |x| x['name'] }
        break if import_user_names.blank?

        import_user_names.each_with_index do |importer_name, index|
          next unless RedisClient.pop_specific_element(import_system_key, importer_name)

          @logger.info { "读取并删除导入员工importer：#{importer_name}，key：#{import_system_key}" }
          # @logger.info { "Import user system #{importer_name}, Worker: #{Parallel.worker_number}" }
          import_quarter_value('system_task') if index.zero?
          import_a_system(importer_name)
        end
        ::ActiveRecord::Base.connection.reconnect!
      end
      # 判断如果有正在导入中的员工，需要等待后重试，直到成功后return
      loop do
        user_import_statuses = QuarterUserImportStatus.where(quarter_id: @quarter.id)
        break unless user_import_statuses.any? { |x| x.pending? || x.processing? }

        time = 8
        @logger.info { "导入员工正在执行中，等待#{time}秒后重试" }
        sleep time
      end

      loop do
        importer_names = @redis.spop(import_system_key, parallel_in_processes)
        break if importer_names.blank?

        @logger.info { "*******#{import_system_key}********" }
        @logger.info { "读取并删除#{importer_names}" }
        importer_names.each_with_index do |importer_name, index|
          @logger.info { "Import system #{importer_name}, Worker: 1" }
          import_quarter_value('system_task') if index.zero?
          import_a_system(importer_name)
        end
        ::ActiveRecord::Base.connection.reconnect!
      end
    end

    def import_system_names
      all_importers = importer_config['importers']
      all_names = all_importers.map { |x| x['name'] }
      return all_names if @importer_names.blank? && @system_ids.blank? && !@only_import_user

      if @importer_names.present?
        @importer_names.each do |name|
          raise "No system called \"#{name}\"" unless all_names.include? name
        end
        @importer_names
      elsif @system_ids.present?
        all_importers.select { |x| @system_ids.include? x['bs_id'] }.map { |x| x['name'] }
      elsif @only_import_user
        all_importers.select { |x| x['is_user'] }.map { |x| x['name'] }
      end
    end

    # 同时运行的多进程数量, 默认与机器 cpu 逻辑核数相同
    def parallel_in_processes
      env_config = ENV['AAS_PARALLEL_IN_PROCESSES'].to_i
      number     = env_config.zero? ? Etc.nprocessors : env_config

      @logger.info { "Parallel running in processes #{number}" }
      number
    end

    def import_a_system(importer_name)
      importer = importer_class_for(importer_name).new(@quarter)
      begin
        importer.prepare_import
        importer.import
        after_run_system(importer)
      rescue DisabledBusinessSystem => e
        importer.import_status.skipped!
        @logger.error { e.message }
      rescue StandardError => e
        importer.import_status.failed!
        @logger.error { e.message }
        @logger.debug { e.backtrace.join("\n") }
      end
    end

    def check_business_systems
      @quarter       = Quarter.new
      importer_names = importer_config['importers'].map { |x| x['name'] }

      importer_names.each do |name|
        importer = importer_class_for(name).new(@quarter)
        importer.prepare_import
        importer.check
      end
    end

    def importer_class_for(name)
      Object.const_get("AasOracleImporter::#{name.camelize}Importer")
    end

    def server_config
      AasOracleImporter.config['server']
    end

    # after_importer_rake是否是after_import:todo
    def after_import_todo?
      server_config['after_importer_rake'] == 'after_import:todo'
    end

    def last_client?
      import_system_client_quantity < 2
    end

    # 开始跑批非系统任务
    def after_importer_rake
      return unless server_config['after_importer_rake']
      return if @skip_after_import_rake

      # Dir.chdir server_config['path']
      # system "bundle exec rails '#{server_config['after_importer_rake']}[#{@quarter.id}]'"

      # 如果已经有客户端在跑批了就退出
      if @redis.exists?(after_todo_task_key)
        @logger.info { "已经有客户端在跑批了，当前客户端退出" }
        return
      end

      if !last_client?
        @logger.info { "客户端数量: #{import_system_client_quantity}，不可以跑批" }
        return
      end

      begin
        server_path = ENV['EXECUTE_PATH'] || server_config['path']
        Dir.chdir server_path
        if after_import_todo?
          @logger.info { "*******#{after_todo_task_key}********" }
          @logger.info { '开始跑批' }
          @redis.set(after_todo_task_key, 'running', nx: true)
          system "java -jar aas.war -S rails 'after_import:todo_non_system[#{@quarter.id}]'"
        else
          system "java -jar aas.war -S rails '#{server_config['after_importer_rake']}[#{@quarter.id}]'"
        end
      ensure
        @redis.del(after_todo_task_key)
      end
    end

    # 开始单系统跑批任务，判断skip_after_import_rake配置参数
    def after_run_system(importer)
      if @skip_after_import_rake
        @logger.warn { 'skip after import rake...' }
        return
      end
      after_run_system_rake(importer)
    end

    # 开始单系统跑批任务
    def after_run_system_rake(importer)
      return if @skip_after_import_rake
      return unless after_import_todo?

      # importer.after_importer_rake
      Dir.chdir server_config['path']
      bs_id = importer.send(:the_system)&.id
      system "bundle exec rails 'after_import:todo_system[#{@quarter.id}, #{bs_id}]'" if bs_id.present?
    end

    # 是否有员工导入
    def import_user_configs
      # 获取配置里所有的导入key
      keys = @redis.smembers import_system_key
      all_importers = importer_config['importers']
      # 获取员工导入key
      # 筛选条件：通过is_user为true，或者bs_id为空判断。并且非外部系统
      all_importers
        .select { |x| keys.include? x['name'] }
        .select { |x| !x['name'].start_with?('external') && (x['bs_id'].blank? || x['is_user'].present?) }
    end
  end
end
