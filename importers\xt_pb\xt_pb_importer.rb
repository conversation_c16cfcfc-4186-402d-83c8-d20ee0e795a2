module AasOracleImporter
  class XtPbImporter < ImporterBase
    def config
      @bs_id       = 258
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @data1_permissions = []
      @data1_accounts_roles_permissions = []
      initialize_tables
    end

    def initialize_tables
      @table_accounts         = "#{@table_space}auth_user#{@sid_suffix}"
      @table_roles            = "#{@table_space}auth_group#{@sid_suffix}"
      @table_accounts_roles   = "#{@table_space}auth_user_groups#{@sid_suffix}"
      @table_menus            = "#{@table_space}roles_permission#{@sid_suffix}"
      @table_menus_roles      = "#{@table_space}roles_group_permission#{@sid_suffix}"
      @table_menus_accounts   = "#{@table_space}roles_user_permission#{@sid_suffix}"
      @table_parent_menus     = "#{@table_space}roles_menu#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_data1_account_permissions
      import_ledgers(XtPb::Account)
    end

    def destroy_exist_datas
      accounts = XtPb::Account.where(quarter_id: @quarter_id)
      XtPb::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      XtPb::Role.where(quarter_id: @quarter_id).delete_all
      XtPb::Data1Permission.where(quarter_id: @quarter_id).delete_all
      XtPb::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select id, id, username, is_active from #{@table_accounts}
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          account = XtPb::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '1'
          )
          @accounts << account

          if @display_status
            QuarterAccountInfo.create(
              account_id:         account.id,
              account_type:       'XtPb::Account',
              business_system_id: @bs_id,
              quarter_id:         @quarter_id,
              display_status:     r[3]&.to_s
            )
          end
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select id, id, name from #{@table_roles}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << XtPb::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select GROUP_ID, USER_ID from #{@table_accounts_roles}
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          XtPb::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select id, id, name, MENU_ID from #{@table_menus} where name is not null
      EOF
    end

    def import_menu_sql
      <<-EOF
        select id, code, name, parent_id from #{@table_parent_menus}
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_permissions_sql).each do |r|
          menu_id = r[3]
          name = full_menu_name(r[2], menu_id)
          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name
          }
          @data1_permissions << XtPb::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select GROUP_ID, PERMISSION_ID from #{@table_menus_roles}
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          XtPb::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_data1_account_permissions_sql
      <<-EOF
        select USER_ID, PERMISSION_ID from #{@table_menus_accounts}
      EOF
    end

    def import_data1_account_permissions
      select_db_datas(import_data1_account_permissions_sql).each do |r|
        account    = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
        permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
        next unless account && permission

        XtPb::Data1AccountsRolesPermission.create(
          quarter_id:          @quarter_id,
          account_id:          account.id,
          data1_permission_id: permission.id
        )
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 通过menu_id,找到全部的树形菜单
    def full_menu_name(menu_name, menu_id)
      names = full_menu_names(menu_id)
      names << menu_name
      names.select(&:present?).uniq.join(' -> ')
    end


    def full_menu_names(menu_id, names = [])
      return names.reverse if menu_id.nil? || menu_id.blank?

      sql = "#{import_menu_sql} where id = #{menu_id} and rownum <= 1"
      select_db_datas(sql).each do |r|
        parent_menu_id = r[3]
        parent_name    = r[2]
        if parent_name.present?
          names << parent_name
          full_menu_names(parent_menu_id, names)
        end
      end
      names.reverse
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
