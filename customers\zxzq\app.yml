---
defaults: &defaults
  customer_id: zxzq
  customer: 中信证券
  ipaddr: localhost
  log_level: DEBUG
  quarter_format: "%Y年%m月%d日"
  quarter_reduce_seconds: 28800
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: odbc
      conn_str: DRIVER=DM8;SERVER=*************;TCP_PORT=5238;UID=sysdba;PWD=******;ENCODING=utf8;
  agent:
    kingbase:
      coremail_db:
        db_host: ************
        db_port: 54321
        db_name: coremail
        db_user: coremail
        db_pass: coremail123
    mysql:
      hspb_db:
        db_host:
        db_name:
        db_user:
        db_pass:
      tyrz_db:
        db_host: *************
        db_name: citics_uum
        db_user: qxjh
        db_pass: qxjh@2023
    oracle:
      jira_db:
        db_host: ************
        db_port: 1521
        db_name: orcl
        db_user: tianlei
        db_pass: tianlei12345
    http:
      zxzq_users:
        base_uri: ''
  importers:
    - name: zxzq_users
      db_type: http
      tnsname: zxzq_users
      base_uri: http://*************/uumWebService/resourceWebService/services/userOrgService?wsdl
      app_key: <EMAIL>
      invoke_key: 7Har@WNxeY?!#q~u
    - name: jira_v8
      bs_id: 112
      db_type: oracle
      tnsname: jira_db
      table_space: 'jiradb.'
      sid_suffix: ''
      email_suffix: citics.com
    #  - name: hspb
    #    bs_id: 52
    #    db_type: mysql
    #    tnsname: hspb_db
    #    table_space: hspbdbdg_
    #    sid_suffix: ''
    #    trade_type_importer: true
    #    station_importer: true
    - name: tyrz
      bs_id: 400
      db_type: mysql
      tnsname: tyrz_db
      table_space: ''
      sid_suffix: ''
    - name: coremail
      bs_id: 401
      db_type: kingbase
      tnsname: coremail_db
      table_space: ''
      sid_suffix: ''

development:
  <<: *defaults
test:
  <<: *defaults
production:
  <<: *defaults
  server:
    path: "/opt/aas-app/aas"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: odbc
      conn_str: DRIVER=DM8;SERVER=*************;TCP_PORT=5238;UID=sysdba;PWD=******;ENCODING=utf8;
