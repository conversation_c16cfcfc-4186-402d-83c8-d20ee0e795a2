module AasOracleImporter
  class External4Importer < ImporterBase
    def config
      @bs_id       = 9004
      @external_bs = External::BusinessSystem.find_by(business_system_id: @bs_id)
      @check_sql   = !@external_bs.nil?
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @roles       = []

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
    end

    def initialize_tables
      @table_account      = "#{@table_space}accounts#{@sid_suffix}"
      @table_role         = "#{@table_space}roles#{@sid_suffix}"
      @table_account_role = "#{@table_space}account_roles#{@sid_suffix}"
      @table_menu         = "#{@table_space}permissions#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}role_permissions#{@sid_suffix}"
      @table_account_menu = "#{@table_space}accounts_permissions#{@sid_suffix}"
    end

    def import_to_do
      return if @external_bs.nil?

      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions
      import_data1_role_permissions
      import_account_permissions
      import_ledgers(External4::Account)
    end

    def import_accounts_sql
      "SELECT id, code, name, status, user_id, field1 FROM #{@table_account} WHERE external_business_system_id = #{@external_bs.id}"
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql).each do |r|
          if @external_bs && @external_bs.use_template && @external_bs.template_code == 'ecert'
            name = r[5] + ' - ' + r[2]
          else
            name = r[2]
          end
          @accounts << External4::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       name,
            status:     r[3],
            objid:      r[4]
          )
        end
      end
    end

    def import_roles_sql
      "SELECT id, code, name FROM #{@table_role} WHERE external_business_system_id = #{@external_bs.id} AND status=1"
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql).each do |r|
          @roles << External4::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      "SELECT external_role_id, external_account_id FROM #{@table_account_role}"
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && role

          External4::AccountsRole.create(account_id: account.id, role_id: role.id)
        end
      end
    end

    def import_data1_permissions_sql
      "SELECT id, code, name, category, additional_permission, show_field FROM #{@table_menu} WHERE external_business_system_id = #{@external_bs.id}"
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_data1_permissions_sql).each do |r|
          level1_name = replace_blank_name(r[2])
          level2_name = replace_blank_name(r[3])
          level3_name = replace_blank_name(r[4])
          show_field  = r[5]

          data_json = {}
          if show_field.present?
            show_field = JSON.parse(show_field, symbolize_names: true)
            show_field.each do |key, value|
              data_json[key] = value
            end
          end

          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: level2_name,
            level3_name: level3_name,
            data_json:   data_json
          }
          @data1_permissions << External4::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      "SELECT external_role_id, external_permission_id FROM #{@table_role_menu}"
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          External4::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def import_account_permissions_sql
      "SELECT external_account_id, external_permission_id FROM #{@table_account_menu}"
    end

    def import_account_permissions
      ActiveRecord::Base.transaction do
        @database.exec(import_account_permissions_sql).each do |r|
          account = @accounts.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless account && permission

          External4::Data1AccountsRolesPermission.create(
            quarter_id: @quarter_id,
            account_id: account.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end

    def the_system
      bs = BusinessSystem.find_by(id: @bs_id)
      @logger.info { "找不到外部系统: #{@bs_id}" } if bs.nil?
      bs
    end
  end
end
