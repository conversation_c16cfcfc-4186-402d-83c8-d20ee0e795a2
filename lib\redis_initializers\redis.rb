# frozen_string_literal: true

require 'redis'
require_relative 'initialize_helpers/redis_helper'

Redis.current = Redis.new(InitializeHelpers::RedisHelper.initialize_options)

# 命名空间
module AasProvider
  DEFAULTS = {
    timeout:       8,
    tcp_keepalive: 30
  }.freeze

  FAKE_INFO = {
    'redis_version'          => '9.9.9',
    'uptime_in_days'         => '9999',
    'connected_clients'      => '9999',
    'used_memory_human'      => '9P',
    'used_memory_peak_human' => '9P'
  }.freeze

  # redis 连接
  class RedisConnection
    class << self
      def create(options = {})
        options.keys.each do |key|
          options[key.to_sym] = options.delete(key)
        end
        build_client(options)
      end

      private

      def build_client(options)
        namespace = options[:namespace]
        client    = Redis.new client_opts(options)
        if namespace
          begin
            require 'redis/namespace'
            Redis::Namespace.new(namespace, redis: client)
          rescue LoadError
            message = <<-MSG
              Your Redis configuration uses the namespace '#{namespace}' but the redis-namespace gem is not included in the Gemfile.
              Add the gem to your Gemfile to continue using a namespace. Otherwise, remove the namespace parameter.
            MSG
            Rails.logger.error(message)
            exit(-127)
          end
        else
          client
        end
      end

      def client_opts(options)
        initialize_options = InitializeHelpers::RedisHelper.initialize_options
        opts               = initialize_options.merge(options)
        opts.delete(:namespace) if opts[:namespace]

        if opts[:network_timeout]
          opts[:timeout] = opts[:network_timeout]
          opts.delete(:network_timeout)
        end

        opts[:driver] ||= 'ruby'

        # Issue #3303, redis-rb will silently retry an operation.
        # This can lead to duplicate jobs if Sidekiq::Client's LPUSH
        # is performed twice but I believe this is much, much rarer
        # than the reconnect silently fixing a problem; we keep it
        # on by default.
        opts[:reconnect_attempts] ||= 1

        opts
      end
    end
  end

  def self.redis_delete_matched!(key, count = 25)
    unless Rails.env.production?
      Rails.cache.delete_matched(key)
      return true
    end

    cursor, keys = redis.scan(0, { match: key.to_s, count: count })

    redis.del(keys) if keys.present?
    redis_delete_matched!(key, count * 2) unless cursor.to_i.zero?
    true
  end

  def self.options
    @options ||= DEFAULTS.dup
  end

  def self.options=(opts)
    @options = opts
  end

  def self.redis
    @redis ||= RedisConnection.create(options)
  end

  def self.redis_info
    # admin commands can't go through redis-namespace starting
    # in redis-namespace 2.0
    if redis.respond_to?(:namespace)
      redis.redis.info
    else
      redis.info
    end
  rescue Redis::CommandError
    # 2850 return fake version when INFO command has (probably) been renamed
    raise unless ex.message =~ /unknown command/

    FAKE_INFO
  end
end





