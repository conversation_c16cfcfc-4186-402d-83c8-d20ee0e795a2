module <PERSON><PERSON><PERSON>O<PERSON>
  def self.table_name_prefix
    'zhiyuan_oa_'
  end
end

class ZhiyuanOa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class ZhiyuanOa::Menu < ActiveRecord::Base
  has_and_belongs_to_many :roles
end

class ZhiyuanOa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class ZhiyuanOa::MenusRoles < ActiveRecord::Base; end
class ZhiyuanOa::AccountsRoles < ActiveRecord::Base; end

class User < ActiveRecord::Base
  belongs_to :department, optional: true
end
class Department < ActiveRecord::Base
  has_many :users
  has_many :children, class_name: "Department", foreign_key: "parent_id"
  belongs_to :parent, class_name: "Department", optional: true
end