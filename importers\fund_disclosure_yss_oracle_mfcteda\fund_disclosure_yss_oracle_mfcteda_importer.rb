# frozen_string_literal: true

module AasOracleImporter
  # 信披系统
  class FundDisclosureYssOracleMfctedaImporter < ImporterBase
    include ConvertTools
    CodeTableRecord = Struct.new(:fd_code, :o32_code)

    def config
      @bs_id                  = importer_config['bs_id']
      @email_suffix           = importer_config['email_suffix']
      @table_space            = importer_config['table_space']
      @sid_suffix             = importer_config['sid_suffix']
      @enable_fund_code_table = importer_config['enable_fund_code_table']
      @fund_code_table_space  = importer_config['fund_code_table_space']

      initialize_tables
    end

    def initialize_tables
      @table_view_port = "#{@table_space}FA_VB_PORT_BASEINFO#{@sid_suffix}"
      @table_view_manager = "#{@table_space}XBRL_T_INVTMANAGER#{@sid_suffix}"
      @table_view_port_manager = "#{@table_space}XBRL_T_INVTMANAGER_DETAIL#{@sid_suffix}"
      @table_fund_info = 'ods.v_dc_to_zm_fundinfo'
    end

    # def sql_check_ignore_list
    #   ignore_list = []
    #   ignore_list << :import_fund_code_table_sql unless @enable_fund_code_table
    #   ignore_list
    # end

    def import_to_do
      destroy_exist_data
      import_accounts
      # import_fund_code_table
      import_ledgers(FundDisclosure::Account)
    end

    def destroy_exist_data
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
       select
        manager.C_IVTM_CODE,
        manager.C_IVTM_NAME,
        manager.C_DUTY,
        port.C_PORT_CODE,
        port.C_PORT_NAME,
        manager.D_POST_IN,
        manager.D_POST_OFF,
        manager.N_STATE,
        fund.V_CREATOR
      from
        #{@table_view_port}  port,
        #{@table_view_manager}  manager,
        #{@table_view_port_manager}  port_manager,
        #{@table_fund_info} fund
      where
        port_manager.C_PORT_CODE = port.C_PORT_CODE
      and
        port_manager.C_IVTM_CODE = manager.C_IVTM_CODE
      and
        fund.C_FUND_CODE = port.C_PORT_CODE
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          user_code, user_name, role_name, fund_code, fund_name, take_post_date, leave_post_date, n_state, type = r
          next unless user_name

          take_post_date  = take_post_date.to_date if take_post_date
          leave_post_date = leave_post_date.to_date if leave_post_date
          is_valid        = is_valid_date(take_post_date, leave_post_date)
          status          = n_state.to_s == '1' && is_valid
          account         = find_or_create_account(user_code, user_name, status)
          role            = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, name: role_name)
          fund_type       = type.to_s == 'ZH' ? '专户' : '公募'

          # 根据客户需求，目前导入所有历史权限
          # next unless in_active_date?(take_post_date, leave_post_date)

          # 放在判定 时间的下面，只记录有效基金
          fund = find_or_create_fund(fund_code, fund_name, fund_type)

          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  take_post_date,
            leave_post_date: leave_post_date,
            quarter_id:      @quarter_id
          )
        end
      end
    end

    # def import_fund_code_table_sql
    #   <<-SQL
    #     select cpzdm, xsdm from #{@fund_code_table_space}oa_uf_ipo where cpzdm != xsdm
    #   SQL
    # end

    # def import_fund_code_table
    #   return unless @enable_fund_code_table

    #   # import_code_table

    #   ActiveRecord::Base.transaction do
    #     FundDisclosure::Fund.where(quarter_id: @quarter_id).each do |fund|
    #       update_fund_o32_code(fund)
    #     end
    #   end
    # end

    # def import_code_table
    #   @code_records = []
    #   @database.exec(import_fund_code_table_sql) do |r|
    #     # 取信披公示和o32 代码不一致的记录
    #     @code_records << CodeTableRecord.new(r[0], r[1])
    #   end
    # end

    # def update_fund_o32_code(fund)
    #   record = @code_records.find { |x| x.fd_code == fund.code }
    #   return unless record

    #   fund.update(o32_fund_codes: add_o32_fund_codes(fund.o32_fund_codes, record.o32_code))
    # end

    def find_or_create_account(code, name, status)
      code ||= name

      user = FundDisclosure::Account.find_or_create_by(quarter_id: @quarter_id, code: code)
      user.update(name: name, status: status)
      user
    end

    def find_or_create_fund(code, name, fund_type)
      fund_code           = convert_fund_code(code)
      fund                = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: fund_code, fund_type: fund_type)
      fund.name           = name
      fund.o32_fund_codes = add_o32_fund_codes(fund.o32_fund_codes, fund_code)
      fund.save
      fund
    end

    # 是否在有效期内
    def is_valid_date(take_post_date, leave_post_date)
      return true if take_post_date.nil? && leave_post_date.present? && Date.current <= leave_post_date
      return true if take_post_date.present? && leave_post_date.nil? && Date.current >= take_post_date
      return true if take_post_date.nil? && leave_post_date.nil?

      take_post_date.present? && leave_post_date.present? && Date.current.between?(take_post_date, leave_post_date)
    end
  end
end

