module AasOracleImporter
  class HsYujiataImporter < ImporterBase
    def config
      @bs_id       = 308
      @accounts    = []
      @roles       = []
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      @data1_permissions = []
      @data1_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}yebuser#{@sid_suffix}"
      @table_role         = "#{@table_space}ROLE#{@sid_suffix}"
      @table_account_role = "#{@table_space}USER_ROLE#{@sid_suffix}"
      @table_menu         = "#{@table_space}PRIVILEGE#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}ROLE_PRIVILEGE#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_datas
      import_accounts
      import_roles
      import_accounts_roles
      import_data1_permissions
      import_data1_role_permissions
      import_ledgers(HsYujiata::Account)
    end

    def destroy_exist_datas
      accounts = HsYujiata::Account.where(quarter_id: @quarter_id)
      HsYujiata::AccountsRole.where(account_id: accounts.pluck(:id)).delete_all
      accounts.delete_all
      HsYujiata::Role.where(quarter_id: @quarter_id).delete_all
      HsYujiata::Data1Permission.where(quarter_id: @quarter_id).delete_all
      HsYujiata::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts_sql
      <<-EOF
        select USER_INDEX, USER_NO, USER_NAME, USER_STATE from #{@table_account} where IS_DELETE = '1'
      EOF
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << HsYujiata::Account.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2],
            status:     r[3]&.to_s == '0'
          )
        end
      end
    end

    def import_roles_sql
      <<-EOF
        select ROLE_INDEX, ID, ROLE_NAME from #{@table_role} where IS_DELETE = '1'
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_roles_sql).each do |r|
          @roles << HsYujiata::Role.create(
            quarter_id: @quarter_id,
            source_id:  r[0],
            code:       r[1],
            name:       r[2]
          )
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select ROLE_INDEX, USER_INDEX from #{@table_account_role} where IS_DELETE = '1'
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_roles_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          HsYujiata::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        select PRIVILEGE_INDEX, ID, NAME, PRIVILEGE_TYPE, PARENT_ID from #{@table_menu} where IS_DELETE = '1'
      EOF
    end

    def import_data1_permissions
      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index = 4
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = r[level1_name_index]
          parent_id_value = r[parent_id_index]
          full_name = full_name(import_data1_permissions_sql, name, parent_id_value, level1_name_index, parent_id_index)
          level1_name = replace_blank_name(full_name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   r[0],
            code:        r[1],
            level1_name: level1_name,
            level2_name: level2_name_text(r[3])
          }
          @data1_permissions << HsYujiata::Data1Permission.create(json)
        end
      end
    end

    # 枚举值
    def level2_name_text(name)
      case name
      when 'MENU' then '菜单'
      when 'BUTTON' then '按钮'
      else
        name
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
        select ROLE_INDEX, PRIVILEGE_INDEX from #{@table_role_menu} where IS_DELETE = '1'
      EOF
    end

    def import_data1_role_permissions
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          next unless permission && role

          HsYujiata::Data1AccountsRolesPermission.create(
            quarter_id:          @quarter_id,
            role_id:             role.id,
            data1_permission_id: permission.id
          )
        end
      end
    end

    def select_db_datas(sql)
      sql = sql.delete(';')
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << r.to_a
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank? || !parent_id.match(/^\d+$/).present?

      sql = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[1]
      new_sql = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' and rownum <= 1"
      select_db_datas(new_sql).each do |r|
        parent_name = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
