source 'https://gems.ruby-china.com'

customer_gems = File.join(__dir__, 'CUSTOMER_GEMS')
eval IO.read(customer_gems) if File.exist? customer_gems

# gem 'activerecord', '~> 5.1.7'
# gem 'activesupport', '~> 5.1.7'
#gem 'mysql2', '~> 0.5.6'

# gem 'activerecord-jdbc-adapter', '51.7', :platform => :jruby
gem 'activerecord', '~> 6.1.2'
gem 'activesupport', '~> 6.1.2'
gem 'activerecord-jdbc-adapter', '~> 61.2', :platform => :jruby
gem 'jdbc-mysql', '~> 8.0', '>= 8.0.17', :platform => :jruby

#gem 'pg', '~> 1.5.4'
# gem 'odbc_adapter', path: '../odbc_adapter'
#gem 'god'
#gem 'ruby-oci8'
gem 'httparty'
#gem 'caxlsx', '~> 3.1.1'
#gem 'axlsx'
gem 'redis', '4.5.1'
gem 'rake'
# 获取文件头信息，在 linux 平台依赖于 file-devel rpm 包
# gem 'ruby-filemagic'
# 读取 xlsx 文件
#gem 'rubyXL', '~> 3.3.29'
# gem 'tiny_tds'
# gem 'net-ldap'
gem 'parallel', '~> 1.20.1'
gem 'bulk_insert' # 批量插入
gem 'ancestry'
gem 'trading_days', '0.3.2'
# gem 'sm2-crypto'
group :development do
  gem 'warbler', require: false
end

group :test do
  gem "bundler"
  gem "rspec", "~> 3.0"
end
