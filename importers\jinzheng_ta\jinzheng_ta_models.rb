module <PERSON>zhengTa
  def self.table_name_prefix
    'jinzheng_ta_'
  end
end

class JinzhengTa::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  has_and_belongs_to_many :menus
end

class JinzhengTa::Menu < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :roles
end

class JinzhengTa::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  has_and_belongs_to_many :menus
end

class JinzhengTaMenusRoles < ActiveRecord::Base; end
class JinzhengTaAccountsRoles < ActiveRecord::Base; end
class JinzhengTaAccountsMenus < ActiveRecord::Base; end
