account_list:
  action: GetSearchData
  controler: User
  argument: offset
  pagesize: 999
  body:
  - key: limit
    value: 999
  - key: offset
    value: 0
update_accounts:
  action: ExGetUserInfo
  controler: User
  argument: username
  body:
  - key: username
    value: ''
role_list:
  action: GetRoleListDataCloud
  controler: Role
  argument: start
  pagesize: 999
  body:
  - key: limit
    value: 999
  - key: start
    value: 0
accounts_roles_list:
  action: GetSearchData
  controler: User
  argument: offset
  pagesize: 999
  body:
  - key: limit
    value: 999
  - key: offset
    value: 0
data1_permissions_list:
  action: GetRcListDataCloud
  controler: Resource
  argument: start
  pagesize: 999
  body:
  - key: limit
    value: 999
  - key: start
    value: 0
  - key: type
    value: 65535
  enum:
    rctype:
      '0': web 资源
      '1': tcp 资源
      '2': l3vpn 资源

# 查询角色信息
import_data1_role_permissions:
  action: GetRoleDataCloud
  controler: Role
  argument: name
  body:
  - key: name
    value: ''
