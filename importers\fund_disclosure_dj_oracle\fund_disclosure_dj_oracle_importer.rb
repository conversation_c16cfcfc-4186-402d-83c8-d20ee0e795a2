# frozen_string_literal: true

module AasOracleImporter
  # 点将的信披系统
  class FundDisclosureDjOracleImporter < ImporterBase
    include ConvertTools
    CodeTableRecord = Struct.new(:fd_code, :o32_code)

    def config
      @bs_id                  = importer_config['bs_id']
      @table_space            = importer_config['table_space']
      @sid_suffix             = importer_config['sid_suffix']
      @enable_fund_code_table = importer_config['enable_fund_code_table']
      @fund_code_table_space  = importer_config['fund_code_table_space']

      initialize_tables
    end

    def initialize_tables
      @table_user    = "#{@table_space}tb_recruitment_fminfo#{@sid_suffix}"
      @table_product = "#{@table_space}tb_recruitment_fmcollect#{@sid_suffix}"
      @table_fund    = "#{@table_space}t_setinfo#{@sid_suffix}"
    end

    def sql_check_ignore_list
      ignore_list = []
      ignore_list << :import_fund_code_table_sql unless @enable_fund_code_table
      ignore_list
    end

    def import_to_do
      destroy_exist_data
      import_accounts
      import_fund_code_table
      import_ledgers(FundDisclosure::Account)
    end

    def destroy_exist_data
      FundDisclosure::Account.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Role.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::Fund.where(quarter_id: @quarter_id).destroy_all
      FundDisclosure::FundPosition.where(quarter_id: @quarter_id).destroy_all
    end

    def import_accounts_sql
      <<-SQL
        select u.id,
          u.fundmanagername,
          u.onjobstatus,
          f.setcode,
          f.setname,
          p.announceofficedate,
          p.announceoutdate
        from #{@table_user} u,
             #{@table_product} p,
             #{@table_fund} f
        where u.id = p.fundmanagerid
        AND   p.fundcode = f.setcode
      SQL
    end

    def import_accounts
      # 永赢基金这里只有公募基金，所以角色只有基金经理
      role = FundDisclosure::Role.find_or_create_by(quarter_id: @quarter_id, name: '基金经理')

      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          user_code, user_name, user_status, fund_code, fund_name, take_post_date, leave_post_date = r

          next unless user_name

          account         = find_or_create_account(user_code, user_name, user_status)

          take_post_date  = Date.parse(take_post_date) if take_post_date
          leave_post_date = Date.parse(leave_post_date) if leave_post_date

          # 根据客户需求，目前导入所有历史权限
          # next unless in_active_date?(take_post_date, leave_post_date)

          # 放在判定 时间的下面，只记录有效基金
          fund = find_or_create_fund(fund_code, fund_name)

          FundDisclosure::FundPosition.create(
            fund_id:         fund.id,
            account_id:      account.id,
            role_id:         role.id,
            take_post_date:  take_post_date,
            leave_post_date: leave_post_date,
            quarter_id:      @quarter_id
          )
        end
      end
    end

    def import_fund_code_table_sql
      <<-SQL
        select cpzdm, xsdm from #{@fund_code_table_space}oa_uf_ipo
      SQL
    end

    def import_fund_code_table
      return unless @enable_fund_code_table

      import_code_table

      ActiveRecord::Base.transaction do
        FundDisclosure::Fund.where(quarter_id: @quarter_id).each do |fund|
          update_fund_o32_code(fund)
        end
      end
    end

    def import_code_table
      @code_records = []
      @database.exec(import_fund_code_table_sql) do |r|
        # 取信披公示和o32 代码不一致的记录
        @code_records << CodeTableRecord.new(r[0], r[1]) if r[0] != r[1]
      end
    end

    def update_fund_o32_code(fund)
      record = @code_records.find { |x| x.fd_code == fund.code }
      return unless record

      fund.update(o32_fund_codes: add_o32_fund_codes(fund.o32_fund_codes, record.o32_code))
    end

    def find_or_create_account(code, name, status)
      user        = FundDisclosure::Account.find_or_initialize_by(quarter_id: @quarter_id, code: code)
      user.name   = name
      user.status = (status.to_i == 1)
      user.save
      user
    end

    def find_or_create_fund(code, name)
      fund_code = convert_fund_code(code)
      fund      = FundDisclosure::Fund.find_or_initialize_by(quarter_id: @quarter_id, code: fund_code)
      fund.name = name
      fund.o32_fund_codes = add_o32_fund_codes(fund.o32_fund_codes, fund_code)
      fund.save
      fund
    end
  end
end



