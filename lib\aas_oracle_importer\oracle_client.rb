# frozen_string_literal: true
module AasOracleImporter
  # Oracle 客户端
  class OracleClient < DatabaseClient
    def initialize(database_type, tnsname)
      super
    end

    def exec(sql)
      if block_given?
        @database.exec(sql) do |row|
          yield convert_encoding(row)
        end
      else
        # 此时返回的是指针，不支持 convert_encoding
        @database.exec(sql)
      end
    end

    private

    def initialize_driver
      load_driver_gem
      databases     = AasOracleImporter.config['agent']['oracle']
      database_info = databases[tnsname]
      database_info['db_port'] ||= 1521
      database_password = ConvertTools::Cryptology.decrypt_if_env(database_info['db_pass'])

      @database = OCI8.new(
        database_info['db_user'],
        database_password,
        oracle_service_name
      )
      @logger.debug { "#{@tnsname} charset_name: " + @database.database_charset_name }
    rescue OCIError => e
      raise OCIError, message_prefix + e.message
    end

    def load_driver_gem
      require 'oci8'
    rescue LoadError
      @logger.error('LoadError: Not found gem \'ruby-oci8\'.')
      exit(-127)
    end

    def oracle_service_name
      if database_info['db_host']
        "#{database_info['db_host']}:#{database_info['db_port']}/#{database_info['db_name']}"
      else
        database_info['db_name']
      end
    end
  end
end
