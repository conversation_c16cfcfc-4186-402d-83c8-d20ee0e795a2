
module AasOracleImporter

  class EfundsUserImporter < ImporterBase
    def config
      @base_url = importer_config['base_url'] #glajj.
      @token = importer_config['token'] #''
      @reqSys = importer_config['reqSys']
      @pass_company = importer_config['pass_company']
      @departments = []
    end

    def import_to_do
      import_departments
      import_users
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    def import_departments
      datas = send_request('/comsvr/sqlservice/zhdc_org_element')['data']
      datas.each do |data|
        next if data['NO'].blank?
        department = Department.find_or_initialize_by(code: data['NO'])
        department.name = data['deptName']
        department.inservice = data['isAvailable'].to_i == 1
        department.save
        @departments << department
      end

      datas.each do |data|
        next if data['parentNO'].blank?
        parent_department = @departments.find{|x| x.code.to_s == data['parentNO'].to_s}
        department = @departments.find{|x| x.code.to_s == data['NO'].to_s}
        next unless parent_department && department
        department.parent_id = parent_department.id
        department.level = data['deptNOLevel'].to_s.split("/").size
        department.save
      end
    end

    def import_users
      datas = send_request('/comsvr/sqlservice/zhdc_org_person')['data']
      datas.each do |data|
        next if @pass_company.include?(data['companyName']) || data['loginName'].blank? || data['name'].blank?
        if (data['isavailable'] == 1 && data['isMainDept'] == 1 ) || data['isavailable'] == 0
          code = data['loginName']
          department = Department.find_by(code: data['parentNO'].to_s)
          user = User.find_or_initialize_by(code: code)
          user.name = data['name']
          user.login_name = data['loginName']
          user.inservice = data['isavailable'].to_i == 1
          user.position = data['postName']
          user.email = data['email']
          user.department_id = department.id if department
          user.save
        end
      end
    end

    def send_request(uri)
      require 'httparty'
      body = {
        "reqSys" => @reqSys,
        "page" => 1,
        "size" => 10000
      }

      response = HTTParty.post("#{@base_url}#{uri}", 
                                  headers: headers,
                                  body: body.to_json
                                )
      result = JSON.parse response.body
      pp result.to_s
      if result['retCode'].to_i != 0 || result['data'].size == 0
        @logger.error "#{@key} 接口响应错误"
        @logger.error result
        return
      end

      result
    end

    def headers
      { 
        'Esb-Token' => @token,
        'Content-Type' => 'application/json' 
      }
    end

  end
end



