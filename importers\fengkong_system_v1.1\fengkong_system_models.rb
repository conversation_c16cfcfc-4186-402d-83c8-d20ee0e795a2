module FengkongSystem
  def self.table_name_prefix
    'fengkong_system_'
  end
end

class JsonWithSymbolizeNames
  def self.load(string)
    return unless string

    JSON.parse(string, symbolize_names: true)
  end

  def self.dump(object)
    object.to_json
  end
end

class FengkongSystem::Account < ActiveRecord::Base
  has_and_belongs_to_many :roles
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class FengkongSystem::AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :permission
  validates :permission_id, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class FengkongSystem::Role < ActiveRecord::Base
  has_and_belongs_to_many :accounts
  validates :code, presence: true
  validates :name, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end


class FengkongSystem::Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :permission_type, presence: true 
  validates :quarter_id, presence: true 

  serialize :data_json, JsonWithSymbolizeNames
end


class FengkongSystem::Data2Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :quarter_id, presence: true 

  serialize :data_json, JsonWithSymbolizeNames
end

class FengkongSystem::Data2AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :data2_permission
  validates :data2_permission_id, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end

class FengkongSystem::Data3Permission < ActiveRecord::Base
  validates :code, presence: true
  validates :name, presence: true 
  validates :quarter_id, presence: true 

  serialize :data_json, JsonWithSymbolizeNames
end

class FengkongSystem::Data3AccountsRolesPermission < ActiveRecord::Base
  belongs_to :quarter
  belongs_to :data3_permission
  validates :data3_permission_id, presence: true
  validates :quarter_id, presence: true

  serialize :data_json, JsonWithSymbolizeNames
end