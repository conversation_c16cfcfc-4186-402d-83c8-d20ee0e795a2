# config/app.yml for rails-settings-cached
defaults: &defaults
  customer_id: zrfunds
  customer: 中融基金
  ipaddr: localhost
  log_level: DEBUG
  server:
    path: "/Users/<USER>/Coding/sdata/account_audit_system"
    after_importer_rake: after_import:todo
    operator_id: 1
    database:
      adapter: mysql2
      encoding: utf8
      database: aas_zrfunds_development
      username: root
      password: 123456
      host: 127.0.0.1
  agent:
    oracle:
      oa:
        db_name: ZHIYUAN_OA_DG
        db_user: oa4rms
        db_pass: zrjj@1234
      qingsuan:
        db_name: adgds
        db_user: testro
        db_pass: testro_1234
      zhixiao:
        db_name: adgds
        db_user: testro
        db_pass: testro_1234
      dingdan:
        db_name: adgds
        db_user: testro
        db_pass: testro_1234
      o32:
        db_name: adgtrade
        db_user: trade_userright
        db_pass: trade_userright_2021
      guzhi:
        db_name: adgfa
        db_user: zrf_userright
        db_pass: zrf_userright_2021
      ta:
        db_name: adgta
        db_user: ta_userright
        db_pass: zrjj@0510
      touyan_db:
        db_host: **************
        db_port: 1521
        db_name: sirmdb
        db_user: auduser
        db_pass: B85b51ae5F
      fxq_db:
        db_host: ***********
        db_port: 1521
        db_name: aml
        db_user: auduser
        db_pass: B85b51ae5F
      dw_db:
        db_host: ***********
        db_port: 1521
        db_name: ctpdb
        db_user: readonly
        db_pass: readonly
    ldap:
      dc:
        host: *************
        port: 389
        base: dc=zhongrong,dc=local
        user: liuximeng
        password: tNLJHtD9QF
    mysql: {}
  importers:
    # - name: zhiyuan_oa
    #   bs_id: 201
    #   db_type: oracle
    #   tnsname: oa
    #   table_space: v3xuser.
    #   import_account: false
    - name: zrfunds_hr
      db_type: oracle
      tnsname: dw_db
      table_space: dmdc.
    - name: jiaoyi
      bs_id: 31
      db_type: oracle
      tnsname: o32
      table_space: zrtrade.
      sid_suffix: ''
      temporary: false
    - name: guzhi
      bs_id: 24
      db_type: oracle
      tnsname: guzhi
      table_space: zrfa.
      sid_suffix: ''
      hs_customer_code: 70879
    - name: hs_qingsuan
      bs_id: 25
      db_type: oracle
      tnsname: qingsuan
      table_space: hsliqpower.
      sid_suffix: ''
    - name: zhixiao_zhongxin
      bs_id: 27
      db_type: oracle
      tnsname: zhixiao
      table_space: hsds.
      sid_suffix: ''
      sub_system: CENTER
    - name: zhixiao_guitai
      bs_id: 28
      db_type: oracle
      tnsname: zhixiao
      table_space: hsds.
      sid_suffix: ''
      sub_system: COUNTER
    - name: yuancheng_guitai
      bs_id: 29
      db_type: oracle
      tnsname: zhixiao
      table_space: hsds.
      sid_suffix: ''
      sub_system: REMOTE
    - name: guohu_sa_order
      bs_id: 42
      db_type: oracle
      tnsname: dingdan
      table_space: hsord.
      sid_suffix: ''
    - name: guohu_ta
      bs_id: 30
      db_type: oracle
      tnsname: ta
      table_space: ta4.
      sid_suffix: ''
    - name: guohu_sa_etf
      bs_id: 40
      db_type: oracle
      tnsname: ta
      table_space: etfta.
      sid_suffix: ''
    - name: jinzheng_ta
      bs_id: 32
      db_type: oracle
      tnsname: ta
      table_accounts: kd_lofta.kd_userid
      table_roles: kd_lofta.kd_role
      table_accounts_roles: kd_lofta.kd_rolemember
      table_menus: kd_lofta.kd_sysmenu
      table_menus_roles: kd_lofta.kd_menuright_role
      table_accounts_menus: kd_lofta.kd_menuright_user
    - name: xiening_touyan_new
      bs_id: 38
      db_type: oracle
      tnsname: touyan_db
      table_space: sirm.
      sid_suffix: ''
      display_status: true

    - name: fanxiqian
      bs_id: 70
      db_type: oracle
      tnsname: fxq_db
      table_space: aml.
      sid_suffix: ''
      data1_parent_code: true
      c_versiontype:
      - fund
    - name: ldap_users
      bs_id: 264
      db_type: ldap
      tnsname: dc
      # 正则匹配名字的字段
      regular_field: 'OU'
      # 是否判断座机号，没座机号的不导入
      telephonenumber: false
      # 是否员工部门，适用于员工的部门信息不明确的，根据部门下的员工修改员工的部门id
      update_user_dept: false
      account_filter: "(&(objectcategory=person)(objectClass=user))"
      account_attrs:
      - cn
      - name
      - samaccountname
      - mail
      - msexchwhenmailboxcreated
      top_ou:
      - 中融基金
development:
  <<: *defaults

test:
  <<: *defaults

production:
  <<: *defaults
  server:
    path: '/opt/aas'
    after_importer_rake: 'after_import:todo'
    operator_id: 1
    database:
      adapter:  mysql2
      encoding: utf8
      database: aas_zrfunds_production
      username: root
      password: <%= ENV['AAS_DATABASE_PASSWORD'] %>
      host: 127.0.0.1
