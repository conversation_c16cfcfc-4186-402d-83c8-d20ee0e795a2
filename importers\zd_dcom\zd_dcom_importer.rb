module AasOracleImporter
  class ZdDcomImporter < ImporterBase

    def config
      @bs_id = importer_config['bs_id']
      @accounts = []
      @data1_permissions = []
    end

    def import_to_do
      return if file_names.blank?

      destroy_exist_datas
      import_accounts
      import_data1_permissions
      import_data1_account_permissions
      import_ledgers(ZdDcom::Account)
    end

    def destroy_exist_datas
      ZdDcom::Account.where(quarter_id: @quarter_id).delete_all
      ZdDcom::Role.where(quarter_id: @quarter_id).delete_all
      ZdDcom::Data1Permission.where(quarter_id: @quarter_id).delete_all
      ZdDcom::Data1AccountsRolesPermission.where(quarter_id: @quarter_id).delete_all
    end

    def import_accounts
      data = []
      file_paths.each do |file_path|
        lines = read_csv(file_path)
        lines.each_with_index do |row, index|
          next if index.zero? || row.nil? || row[0].blank? || row[1].blank?

          data << [row[0].to_s.gsub(/\[|\]/, ''), row[1].to_s.gsub(/\[|\]/, '')]
        end
      end
      data.uniq!
      data.each do |row|
        code = row[0]
        name = row[1]
        @accounts << ZdDcom::Account
                       .create_with(source_id: code, status: true)
                       .find_or_create_by(
                         quarter_id: @quarter.id,
                         code:       code,
                         name:       name
                       )
      end
    end

    def import_data1_permissions
      data = []
      file_paths.each do |file_path|
        lines = read_csv(file_path)
        uniq_lines = lines.from(1).reject { |x| x[2].to_s.include?('类型权限') }.uniq { |x| x[2] }
        uniq_lines.each do |row|
          next if row.nil? || row[2].blank? || row[3].blank?

          data << [row[2].to_s.gsub(/\[|\]/, ''), row[3].to_s.gsub(/\[|\]/, '')]
        end
      end
      data.uniq!
      data.each do |row|
        code = row[0]
        name = row[1]

        json = {
          quarter_id:  @quarter_id,
          code:        code,
          level1_name: name
        }
        @data1_permissions << ZdDcom::Data1Permission
                                .create_with(source_id: code)
                                .find_or_create_by(json)
      end
    end

    def import_data1_account_permissions
      data = []
      file_paths.each do |file_path|
        lines = read_csv(file_path)
        account_code = nil
        lines.each_with_index do |row, index|
          next if index.zero? || row.nil?

          permission_code = row[2].to_s.gsub(/\[|\]/, '')
          account_code = row[0].to_s.gsub(/\[|\]/, '') if row[0].present?
          account = @accounts.find { |x| x.source_id.to_s == account_code }
          permission = @data1_permissions.find { |x| x.source_id.to_s == permission_code }
          next unless account && permission

          data << [account.id, permission.id]
        end
      end
      data.uniq!
      data.each do |row|
        ZdDcom::Data1AccountsRolesPermission.find_or_create_by(
          quarter_id:          @quarter_id,
          account_id:          row[0],
          data1_permission_id: row[1]
        )
      end
    end

    protected

    def file_names
      Dir.entries(@path.to_s).select { |x| x.include?('dcom权限列表') && x.include?('csv') }
    end

    # 所有文件路径
    def file_paths
      file_names.map { |name| @path.join(name) }
    end

    # 读取csv数据，支持两种encode，默认导出来的数据要用第一种解析，如果用户修改过内容，第一种会报错，需要用第二种解析
    def read_csv(file_path)
      parse_csv(file_path, 'r:gb2312:utf-8')
    rescue StandardError => _e
      parse_csv(file_path, 'r:bom|utf-8')
    end

    # 解析csv
    def parse_csv(file_path, encode)
      file = File.open(file_path, encode)
      CSV.parse(file, headers: false)
    end
  end
end