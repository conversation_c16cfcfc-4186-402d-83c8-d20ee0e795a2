require 'httparty'
module AasOracleImporter
  class QzBaoleijiImporter < ImporterBase
    include HTTParty
    default_options.update(verify: false)
    def config
      @bs_id              = importer_config['bs_id']
      @user               = importer_config['user']
      @password           = importer_config['password']
      @users_url           = importer_config['users_url'] || '/shterm/api/user?loginNameContains=&userNameStartsWith&size=2000'
      @token              = nil
      self.class.base_uri importer_config['base_uri']
    end

    def import_to_do
      destroy_old_data
      login
      import_accounts
      import_ledgers(QzBaoleiji::Account)
    end

    # MARK: !!!这个系统不存在，因此重写，防止输出日志出错, 实际存在系统不要包含此代码
    def the_system
      BusinessSystem.find_by(id: 310)
    end

    def import_test
      puts login
    end

    private

    def destroy_old_data
      QzBaoleiji::Account.where(quarter_id: @quarter_id).delete_all
    end

    def login
      body     = { username: @user, password: @password }
      response = self.class.post('/shterm/api/authenticate', { headers: headers, body: body.to_json })
      result   = JSON.parse response.body
      @token = result['ST_AUTH_TOKEN']
    end

    def logout
      # response = self.class.post('/loginController/logout', { headers: headers })
      # result   = JSON.parse response.body
      # pp result['code']
      # pp result['message']
    end

    def headers
      { 'Content-Type' => 'application/json', 'charset' => 'utf-8', 'st-auth-token' => @token }
    end

    def import_accounts
      response = self.class.get(@users_url, { headers: headers })
      result   = JSON.parse response.body
      accounts = result['content'].to_a
      accounts.each do |account|
        QzBaoleiji::Account.create(
          quarter_id:   @quarter_id,
          code:         account['loginName'],
          name:         account['userName'],
          status:       account['state'].to_s == '0' || account['state'].to_s == '1'
        )
      end
    end

  end
end



