# frozen_string_literal: true

module AasOracleImporter
  class QingsuanDepartmentImporter < ImporterBase
    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']
      @accounts    = []
      @data1_permissions = []
      initialize_tables
    end

    def initialize_tables
      @table_departments     = "#{@table_space}department#{@sid_suffix}"
      @table_users           = "#{@table_space}users#{@sid_suffix}"
      @table_user_roles      = "#{@table_space}user_role#{@sid_suffix}"
      @table_menus           = "#{@table_space}menu_def#{@sid_suffix}"
      @table_user_role_rela  = "#{@table_space}user_role_rela#{@sid_suffix}"
      @table_user_role_right = "#{@table_space}user_role_right#{@sid_suffix}"
    end

    def import_to_do
      destroy_exist_records
      import_departments
      import_accounts
      import_roles
      import_menus
      import_accounts_roles
      import_menus_roles
      import_ledgers(Qingsuan::Account)
    end

    def destroy_exist_records
      Qingsuan::Account.where(quarter_id: @quarter_id).destroy_all
      Qingsuan::Department.where(quarter_id: @quarter_id).destroy_all
      Qingsuan::Role.where(quarter_id: @quarter_id).destroy_all
      Qingsuan::Menu.where(quarter_id: @quarter_id).destroy_all
    end

    def import_departments_sql
      <<-SQL
        select dep_id, dep_code, dep_name, dep_full_name, dep_type, parent_dep_id, dep_status from #{@table_departments}
      SQL
    end

    def import_departments
      ActiveRecord::Base.transaction do
        @database.exec(import_departments_sql) do |r|
          if r[0] && r[1]
            Qingsuan::Department.create(
              quarter_id:                  @quarter_id,
              import_department_id:        r[0],
              code:                        r[1],
              name:                        r[2],
              full_name:                   r[3],
              category:                    r[4],
              parent_import_department_id: r[5],
              status:                      r[6].to_i.zero?
            )
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[1]}' or name '#{r[2]}' in #{r.join}"
          end
        end
      end
    end

    def import_accounts_sql
      <<-SQL
        select user_code, user_name, user_status from #{@table_users}
      SQL
    end

    def import_accounts
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_sql) do |r|
          if r[0] && r[1]
            account = Qingsuan::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1],
              status:     r[2].to_i.zero?
            )
            @accounts << account
          else
            @logger.warn "#{self.class}.#{__method__}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles_sql
      <<-SQL
        select role_id, role_name, dep_id from #{@table_user_roles}
      SQL
    end

    def import_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_sql) do |r|
          department = Qingsuan::Department.find_by(import_department_id: r[2])
          name = department.present? ? department.name + ' - ' + r[1] : r[1]
          Qingsuan::Role.create(
            quarter_id:           @quarter_id,
            code:                 r[0],
            name:                 name,
            import_department_id: r[2]
          )
        end
      end
    end

    def import_menus_sql
      <<-SQL
        select
          menu_code,
          menu_name,
          menu_level,
          parent_menu_code
        from
          #{@table_menus}
        order by
          menu_code
      SQL
    end

    def import_menus
      ActiveRecord::Base.transaction do
        menus = []
        @database.exec(import_menus_sql) do |r|
          menus << r
        end
        new_menus = calculate_menus(menus)
        time = Time.now
        Qingsuan::Menu.bulk_insert(:quarter_id, :code, :name, :level, :parent_code, :full_name, :created_at, :updated_at) do |obj|
          obj.set_size = 1000
          new_menus.each do |menu|
            obj.add [@quarter_id, menu[0], menu[1], menu[2], menu[3], menu[4], time, time]
          end
        end
      end
    end

    def import_accounts_roles_sql
      <<-SQL
        select
          user_code, role_id
        from
          #{@table_user_role_rela}
      SQL
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_accounts_roles_sql) do |r|
          account = Qingsuan::Account.where(quarter_id: @quarter_id).find_by(code: r[0])
          role    = Qingsuan::Role.where(quarter_id: @quarter_id).find_by(code: r[1])

          if account && role
            QingsuanAccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[1]}" unless role
          end
        end
      end
    end

    def import_menus_roles_sql
      <<-SQL
        select
          role_id, menu_code
        from
          #{@table_user_role_right}
      SQL
    end

    def import_menus_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_menus_roles_sql) do |r|
          role = Qingsuan::Role.where(quarter_id: @quarter_id).find_by(code: r[0])
          menu = Qingsuan::Menu.where(quarter_id: @quarter_id).find_by(code: r[1])

          if menu && role
            QingsuanMenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}.#{__method__}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}.#{__method__}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    # 菜单数据进行运算，计算树形菜单组合的名称
    def calculate_menus(menus)
      menus.map do |menu|
        full_name = get_full_name(menu, menus)
        menu << full_name
      end
    end

    # 菜单全名，包括所有祖先级菜单
    def get_full_name(menu, menus)
      names = []
      names << menu[1]
      while (menu_data = menus.find { |m| menu[3] == m[0] }).present?
        names << menu_data[1]
        menu = menu_data
      end
      names.reverse.join(' -> ')
    end
  end
end
