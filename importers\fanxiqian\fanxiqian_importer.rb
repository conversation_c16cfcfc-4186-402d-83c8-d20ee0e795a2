module AasOracleImporter
  class FanxiqianImporter < ImporterBase
    def config
      @bs_id    = 70
      @accounts = []
      @roles    = []

      @table_space   = importer_config['table_space']
      @sid_suffix    = importer_config['sid_suffix']
      @c_versiontype = importer_config['c_versiontype']
      @data1_parent_code = importer_config['data1_parent_code']
      @data1_permissions                = []
      @data1_accounts_roles_permissions = []

      @data2_permissions                = []
      @data2_accounts_roles_permissions = []

      initialize_tables
      # initialize_classes
    end

    def initialize_tables
      @table_account      = "#{@table_space}hsi_user#{@sid_suffix}"
      @table_role         = "#{@table_space}hsi_group#{@sid_suffix}"
      @table_account_role = "#{@table_space}hsi_usergroup#{@sid_suffix}"
      @table_menu         = "#{@table_space}hsi_menu#{@sid_suffix}"
      @table_role_menu    = "#{@table_space}hsi_groupright#{@sid_suffix}"
      @table_right        = "#{@table_space}hsi_right#{@sid_suffix}"
      @table_role_right   = "#{@table_space}hsi_groupright#{@sid_suffix}"
    end

    def import_to_do
      import_accounts
      import_roles
      import_accounts_roles

      import_data1_permissions

      import_data1_role_permissions

      import_data2_permissions

      import_data2_role_permissions

      import_ledgers(Fanxiqian::Account)
    end

    def import_accounts_sql
      <<-EOF
        select t.l_userid ,t.c_usercode ,t.c_username ,c_status from #{@table_account} t
      EOF
    end

    def import_accounts
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_accounts_sql).each do |r|
          @accounts << Fanxiqian::Account.create(
            quarter_id: @quarter_id,

            source_id:  get_enum(enums, 'source_id', r[0]),

            code:       get_enum(enums, 'code', r[1]),

            name:       get_enum(enums, 'name', r[2]),

            status:     r[3]&.to_i == 0
          )
        end
      end
    end

    def import_roles_sql
      filter = @c_versiontype.present? ? " and t.c_versiontype in ('#{@c_versiontype.join("','")}')" : ''

      <<-EOF
         select t.l_groupid,t.l_groupid, t.c_groupname from #{@table_role} t where t.c_isuse = 1 #{filter}
      EOF
    end

    def import_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_roles_sql).each do |r|
          @roles << Fanxiqian::Role.create(quarter_id: @quarter_id, source_id: get_enum(enums, 'source_id', r[0]),
                                           code: get_enum(enums, 'code', r[1]), name: get_enum(enums, 'name', r[2]))
        end
      end
    end

    def import_accounts_roles_sql
      <<-EOF
        select t.l_groupid, t.l_userid from #{@table_account_role} t
      EOF
    end

    def import_accounts_roles
      ActiveRecord::Base.transaction do
        enums = []
        select_db_datas(import_accounts_roles_sql).each do |r|
          role    = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          account = @accounts.find { |x| x.source_id.to_s == r[1].to_s }
          Fanxiqian::AccountsRole.create(account_id: account.id, role_id: role.id) if account && role
        end
      end
    end

    def import_data1_permissions_sql
      <<-EOF
        SELECT T.C_MENUCODE,T.C_MENUCODE, T.C_MENUNAME#{@data1_parent_code ? ', T.C_PARENTCODE' : ''} FROM #{@table_menu} T where  t.c_isuse = 1
      EOF
    end

    def import_data1_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data1_permissions_sql).each do |r|
          name = get_enum(enums, 'level1_name', r[level1_name_index])
          level1_name = replace_blank_name(name)
          json = {
            quarter_id:  @quarter_id,
            source_id:   get_enum(enums, 'source_id', r[0]),
            code:        get_enum(enums, 'code', r[1]),
            level1_name: level1_name
          }
          json[:level2_name] = r[3].to_s if @data1_parent_code
          @data1_permissions << Fanxiqian::Data1Permission.create(json)
        end
      end
    end

    def import_data1_role_permissions_sql
      <<-EOF
         select t.l_groupid, t.c_rightcode from #{@table_role_menu} t  where t.c_rightclass = 1
      EOF
    end

    def import_data1_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data1_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data1_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          if permission && role
            Fanxiqian::Data1AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                           data1_permission_id: permission.id)
          end
        end
      end
    end

    def import_data2_permissions_sql
      <<-EOF
         select t.c_rightcode, t.c_rightcode, t.c_rightname  from #{@table_right} t where t.c_isuse = 1
      EOF
    end

    def import_data2_permissions
      enums = []

      ActiveRecord::Base.transaction do
        level1_name_index = 2
        parent_id_index   = nil
        select_db_datas(import_data2_permissions_sql).each do |r|
          name = get_enum(enums, 'level1_name', r[level1_name_index])

          level1_name = replace_blank_name(name)

          json = {
            quarter_id:  @quarter_id,

            source_id:   get_enum(enums, 'source_id', r[0]),

            code:        get_enum(enums, 'code', r[1]),

            level1_name: level1_name

          }
          @data2_permissions << Fanxiqian::Data2Permission.create(json)
        end
      end
    end

    def import_data2_role_permissions_sql
      <<-EOF
        select t.l_groupid, t.c_rightcode from #{@table_role_right} t where t.c_rightclass = 0
      EOF
    end

    def import_data2_role_permissions
      enums = []
      ActiveRecord::Base.transaction do
        select_db_datas(import_data2_role_permissions_sql).each do |r|
          role       = @roles.find { |x| x.source_id.to_s == r[0].to_s }
          permission = @data2_permissions.find { |x| x.source_id.to_s == r[1].to_s }
          if permission && role
            Fanxiqian::Data2AccountsRolesPermission.create(quarter_id: @quarter_id, role_id: role.id,
                                                           data2_permission_id: permission.id)
          end
        end
      end
    end

    def select_db_datas(sql)
      output_datas = []
      @database.exec(sql) do |r|
        output_datas << (r.instance_of?(Hash) ? r.map { |_k, v| v } : r)
      end
      output_datas
    end

    # 通过枚举获取值,注意对比的value都是字符串
    def get_enum(enums, field, value)
      enum = enums.find { |obj| obj['name'] == field && obj['value'] == value&.to_s }
      enum&.[]('enum_value') || value
    end

    # 返回包含祖先菜单名称的名称
    # name: 名称、parent_id：父ID、name_index: 名称索引，parent_id_index: 父ID索引
    def full_name(sql, name, parent_id = nil, name_index, parent_id_index)
      return name if parent_id.blank?

      sql       = sql.downcase
      id_column = sql.match(/(?<=select).*(?=from)/).to_s.split(',').map(&:strip)[0]
      new_sql   = sql + " #{sql.downcase.include?(' where ') ? 'and' : 'where'} #{id_column}='#{parent_id}' limit 1"
      select_db_datas(new_sql).each do |r|
        parent_name     = r[name_index]
        parent_id_value = r[parent_id_index]
        if parent_name.present? && r[parent_id_index] != parent_id
          name = "#{parent_name} -> #{name}"
          name = full_name(sql, name, parent_id_value, name_index, parent_id_index)
        end
      end
      name
    end

    def replace_blank_name(name)
      return name if name.blank?

      name.gsub('&nbsp;', ' ')
    end
  end
end
