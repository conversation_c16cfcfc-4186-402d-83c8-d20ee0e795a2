module AasOracleImporter

  class XieningTouyanCsfundsImporter < ImporterBase

    def config
      @bs_id       = importer_config['bs_id']
      @table_space = importer_config['table_space']
      @sid_suffix  = importer_config['sid_suffix']

      initialize_tables
      # initialize_classes
    end

    def import_to_do
      destroy_old_data
      import_accounts_and_roles
      import_roles_relation
      import_permissions
      re_report_parent_code(@quarter_id)
      import_ledgers(Touyan::Account)
      # `cd #{AasOracleImporter.config['server']['path']} && rails runner 'Quarter.find(#{@quarter_id}).store_all_users_accounts_caches'`
    end

    private

    def initialize_tables
      @table_org_object    = "#{@table_space}sprt_orgobject#{@sid_suffix}"  # 账号和角色表
      @table_user_property = "#{@table_space}um_userproperty#{@sid_suffix}" # 获取账号状态表
      @table_org_rela      = "#{@table_space}sprt_orgrela#{@sid_suffix}"    # 角色与账号及上级角色关联表
      @table_right_auth    = "#{@table_space}sprt_rightauth#{@sid_suffix}"  # 权限角色账号关联
      @table_right_def     = "#{@table_space}sprt_rightdef#{@sid_suffix}"   # 获取权限类型表名
      @table_meta_db       = "#{@table_space}metadb_entity#{@sid_suffix}"   # 获取权限类型表名
      @table_sirm          = "#{@table_space}sirm_enum#{@sid_suffix}"       # 附加权限字典
      @table_userInfo      = "#{@table_space}um_userinfo#{@sid_suffix}"     # 用户信息，获取登录名，用于关联匹配
    end

    def destroy_old_data
      Touyan::Account.where(quarter_id: @quarter_id).destroy_all
      Touyan::Role.where(quarter_id: @quarter_id).destroy_all
      Touyan::Permission.where(quarter_id: @quarter_id).destroy_all
      Touyan::AccountsRolesPermission.where(quarter_id: @quarter_id).destroy_all
    end

    def import_org_object_sql
      <<-SQL
      select o.OBJID,o.ORGNAME,o.OBJECTTYPE,o.DESCRIPTION,o.UNITTYPE,o.USERID,o.INSERVICE,o.ORGID,u.username from #{@table_org_object} o
      left join #{@table_userInfo} u
      on u.userid = o.userid
      SQL
    end

    # 由于 @table_user_property 需要参数，下面方法供 sql 测试使用
    def check_user_property_sql
      <<-SQL
        select OBJID,USERID,NAME,VALUE from #{@table_user_property} where rownum = 1
      SQL
    end

    def user_property_sql_by_userid(user_id)
      <<-SQL
        select OBJID,USERID,NAME,VALUE from #{@table_user_property} where USERID = '#{user_id}' and NAME = 'inservice'
      SQL
    end

    def import_accounts_and_roles
      ActiveRecord::Base.transaction do
        @database.exec(import_org_object_sql) do |r|
          if r[2].to_i == 1
            account_status = r[6].to_i == 1
            # 优先获取登录名为code
            code = r[8].present? ? r[8] : r[5]
            Touyan::Account.create(quarter_id: @quarter_id, code: code, name: r[1], status: account_status, objid: r[7])
          else
            Touyan::Role.create(quarter_id: @quarter_id, code: r[7], name: r[1], role_type: r[4])
          end
        end
      end
    end

    def import_roles_relation_sql
      <<-SQL
        select FROMOBJECTID, TOOBJECTID from #{@table_org_rela}
      SQL
    end

    def import_roles_relation
      ActiveRecord::Base.transaction do
        @database.exec(import_roles_relation_sql) do |r|
          role = Touyan::Role.find_by(quarter_id: @quarter_id, code: r[1])
          role&.update(parent_code: r[0])
        end

        @database.exec(import_roles_relation_sql) do |r|
          account = Touyan::Account.find_by(quarter_id: @quarter_id, objid: r[1])
          role    = Touyan::Role.find_by(quarter_id: @quarter_id, code: r[0])
          if account && role
            account.roles << role unless account.roles.include?(role)

            while role.parent_code
              role = Touyan::Role.find_by(quarter_id: @quarter_id, code: role.parent_code)
              break unless role

              account.roles << role unless account.roles.include?(role)
            end
          end
        end
      end
    end

    def right_auth_sql
      <<-SQL
        select OBJID, AUTHORGID, AUTHORGEXP, OBJECTKEY, RIGHTDEFINEKEY, REJECTFLAG, RIGHTTYPE from #{@table_right_auth}
      SQL
    end

    %w[IR_REPORTCOLUMN IR_REPORTTYPE SPRT_SYSMENU IP_poolinfo DI_DOCTYPE DI_DOCCOLUMN].each do |the_table|
      define_method("check_#{the_table}_sql") do
        <<-SQL
          select * from #{@table_space}#{the_table}#{@sid_suffix} where rownum = 1
        SQL
      end
    end

    def import_permissions
      table_json = set_permission_table_json
      key_json   = set_permission_key_json #附加权限

      ActiveRecord::Base.transaction do
        table_json.each do |key, value|
          next unless %w[IR_REPORTCOLUMN IR_REPORTTYPE SPRT_SYSMENU IP_poolinfo DI_DOCTYPE DI_DOCCOLUMN].include?(value[:table_name])

          begin
            # 目前已知从以下表格中获取权限， 0 1 2分别代表获取到的数据第几位是code、name、和上级权限
            case value[:table_name]
            when 'IR_REPORTCOLUMN'
              code, name, parent_code = 0, 1, 2
            when 'IR_REPORTTYPE'
              code, name, parent_code = 0, 2, 1
            when 'SPRT_SYSMENU'
              code, name, parent_code = 0, 3, 2
            when 'IP_poolinfo'
              code, name, parent_code = 0, 1, 4
            when 'DI_DOCTYPE'
              code, name, parent_code = 0, 1, 2
            when 'DI_DOCCOLUMN'
              code, name, parent_code = 0, 2, -1
            end
            sql = <<-EOF
              select * from #{@table_space}#{value[:table_name]}#{@sid_suffix}
            EOF

            @database.exec(sql) do |r|
              r_code, r_name = "#{key}-#{r[code]}", r[name]
              r_parent_code  = parent_code == -1 || r[parent_code].to_i <= 0 ? nil : "#{key}-#{r[parent_code]}"

              permission = Touyan::Permission.create(quarter_id: @quarter_id, code: r_code, name: r_name, parent_code: r_parent_code, permission_type: value[:name])
            end
          rescue
            puts "table #{value[:table_name]} not exist #{$!} #{$@}"
          end
        end

        permission_list_json = {}
        Touyan::Permission.where(quarter_id: @quarter_id).each do |permission|
          permission_list_json[permission.code] = permission.id
        end

        role_list_json = {}
        Touyan::Role.where(quarter_id: @quarter_id).each do |role|
          role_list_json[role.code] = role.id
        end

        account_list_json = {}
        Touyan::Account.where(quarter_id: @quarter_id).each do |account|
          account_list_json[account.code] = account.objid
        end

        # 权限关联到账号和角色
        @database.exec(right_auth_sql) do |r|
          permission_id = permission_list_json["#{r[4]}-#{r[3]}"]
          if permission_id
            account_id = account_list_json[r[1]]
            role_id    = role_list_json[r[1]]
            if account_id
              roles_permission = Touyan::AccountsRolesPermission.create(
                quarter_id:    @quarter_id,
                permission_id: permission_id,
                account_id:    account_id
              )
            elsif role_id
              roles_permission = Touyan::AccountsRolesPermission.create(
                quarter_id:    @quarter_id,
                permission_id: permission_id,
                role_id:       role_id
              )
            end
            if roles_permission
              #puts "#{r}~~~~~#{roles_permission.to_json}11~~~~~~~~~#{key_json[r[4]]}~~~~~~~#{r[6]}~~~~~"
              if key_json[r[4]]
                additional_permission = key_json[r[4]].find { |x| x[:code] == r[6] }
                if additional_permission
                  additional_permission_list             = roles_permission.additional_permission.to_s.split(',')
                  roles_permission.additional_permission = (additional_permission_list | [additional_permission[:name]]).sort.join(",")
                  roles_permission.save
                end
              end
            end
          end
        end
      end
    end

    def set_permission_table_json_sql
      # 从下边sql中获取权限类型，及权限表名称
      <<-SQL
        select a.rightdefinekey 权限关键字,nvl(b.entityinfo,'外部报告') 说明,nvl(b.entitytable,a.rightdefinekey) 相关表名 
        from #{@table_right_def} a left join #{@table_meta_db} b on a.rightdefinekey = b.entityname
      SQL
    end

    def set_permission_table_json
      permission_table_json = {}

      @database.exec(set_permission_table_json_sql) do |r|
        # DOCTYPE2 和 DOCCOLUMN2 为新建表名，类型统一
        r[2] = 'DI_DOCTYPE' if r[0] == 'DOCTYPE2'
        r[2] = 'DI_DOCCOLUMN' if r[0] == 'DOCCOLUMN2'

        permission_table_json[r[0]] = {
          name:       r[1],
          table_name: r[2]
        }
      end

      permission_table_json
    end

    def set_permission_key_json_sql
      <<-SQL
        select OBJID, CATALOG, TYPE, NAME from #{@table_sirm} where catalog = 'ORG' and type = 'authority'
      SQL
    end

    def set_permission_key_json
      permission_key_json = {}

      @database.exec(set_permission_key_json_sql) do |r|
        param_s                         = r[3].split(":")

        permission_key_json[param_s[0]] = [] unless permission_key_json[param_s[0]]
        permission_key_json[param_s[0]] << {
          code: param_s[1],
          name: param_s[2]
        }
      end
      permission_key_json
    end

    def re_report_parent_code(quarter_id)
      # 统一权限类型名称

      Touyan::Permission.where(quarter_id: quarter_id, permission_type: '内部报告类型').each do |permission|
        next unless permission.parent_code

        parent_permission = Touyan::Permission.find_by(quarter_id: quarter_id, code: permission.parent_code)
        unless parent_permission
          permission.parent_code = permission.parent_code.gsub("REPORTTYPE", "REPORTCOLUMN")
          permission.save
        end
      end
      Touyan::Permission.where(quarter_id: quarter_id, permission_type: '内部报告栏目').each do |permission|
        next unless permission.parent_code

        parent_permission = Touyan::Permission.find_by(quarter_id: quarter_id, code: permission.parent_code)
        unless parent_permission
          permission.parent_code = permission.parent_code.gsub("REPORTCOLUMN", "REPORTTYPE")
          permission.save
        end
      end
      Touyan::Permission.where(quarter_id: quarter_id, permission_type: '外部报告').each do |permission|
        next unless permission.parent_code

        parent_permission = Touyan::Permission.find_by(quarter_id: quarter_id, code: permission.parent_code)
        next if parent_permission

        if permission.parent_code.include?('DOCTYPE2')
          permission.parent_code = permission.parent_code.gsub("DOCTYPE2", "DOCCOLUMN2")
        else
          permission.parent_code = permission.parent_code.gsub("DOCCOLUMN2", "DOCTYPE2")
        end
        permission.save
      end
    end

  end
end



