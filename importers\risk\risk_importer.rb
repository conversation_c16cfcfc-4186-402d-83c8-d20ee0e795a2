module AasOracleImporter

  class RiskImporter < ImporterBase

    def config
      @bs_id                = importer_config['bs_id']
      @table_accounts       = importer_config['table_accounts']
      @table_roles          = importer_config['table_roles']
      @table_accounts_roles = importer_config['table_accounts_roles']
      @table_menus          = importer_config['table_menus']
      @table_menus_roles    = importer_config['table_menus_roles']
    end

    def import_to_do
      import_accounts
      import_roles
      import_menus
      import_menus_roles
      import_accounts_roles
      import_ledgers(Risk::Account)
    end

    def import_accounts

      Risk::Account.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT
          u.id,
          u.username,
          u.EMPLID,
          USEFLAG
        FROM
          #{@table_accounts} u
        ORDER BY
          u.USERNAME
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          if r[0] and r[1]
            status = r[3] == 'A'
            Risk::Account.create(
              quarter_id: @quarter_id,
              code:       r[0],
              name:       r[1].strip,
              emplid:     r[2]&.strip,
              status:     status
              )
          else
            @logger.warn "#{self.class}: not found account code '#{r[0]}' or name '#{r[1]}' in #{r.join}"
          end
        end
      end
    end

    def import_roles

      Risk::Role.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT rolecode, rolename FROM #{@table_roles}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Risk::Role.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus

      Risk::Menu.where(quarter_id: @quarter_id).destroy_all

      sql = <<-EOF
        SELECT menucode, menuname FROM #{@table_menus}
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          Risk::Menu.create(
            quarter_id: @quarter_id,
            code: r[0],
            name: r[1]
            )
        end
      end
    end

    def import_menus_roles

      sql = <<-EOF
        SELECT
          r.ROLECODE,
          m.menucode
        FROM
          #{@table_roles} r,
          #{@table_menus} m,
          #{@table_menus_roles} rm
        WHERE
          r.id = rm.ROLE_ID
          AND m.ID = rm.MENU_ID
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          role = Risk::Role.where(quarter_id: @quarter_id).find_by_code(r[0])
          menu = Risk::Menu.where(quarter_id: @quarter_id).find_by_code(r[1])

          if menu and role
            Risk::MenusRoles.create(menu_id: menu.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found role #{r[0]}" unless role
            @logger.warn "#{self.class}: not found menu #{r[1]}" unless menu
          end
        end
      end
    end

    def import_accounts_roles
      sql = <<-EOF
        SELECT
          u.id,
          r.rolecode
        FROM
          #{@table_accounts} u,
          #{@table_roles} r,
          #{@table_accounts_roles} ur
        WHERE
          u.id = ur.USER_ID
          AND r.ID = ur.ROLE_ID
          AND u.USEFLAG = 'A'
      EOF

      ActiveRecord::Base.transaction do
        @database.exec(sql) do |r|
          account = Risk::Account.where(quarter_id: @quarter_id).find_by_code(r[0])
          role    = Risk::Role.where(quarter_id: @quarter_id).find_by_code(r[1])
          if account and role
            Risk::AccountsRoles.create(account_id: account.id, role_id: role.id)
          else
            @logger.warn "#{self.class}: not found account #{r[0]}" unless account
            @logger.warn "#{self.class}: not found role #{r[1]}" unless role
          end
        end
      end
    end

  end
end



